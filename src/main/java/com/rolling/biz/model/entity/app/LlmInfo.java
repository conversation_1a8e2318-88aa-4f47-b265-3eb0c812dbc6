package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.handler.GsonTypeHandler;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties({"version", "creatorId", "creatorName", "updaterId", "updaterName"})
public class LlmInfo extends BaseEntity {
    @Column(ignore = true)
    private Long creatorId;
    @Column(ignore = true)
    private String creatorName;
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;

    private String name;
    private String icon;
    private String provider;
    @Column(typeHandler = GsonTypeHandler.class)
    private ProviderParams providerParams;
    private String endPointId;
    private String remark;

    @Getter
    @Setter
    public static class ProviderParams {
        private String modelName;
        private String apiUrl;
        private String apiKey;
    }
}

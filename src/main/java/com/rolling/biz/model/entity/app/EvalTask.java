package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties({"version", "creatorId", "creatorName", "updaterId", "updaterName"})
public class EvalTask extends BaseEntity {

    @Column(ignore = true)
    private Long creatorId;
    @Column(ignore = true)
    private String creatorName;
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;

    private Long evalAppId;
    private String taskType;
    private String taskDef;
    private String status;
    private String failedReason;
    private String externalId;
}

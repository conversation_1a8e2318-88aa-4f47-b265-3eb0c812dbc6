package com.rolling.biz.model.entity.system;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("file_info")
@JsonIgnoreProperties({"version"})
public class FileInfo extends BaseEntity {
    private String uuid;
    private String originalFileName;
    private String fileName;
    private Long fileSize;
    private String contentType;
    private byte[] content;
    private String storageKey;
}

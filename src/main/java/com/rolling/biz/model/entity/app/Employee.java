package com.rolling.biz.model.entity.app;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Table("employee")
@JsonIgnoreProperties({"version", "createTime", "updateTime", "creatorId", "creatorName", "updaterId", "updaterName"})
public class Employee extends BaseEntity {
    @Column(ignore = true)
    private Long version;
    @Column(ignore = true)
    private Instant createTime;
    @Column(ignore = true)
    private Instant updateTime;
    @Column(ignore = true)
    private Long creatorId;
    @Column(ignore = true)
    private String creatorName;
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;

    @ExcelProperty(value = "员工工号")
    private String jobNumber;
    @ExcelProperty(value = "钉钉Unionid")
    private String unionId;
    @ExcelProperty(value = "姓名")
    private String name;
    @ExcelProperty(value = "一级部门描述")
    private String dept1;
    @ExcelProperty(value = "二级部门描述")
    private String dept2;
    @ExcelProperty(value = "三级部门描述")
    private String dept3;
    @ExcelProperty(value = "四级部门描述")
    private String dept4;
    @ExcelProperty(value = "五级部门描述")
    private String dept5;
    @ExcelProperty(value = "六级部门描述")
    private String dept6;
    @ExcelProperty(value = "标准岗位")
    private String standardPost;
    @ExcelProperty(value = "岗位号码")
    private String postNumber;
    @ExcelProperty(value = "个人岗位")
    private String personalPost;
    @ExcelProperty(value = "个人职级描述")
    private String personalRank;
    @ExcelProperty(value = "所属条线")
    private String lineName;
    /**
     * 提取的岗位职责描述
     */
    @ExcelProperty(value = "岗位职责描述")
    private String jobContent;
    @Column(ignore = true)
    private Integer taskNumber = 0;
    @Column(ignore = true)
    private Integer completeTaskNumber = 0;
}

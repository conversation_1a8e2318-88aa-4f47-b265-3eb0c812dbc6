package com.rolling.biz.model.entity.app;

import com.mybatisflex.annotation.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Table("calendar_events")
public class CalendarEvent {
    private String eventId;
    private Instant startTime;
    private Instant endTime;
    private String isAllDay;
    private String organizer;
    private String attendees;
    private String summary;
    private String description;
    private String userId;
    private String isRepeat;
}

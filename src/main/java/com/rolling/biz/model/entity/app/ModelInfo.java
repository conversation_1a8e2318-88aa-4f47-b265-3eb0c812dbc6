package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties({"apiUrl", "apiKey"})
public class ModelInfo {
    private String provider;
    private String providerName;
    private Map<String, Object> providerParams;
    private String apiUrl;
    private String apiKey;

    @Getter
    @Setter
    @JsonIgnoreProperties({"voices"})
    public static class VoiceInfo extends ModelInfo {
        private List<Map<String, Object>> voices;
    }
}

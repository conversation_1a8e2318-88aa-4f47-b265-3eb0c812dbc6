package com.rolling.biz.model.entity.system;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties({"version", "password"})
@Table("user")
public class User extends BaseEntity {
    private String name;
    private String avatar;
    private String phone;
    private String email;
    private String password;
}

package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.handler.GsonTypeHandler;
import com.rolling.library.model.entity.BaseEntity;
import lombok.*;

@Getter
@Setter
@Builder

@NoArgsConstructor
@AllArgsConstructor
@Table("robot")
@JsonIgnoreProperties({"version"})
public class Robot extends BaseEntity {
    private String name;
    private String desc;
    private String avatar;
    private Boolean disabled;
    /**
     * 关联的大模型ID
     */
    private Long llmId;
    @Column(typeHandler = GsonTypeHandler.class)
    private LlmConfig llmConfig;
    @Column(typeHandler = GsonTypeHandler.class)
    private TtsConfig ttsConfig;
    @Column(typeHandler = GsonTypeHandler.class)
    private AsrConfig asrConfig;
    /**
     * 外呼机器人配置
     */
    @Column(typeHandler = GsonTypeHandler.class)
    private OutboundbotConfig outboundbotConfig;
    @Column(ignore = true)
    @Builder.Default
    private long processingTaskCount = 0L;
    @Column(ignore = true)
    @Builder.Default
    private long todayCompletedTaskCount = 0L;
    private String platform;
    private String externalAgentId;

    private String evalAppIds;


    @Getter
    @Setter
    public static class LlmConfig {
        /**
         * 模型提供商
         */
        private String provider;
        /**
         * 系统提示词
         */
        private String systemMessage;
        /**
         * 欢迎语
         */
        private String welcomeMessage;
        /**
         * 采样温度
         */
        private Double temperature;
        /**
         * 历史问题轮数
         */
        private Integer historyLength;
    }

    @Getter
    @Setter
    public static class TtsConfig {
        /**
         * 模型提供商
         */
        private String provider;
        /**
         * 音色
         */
        private String voiceType;
        /**
         * 语速
         */
        private Double speedRatio;
        /**
         * 音量
         */
        private Double volumeRatio;
    }

    @Getter
    @Setter
    public static class AsrConfig {
        /**
         * 模型提供商
         */
        private String provider;
        /**
         * 判停时间
         */
        private Integer silenceTime;
        /**
         * 自动打断触发阈值
         */
        private Integer interruptSpeechDuration;
    }


    @Getter
    @Setter
    public static class OutboundbotConfig {
        /**
         * 业务ID
         */
        private String instanceId;
        /**
         * 大模型场景ID
         */
        private String scenarioId;
    }
}

package com.rolling.biz.model.entity.app;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.TreeMap;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Date;


public class AccessToken {
    private final Logger log = LoggerFactory.getLogger(AccessToken.class);

    public enum Privileges {
        PrivPublishStream(0),

        // not exported, do not use directly
        privPublishAudioStream(1),
        privPublishVideoStream(2),
        privPublishDataStream(3),

        PrivSubscribeStream(4);

        public short intValue;

        Privileges(int value) {
            intValue = (short) value;

        }
    }

    public String appID;
    public String appKey;
    public String roomID;
    public String userID;
    public int issuedAt;
    public int expireAt;
    public int nonce;
    public TreeMap<Short, Integer> privileges;

    public byte[] signature;

    // Initializes token struct by required parameters.
    public AccessToken(String appID, String appKey, String roomID, String userID) {
        this.appID = appID;
        this.appKey = appKey;
        this.roomID = roomID;
        this.userID = userID;
        this.issuedAt = Utils.getTimestamp();
        this.nonce = Utils.randomInt();
        this.privileges = new TreeMap<>();
    }

    public static String getVersion() {
        return "001";
    }

    // AddPrivilege adds permission for token with an expiration.
    public void AddPrivilege(Privileges privilege, int expireTimestamp) {
        this.privileges.put(privilege.intValue, expireTimestamp);

        if (privilege.intValue == Privileges.PrivPublishStream.intValue) {
            this.privileges.put(Privileges.privPublishVideoStream.intValue, expireTimestamp);
            this.privileges.put(Privileges.privPublishAudioStream.intValue, expireTimestamp);
            this.privileges.put(Privileges.privPublishDataStream.intValue, expireTimestamp);
        }
    }

    // ExpireTime sets token expire time, won't expire by default.
    // The token will be invalid after expireTime no matter what privilege's expireTime is.
    public void ExpireTime(int expireTimestamp) {
        this.expireAt = expireTimestamp;
    }

    public byte[] packMsg() {
        ByteBuf buffer = new ByteBuf();
        return buffer.put(this.nonce).put(this.issuedAt).put(this.expireAt).put(this.roomID).put(this.userID).putIntMap(this.privileges).asBytes();
    }

    // Serialize generates the token string
    public String Serialize() {
        byte[] msg = this.packMsg();
        try {
            this.signature = Utils.hmacSign(this.appKey, msg);
        } catch (Exception ex) {
            log.error("invalid token", ex);
        }
        ByteBuf buffer = new ByteBuf();
        byte[] content = buffer.put(msg).put(signature).asBytes();
        return getVersion() + this.appID + Utils.base64Encode(content);
    }

    // Parse retrieves token information from raw string
    public static AccessToken Parse(String raw) {
        AccessToken token = new AccessToken("", "", "", "");

        if (raw.length() <= Utils.VERSION_LENGTH + Utils.APP_ID_LENGTH) {
            return token;
        }

        if (!getVersion().equals(raw.substring(0, Utils.VERSION_LENGTH))) {
            return token;
        }

        token.appID = raw.substring(Utils.VERSION_LENGTH, Utils.VERSION_LENGTH + Utils.APP_ID_LENGTH);
        byte[] content = Utils.base64Decode(raw.substring(Utils.VERSION_LENGTH + Utils.APP_ID_LENGTH, raw.length()));

        ByteBuf buffer = new ByteBuf(content);
        byte[] msg = buffer.readBytes();
        token.signature = buffer.readBytes();

        ByteBuf msgBuf = new ByteBuf(msg);
        token.nonce = msgBuf.readInt();
        token.issuedAt = msgBuf.readInt();
        token.expireAt = msgBuf.readInt();
        token.roomID = msgBuf.readString();
        token.userID = msgBuf.readString();
        token.privileges = msgBuf.readIntMap();

        return token;
    }

    @Override
    public String toString() {
        return "AccessToken{" +
                "appID='" + appID + '\'' +
                ", appKey='" + appKey + '\'' +
                ", roomID='" + roomID + '\'' +
                ", userID='" + userID + '\'' +
                ", issuedAt=" + issuedAt +
                ", expireAt=" + expireAt +
                ", nonce=" + nonce +
                ", privileges=" + privileges +
                ", signature=" + Arrays.toString(signature) +
                '}';
    }

    public static class ByteBuf {

        ByteBuffer buffer = ByteBuffer.allocate(1024).order(ByteOrder.LITTLE_ENDIAN);

        public ByteBuf() {
        }

        public ByteBuf(byte[] bytes) {
            this.buffer = ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN);
        }

        public byte[] asBytes() {
            byte[] out = new byte[buffer.position()];
            buffer.rewind();
            buffer.get(out, 0, out.length);
            return out;
        }

        // packUint16
        public ByteBuf put(short v) {
            buffer.putShort(v);
            return this;
        }

        public ByteBuf put(byte[] v) {
            put((short) v.length);
            buffer.put(v);
            return this;
        }

        // packUint32
        public ByteBuf put(int v) {
            buffer.putInt(v);
            return this;
        }

        public ByteBuf put(long v) {
            buffer.putLong(v);
            return this;
        }

        public ByteBuf put(String v) {
            return put(v.getBytes());
        }

        public ByteBuf put(TreeMap<Short, String> extra) {
            put((short) extra.size());

            for (Map.Entry<Short, String> pair : extra.entrySet()) {
                put(pair.getKey());
                put(pair.getValue());
            }

            return this;
        }

        public ByteBuf putIntMap(TreeMap<Short, Integer> extra) {
            put((short) extra.size());

            for (Map.Entry<Short, Integer> pair : extra.entrySet()) {
                put(pair.getKey());
                put(pair.getValue());
            }

            return this;
        }

        public short readShort() {
            return buffer.getShort();
        }


        public int readInt() {
            return buffer.getInt();
        }

        public byte[] readBytes() {
            short length = readShort();
            byte[] bytes = new byte[length];
            buffer.get(bytes);
            return bytes;
        }

        public String readString() {
            byte[] bytes = readBytes();
            return new String(bytes);
        }

        public TreeMap readMap() {
            TreeMap<Short, String> map = new TreeMap<>();
            short length = readShort();
            for (short i = 0; i < length; ++i) {
                short k = readShort();
                String v = readString();
                map.put(k, v);
            }
            return map;
        }

        public TreeMap<Short, Integer> readIntMap() {
            TreeMap<Short, Integer> map = new TreeMap<>();
            short length = readShort();
            for (short i = 0; i < length; ++i) {
                short k = readShort();
                Integer v = readInt();
                map.put(k, v);
            }
            return map;
        }
    }

    public static class Utils {
        public static final long HMAC_SHA256_LENGTH = 32;
        public static final int VERSION_LENGTH = 3;
        public static final int APP_ID_LENGTH = 24;

        public static byte[] hmacSign(String keyString, byte[] msg) throws InvalidKeyException, NoSuchAlgorithmException {
            SecretKeySpec keySpec = new SecretKeySpec(keyString.getBytes(), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(keySpec);
            return mac.doFinal(msg);
        }

        public static String base64Encode(byte[] data) {
            byte[] encodedBytes = Base64.getEncoder().encode(data);
            return new String(encodedBytes);
        }

        public static byte[] base64Decode(String data) {
            return Base64.getDecoder().decode(data.getBytes());
        }

        public static int getTimestamp() {
            return (int) ((new Date().getTime()) / 1000);
        }

        public static int randomInt() {
            return new SecureRandom().nextInt();
        }
    }
}

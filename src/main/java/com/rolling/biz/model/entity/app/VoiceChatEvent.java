package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.handler.GsonTypeHandler;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Table("voice_chat_event")
@JsonIgnoreProperties({"version", "creatorId", "creatorName", "updaterId", "updaterName"})
public class VoiceChatEvent extends BaseEntity {
    @Column(ignore = true)
    private Long creatorId;
    @Column(ignore = true)
    private String creatorName;
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;

    private String appId;
    private String roomId;
    private String taskId;
    private String userId;
    private Integer roundId;
    private Integer eventType;
    private String runStage;
    @Column(typeHandler = GsonTypeHandler.class)
    private ErrorInfo errorInfo;

    @Getter
    @Setter
    public static class ErrorInfo {
        private Integer errorCode;
        private String reason;
    }
}
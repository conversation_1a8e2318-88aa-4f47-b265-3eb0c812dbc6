package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.*;

import java.time.Instant;
import java.util.Map;

@Getter
@Setter
@Builder

@NoArgsConstructor
@AllArgsConstructor
@Table("task_info")
@JsonIgnoreProperties({"version"})
public class TaskInfo extends BaseEntity {
    private String uuid;
    private String name;
    private String type;
    private String desc;
    private String status;
    private Long robotId;
    private String failedReason;
    private String callerNumber;
    private Instant beginTime;
    private Instant endTime;
    private String bgUrl;
    private String inboundConfig;
    private String platform;
    private String externalId;
    private String evalAppIds;

    @Column(ignore = true)
    private Map<String, Object> robot;
    @Column(ignore = true)
    private String clientUrl;
    @Column(ignore = true)
    private Long folderId;
    @Column(ignore = true)
    private Boolean hasConversation;
    @Column(ignore = true)
    private String contactStatus;
    @Column(ignore = true)
    private String taskStatus;
    @Column(ignore = true)
    private String reportUrl;
}

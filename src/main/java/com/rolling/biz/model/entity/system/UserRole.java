package com.rolling.biz.model.entity.system;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties({"version"})
@Table(value = "user_role")
public class UserRole extends BaseEntity {
    private Long userId;
    private Long roleId;
}

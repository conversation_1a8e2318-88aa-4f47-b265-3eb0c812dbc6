package com.rolling.biz.model.entity.app;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Table("department")
@JsonIgnoreProperties({"version", "createTime", "updateTime", "creatorId", "creatorName", "updaterId", "updaterName"})
public class Department extends BaseEntity {
    @Column(ignore = true)
    private Long version;
    @Column(ignore = true)
    private Instant createTime;
    @Column(ignore = true)
    private Instant updateTime;
    @Column(ignore = true)
    private Long creatorId;
    @Column(ignore = true)
    private String creatorName;
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;

    @ExcelProperty(value = "部门ID")
    @Id(keyType = KeyType.Auto)
    private Long id;
    @ExcelProperty(value = "部门名称")
    private String name;
    @ExcelProperty(value = "父部门ID")
    private Long parentId;
    @ExcelIgnore
    private Long ppId;
}

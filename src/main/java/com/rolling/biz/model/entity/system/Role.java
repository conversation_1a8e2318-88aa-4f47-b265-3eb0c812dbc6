package com.rolling.biz.model.entity.system;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties({"version"})
@Table("role")
public class Role extends BaseEntity {
    public final static String ROLE_ADMIN = "admin";
    private String code;
    private String name;
    private String description;
}

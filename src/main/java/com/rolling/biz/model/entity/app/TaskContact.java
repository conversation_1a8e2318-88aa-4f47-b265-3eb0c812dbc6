package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("task_contact")
@JsonIgnoreProperties({"version", "creatorId", "creatorName", "updaterId", "updaterName"})
public class TaskContact extends BaseEntity {
    @Column(ignore = true)
    private Long creatorId;
    @Column(ignore = true)
    private String creatorName;
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;
    @Column(ignore = true)
    private String durationText;

    private Long taskId;
    private Long robotId;
    private String name;
    private String phone;
    private String status;
    private String externalId;
}

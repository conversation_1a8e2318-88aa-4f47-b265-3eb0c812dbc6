package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Table("operate_log")
@JsonIgnoreProperties({"version", "updaterId", "updaterName"})
public class OperateLog extends BaseEntity {
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;

    private String operate;
    private String module;
    private Long moduleId;
    private String moduleCode;
    private String moduleName;
    private String content;
}

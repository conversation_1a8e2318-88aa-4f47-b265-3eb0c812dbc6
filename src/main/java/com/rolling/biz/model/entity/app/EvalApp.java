package com.rolling.biz.model.entity.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties({"version", "creatorId", "creatorName", "updaterId", "updaterName"})
public class EvalApp extends BaseEntity {

    @Column(ignore = true)
    private Long creatorId;
    @Column(ignore = true)
    private String creatorName;
    @Column(ignore = true)
    private Long updaterId;
    @Column(ignore = true)
    private String updaterName;

    private String name;
    private String code;
    private Double temperature;
    private String desc;
    private String status;
    private Long llmId;
    private String systemMessage;
    private String avatar;
    private Boolean disabled;

    @Column(ignore = true)
    private long todayCompletedCount = 0L;
}

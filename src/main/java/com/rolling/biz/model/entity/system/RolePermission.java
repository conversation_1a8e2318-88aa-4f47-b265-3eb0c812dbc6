package com.rolling.biz.model.entity.system;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.rolling.library.model.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties({"version"})
@Table("role_permission")
public class RolePermission extends BaseEntity {
    @Column(ignore = true)
    private Long version;
    private Long roleId;
    private String permission;
}

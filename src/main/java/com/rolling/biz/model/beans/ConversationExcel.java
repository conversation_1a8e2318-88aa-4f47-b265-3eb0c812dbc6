package com.rolling.biz.model.beans;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationExcel {
    @ColumnWidth(50)
    @ExcelProperty(value = "任务名称", index = 0)
    private String taskName;

    @ColumnWidth(40)
    @ExcelProperty(value = "AI访谈官", index = 1)
    private String aiBotName;

    @ColumnWidth(30)
    @ExcelProperty(value = "员工工号", index = 2)
    private String userId;

    @ColumnWidth(30)
    @ExcelProperty(value = "员工姓名", index = 3)
    private String userName;

    @ColumnWidth(40)
    @ExcelProperty(value = "开始时间", index = 4)
    private String beginTime;

    @ColumnWidth(40)
    @ExcelProperty(value = "结束时间", index = 5)
    private String endTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "用时(秒)", index = 6)
    private String duration;

    @ColumnWidth(100)
    @ExcelProperty(value = "对话内容", index = 7)
    private String content;

    @ColumnWidth(50)
    @ExcelProperty(value = "会话ID", index = 8)
    private String roomId;
}

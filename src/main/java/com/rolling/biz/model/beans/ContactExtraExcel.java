package com.rolling.biz.model.beans;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactExtraExcel {
    @ExcelProperty(value = "员工工号")
    private String jobNumber;
    @ExcelProperty(value = "员工姓名")
    private String name;
    @ExcelProperty(value = "岗位描述")
    private String jd;
    @ExcelProperty(value = "问题描述")
    private String qa;
}

package com.rolling.biz.model.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class ListKnowledgeCollectionRes {
    @JSONField(name = "code")
    Integer code;
    @JSONField(name = "message")
    String message;
    @JSONField(name = "request_id")
    String requestId;
    @JSONField(name = "data")
    Data data;

    @Getter
    @Setter
    public static class Data {
        @JSONField(name = "total_num")
        Integer totalNum;
        @JSONField(name = "collection_list")
        List<Collection> collectionList;

        @Getter
        @Setter
        public static class Collection {
            @JSONField(name = "collection_name")
            String collectionName;
            @JSONField(name = "doc_num")
            Integer docNum;
            @JSONField(name = "description")
            String description;
            @JSONField(name = "resource_id")
            String resourceId;
            @JSONField(name = "project")
            String project;
            // @J<PERSON>NField(name = "pipeline_list")
            // private List<Map<String, Object>> pipelineList;
        }
    }
}

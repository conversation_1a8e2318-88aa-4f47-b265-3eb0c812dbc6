package com.rolling.biz.model.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.model.response.ResponseMetadata;
import lombok.Data;

@Data
public class ListRoomInfoResp {
    @JSONField(name = "ResponseMetadata")
    ResponseMetadata responseMetadata;
    @JSONField(name = "Result")
    ListRoomInfoResp.ResultBean result;

    @Data
    public static class ResultBean {
        @JSONField(name = "Total")
        int total;
        @JSONField(name = "PageNum")
        int pageNum;
        @JSONField(name = "PageSize")
        int pageSize;
        @JSONField(name = "<PERSON>M<PERSON>")
        Boolean hasMore;
        @JSONField(name = "RoomList")
        RoomInfo[] roomList;
    }

    @Data
    public static class RoomInfo {
        @JSONField(name = "CallId")
        String callId;
        @JSONField(name = "RoomId")
        String roomId;
        @JSONField(name = "ActiveUserNum")
        int activeUserNum;
        @JSONField(name = "CreatedTime")
        String createdTime;
        @JSONField(name = "DestroyTime")
        String destroyTime;
        @JSONField(name = "IsFinished")
        Boolean isFinished;
    }
}

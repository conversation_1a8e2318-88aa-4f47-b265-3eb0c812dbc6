package com.rolling.biz.model.request.volc;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateVoiceChatReq {
    @J<PERSON><PERSON><PERSON>(name = "AppId")
    String appId;
    @J<PERSON>NField(name = "RoomId")
    String roomId;
    @JSONField(name = "TaskId")
    String taskId;
    @JSONField(name = "Command")
    String command;
    @JSONField(name = "Message")
    String message;
    @JSONField(name = "InterruptMode")
    Integer interruptMode;
}

package com.rolling.biz.model.request.volc;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class StartSubtitleReq {
    @J<PERSON>NField(name = "AppId")
    String appId;
    @J<PERSON><PERSON>ield(name = "RoomId")
    String roomId;
    @J<PERSON>NField(name = "TaskId")
    String taskId;
    @JSONField(name = "DistributionMode")
    int distributionMode = 2;
    @JSONField(name = "ServerMessage")
    ServerMessage serverMessage;
    @JSONField(name = "LanguageConfig")
    LanguageConfig languageConfig;

    @Getter
    @Setter
    @Builder
    public static class LanguageConfig {
        @J<PERSON>NField(name = "SourceLanguages")
        public List<SourceLanguage> sourceLanguages;

        @Getter
        @Setter
        @Builder
        public static class SourceLanguage {
            @JSONField(name = "UserId")
            String userId;
            @J<PERSON>NField(name = "LanguageCode")
            List<String> languageCode;
        }
    }

    @Getter
    @Setter
    @Builder
    public static class ServerMessage {
        @J<PERSON><PERSON>ield(name = "Signature")
        String signature;
        @J<PERSON>NField(name = "Url")
        String url;
    }
}

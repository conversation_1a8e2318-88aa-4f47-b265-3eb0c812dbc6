package com.rolling.biz.model.request.app;

import com.rolling.biz.model.entity.app.Robot;
import com.rolling.library.model.request.BaseReq;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RobotEditReq extends BaseReq {
    private String name;
    private String desc;
    private String avatar;
    private Boolean disabled;
    private Long llmId;
    private Robot.LlmConfig llmConfig;
    private Robot.TtsConfig ttsConfig;
    private Robot.AsrConfig asrConfig;
    private Robot.OutboundbotConfig outboundbotConfig;
    private String platform;
    private String externalAgentId;
    private String evalAppIds;
}

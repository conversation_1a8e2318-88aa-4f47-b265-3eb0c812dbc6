package com.rolling.biz.model.request.app;

import com.rolling.library.model.request.BaseReq;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
public class TaskEditReq extends BaseReq {
    private String name;
    private String type;
    private String desc;
    private Long robotId;
    private String callerNumber;
    private String bgUrl;
    private String inboundConfig;
    private Instant endTime;
    private String evalAppIds;
}

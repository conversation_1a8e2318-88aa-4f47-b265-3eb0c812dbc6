package com.rolling.biz.model.request.volc;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class StartVoiceChatReq {
    @JSONField(name = "AppId")
    String appId;
    @JSONField(name = "RoomId")
    String roomId;
    @JSONField(name = "TaskId")
    String taskId;
    @JSONField(name = "Config")
    Config config;
    @JSONField(name = "AgentConfig")
    AgentConfig agentConfig;

    @Data
    public static class Config {
        /**
         * 语音识别相关配置
         */
        @JSONField(name = "ASRConfig")
        private Map<String, Object> asrConfig;
        /**
         * 语音合成相关配置
         */
        @JSONField(name = "TTSConfig")
        private Map<String, Object> ttsConfig;
        /**
         * 大模型相关配置
         */
        @JSONField(name = "LLMConfig")
        private Map<String, Object> llmConfig;

        /**
         * 打断模式
         * 0: 语音自动打断，默认取值；
         * 1: 禁用语音打断，智能体说话期间，用户的输入内容会被忽略不做处理。
         */
        @JSONField(name = "InterruptMode")
        private int InterruptMode = 0;
    }

    @Data
    public static class AgentConfig {
        @JSONField(name = "TargetUserId")
        List<String> targetUserId;
        @JSONField(name = "WelcomeMessage")
        String welcomeMessage;
        @JSONField(name = "EnableConversationStateCallback")
        boolean enableConversationStateCallback = true;
        @JSONField(name = "ServerMessageSignatureForRTS")
        String serverMessageSignatureForRTS = "conversation";
    }
}

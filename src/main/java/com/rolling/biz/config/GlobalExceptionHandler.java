package com.rolling.biz.config;

import cn.dev33.satoken.exception.NotLoginException;
import com.rolling.library.model.response.ResultResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice(annotations = {RestController.class})
public class GlobalExceptionHandler {
    @ExceptionHandler({NotLoginException.class})
    public ResponseEntity<ResultResp> handleBusinessException(NotLoginException exception) {
        log.warn(String.format("%s caught: {}", ClassUtils.getQualifiedName(exception.getClass())), exception.getMessage());
        return ResponseEntity.status(HttpStatus.OK).body(ResultResp.fail(HttpStatus.UNAUTHORIZED.value(), exception.getMessage()));
    }
}

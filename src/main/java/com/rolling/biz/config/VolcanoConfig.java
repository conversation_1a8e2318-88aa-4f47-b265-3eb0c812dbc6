package com.rolling.biz.config;

import com.rolling.biz.suport.tos.TosConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class VolcanoConfig {
    @Bean(name = "tosConfig")
    @ConditionalOnMissingBean
    public TosConfig ossConfig(
            @Value("${tos.endpoint}") String endpoint,
            @Value("${tos.access-key}") String accessKey,
            @Value("${tos.access-secret}") String accessSecret,
            @Value("${tos.bucket}") String bucket,
            @Value("${tos.region}") String region,
            @Value("${tos.prefix}") String prefix,
            @Value("${tos.cdn}") String cdn
    ) {
        return new TosConfig(endpoint, accessKey, accessSecret, bucket, region, prefix, cdn);
    }
}

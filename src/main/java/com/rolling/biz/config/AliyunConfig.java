package com.rolling.biz.config;

import com.rolling.biz.suport.oss.OssConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class AliyunConfig {
    @Bean(name = "ossConfig")
    @ConditionalOnMissingBean
    public OssConfig ossConfig(
            @Value("${oss.endpoint}") String endPoint,
            @Value("${oss.access-key}") String accessKey,
            @Value("${oss.access-secret}") String accessSecret,
            @Value("${oss.bucket}") String bucketName,
            @Value("${oss.prefix}") String prefix,
            @Value("${oss.cdn}") String cdn
    ) {
        return new OssConfig(endPoint, accessKey, accessSecret, bucketName, prefix, cdn);
    }

    @Bean(name = "teaClient")
    @ConditionalOnMissingBean
    public com.aliyun.teaopenapi.Client teaClient(
            @Value("${aliyun.access_key_id}") String accessKeyId,
            @Value("${aliyun.access_key_secret}") String accessKeySecret,
            @Value("${aliyun.aiagent.region}") String region
    ) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = "ice." + region + ".aliyuncs.com";
        return new com.aliyun.teaopenapi.Client(config);
    }
}

package com.rolling.biz.mapper.app;

import com.mybatisflex.core.BaseMapper;
import com.rolling.biz.model.entity.app.FolderContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FolderContentMapper extends BaseMapper<FolderContent> {

    List<FolderContent> findByAllContentId(@Param("type") String type, @Param("contentIds") List<Long> contentIds);
}

package com.rolling.biz.mapper.app;

import com.mybatisflex.core.BaseMapper;
import com.rolling.biz.model.entity.app.TaskInfo;
import com.rolling.biz.model.request.app.TaskPageReq;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface TaskMapper extends BaseMapper<TaskInfo> {

    List<Map<String, Object>> stateTaskBoard(TaskPageReq req);

    int countTaskBoard(TaskPageReq req);
}

package com.rolling.biz.mapper.app;

import com.mybatisflex.core.BaseMapper;
import com.rolling.biz.model.entity.app.TaskContact;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TaskContactMapper extends BaseMapper<TaskContact> {

    List<Long> findAllIdByStatus(@Param("status") String status);

    List<Long> findAllIdByPostNumber(@Param("postNumber") String postNumber);

    /**
     * 获取任务下的未有访谈记录的员工
     *
     * @param taskId
     * @param maxId
     * @return
     */
    List<TaskContact> listNotInterviewRecord(@Param("taskId") Long taskId, @Param("maxId") Long maxId);
}

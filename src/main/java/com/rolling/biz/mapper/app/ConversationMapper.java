package com.rolling.biz.mapper.app;

import com.mybatisflex.core.BaseMapper;
import com.rolling.biz.model.entity.app.Conversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ConversationMapper extends BaseMapper<Conversation> {
    List<Map<String, Object>> groupingByTaskId(@Param("taskId") Long taskId, @Param("maxId") Long maxId);

    Map<String, Object> findRoomIdByMax(@Param("userId") String userId, @Param("taskId") Long taskId);
}

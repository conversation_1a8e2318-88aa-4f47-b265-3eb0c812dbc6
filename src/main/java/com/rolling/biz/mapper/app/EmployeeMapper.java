package com.rolling.biz.mapper.app;

import com.mybatisflex.core.BaseMapper;
import com.rolling.biz.model.entity.app.Employee;
import com.rolling.biz.model.request.app.EmployeePageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface EmployeeMapper extends BaseMapper<Employee> {

    List<String> groupByPostNumber(@Param("taskId") Long taskId);

    List<Employee> batchGetEmployee(EmployeePageReq req);

    int countByTotal(EmployeePageReq req);

    List<Map<String, Object>> statePostNumber(EmployeePageReq req);

    int countPostNumber(EmployeePageReq req);
}

package com.rolling.biz.suport.annotation;

import com.rolling.biz.constants.system.OperationConstant;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.constants.system.OperateEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Operation {
    /**
     * 操作（登录，登出，新建，查询，修改，删除）
     */
    OperateEnum operate();

    /**
     * 模块（用户，渠道...）
     */
    ModuleEnum module();

    /**
     * 操作日志内容
     */
    String content() default "编号{id},名称{name}";

    /**
     * 参数来源
     * 枚举 request response
     */
    String resource() default OperationConstant.RESPONSE;

    /**
     * 内容参数
     * id，code，name 不用填
     */
    String[] params() default {};


    String nameKey() default "name";

    String codeKey() default "code";
}

package com.rolling.biz.suport.tos;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.model.object.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDateTime;

@Slf4j
@Component
@RequiredArgsConstructor
public class TosService {
    private final TosConfig tosConfig;

    public String getCdnUrl() {
        return tosConfig.getCdn();
    }

    public String putObject(MultipartFile file) {
        TOSV2 client = tosConfig.getClient();
        String uuid = IdUtil.simpleUUID();
        String originalFilename = file.getOriginalFilename();
        String fileName = uuid + originalFilename.substring(originalFilename.lastIndexOf('.'));
        String dateName = DateUtil.format(LocalDateTime.now(), "yyyyMM/dd");
        String storageKey = tosConfig.getPrefix() + "/" + dateName + "/" + fileName;
        try {
            PutObjectBasicInput basicInput = new PutObjectBasicInput().setBucket(tosConfig.getBucket()).setKey(storageKey);
            PutObjectInput input = new PutObjectInput().setPutObjectBasicInput(basicInput).setContent(file.getInputStream());
            PutObjectOutput output = client.putObject(input);
            log.info("put object success, the etag is {}", output.getEtag());
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return storageKey;
    }

    public ByteArrayOutputStream getObject(String objectKey) {
        TOSV2 client = tosConfig.getClient();
        GetObjectV2Input input = new GetObjectV2Input().setBucket(tosConfig.getBucket()).setKey(objectKey);
        try (GetObjectV2Output output = client.getObject(input)) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            output.getContent().transferTo(outputStream);
            return outputStream;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }
}

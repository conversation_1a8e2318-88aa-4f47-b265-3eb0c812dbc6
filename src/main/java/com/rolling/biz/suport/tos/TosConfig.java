package com.rolling.biz.suport.tos;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class TosConfig {
    private String endpoint;
    private String accessKey;
    private String accessSecret;
    private String bucket;
    private String region;
    private String prefix;
    private String cdn;

    public TOSV2 getClient() {
        return new TOSV2ClientBuilder().build(region, endpoint, accessKey, accessSecret);
    }
}

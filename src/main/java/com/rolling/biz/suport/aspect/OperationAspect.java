package com.rolling.biz.suport.aspect;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.biz.suport.annotation.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Parameter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class OperationAspect {
    private final static String RESPONSE_NAME = "response";
    private final static String REQUEST_NAME = "request";
    private final static String ID_NAME = "id";
    private final static String ERROR_NAME = "error";
    private final static String RESULT_KEY = "result";

    private final OperateLogService operateLogService;

    @Pointcut("@annotation(com.rolling.biz.suport.annotation.Operation)")
    private void operate() {
    }

    @Around("operate()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Operation operation = null;
        Parameter[] parameters = null;
        if (null != signature) {
            operation = signature.getMethod().getAnnotation(Operation.class);
            parameters = signature.getMethod().getParameters();
        }
        final String requestStr = JSONUtil.toJsonStr(joinPoint.getArgs());
        // 参数
        Object proceed = joinPoint.proceed();
        addOperate(operation, parameters, requestStr, proceed);
        return proceed;
    }

    private void addOperate(Operation operation, Parameter[] parameters, String requestStr, Object response) {
        if (Objects.isNull(operation)) {
            return;
        }
        if (Objects.isNull(response)) {
            return;
        }
        final String resource = operation.resource();
        Map map = new HashMap();
        if (RESPONSE_NAME.equalsIgnoreCase(resource)) {
            final String responseStr = JSONUtil.toJsonStr(response);
            if (!StringUtils.hasText(responseStr)) {
                return;
            }
            map = JSONUtil.toBean(responseStr, Map.class);
        } else if (REQUEST_NAME.equals(resource)) {
            if (Objects.isNull(parameters) || !StringUtils.hasText(requestStr)) {
                return;
            }
            final List<Object> list = JSONUtil.toList(requestStr, Object.class);
            for (int i = 0; i < parameters.length; i++) {
                final Parameter parameter = parameters[i];
                final String name = parameter.getName();
                final Class<?> type = parameter.getType();
                if (type == Integer.class || type == Long.class
                        || type == Byte.class || type == Double.class
                        || type == Float.class || type == Character.class
                        || type == Short.class || type == Boolean.class
                        || type == String.class || type == List.class) {
                    map.put(name, list.get(i));
                } else {
                    map.putAll((Map) list.get(i));
                }
            }
        }
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        Set<String> collect = Arrays.stream(operation.params()).collect(Collectors.toSet());
        collect.add(operation.nameKey());
        collect.add(operation.codeKey());
        collect.add(ID_NAME);
        final Map<String, String> result = get(collect, map);
        if (CollectionUtils.isEmpty(result) || result.containsKey(ERROR_NAME)) {
            return;
        }
        String content = operation.content();
        for (Map.Entry<String, String> entry : result.entrySet()) {
            final String key = entry.getKey();
            final String value = entry.getValue();
            if (StringUtils.hasText(key) && StringUtils.hasText(value) && content.contains(key)) {
                content = content.replaceAll(key, value);
            }
        }
        /**
         OperateLog optlog = new OperateLog();
         optlog.setContent(content);
         optlog.setOperate(operation.operate().getDesc());
         optlog.setModule(operation.module().getDesc());
         optlog.setModuleCode(result.get(operation.codeKey()));
         optlog.setModuleName(result.get(operation.nameKey()));
         String id = MapUtil.getStr(result, ID_NAME);
         if (StringUtils.hasText(id)) {
         if (NumberUtil.isLong(id)) {
         optlog.setModuleId(NumberUtil.parseLong(id));
         } else {
         optlog.setModuleCode(id);
         }
         }
         final LoginUserContext userContext = LoginUserContextHolder.getContext();
         Optional.ofNullable(userContext).ifPresent(context -> {
         optlog.setCreatorId(context.getUserId());
         optlog.setCreatorName(context.getUserName());
         });
         operateLogService.add(optlog);
         */
    }

    private Map<String, String> get(Set<String> collect, Map map) {
        Map ret = map;
        Map<String, String> result = new HashMap<>();
        if (map.containsKey(RESULT_KEY) && map.get(RESULT_KEY) instanceof Map) {
            ret = (Map) map.get(RESULT_KEY);
        }
        for (String param : collect) {
            if (!StringUtils.hasText(param)) {
                continue;
            }
            String value = null == ret.get(param) ? null : StrUtil.toString(ret.get(param));
            result.put(param, value);
        }
        return result;
    }
}

package com.rolling.biz.suport.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

public class HeadReadListener<T> extends AnalysisEventListener<T> {
    private AtomicInteger count = new AtomicInteger(0);
    private Map<Integer, String> head = new HashMap<>();

    private final BiConsumer<Map<Integer, String>, T> consumer;

    public HeadReadListener(BiConsumer<Map<Integer, String>, T> consumer) {
        this.consumer = consumer;
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        this.head.putAll(headMap);
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        count.incrementAndGet();
        consumer.accept(head, data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        if (count.get() >= 1) {
            return false;
        }
        return true;
    }
}

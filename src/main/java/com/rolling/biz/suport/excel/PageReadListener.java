package com.rolling.biz.suport.excel;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import org.springframework.util.CollectionUtils;

public class PageReadListener<T> implements ReadListener<T> {
    private int batchCount;
    private int limitCount;
    private List<T> cachedDataList;
    private final BiConsumer<List<T>, Integer> consumer;

    private AtomicInteger count = new AtomicInteger(0);
    private AtomicInteger page = new AtomicInteger(1);

    public PageReadListener(int batchCount, int limitCount, BiConsumer<List<T>, Integer> consumer) {
        this.consumer = consumer;
        this.batchCount = batchCount;
        this.limitCount = limitCount;
        this.cachedDataList = ListUtils.newArrayListWithExpectedSize(batchCount);
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        count.incrementAndGet();
        cachedDataList.add(data);
        if (cachedDataList.size() >= this.batchCount) {
            consumer.accept(cachedDataList, page.get());
            page.incrementAndGet();
            cachedDataList = ListUtils.newArrayListWithExpectedSize(this.batchCount);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!CollectionUtils.isEmpty(cachedDataList)) {
            consumer.accept(cachedDataList, page.get());
        }
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        if (count.get() >= limitCount) {
            return false;
        }
        return true;
    }
}

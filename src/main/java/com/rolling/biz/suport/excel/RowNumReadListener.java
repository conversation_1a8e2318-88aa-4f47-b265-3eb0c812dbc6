package com.rolling.biz.suport.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

public class RowNumReadListener extends AnalysisEventListener {
    private AtomicInteger count = new AtomicInteger(0);
    private final Consumer<Integer> consumer;

    public RowNumReadListener(Consumer consumer) {
        this.consumer = consumer;
    }

    @Override
    public void invoke(Object data, AnalysisContext context) {
        count.incrementAndGet();
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        consumer.accept(count.get());
    }
}

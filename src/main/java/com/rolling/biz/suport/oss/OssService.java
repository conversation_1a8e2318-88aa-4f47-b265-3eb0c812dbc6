package com.rolling.biz.suport.oss;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
public class OssService {
    private final OssConfig ossConfig;

    public String getCdnUrl() {
        return ossConfig.getCdn();
    }

    public String getUrl(String storageKey) {
        return String.format("%s/%s", ossConfig.getCdn(), storageKey);
    }

    public String putObject(MultipartFile file) {
        OSS ossClient = ossConfig.getOssClient();
        try {
            String uuid = IdUtil.simpleUUID();
            String originalFilename = file.getOriginalFilename();
            String fileName = uuid + originalFilename.substring(originalFilename.lastIndexOf('.'));
            String dateName = DateUtil.format(LocalDateTime.now(), "yyyyMM/dd");
            String storageKey = ossConfig.getPrefix() + "/" + dateName + "/" + fileName;
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossConfig.getBucket(), storageKey, file.getInputStream());
            ossClient.putObject(putObjectRequest);
            return storageKey;
        } catch (OSSException oe) {
            throw oe;
        } catch (ClientException ce) {
            throw ce;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    public ByteArrayOutputStream getObject(String storageKey) {
        OSS ossClient = ossConfig.getOssClient();
        try {
            GetObjectRequest request = new GetObjectRequest(ossConfig.getBucket(), storageKey);
            OSSObject ossObject = ossClient.getObject(request);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ossObject.getObjectContent().transferTo(outputStream);
            return outputStream;
        } catch (OSSException oe) {
            throw oe;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}

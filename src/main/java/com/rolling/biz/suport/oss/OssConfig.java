package com.rolling.biz.suport.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class OssConfig {
    private String endPoint;
    private String accessKey;
    private String accessSecret;
    private String bucket;
    private String prefix;
    private String cdn;

    public OSS getOssClient() {
        return new OSSClientBuilder().build(endPoint, accessKey, accessSecret);
    }
}

package com.rolling.biz.suport.spi;

import com.dtflys.forest.annotation.*;

import java.util.Map;

@BaseRequest
public interface CustomLlmSpi {
    @Post(
            value = "{apiUrl}",
            headers = {
                    "Content-Type: application/json",
                    "Authorization: Bearer ${apiKey}",
            }
    )
    Map<String, Object> chatCompletion(
            @Var("apiUrl") String apiUrl,
            @Var("apiKey") String apiKey,
            @JSONBody Map<String, Object> body
    );
}

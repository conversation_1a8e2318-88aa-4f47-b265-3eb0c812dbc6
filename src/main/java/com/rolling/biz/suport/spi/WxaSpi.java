package com.rolling.biz.suport.spi;

import com.dtflys.forest.annotation.*;

import java.util.Map;

@BaseRequest(baseURL = "https://api.weixin.qq.com")
public interface WxaSpi {
    /**
     * 获取token
     */
    @Get(value = "/cgi-bin/token?grant_type=client_credential&appid={appId}&secret={secret}")
    Map<String, Object> getToken(@Var("appId") String appId, @Var("secret") String secret);

    /**
     * 小程序登录
     */
    @Get(value = "/sns/jscode2session?grant_type=authorization_code&appid={appId}&secret={secret}&js_code={code}")
    Map<String, Object> codeToSession(@Var("appId") String appId, @Var("secret") String secret, @Var("code") String code);

    /**
     * 获取用户手机号
     */
    @Post(value = "/wxa/business/getuserphonenumber?access_token={token}")
    Map<String, Object> getUserPhoneNumber(@Var("token") String token, @JSONBody Map<String, Object> body);
}

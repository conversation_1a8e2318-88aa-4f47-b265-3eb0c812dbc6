package com.rolling.biz.suport.spi;

import com.dtflys.forest.annotation.*;

import java.util.Map;

@BaseRequest(baseURL = "https://oapi.dingtalk.com")
public interface DingtalkSpi {
    @Get(value = "/gettoken")
    Map<String, Object> getToken(@Query("appkey") String appKey, @Query("appsecret") String appSecret);

    @Post("/topapi/v2/user/getuserinfo")
    Map<String, Object> getUserInfo(@Query("access_token") String token, @JSONBody Map<String, Object> payload);

    @Post("/topapi/v2/user/get")
    Map<String, Object> getUserDetail(@Query("access_token") String token, @Query("userid") String userId);

    @Post("/topapi/v2/department/get")
    Map<String, Object> getDeptDetail(@Query("access_token") String token, @Query("dept_id") Long deptId);

    @Post("/topapi/message/corpconversation/asyncsend_v2")
    Map<String, Object> sendMessage(@Query("access_token") String token, @JSONBody Map<String, Object> payload);
}

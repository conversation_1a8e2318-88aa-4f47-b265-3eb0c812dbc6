package com.rolling.biz.constants.system;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OperateEnum implements IEnum {
    LOGIN("login", "登陆"),
    CREATE("create", "新建"),
    UPDATE("update", "修改"),
    DELETE("delete", "删除"),
    DOWNLOAD("download", "下载"),
    TEST("test", "测试"),
    ACTIVATE("activate", "激活"),
    PUBLISH("publish", "发布"),
    UNPUBLISHED("unpublished", "取消发布");

    private final String code;
    private final String desc;
}

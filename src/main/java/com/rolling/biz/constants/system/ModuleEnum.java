package com.rolling.biz.constants.system;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ModuleEnum implements IEnum {
    USER("user", "用户"),
    ROLE("role", "角色"),
    TASK("task", "任务管理"),
    EVALAPP("evalApp", "分析助手"),
    EMPLOYEE("employee", "员工"),
    ROBOT("robot", "访谈助手"),
    LLM("llm", "模型管理"),
    ;

    private final String code;
    private final String desc;
}

package com.rolling.biz.constants.app;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ContactStatusEnum implements IEnum<String, String> {
    INITIAL("initial", "未开始"),
    PROCESSING("processing", "进行中"),
    NOTEXIST("notexist", "空号"),
    BUSY("busy", "占线"),
    SUCCESS("success", "成功"),
    FAILED("failed", "失败");

    private final String code;
    private final String desc;
}

package com.rolling.biz.constants.app;

public interface RabbitQueues {
    String CONVERSATION_EXCHANGE = "rtc_conversation_exchange";
    String CONVERSATION_QUEUE = "rtc_conversation_queue";

    String TASK_EXCHANGE = "rtc_task_exchange";
    String TASK_DISPATCH_QUEUE = "rtc_task_dispatch_queue";
    String TASK_DISPATCH_KEY = "dispatch";

    String EVALUATE_EXCHANGE = "rtc_evaluate_exchange";
    String EVALUATE_QUEUE = "rtc_evaluate_queue";
    String EVALUATE_ROUTING_KEY = "dispatch";

    String VOICE_CHAT_EVENT_EXCHANGE = "rtc_voice_chat_event_exchange";
    String VOICE_CHAT_EVENT_QUEUE = "rtc_voice_chat_event_queue";
    String VOICE_CHAT_EVENT_ROUTING_KEY = "dispatch";

    String OPERATE_LOG_EXCHANGE = "rtc_operate_log_exchange";
    String OPERATE_LOG_QUEUE = "rtc_operate_log_queue";
    String OPERATE_LOG_KEY = "dispatch";
}

package com.rolling.biz.constants.app;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum JobStatusEnum implements IEnum<String, String> {
    SCHEDULING("Scheduling", "调度中"),
    EXECUTING("Executing", "执行中"),
    SUCCEEDED("Succeeded", "成功"),
    PAUSED("Paused", "挂起"),
    FAILED("Failed", "失败"),
    CANCELLED("Cancelled", "已取消"),
    DRAFTED("Drafted", "草稿态");

    private final String code;
    private final String desc;
}

package com.rolling.biz.constants.app;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum JobTaskStatusEnum implements IEnum<String, String> {
    EXECUTING("Executing", "正在拨打"),
    SUCCEEDED("Succeeded", "已接通"),
    SUCCEEDED_FINISH("SucceededFinish", "已接通-正常完结"),
    NOTEXIST("NotExist", "未接通-空号"),
    BUSY("Busy", "未接通-占线"),
    COMPLETED("Completed", "已完成");

    private final String code;
    private final String desc;
}

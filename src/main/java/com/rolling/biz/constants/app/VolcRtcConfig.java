package com.rolling.biz.constants.app;

import com.volcengine.helper.Const;
import com.volcengine.model.ApiInfo;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class VolcRtcConfig {

    public static Map<String, ApiInfo> apiInfoList = new HashMap() {
        {
            put(VolcApiConst.ListRoomInfo, new ApiInfo(
                    new HashMap() {
                        {
                            put(Const.Method, Const.GET);
                            put(Const.Path, "/");
                            put(Const.Query, new ArrayList<NameValuePair>() {
                                {
                                    add(new BasicNameValuePair("Action", VolcApiConst.ListRoomInfo));
                                    add(new BasicNameValuePair("Version", "2020-12-01"));
                                }
                            });
                        }
                    }
            ));
            put(VolcApiConst.StartVoiceChat, new ApiInfo(
                    new HashMap() {
                        {
                            put(Const.Method, Const.POST);
                            put(Const.Path, "/");
                            put(Const.Query, new ArrayList<NameValuePair>() {
                                {
                                    add(new BasicNameValuePair("Action", VolcApiConst.StartVoiceChat));
                                    add(new BasicNameValuePair("Version", "2024-06-01"));
                                }
                            });
                        }
                    }
            ));
            put(VolcApiConst.UpdateVoiceChat, new ApiInfo(
                    new HashMap() {
                        {
                            put(Const.Method, Const.POST);
                            put(Const.Path, "/");
                            put(Const.Query, new ArrayList<NameValuePair>() {
                                {
                                    add(new BasicNameValuePair("Action", VolcApiConst.UpdateVoiceChat));
                                    add(new BasicNameValuePair("Version", "2024-06-01"));
                                }
                            });
                        }
                    }
            ));
            put(VolcApiConst.StopVoiceChat, new ApiInfo(
                    new HashMap() {
                        {
                            put(Const.Method, Const.POST);
                            put(Const.Path, "/");
                            put(Const.Query, new ArrayList<NameValuePair>() {
                                {
                                    add(new BasicNameValuePair("Action", VolcApiConst.StopVoiceChat));
                                    add(new BasicNameValuePair("Version", "2024-06-01"));
                                }
                            });
                        }
                    }
            ));
            put(VolcApiConst.StartSubtitle, new ApiInfo(
                    new HashMap() {
                        {
                            put(Const.Method, Const.POST);
                            put(Const.Path, "/");
                            put(Const.Query, new ArrayList<NameValuePair>() {
                                {
                                    add(new BasicNameValuePair("Action", VolcApiConst.StartSubtitle));
                                    add(new BasicNameValuePair("Version", "2024-06-01"));
                                }
                            });
                        }
                    }
            ));
            put(VolcApiConst.StopSubtitle, new ApiInfo(
                    new HashMap() {
                        {
                            put(Const.Method, Const.POST);
                            put(Const.Path, "/");
                            put(Const.Query, new ArrayList<NameValuePair>() {
                                {
                                    add(new BasicNameValuePair("Action", VolcApiConst.StopSubtitle));
                                    add(new BasicNameValuePair("Version", "2024-06-01"));
                                }
                            });
                        }
                    }
            ));
        }
    };
}

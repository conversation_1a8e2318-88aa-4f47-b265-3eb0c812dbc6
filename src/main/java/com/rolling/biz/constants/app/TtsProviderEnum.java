package com.rolling.biz.constants.app;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TtsProviderEnum implements IEnum<String, String> {
    VOLCANO("volcano", "火山引擎"),
    MINIMAX("minimax", "MiniMax");

    private final String code;
    private final String desc;

    public static final Map<String, TtsProviderEnum> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(TtsProviderEnum.values()).forEach(e -> ENUM_MAP.put(e.getCode(), e));
    }
}

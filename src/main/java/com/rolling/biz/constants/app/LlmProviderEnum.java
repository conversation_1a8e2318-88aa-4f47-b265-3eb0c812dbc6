package com.rolling.biz.constants.app;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum LlmProviderEnum implements IEnum<String, String> {
    ARKV3("ArkV3", "火山方舟"),
    CUSTOM_LLM("CustomLLM", "第三方大模型");

    private final String code;
    private final String desc;

    public static final Map<String, LlmProviderEnum> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(LlmProviderEnum.values()).forEach(e -> ENUM_MAP.put(e.getCode(), e));
    }
}

package com.rolling.biz.constants.app;

import com.rolling.library.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskStatusEnum implements IEnum<String, String> {
    INITIAL("initial", "未开始"),
    PROCESSING("processing", "进行中"),
    COMPLETED("completed", "已完成"),
    FAILED("failed", "已失败");

    private final String code;
    private final String desc;
}

package com.rolling.biz.controller.app;

import cn.hutool.core.map.MapUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.app.TaskStatusEnum;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.request.app.RobotPageReq;
import com.rolling.biz.model.request.app.RobotEditReq;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.biz.service.app.RobotService;
import com.rolling.biz.service.app.TaskContactService;
import com.rolling.biz.service.app.TaskInfoService;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseCrudController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/robots")
public class RobotController extends BaseCrudController<RobotService, Robot, RobotPageReq, RobotEditReq, RobotEditReq> {
    private final TaskInfoService taskInfoService;
    private final TaskContactService taskContactService;
    private final OperateLogService operateLogService;

    @Override
    public OperateLogService getOperateLogService() {
        return operateLogService;
    }

    @Override
    protected void bindOperateLog(OperateLog operateLog, Robot entity) {
        operateLog.setModule(ModuleEnum.ROBOT.getCode());
        operateLog.setModuleName(entity.getName());
        operateLog.setContent(String.format("编号{%s},名称{%s}", entity.getId(), entity.getName()));
    }

    @Override
    protected void bindQueryWrapper(RobotPageReq req, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.like(Robot::getName, value));
        Optional.ofNullable(req.getDisabled()).ifPresent(value -> queryWrapper.eq(Robot::getDisabled, value));
    }

    @Override
    protected void handleRecords(List<Robot> records, RobotPageReq req) {
        List<Long> robotIds = records.stream().mapToLong(Robot::getId).boxed().collect(Collectors.toList());
        if (robotIds.size() > 0) {
            Map<String, Object> taskCountMap = taskInfoService.batchGetCount(robotIds, TaskStatusEnum.PROCESSING.getCode());
            Map<String, Object> todayCountMap = taskContactService.batchGetTodayCount(robotIds, TaskStatusEnum.COMPLETED.getCode());
            records.forEach(record -> {
                record.setProcessingTaskCount(MapUtil.getLong(taskCountMap, String.valueOf(record.getId()), 0L));
                record.setTodayCompletedTaskCount(MapUtil.getLong(todayCountMap, String.valueOf(record.getId()), 0L));
            });
        }
    }

    @Override
    protected void handleRecord(Robot entity) {
        Map<String, Object> taskCountMap = taskInfoService.batchGetCount(Arrays.asList(entity.getId()), TaskStatusEnum.PROCESSING.getCode());
        entity.setProcessingTaskCount(MapUtil.getLong(taskCountMap, String.valueOf(entity.getId()), 0L));
    }
}

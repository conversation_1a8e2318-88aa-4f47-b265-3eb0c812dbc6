package com.rolling.biz.controller.app;

import com.rolling.biz.service.app.ModelInfoService;
import com.rolling.library.model.response.ResultResp;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/model")
@RequiredArgsConstructor
public class ModelController {
    private final ModelInfoService modelInfoService;

    @GetMapping("/tts")
    public ResultResp getTtsList() {
        return ResultResp.success(modelInfoService.getTtsList());
    }

    @GetMapping("/asr")
    public ResultResp getAsrList() {
        return ResultResp.success(modelInfoService.getAsrList());
    }
}

package com.rolling.biz.controller.app;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.rolling.biz.mapper.app.EmployeeMapper;
import com.rolling.biz.model.entity.app.Employee;
import com.rolling.biz.model.request.app.EmployeePageReq;
import com.rolling.biz.service.app.EmployeeService;
import com.rolling.biz.suport.excel.PageReadListener;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.BeanHelper;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseQueryController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/employee")
public class EmployeeController extends BaseQueryController<EmployeeService, Employee, EmployeePageReq> {

    private final EmployeeService employeeService;

    @Override
    protected void bindQueryWrapper(EmployeePageReq pageReq, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(pageReq.getKeyword(), value -> queryWrapper.like(Employee::getName, value));
    }

    @GetMapping("/list")
    public ResultResp batchEmployee(EmployeePageReq pageReq) {
        return ResultResp.success(employeeService.batchGetEmployee(pageReq));
    }

    @GetMapping("/postNumber")
    public ResultResp postNumber(EmployeePageReq pageReq) {

        pageReq.setOrder(StringUtils.isEmpty(pageReq.getOrder()) ? "DESC" : pageReq.getOrder());
        pageReq.setSort(StringUtils.isEmpty(pageReq.getSort()) ? "postNumber" : pageReq.getSort());
        pageReq.setPage((pageReq.getPage() - 1) * pageReq.getPageSize());

        List<Map<String, Object>> data = employeeService.getMapper().statePostNumber(pageReq);
        int total = employeeService.getMapper().countPostNumber(pageReq);

        Map resp = new HashMap();
        resp.put("records", data);
        resp.put("total", total);
        return ResultResp.success(resp);
    }

    @GetMapping("/postNumber/search")
    public ResultResp postList(EmployeePageReq pageReq) {

        int offset = (pageReq.getPage() - 1) * pageReq.getPageSize();

        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.select(
                QueryMethods.column(Employee::getPostNumber).as("postNumber"),
                QueryMethods.column(Employee::getStandardPost).as("standardPost")
        );
        if (StringUtils.isNotEmpty(pageReq.getKeyword())) {
            queryWrapper.like(Employee::getStandardPost, pageReq.getKeyword());
        }
        long count = employeeService.getMapper().selectCountByQuery(queryWrapper);
        queryWrapper.orderBy(Employee::getPostNumber).desc();
        queryWrapper.limit(offset, pageReq.getPageSize());
        List<Row> rows = employeeService.getMapper().selectListByQueryAs(queryWrapper, Row.class);
        Map resp = new HashMap();
        resp.put("total", count);
        resp.put("records", rows);
        return ResultResp.success(resp);
    }

    @PostMapping("/import")
    public ResultResp doImport(@RequestParam("file") MultipartFile file) {
        try {
            EasyExcel.read(file.getInputStream(), Employee.class, new PageReadListener<Employee>(10000, 10000, (list, page) -> {
                List<Employee> entities = new ArrayList<>();
                for (var data : list) {
                    Employee entity = BeanHelper.copyProperties(data, Employee.class);
                    entities.add(entity);
                }
                getService().saveBatchIgnore(entities);
            })).sheet().doRead();
        } catch (Exception ex) {
            log.error("===import employee failed: {}", ex.getMessage(), ex);
        }
        return ResultResp.success(Boolean.TRUE);
    }
}

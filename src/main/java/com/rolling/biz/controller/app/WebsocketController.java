package com.rolling.biz.controller.app;


import com.rolling.biz.service.app.WebsocketService;
import com.rolling.library.utils.StringHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@RequiredArgsConstructor
public class WebsocketController extends TextWebSocketHandler {
    private final WebsocketService websocketService;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String clientId = getClientId(session.getUri().getPath());
        StringHelper.ifPresent(clientId, value -> websocketService.saveSession(value, session));
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String clientId = getClientId(session.getUri().getPath());
        StringHelper.ifPresent(clientId, value -> websocketService.removeSession(value));
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        String clientId = getClientId(session.getUri().getPath());
        websocketService.pingSession(clientId);
    }

    private String getClientId(String path) {
        Pattern pattern = Pattern.compile("/api/ws/(\\w+)");
        Matcher matcher = pattern.matcher(path);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}

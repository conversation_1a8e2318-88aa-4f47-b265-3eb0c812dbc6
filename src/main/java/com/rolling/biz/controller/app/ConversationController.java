package com.rolling.biz.controller.app;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.model.entity.app.Conversation;
import com.rolling.biz.model.request.app.ConversationPageReq;
import com.rolling.biz.service.app.ConversationService;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseQueryController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/conversations")
public class ConversationController extends BaseQueryController<ConversationService, Conversation, ConversationPageReq> {
    @Override
    protected void bindQueryWrapper(ConversationPageReq pageReq, QueryWrapper queryWrapper) {
        Optional.ofNullable(pageReq.getTaskId()).ifPresent(value -> queryWrapper.eq(Conversation::getTaskId, value));
        Optional.ofNullable(pageReq.getContactId()).ifPresent(value -> queryWrapper.eq(Conversation::getContactId, value));
        StringHelper.ifPresent(pageReq.getUserType(), value -> queryWrapper.eq(Conversation::getUserType, value));
        StringHelper.ifPresent(pageReq.getUserId(), value -> queryWrapper.eq(Conversation::getUserId, value));
    }
}

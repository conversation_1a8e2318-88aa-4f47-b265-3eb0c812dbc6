package com.rolling.biz.controller.app;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.model.entity.app.EvalApp;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.biz.model.request.app.AddToFolderReq;
import com.rolling.biz.model.entity.app.Folder;
import com.rolling.biz.model.request.app.FolderEditReq;
import com.rolling.biz.model.request.app.FolderPageReq;
import com.rolling.biz.service.app.FolderContentService;
import com.rolling.biz.service.app.FolderService;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseCrudController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/folder")
public class FolderController extends BaseCrudController<FolderService, Folder, FolderPageReq, FolderEditReq, FolderEditReq> {

    private final FolderContentService folderContentService;
    private final OperateLogService operateLogService;

    @Override
    protected boolean enableOperateLog() {
        return false;
    }

    @Override
    public OperateLogService getOperateLogService() {
        return operateLogService;
    }

    @Override
    protected void bindQueryWrapper(FolderPageReq req, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.like(Folder::getName, value));
        StringHelper.ifPresent(req.getType(), value -> queryWrapper.eq(Folder::getType, value));
    }

    @PostMapping("/addToFolder")
    public ResultResp addToFolder(@RequestBody @Valid AddToFolderReq req) {
        folderContentService.addToFolder(req.getFolderId(), req.getContentIds());
        return ResultResp.success(Boolean.TRUE);
    }

    @PostMapping("/removeFromFolder")
    public ResultResp removeFromFolder(@RequestBody @Valid AddToFolderReq req) {
        folderContentService.removeFromFolder(req.getFolderId(), req.getContentIds());
        return ResultResp.success(Boolean.TRUE);
    }
}

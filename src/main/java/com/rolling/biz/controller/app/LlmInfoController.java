package com.rolling.biz.controller.app;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.app.LlmProviderEnum;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.model.entity.app.LlmInfo;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.biz.model.request.app.LlmInfoEditReq;
import com.rolling.biz.service.app.LlmInfoService;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.library.model.request.BasePageReq;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseCrudController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/llms")
@RequiredArgsConstructor
public class LlmInfoController extends BaseCrudController<LlmInfoService, LlmInfo, BasePageReq, LlmInfoEditReq, LlmInfoEditReq> {

    private final OperateLogService operateLogService;

    @Override
    protected void bindQueryWrapper(BasePageReq req, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.likeRight(LlmInfo::getName, value));
    }

    @Override
    public OperateLogService getOperateLogService() {
        return operateLogService;
    }

    @Override
    protected void bindOperateLog(OperateLog operateLog, LlmInfo entity) {
        operateLog.setModule(ModuleEnum.LLM.getCode());
        operateLog.setModuleName(entity.getName());
        operateLog.setContent(String.format("编号{%s},名称{%s}", entity.getId(), entity.getName()));
    }

    @GetMapping("/providers")
    public ResultResp getProviderList() {
        var dataList = Arrays.stream(LlmProviderEnum.values()).map(item -> Map.of("code", item.getCode(), "desc", item.getDesc())).collect(Collectors.toList());
        return ResultResp.success(dataList);
    }
}

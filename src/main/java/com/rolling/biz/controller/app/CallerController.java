package com.rolling.biz.controller.app;

import com.rolling.biz.service.app.CallerService;
import com.rolling.library.model.response.ResultResp;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/callers")
public class CallerController {
    private final CallerService callerService;

    @GetMapping
    public ResultResp list() {
        return ResultResp.success(callerService.list());
    }
}

package com.rolling.biz.controller.app;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.io.Files;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.app.EvalTypeEnum;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.constants.app.TaskStatusEnum;
import com.rolling.biz.constants.app.TaskTypeEnum;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.model.beans.ConversationExcel;
import com.rolling.biz.model.entity.app.*;
import com.rolling.biz.model.request.app.TaskPageReq;
import com.rolling.biz.model.request.app.TaskEditReq;
import com.rolling.biz.service.app.*;
import com.rolling.biz.service.system.DomainService;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import com.rolling.library.utils.DateHelper;
import com.rolling.library.utils.JacksonHelper;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseCrudController;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
public class TaskInfoController extends BaseCrudController<TaskInfoService, TaskInfo, TaskPageReq, TaskEditReq, TaskEditReq> {
    private final DomainService domainService;
    private final TaskContactService taskContactService;
    private final RobotService robotService;
    private final EvalTaskService evalTaskService;
    private final RabbitMQTemplate rabbitMQTemplate;
    private final ConversationService conversationService;
    private final FolderContentService folderContentService;
    private final FolderService folderService;
    private final OperateLogService operateLogService;

    @Override
    public OperateLogService getOperateLogService() {
        return operateLogService;
    }

    @Override
    protected void bindOperateLog(OperateLog operateLog, TaskInfo task) {
        operateLog.setModule(ModuleEnum.TASK.getCode());
        operateLog.setModuleName(task.getName());
        operateLog.setModuleCode(task.getUuid());
        operateLog.setContent(String.format("编号{%s},名称{%s}", task.getId(), task.getName()));
    }

    @Override
    protected void bindQueryWrapper(TaskPageReq req, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(req.getStatus(), value -> queryWrapper.eq(TaskInfo::getStatus, value));
        StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.likeRight(TaskInfo::getName, value));
        Optional.ofNullable(req.getRobotId()).ifPresent(value -> queryWrapper.eq(TaskInfo::getRobotId, value));

        // 分组查询
        if (req.getFolderId() != null) {
            List<FolderContent> folderContentList = folderContentService.list(QueryWrapper.create().eq(FolderContent::getFolderId, req.getFolderId()));
            List<Long> contentIds = folderContentList.stream().map(FolderContent::getContentId).collect(Collectors.toList());
            contentIds = ObjectUtils.isEmpty(contentIds) ? ListUtil.toList(-1L) : contentIds;
            queryWrapper.in(TaskInfo::getId, contentIds);
        }
        // 员工访谈任务列表查询
        if (StringUtils.isNotEmpty(req.getEmployeeId())) {
            QueryWrapper wrapper = QueryWrapper.create().eq(TaskContact::getExternalId, req.getEmployeeId());
            if (StringUtils.isNotEmpty(req.getContactStatus())) {
                wrapper.eq(TaskContact::getStatus, req.getContactStatus());
            }
            List<TaskContact> contactList = taskContactService.list(wrapper);
            List<Long> taskIds = contactList.stream().map(TaskContact::getTaskId).collect(Collectors.toList());
            taskIds = ObjectUtils.isEmpty(taskIds) ? ListUtil.toList(-1L) : taskIds;
            queryWrapper.in(TaskInfo::getId, taskIds);
        }
        // 岗位访谈任务列表查询
        if (StringUtils.isNotEmpty(req.getPostNumber())) {
            List<Long> taskIds = taskContactService.getMapper().findAllIdByPostNumber(req.getPostNumber());
            taskIds = ObjectUtils.isEmpty(taskIds) ? ListUtil.toList(-1L) : taskIds;
            queryWrapper.in(TaskInfo::getId, taskIds);
        }
    }

    @Override
    protected void saveBefore(TaskInfo entity, TaskEditReq req) {
        entity.setUuid(IdUtil.simpleUUID());
        entity.setStatus(TaskStatusEnum.INITIAL.getCode());
    }

    @PostMapping("/{id}/start")
    public ResultResp start(@PathVariable Long id) {
        TaskInfo task = getService().getById(id, true);
        task.setStatus(TaskStatusEnum.PROCESSING.getCode());
        task.setBeginTime(Instant.now());
        getService().updateById(task, true);
        if (StringUtils.isNotBlank(task.getType()) && task.getType().equals(TaskTypeEnum.OUTBOUND.getCode())) {
            rabbitMQTemplate.sendMessage(
                    RabbitQueues.TASK_EXCHANGE,
                    RabbitQueues.TASK_DISPATCH_KEY,
                    MapUtil.<String, Object>builder().put("taskId", task.getId()).build()
            );
        }
        return ResultResp.success(Boolean.TRUE);
    }

    @PostMapping("/{id}/stop")
    public ResultResp stop(@PathVariable Long id) {
        TaskInfo record = getService().getById(id, true);
        TaskInfo entity = new TaskInfo();
        entity.setStatus(TaskStatusEnum.COMPLETED.getCode());
        entity.setEndTime(Instant.now());
        getService().updateById(record, entity);
        return ResultResp.success(Boolean.TRUE);
    }

    @Override
    protected void handleRecords(List<TaskInfo> records, TaskPageReq req) {
        if (ObjectUtils.isEmpty(records)) {
            return;
        }
        String type = "";
        if (StringUtils.isNotEmpty(req.getPostNumber())) {
            type = EvalTypeEnum.TASK.getCode();
        } else if (StringUtils.isNotEmpty(req.getEmployeeId())) {
            type = EvalTypeEnum.USER.getCode();
        }
        List<Long> taskIds = records.stream().map(TaskInfo::getId).collect(Collectors.toList());

        List<FolderContent> folderContentList = folderContentService.findByAllContentId("task", taskIds);
        Map<String, Long> folderMap = folderContentList.stream().collect(
                Collectors.toMap(folder -> String.valueOf(folder.getContentId()), FolderContent::getFolderId)
        );

        Map<String, String> contactStatusMap = new HashMap<>();
        if (StringUtils.isNotEmpty(req.getEmployeeId())) {
            List<TaskContact> taskContacts = taskContactService.list(QueryWrapper.create().in(TaskContact::getTaskId, taskIds).eq(TaskContact::getExternalId, req.getEmployeeId()));
            contactStatusMap = taskContacts.stream().collect(
                    Collectors.toMap(contact -> String.valueOf(contact.getTaskId()), TaskContact::getStatus)
            );
        }

        Map<String, String> taskStatusMap = new HashMap<>();
        if (StringUtils.isNotEmpty(req.getPostNumber()) || StringUtils.isNotEmpty(req.getEmployeeId())) {
            taskStatusMap = evalTaskService.statusAnalysis(type, taskIds);
        }
        for (TaskInfo task : records) {
            setClientUrl(task);
            setReportUrl(type, task, req);
            task.setFolderId(folderMap.get(String.valueOf(task.getId())));
            task.setTaskStatus(taskStatusMap.get(String.valueOf(task.getId())));
            if (StringUtils.isNotEmpty(req.getEmployeeId())) {
                task.setContactStatus(contactStatusMap.get(String.valueOf(task.getId())));
            }
        }
    }

    @Override
    protected void handleRecord(TaskInfo task) {
        setClientUrl(task);
        Folder folder = folderService.getMapper().findByTypeAndContentId("task", task.getId());
        if (ObjectUtils.isNotEmpty(folder)) {
            task.setFolderId(folder.getId());
        }
    }

    private void setClientUrl(TaskInfo taskInfo) {
        taskInfo.setClientUrl(StrUtil.format("{}/pgw/{}", domainService.getBaseUrl(), taskInfo.getUuid()));
    }

    private void setReportUrl(String type, TaskInfo task, TaskPageReq req) {
        if (StringUtils.isEmpty(type)) {
            return;
        }
        String baseUrl = domainService.getReportBaseUrl(type);
        if ("user".equals(type) && StringUtils.isNotEmpty(req.getEmployeeId())) {
            String base64 = Base64.encode(JacksonHelper.toJson(Map.of("userId", ListUtil.toList(req.getEmployeeId()))));
            baseUrl = baseUrl + "?attachParams=" + base64;
        } else if ("task".equals(type) && StringUtils.isNotEmpty(req.getPostNumber())) {
            String base64 = Base64.encode(JacksonHelper.toJson(Map.of("positionId", ListUtil.toList(req.getPostNumber()))));
            baseUrl = baseUrl + "?attachParams=" + base64;
        }
        task.setReportUrl(baseUrl);
    }

    @GetMapping("/{id}/conversation/export")
    public void exportConversation(@PathVariable Long id, HttpServletResponse response) {
        TaskInfo task = getService().getById(id, true);
        Robot robot = robotService.getById(task.getRobotId(), false);
        String dateTime = DatePattern.PURE_DATETIME_FORMAT.format(Date.from(DateHelper.utcToLocal(Instant.now())));
        String fileName = String.format("%s_%s.xlsx", task.getName(), dateTime);
        File file = new File(Files.createTempDir(), fileName);
        try {
            try (ExcelWriter excelWriter = EasyExcel.write(file, ConversationExcel.class).build()) {
                WriteSheet sheet = EasyExcel.writerSheet(0).build();
                List<ConversationExcel> dataList = new ArrayList<>();
                conversationService.groupingByTaskId(task.getId(), roomIds -> {
                    List<Conversation> conversationList = conversationService.list(task.getId(), roomIds);
                    Map<String, String> contactMap = taskContactService.list(
                            task.getId(),
                            conversationList.stream().map(Conversation::getUserId).distinct().collect(Collectors.toList())
                    ).stream().collect(Collectors.toMap(TaskContact::getExternalId, TaskContact::getName));
                    conversationList.stream().collect(Collectors.groupingBy(Conversation::getRoomId)).forEach((roomId, conversations) -> {
                        List<Conversation> sessionList = conversations.stream().sorted(Comparator.comparing(Conversation::getCreateTime)).collect(Collectors.toList());
                        ConversationExcel excelData = new ConversationExcel();
                        excelData.setTaskName(task.getName());
                        Optional.ofNullable(robot).ifPresent(obj -> excelData.setAiBotName(obj.getName()));
                        Optional.ofNullable(contactMap.get(sessionList.get(0).getUserId())).ifPresent(value -> excelData.setUserName(value));
                        excelData.setUserId(sessionList.get(0).getUserId());
                        Instant beginTime = DateHelper.utcToLocal(sessionList.get(0).getCreateTime());
                        Instant endTime = DateHelper.utcToLocal(sessionList.get(sessionList.size() - 1).getCreateTime());
                        excelData.setDuration(String.valueOf(DateUtil.between(Date.from(beginTime), Date.from(endTime), DateUnit.SECOND, true)));
                        excelData.setBeginTime(DatePattern.NORM_DATETIME_FORMAT.format(Date.from(beginTime)));
                        excelData.setEndTime(DatePattern.NORM_DATETIME_FORMAT.format(Date.from(endTime)));
                        List<String> contents = sessionList.stream().map(obj -> {
                            String userType = obj.getUserType().equals("robot") ? "AI访谈官" : "员工";
                            return String.format("%s：%s", userType, obj.getContent());
                        }).collect(Collectors.toList());
                        excelData.setContent(String.join("\n", contents));
                        excelData.setRoomId(roomId);
                        dataList.add(excelData);
                    });
                });
                excelWriter.write(dataList, sheet);
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            response.getOutputStream().write(Files.toByteArray(file));
        } catch (Exception ex) {
            log.error("===exportConversation error: {}", ex.getMessage(), ex);
        } finally {
            if (file.exists()) {
                file.delete();
            }
        }
    }

    @GetMapping("/monitor/list")
    public ResultResp monitorList(TaskPageReq req) {
        req.setOffset((req.getPage() - 1) * req.getPageSize());
        req.setOrder(StringUtils.isEmpty(req.getOrder()) ? "DESC" : req.getOrder());
        req.setSort(StringUtils.isEmpty(req.getSort()) ? "id" : req.getSort());
        List<Map<String, Object>> records = getService().getMapper().stateTaskBoard(req);
        int total = getService().getMapper().countTaskBoard(req);
        Map resp = new HashMap();
        resp.put("records", records);
        resp.put("total", total);
        return ResultResp.success(resp);
    }
}

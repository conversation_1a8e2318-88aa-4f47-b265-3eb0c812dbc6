package com.rolling.biz.controller.app;

import com.alibaba.excel.EasyExcel;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.model.entity.app.Department;
import com.rolling.biz.model.request.app.DepartmentPageReq;
import com.rolling.biz.service.app.DepartmentService;
import com.rolling.biz.suport.excel.PageReadListener;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.BeanHelper;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseQueryController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/department")
public class DepartmentController extends BaseQueryController<DepartmentService, Department, DepartmentPageReq> {
    @Override
    protected void bindQueryWrapper(DepartmentPageReq pageReq, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(pageReq.getKeyword(), value -> queryWrapper.like(Department::getName, value));
        Optional.ofNullable(pageReq.getParentId()).ifPresent(value -> queryWrapper.eq(Department::getParentId, value));
    }

    @PostMapping("/import")
    public ResultResp doImport(@RequestParam("file") MultipartFile file) {
        try {
            EasyExcel.read(file.getInputStream(), Department.class, new PageReadListener<Department>(10000, 10000, (list, page) -> {
                List<Department> entities = new ArrayList<>();
                for (var data : list) {
                    Department department = BeanHelper.copyProperties(data, Department.class);
                    entities.add(department);
                }
                getService().saveBatchIgnore(entities);
            })).sheet().doRead();
        } catch (Exception ex) {
            log.error("===import department failed: {}", ex.getMessage(), ex);
        }
        return ResultResp.success(Boolean.TRUE);
    }
}

package com.rolling.biz.controller.app;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.constants.app.TaskStatusEnum;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.model.entity.app.*;
import com.rolling.biz.model.request.app.EvalAppEditReq;
import com.rolling.biz.model.request.app.EvalAppPageReq;
import com.rolling.biz.service.app.EvalAppService;
import com.rolling.biz.service.app.EvalTaskService;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.biz.service.app.TaskInfoService;
import com.rolling.library.enums.CustomHttpStatus;
import com.rolling.library.exception.AppException;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseCrudController;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/evalapp")
public class EvalAppController extends BaseCrudController<EvalAppService, EvalApp, EvalAppPageReq, EvalAppEditReq, EvalAppEditReq> {

    private final EvalTaskService evalTaskService;
    private final RabbitMQTemplate rabbitMQTemplate;
    private final TaskInfoService taskInfoService;
    private final OperateLogService operateLogService;

    @Override
    public OperateLogService getOperateLogService() {
        return operateLogService;
    }

    @Override
    protected void bindOperateLog(OperateLog operateLog, EvalApp entity) {
        operateLog.setModule(ModuleEnum.EVALAPP.getCode());
        operateLog.setModuleName(entity.getName());
        operateLog.setModuleCode(entity.getCode());
        operateLog.setContent(String.format("编号{%s},名称{%s}", entity.getId(), entity.getName()));
    }

    @Override
    protected void bindQueryWrapper(EvalAppPageReq req, QueryWrapper queryWrapper) {
        if (StrUtil.isNotBlank(req.getKeyword())) {
            StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.like(EvalApp::getName, value));
        }
        if ("offline".equals(req.getStatus())) {
            queryWrapper.eq(EvalApp::getDisabled, true);
        } else if ("online".equals(req.getStatus())) {
            queryWrapper.eq(EvalApp::getDisabled, false);
            List<Long> evalAppIds = evalTaskService.batchGetByStatus(TaskStatusEnum.PROCESSING.getCode());
            queryWrapper.in(EvalApp::getId, evalAppIds.size() > 0 ? evalAppIds : CollUtil.newArrayList(-1));
        } else if ("free".equals(req.getStatus())) {
            queryWrapper.eq(EvalApp::getDisabled, false);
            List<Long> evalAppIds = evalTaskService.batchGetByStatus(TaskStatusEnum.PROCESSING.getCode());
            queryWrapper.notIn(EvalApp::getId, evalAppIds.size() > 0 ? evalAppIds : CollUtil.newArrayList(-1));
        }
    }

    @Override
    protected void handleRecords(List<EvalApp> records, EvalAppPageReq req) {
        if (Objects.isNull(records)) {
            return;
        }
        List<Long> evalAppIds = records.stream().mapToLong(EvalApp::getId).boxed().collect(Collectors.toList());

        Map<String, Object> totalMap = evalTaskService.batchGetTaskStatus(evalAppIds);
        Map<String, Object> todayCompletedMap = evalTaskService.batchTotalTodayCompleted(evalAppIds);

        for (EvalApp record : records) {
            record.setTodayCompletedCount(MapUtil.getLong(todayCompletedMap, String.valueOf(record.getId()), 0L));
            if (record.getDisabled()) {
                record.setStatus("offline");
            } else {
                Long count = MapUtil.getLong(totalMap, String.valueOf(record.getId()));
                record.setStatus((count != null && count > 0) ? "online" : "free");
            }
        }
    }

    @Override
    protected void saveBefore(EvalApp entity, EvalAppEditReq req) {
        entity.setStatus("offline");
        if (req.getDisabled() == null) {
            entity.setDisabled(false);
        }
    }

    @PostMapping("/repeat/run")
    public ResultResp repeatRun(@RequestBody Map data) {

        Long taskId = Convert.toLong(data.get("taskId"));
        TaskInfo taskInfo = taskInfoService.getById(taskId, false);
        if (ObjectUtils.isEmpty(taskInfo)) {
            throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), "taskInfo not found");
        }

        rabbitMQTemplate.sendMessage(RabbitQueues.EVALUATE_EXCHANGE, RabbitQueues.EVALUATE_ROUTING_KEY, MapUtil.<String, Object>builder()
                .put("taskId", taskInfo.getUuid())
                .put("userId", data.get("userId"))
                .put("type", data.get("type"))
                .put("postNumber", data.get("postNumber"))
                .build());
        return ResultResp.success(Boolean.TRUE);
    }
}

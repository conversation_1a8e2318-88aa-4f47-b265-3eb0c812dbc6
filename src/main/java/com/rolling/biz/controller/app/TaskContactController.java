package com.rolling.biz.controller.app;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.app.ContactStatusEnum;
import com.rolling.biz.model.beans.TaskContactExcel;
import com.rolling.biz.model.entity.app.TaskContact;
import com.rolling.biz.model.entity.app.TaskInfo;
import com.rolling.biz.model.request.app.ContactPageReq;
import com.rolling.biz.model.request.app.StatContactReq;
import com.rolling.biz.service.app.TaskContactService;
import com.rolling.biz.service.app.ConversationService;
import com.rolling.biz.service.app.TaskInfoService;
import com.rolling.biz.suport.excel.PageReadListener;
import com.rolling.library.exception.AppExceptionAssert;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.BeanHelper;
import com.rolling.library.utils.StringHelper;
import com.rolling.library.web.controller.BaseQueryController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/contacts")
public class TaskContactController extends BaseQueryController<TaskContactService, TaskContact, ContactPageReq> {
    private final TaskInfoService taskInfoService;
    private final ConversationService conversationService;

    @Override
    protected void bindQueryWrapper(ContactPageReq req, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.and(qw1 -> {
            qw1.likeRight(TaskContact::getName, value).or(qw2 -> {
                qw2.likeRight(TaskContact::getPhone, value);
            });
        }));
        StringHelper.ifPresent(req.getStatus(), value -> queryWrapper.in(TaskContact::getStatus, Arrays.stream(value.split(StrUtil.COMMA)).collect(Collectors.toList())));
        Optional.ofNullable(req.getRobotId()).ifPresent(value -> queryWrapper.eq(TaskContact::getRobotId, value));
        Optional.ofNullable(req.getTaskId()).ifPresent(value -> queryWrapper.eq(TaskContact::getTaskId, value));
    }

    @Override
    protected void handleRecords(List<TaskContact> records, ContactPageReq req) {
        List<Long> idList = records.stream().map(TaskContact::getId).collect(Collectors.toList());
        if (idList.size() > 0) {
            Long taskId = req.getTaskId();
            if (Objects.nonNull(taskId)) {
                Map<String, Map<String, Instant>> dataMap = conversationService.groupingByContactId(taskId, idList).stream().collect(Collectors.toMap(
                        item -> String.valueOf(MapUtil.getStr(item, "contactId")),
                        item -> MapUtil.<String, Instant>builder()
                                .put("begin", MapUtil.get(item, "begin", Instant.class))
                                .put("end", MapUtil.get(item, "end", Instant.class))
                                .build()));
                for (TaskContact contact : records) {
                    Map<String, Instant> instantMap = dataMap.get(String.valueOf(contact.getId()));
                    if (!CollectionUtils.isEmpty(instantMap)) {
                        String durationText = DateUtil.formatBetween(Date.from(instantMap.get("begin")), Date.from(instantMap.get("end")), BetweenFormatter.Level.SECOND);
                        contact.setDurationText(durationText);
                    }
                }
            }
        }
    }

    @GetMapping("/status/count")
    public ResultResp getStatusCount(StatContactReq req) {
        if (StringUtils.isNotBlank(req.getTaskId())) {
            return ResultResp.success(getService().getStatusCountByTaskIds(req));
        }
        return ResultResp.success(getService().getStatusCount(req));
    }

    @PostMapping("/{taskId}/import")
    public ResultResp doImport(@PathVariable Long taskId, @RequestParam("file") MultipartFile file) {
        TaskInfo taskInfo = taskInfoService.getById(taskId, true);
        try {
            EasyExcel.read(file.getInputStream(), TaskContactExcel.class, new PageReadListener<TaskContactExcel>(10000, 10000, (list, page) -> {
                List<TaskContact> taskContactList = new ArrayList<>();
                for (var data : list) {
                    TaskContact taskContact = BeanHelper.copyProperties(data, TaskContact.class);
                    taskContact.setTaskId(taskInfo.getId());
                    taskContact.setRobotId(taskInfo.getRobotId());
                    taskContact.setStatus(ContactStatusEnum.INITIAL.getCode());
                    taskContactList.add(taskContact);
                }
                getService().saveBatchIgnore(taskContactList);
            })).sheet().doRead();
        } catch (Exception ex) {
            log.error("===import task_contact failed: {}", ex.getMessage(), ex);
        }
        return ResultResp.success(Boolean.TRUE);
    }

    @PostMapping("/{taskId}/save")
    public ResultResp save(@PathVariable Long taskId, @RequestBody Map<String, Object> payload) {
        String name = MapUtil.getStr(payload, "name");
        String externalId = MapUtil.getStr(payload, "externalId");
        AppExceptionAssert.pass(StringUtils.isNotBlank(name), "name is required");
        AppExceptionAssert.pass(StringUtils.isNotBlank(externalId), "externalId is required");
        TaskInfo taskInfo = taskInfoService.getById(taskId, true);
        TaskContact entity = new TaskContact();
        entity.setName(name);
        entity.setExternalId(externalId);
        entity.setTaskId(taskInfo.getId());
        entity.setRobotId(taskInfo.getRobotId());
        entity.setStatus(ContactStatusEnum.INITIAL.getCode());
        getService().saveBatchIgnore(Arrays.asList(entity));
        return ResultResp.success(Boolean.TRUE);
    }

    @DeleteMapping("/{taskId}/remove")
    public ResultResp remove(@PathVariable Long taskId, @RequestBody Map<String, Object> payload) {
        String externalId = MapUtil.getStr(payload, "externalId");
        TaskInfo taskInfo = taskInfoService.getById(taskId, true);
        AppExceptionAssert.pass(StringUtils.isNotBlank(externalId), "externalId is required");
        getService().remove(getService().query().eq(TaskContact::getTaskId, taskInfo.getId()).eq(TaskContact::getExternalId, externalId));
        return ResultResp.success(Boolean.TRUE);
    }
}

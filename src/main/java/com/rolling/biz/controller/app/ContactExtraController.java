package com.rolling.biz.controller.app;

import com.alibaba.excel.EasyExcel;
import com.rolling.biz.model.beans.ContactExtraExcel;
import com.rolling.biz.model.entity.app.ContactExtra;
import com.rolling.biz.service.app.ContactExtraService;
import com.rolling.biz.suport.excel.PageReadListener;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.BeanHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/contactextra")
public class ContactExtraController {
    private final ContactExtraService contactExtraService;

    @PostMapping("/import")
    public ResultResp doJdImport(@RequestParam("file") MultipartFile file) {
        try {
            EasyExcel.read(file.getInputStream(), ContactExtraExcel.class, new PageReadListener<ContactExtraExcel>(100, 10000, (dataList, page) -> {
                var entities = dataList.stream().map(data -> BeanHelper.copyProperties(data, ContactExtra.class)).collect(Collectors.toList());
                contactExtraService.getMapper().upsertBatch(entities);
            })).sheet().doRead();
        } catch (Exception ex) {
            log.error("===import contact_extra failed: {}", ex.getMessage(), ex);
        }
        return ResultResp.success(Boolean.TRUE);
    }
}

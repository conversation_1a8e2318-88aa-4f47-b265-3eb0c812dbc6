package com.rolling.biz.controller.pgw;


import cn.hutool.core.map.MapUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.LambdaUtil;
import com.rolling.biz.constants.app.TaskStatusEnum;
import com.rolling.biz.model.entity.app.TaskContact;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.entity.app.TaskInfo;
import com.rolling.biz.model.request.pgw.EndTaskPageReq;
import com.rolling.biz.service.app.ConversationService;
import com.rolling.biz.service.app.RobotService;
import com.rolling.biz.service.app.TaskInfoService;
import com.rolling.library.model.response.PageResp;
import com.rolling.library.model.response.ResultResp;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/pgw/api/task")
public class EndTaskController {
    private final RobotService robotService;
    private final TaskInfoService taskInfoService;
    private final ConversationService conversationService;

    @GetMapping
    public ResultResp getTaskList(@Valid EndTaskPageReq req) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(TaskInfo.class)
                .innerJoin(TaskContact.class)
                .on(QueryCondition.create(LambdaUtil.getQueryColumn(TaskContact::getTaskId), LambdaUtil.getQueryColumn(TaskInfo::getId)).and(LambdaUtil.getQueryColumn(TaskContact::getExternalId).eq(req.getUserId())))
                .in(TaskInfo::getStatus, TaskStatusEnum.PROCESSING.getCode(), TaskStatusEnum.COMPLETED.getCode());
        Page<TaskInfo> page = taskInfoService.page(Page.of(req.getPage(), req.getPageSize()), queryWrapper);
        List<Long> taskIds = page.getRecords().stream().mapToLong(TaskInfo::getId).boxed().collect(Collectors.toList());
        List<Long> robotIds = page.getRecords().stream().mapToLong(TaskInfo::getRobotId).boxed().collect(Collectors.toList());
        if (taskIds.size() > 0) {
            Map<String, Long> taskCountMap = new HashMap<>();
            conversationService.groupingByTaskId(taskIds, req.getUserId()).forEach(map -> {
                taskCountMap.put(MapUtil.getStr(map, "taskId"), MapUtil.getLong(map, "count"));
            });
            Map<Long, Robot> robotMap = robotService.list(QueryWrapper.create().in(Robot::getId, robotIds)).stream().collect(Collectors.toMap(Robot::getId, obj -> obj));
            page.getRecords().forEach(task -> {
                task.setHasConversation(Optional.ofNullable(taskCountMap.get(String.valueOf(task.getId()))).orElse(0L) > 0);
                Optional.ofNullable(robotMap.get(task.getRobotId())).ifPresent(robot ->
                        task.setRobot(MapUtil.<String, Object>builder()
                                .put("name", robot.getName())
                                .put("avatar", robot.getAvatar())
                                .put("desc", robot.getDesc())
                                .put("platform", robot.getPlatform())
                                .put("externalAgentId", robot.getExternalAgentId())
                                .build())
                );
            });
        }
        return PageResp.success(page);
    }
}

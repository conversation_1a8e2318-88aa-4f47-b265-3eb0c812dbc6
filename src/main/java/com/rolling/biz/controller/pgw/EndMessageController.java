package com.rolling.biz.controller.pgw;

import cn.hutool.core.bean.BeanUtil;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.model.request.app.SendMessageReq;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import com.rolling.library.utils.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Slf4j
@RestController
@RequiredArgsConstructor
public class EndMessageController {
    private final RabbitMQTemplate rabbitMQTemplate;

    @PostMapping("/pgw/api/message/send")
    public ResultResp sendMessage(@RequestBody SendMessageReq req) {
        try {
            if (StringUtils.isBlank(req.getTaskId()) || StringUtils.isBlank(req.getRoomId()) || StringUtils.isBlank(req.getUserId())) {
                log.warn("===sendMessage payload failed: {}", JacksonHelper.toJson(req));
            } else {
                if (Objects.isNull(req.getPushTime())) {
                    req.setPushTime(System.currentTimeMillis());
                }
                rabbitMQTemplate.sendMessage(RabbitQueues.CONVERSATION_EXCHANGE, BeanUtil.beanToMap(req));
            }
        } catch (Exception ex) {
            log.error("===sendMessage error: {}", ex.getMessage(), ex);
        }
        return ResultResp.success(Boolean.TRUE);
    }
}

package com.rolling.biz.controller.pgw;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.rolling.biz.constants.app.*;
import com.rolling.biz.model.entity.app.EvalTask;
import com.rolling.biz.model.entity.app.TaskContact;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.entity.app.TaskInfo;
import com.rolling.biz.model.request.aliyun.DescribeAIAgentReq;
import com.rolling.biz.model.request.aliyun.RtcAuthTokenReq;
import com.rolling.biz.model.request.app.ClientOperateReq;
import com.rolling.biz.model.response.StartVoiceChatResp;
import com.rolling.biz.model.response.StopVoiceChatResp;
import com.rolling.biz.model.response.aliyun.DescribeAiAgentRes;
import com.rolling.biz.service.aliyun.AliyunRtcService;
import com.rolling.biz.service.app.*;
import com.rolling.biz.service.volc.VolcRtcService;
import com.rolling.library.exception.AppExceptionAssert;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequiredArgsConstructor
public class EndInboundController {
    private final RobotService robotService;
    private final VolcRtcService volcRtcService;
    private final TaskInfoService taskInfoService;
    private final ThrottleService throttleService;
    private final AliyunRtcService aliyunRtcService;
    private final RabbitMQTemplate rabbitMQTemplate;
    private final TaskContactService taskContactService;
    private final EvalTaskService evalTaskService;

    //===================火山引擎===========================
    @PostMapping("/pgw/api/client/proxyFetch")
    public ResultResp proxyFetch(@RequestParam String action, @RequestBody ClientOperateReq req) {
        TaskInfo taskInfo = taskInfoService.getByUuid(req.getTaskId(), true);
        if ("GetConfig".equals(action)) {
            String roomId = IdUtil.simpleUUID();
            Map<String, Object> result = MapUtil.builder(MapUtil.<String, Object>newHashMap())
                    .put("appId", volcRtcService.getRtcAppId())
                    .put("token", volcRtcService.getToken(roomId, req.getUserId()))
                    .put("roomId", roomId)
                    .build();
            return ResultResp.success(result);
        } else if (VolcApiConst.StartVoiceChat.equals(action)) {
            Robot robot = robotService.getById(taskInfo.getRobotId(), true);
            TaskContact taskContact = taskContactService.getOneByExternalId(taskInfo.getId(), req.getUserId(), false);
            AppExceptionAssert.pass(Objects.nonNull(taskContact), "没有访谈任务");
            taskContact.setStatus(ContactStatusEnum.PROCESSING.getCode());
            taskContactService.updateById(taskContact);
            StartVoiceChatResp resp = robotService.startVoiceChat(robot, req.getUserId(), req.getRoomId());
            if ("ok".equals(resp.getResult())) {
                throttleService.renew(req.getUserId());
            }
            return ResultResp.success(resp);
        } else {
            StopVoiceChatResp resp = robotService.stopVoiceChat(req.getUserId(), req.getRoomId());
            if ("ok".equals(resp.getResult())) {
                throttleService.remove(req.getUserId());
                TaskContact taskContact = taskContactService.getOneByExternalId(taskInfo.getId(), req.getUserId(), false);
                if (Objects.nonNull(taskContact)) {
                    taskContact.setStatus(ContactStatusEnum.SUCCESS.getCode());
                    taskContactService.updateById(taskContact);
                }
                rabbitMQTemplate.sendMessage(RabbitQueues.EVALUATE_EXCHANGE, RabbitQueues.EVALUATE_ROUTING_KEY, MapUtil.<String, Object>builder()
                        .put("taskId", req.getTaskId())
                        .put("userId", req.getUserId())
                        .put("type", EvalTypeEnum.USER.getCode())
                        .build());
            }
            return ResultResp.success(resp);
        }
    }

    //===================阿里云===========================
    @PostMapping("/pgw/api/aiagent/authToken")
    public ResultResp getAuthToken(@RequestBody @Valid RtcAuthTokenReq req) {
        String authToken = aliyunRtcService.getRtcAuthToken(req.getChannelId(), req.getUserId());
        return ResultResp.success(authToken);
    }

    @PostMapping("/pgw/api/aiagent/describe")
    public ResultResp describeAIAgent(@RequestBody @Valid DescribeAIAgentReq req) {
        DescribeAiAgentRes res = aliyunRtcService.describeAIAgentInstance(req.getAiAgentInstanceId(), req.getUserId());
        return ResultResp.success(res);
    }

    @PostMapping("/pgw/api/aiagent/start")
    public ResultResp startAIAgent(@RequestBody @Valid ClientOperateReq req) {
        TaskInfo taskInfo = taskInfoService.getByUuid(req.getTaskId(), true);
        TaskContact taskContact = taskContactService.getOneByExternalId(taskInfo.getId(), req.getUserId(), false);
        AppExceptionAssert.pass(Objects.nonNull(taskContact), "没有访谈任务");
        taskContact.setStatus(ContactStatusEnum.PROCESSING.getCode());
        taskContactService.updateById(taskContact);
        return ResultResp.success(taskContact);
    }

    @PostMapping("/pgw/api/aiagent/stop")
    public ResultResp stopAIAgent(@RequestBody @Valid ClientOperateReq req) {
        TaskInfo taskInfo = taskInfoService.getByUuid(req.getTaskId(), true);
        TaskContact contact = taskContactService.getOneByExternalId(taskInfo.getId(), req.getUserId(), true);
        if (Objects.nonNull(contact)) {
            contact.setStatus(ContactStatusEnum.SUCCESS.getCode());
            taskContactService.updateById(contact);
        }
        return ResultResp.success(contact);
    }
}

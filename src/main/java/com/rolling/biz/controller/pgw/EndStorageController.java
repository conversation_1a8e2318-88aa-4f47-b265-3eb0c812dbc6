package com.rolling.biz.controller.pgw;

import com.rolling.biz.service.system.StorageService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

@RestController
@RequiredArgsConstructor
public class EndStorageController {
    private final StorageService storageService;

    @GetMapping("/pgw/static/**")
    public ResponseEntity<InputStreamResource> show(HttpServletRequest request) throws Exception {
        String requestUri = request.getRequestURI();
        String storageKey = requestUri.replace("/pgw/static/", "");
        ByteArrayOutputStream outputStream = storageService.getObject(storageKey);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "max-age=31536000");
        return ResponseEntity.ok().headers(headers).contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new InputStreamResource(new ByteArrayInputStream(outputStream.toByteArray())));
    }
}

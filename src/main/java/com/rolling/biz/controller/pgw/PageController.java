package com.rolling.biz.controller.pgw;

import cn.hutool.core.map.MapUtil;
import com.rolling.biz.constants.app.RtcPlatformEnum;
import com.rolling.biz.constants.app.TaskStatusEnum;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.entity.app.TaskInfo;
import com.rolling.biz.service.aliyun.DingtalkService;
import com.rolling.biz.service.app.RobotService;
import com.rolling.biz.service.app.TaskInfoService;
import com.rolling.library.utils.JacksonHelper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Controller
@RequiredArgsConstructor
public class PageController {
    private final RobotService robotService;
    private final TaskInfoService taskInfoService;
    private final DingtalkService dingtalkService;

    @GetMapping("/pgw/index")
    public String index(Model model, HttpServletRequest request) {
        String debug = request.getParameter("debug");
        String userId = request.getParameter("userId");
        Map<String, Object> data = new HashMap<>();
        data.put("dingCorpId", dingtalkService.getCorpId());
        data.put("dingClientId", dingtalkService.getClientId());
        data.put("debug", debug);
        data.put("userId", userId);
        model.addAttribute("data", data);
        return "volcano/index";
    }

    @GetMapping("/pgw/{uuid}")
    public String openPage(@PathVariable String uuid, Model model) {
        TaskInfo task = taskInfoService.getByUuid(uuid, false);
        if (Objects.nonNull(task) && task.getStatus().equals(TaskStatusEnum.PROCESSING.getCode())) {
            Robot robot = robotService.getByCache(task.getRobotId());
            if (Objects.nonNull(robot)) {
                Map<String, Object> getInboundConfig = JacksonHelper.toBean(StringUtils.isBlank(task.getInboundConfig()) ? "{}" : task.getInboundConfig(), Map.class);
                Map<String, Object> data = new HashMap<>();
                data.put("uuid", task.getUuid());
                data.put("bgUrl", task.getBgUrl());
                data.put("holdTalkTimeout", MapUtil.getInt(getInboundConfig, "holdTalkTimeout", 60));
                data.put("showAudioWave", MapUtil.getBool(getInboundConfig, "showAudioWave", false));
                model.addAttribute("data", data);
                if (StringUtils.isNotBlank(robot.getPlatform()) && robot.getPlatform().equals(RtcPlatformEnum.ALIYUN.getCode())) {
                    data.put("voiceAgentId", robot.getExternalAgentId());
                    return "aliyun/index";
                }
                return "volcano/index";
            }
        }
        return "404";
    }
}

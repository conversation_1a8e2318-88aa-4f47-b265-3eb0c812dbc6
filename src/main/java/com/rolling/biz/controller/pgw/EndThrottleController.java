package com.rolling.biz.controller.pgw;

import com.rolling.biz.model.request.app.ThrottleLimitReq;
import com.rolling.biz.service.app.ThrottleService;
import com.rolling.library.model.response.ResultResp;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/pgw/api/throttle")
public class EndThrottleController {
    private final ThrottleService throttleService;

    @GetMapping("/limit")
    public ResultResp limit(@Valid ThrottleLimitReq req) {
        boolean result = throttleService.limit();
        return ResultResp.success(result);
    }
}

package com.rolling.biz.controller.pgw;

import cn.hutool.core.map.MapUtil;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import com.rolling.library.utils.JacksonHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequiredArgsConstructor
public class CallbackController {
    private final RabbitMQTemplate rabbitMQTemplate;
    private final static String VOICE_CHAT_NAME = "VoiceChat";

    @PostMapping("/pgw/callback/event")
    public ResultResp callback(@RequestBody Map<String, Object> payload) {
        try {
            String eventType = MapUtil.getStr(payload, "EventType");
            String eventData = MapUtil.getStr(payload, "EventData");
            if (VOICE_CHAT_NAME.equals(eventType)) {
                Map<String, Object> eventDataMap = JacksonHelper.toBean(eventData, Map.class);
                Integer statusType = MapUtil.getInt(eventDataMap, "EventType");
                // 0: 智能体任务状态发生变化，1: 智能体任务出现错误
                if (Objects.nonNull(statusType) && statusType == 1) {
                    rabbitMQTemplate.sendMessage(RabbitQueues.VOICE_CHAT_EVENT_EXCHANGE, RabbitQueues.VOICE_CHAT_EVENT_ROUTING_KEY, eventDataMap);
                }
            }
        } catch (Exception ex) {
            log.error("===callback event error: {}", ex.getMessage(), ex);
        }
        return ResultResp.success(Boolean.TRUE);
    }
}

package com.rolling.biz.controller.pgw;


import com.rolling.biz.service.aliyun.DingtalkService;
import com.rolling.biz.service.app.ContactInfoService;
import com.rolling.library.exception.AppExceptionAssert;
import com.rolling.library.model.response.ResultResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/pgw/api/dingtalk")
public class EndUserController {
    private final DingtalkService dingtalkService;
    private final ContactInfoService contactInfoService;

    @PostMapping("/userinfo")
    public ResultResp getUserInfo(@RequestParam String code) {
        AppExceptionAssert.pass(StringUtils.isNotBlank(code), "免登码不能为空");
        String userId = dingtalkService.getUserId(code);
        AppExceptionAssert.pass(StringUtils.isNotBlank(userId), "userid not found");
        var userInfo = dingtalkService.getUserDetail(userId);
        AppExceptionAssert.pass(Objects.nonNull(userInfo), "userinfo not exist");
        contactInfoService.saveUserInfo(userInfo);
        return ResultResp.success(userInfo);
    }
}

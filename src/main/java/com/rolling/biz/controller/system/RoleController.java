package com.rolling.biz.controller.system;

import cn.hutool.core.util.IdUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.biz.model.entity.system.Role;
import com.rolling.biz.model.entity.system.RolePermission;
import com.rolling.biz.model.request.system.RolePermissionReq;
import com.rolling.biz.model.request.system.RoleReq;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.biz.service.system.RoleService;
import com.rolling.library.web.controller.BaseCrudController;
import com.rolling.library.model.request.BasePageReq;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.StringHelper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/api/system/roles")
@RequiredArgsConstructor
public class RoleController extends BaseCrudController<RoleService, Role, BasePageReq, RoleReq, RoleReq> {

    private final OperateLogService operateLogService;

    @Override
    public OperateLogService getOperateLogService() {
        return operateLogService;
    }

    @Override
    protected void bindOperateLog(OperateLog operateLog, Role role) {
        operateLog.setModule(ModuleEnum.ROLE.getCode());
        operateLog.setModuleName(role.getName());
        operateLog.setContent(String.format("编号{%s},名称{%s}", role.getId(), role.getName()));
    }

    @Override
    protected void bindQueryWrapper(BasePageReq req, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.likeRight(Role::getName, value));
    }

    @Override
    protected void saveBefore(Role entity, RoleReq req) {
        entity.setCode(IdUtil.fastSimpleUUID());
    }

    @GetMapping("/{id}/permissions")
    public ResultResp listRolePermission(@PathVariable Long id) {
        Role role = getService().getById(id, Boolean.TRUE);
        List<RolePermission> list = getService().listRolePermission(Arrays.asList(role.getId()));
        return ResultResp.success(list);
    }

    @PostMapping("/{id}/permissions")
    public ResultResp saveRolePermissions(@PathVariable Long id, @RequestBody @Valid RolePermissionReq req) {
        Role role = getService().getById(id, Boolean.TRUE);
        getService().saveRolePermission(role, req.getPermissions());
        return ResultResp.success(id);
    }
}

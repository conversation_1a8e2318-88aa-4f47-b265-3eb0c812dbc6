package com.rolling.biz.controller.system;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONUtil;
import com.rolling.library.model.response.ResultResp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
public class MenuController {
    private final String permissionTree = ResourceUtil.readUtf8Str("meta/permission.json");

    @GetMapping("/api/system/permissionTree")
    public ResultResp getPermissionTree() {
        List<Map> permissions = JSONUtil.toList(permissionTree, Map.class);
        return ResultResp.success(permissions);
    }
}

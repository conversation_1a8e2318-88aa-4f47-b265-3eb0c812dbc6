package com.rolling.biz.controller.system;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.rolling.biz.model.entity.system.Role;
import com.rolling.biz.model.entity.system.RolePermission;
import com.rolling.biz.model.entity.system.User;
import com.rolling.biz.model.request.system.LoginReq;
import com.rolling.biz.service.system.RoleService;
import com.rolling.biz.service.system.UserService;
import com.rolling.library.exception.AppExceptionAssert;
import com.rolling.library.model.response.ResultResp;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
public class MainController {
    private final UserService userService;
    private final RoleService roleService;

    @PostMapping("/api/user/login")
    public ResultResp login(@RequestBody LoginReq req) {
        User user = userService.findByUsername(req.getUsername());
        AppExceptionAssert.pass(Objects.nonNull(user), "用户名或密码错误");
        AppExceptionAssert.pass(BCrypt.checkpw(req.getPassword(), user.getPassword()), "用户名或密码错误");
        StpUtil.login(String.valueOf(user.getId()));
        return ResultResp.success(user);
    }

    @PostMapping("/api/user/logout")
    public ResultResp logout() {
        StpUtil.logout();
        return ResultResp.success(true);
    }

    @GetMapping("/api/user/current")
    public ResultResp current() {
        Long userId = Long.valueOf(StpUtil.getLoginId().toString());
        User user = userService.getLoginUser(userId, StpUtil.getTokenValue());
        List<String> permissions = new ArrayList<>();
        List<Role> roles = userService.getRoleList(user);
        boolean isAdmin = roles.stream().anyMatch(role -> role.getCode().equals(Role.ROLE_ADMIN));
        if (isAdmin) {
            permissions.add("*");
        } else {
            // 获取角色关联的权限
            List<Long> roleIds = roles.stream().mapToLong(Role::getId).boxed().distinct().collect(Collectors.toList());
            if (roleIds.size() > 0) {
                permissions.addAll(roleService.listRolePermission(roleIds).stream().map(RolePermission::getPermission).distinct().collect(Collectors.toList()));
            }
        }
        Map<String, Object> userMap = BeanUtil.beanToMap(user, "id", "name", "avatar", "phone", "email", "tenantId");
        userMap.put("permissions", permissions);
        userMap.put("isAdmin", isAdmin);
        return ResultResp.success(userMap);
    }
}

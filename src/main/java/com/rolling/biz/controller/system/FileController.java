package com.rolling.biz.controller.system;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.rolling.biz.model.entity.system.FileInfo;
import com.rolling.biz.service.system.FileService;
import com.rolling.biz.service.system.DomainService;
import com.rolling.library.model.response.ResultResp;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.net.URLEncoder;

@RestController
@RequestMapping("/api/file")
@RequiredArgsConstructor
public class FileController {
    private final FileService fileService;
    private final DomainService domainService;

    @PostMapping("/upload")
    public ResultResp upload(@RequestParam("file") MultipartFile file) throws Exception {
        String uuid = IdUtil.fastSimpleUUID();
        FileInfo fileInfo = FileInfo.builder()
                .uuid(uuid)
                .contentType(file.getContentType())
                .originalFileName(file.getOriginalFilename())
                .content(file.getBytes())
                .fileSize(file.getSize())
                .fileName(StrUtil.format("{}.{}", uuid, file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1)))
                .build();
        fileService.save(fileInfo);
        return ResultResp.success(MapUtil.builder(MapUtil.<String, Object>newHashMap(2))
                .put("uuid", fileInfo.getUuid())
                .put("url", StrUtil.format("{}/api/file/download/{}", domainService.getBaseUrl(), fileInfo.getUuid()))
                .build());
    }

    @GetMapping("/download/{uuid}")
    public ResponseEntity<InputStreamResource> download(@PathVariable String uuid) throws Exception {
        FileInfo fileInfo = fileService.getByUuid(uuid);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition", String.format("attachment; filename=\"%s\"", URLEncoder.encode(fileInfo.getOriginalFileName(), "UTF-8")));
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        return ResponseEntity.ok().headers(headers)
                .contentLength(fileInfo.getFileSize())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(new InputStreamResource(new ByteArrayInputStream(fileInfo.getContent())));
    }
}

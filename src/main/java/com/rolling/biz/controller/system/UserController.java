package com.rolling.biz.controller.system;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.biz.model.entity.system.Role;
import com.rolling.biz.model.entity.system.User;
import com.rolling.biz.model.request.system.UserAddReq;
import com.rolling.biz.model.request.system.UserReq;
import com.rolling.biz.model.request.system.UserRoleReq;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.biz.service.system.UserService;
import com.rolling.library.web.controller.BaseCrudController;
import com.rolling.library.model.request.BasePageReq;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.utils.StringHelper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/system/users")
@RequiredArgsConstructor
public class UserController extends BaseCrudController<UserService, User, BasePageReq, UserAddReq, UserReq> {

    private final OperateLogService operateLogService;

    @Override
    public OperateLogService getOperateLogService() {
        return operateLogService;
    }

    @Override
    protected void bindOperateLog(OperateLog operateLog, User entity) {
        operateLog.setModule(ModuleEnum.USER.getCode());
        operateLog.setModuleName(entity.getName());
        operateLog.setContent(String.format("编号{%s},名称{%s}", entity.getId(), entity.getName()));
    }

    @Override
    protected void bindQueryWrapper(BasePageReq req, QueryWrapper queryWrapper) {
        StringHelper.ifPresent(req.getKeyword(), value -> queryWrapper.likeRight(User::getName, value));
    }

    @Override
    protected void saveBefore(User entity, UserAddReq req) {
        entity.setPassword(BCrypt.hashpw(entity.getPassword()));
    }

    @GetMapping("/roles")
    public ResultResp getRoleMaps(@RequestParam String userIds) {
        List<Long> idList = Arrays.stream(userIds.split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        Map<Long, List<Role>> userRoles = getService().getRoleMap(idList);
        return ResultResp.success(userRoles);
    }

    @PostMapping("/{id}/roles")
    public ResultResp saveUserRoles(@PathVariable Long id, @RequestBody @Valid UserRoleReq req) {
        User user = getService().getById(id, Boolean.TRUE);
        getService().saveUserRoles(user, req.getRoleIds());
        return ResultResp.success(id);
    }
}

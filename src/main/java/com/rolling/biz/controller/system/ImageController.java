package com.rolling.biz.controller.system;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.rolling.biz.service.system.DomainService;
import com.rolling.biz.service.system.StorageService;
import com.rolling.library.model.response.ResultResp;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/image")
public class ImageController {
    private final DomainService domainService;
    private final StorageService storageService;

    @PostMapping("/upload")
    public ResultResp upload(@RequestParam("file") MultipartFile file) throws Exception {
        String storageKey = storageService.putObject(file);
        /**
         ImageInfo imageInfo = new ImageInfo();
         imageInfo.setUuid(IdUtil.simpleUUID());
         imageInfo.setFileSize(file.getSize());
         imageInfo.setContentType(file.getContentType());
         imageInfo.setOriginalFileName(file.getOriginalFilename());
         imageInfo.setStorageKey(storageKey);
         imageService.save(imageInfo);
         */
        return ResultResp.success(MapUtil.builder(MapUtil.<String, Object>newHashMap(1))
                .put("url", StrUtil.format("{}/pgw/static/{}", domainService.getBaseUrl(), storageKey))
                .put("uuid", IdUtil.simpleUUID())
                .build());
    }
}

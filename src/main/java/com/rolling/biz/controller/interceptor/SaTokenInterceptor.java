package com.rolling.biz.controller.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.rolling.biz.model.entity.system.User;
import com.rolling.biz.service.system.UserService;
import com.rolling.library.constant.HandlerInterceptorOrder;
import com.rolling.library.context.TenantContextHolder;
import com.rolling.library.context.UserContext;
import com.rolling.library.context.UserContextHolder;
import com.rolling.library.exception.AppExceptionAssert;
import com.rolling.library.web.interceptor.CustomHandlerInterceptor;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Component
@RequiredArgsConstructor
public class SaTokenInterceptor implements CustomHandlerInterceptor {
    private final UserService userService;

    @Override
    public int getOrder() {
        return HandlerInterceptorOrder.ORDER_USER_RESOLVER - 10;
    }

    @Override
    public Set<String> getExcludePathPatterns() {
        Set<String> set = new HashSet<>();
        set.add("/api/user/logout");
        set.add("/api/user/login");
        return set;
    }

    @Override
    public Set<String> getIncludePathPatterns() {
        Set<String> set = new HashSet<>();
        set.add("/api/**");
        return set;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        Object loginId = StpUtil.getLoginId();
        User user = userService.getLoginUser(Long.valueOf(loginId.toString()), StpUtil.getTokenValue());
        AppExceptionAssert.pass(Objects.nonNull(user), HttpStatus.UNAUTHORIZED.getReasonPhrase());
        UserContextHolder.setContext(UserContext.builder().userId(user.getId()).userName(user.getName()).build());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserContextHolder.clearContext();
        TenantContextHolder.clearContext();
    }
}

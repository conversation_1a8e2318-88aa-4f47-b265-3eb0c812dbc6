package com.rolling.biz.service.aliyun;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Params;
import com.rolling.biz.model.response.aliyun.DescribeAiAgentRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class AliyunRtcService {
    @Value("${aliyun.artc.app_id}")
    private String artcAppId;
    @Value("${aliyun.artc.app_key}")
    private String artcAppKey;
    @Value("${aliyun.artc.gslb}")
    private String gslb;
    private final com.aliyun.teaopenapi.Client teaClient;

    public String getRtcAuthToken(String channelId, String userId) {
        long timestamp = getClientTimestamp();
        // 生成客户端的rtcAuthToken，基于客户端传的userid
        String rtcAuthToken = createBase64Token(channelId, userId, timestamp);
        return rtcAuthToken;
    }


    private long getClientTimestamp() {
        /* 过期时间戳最大24小时 */
        return DateUtils.addDays(new Date(), 2).getTime() / 1000;
    }

    private String createBase64Token(String channelId, String userId, long timestamp) {
        String rtcAuthStr = String.format("%s%s%s%s%d", artcAppId, artcAppKey, channelId, userId, timestamp);
        String rtcAuth = getSHA256(rtcAuthStr);
        JSONObject tokenJson = new JSONObject();
        tokenJson.put("appid", artcAppId);
        tokenJson.put("channelid", channelId);
        tokenJson.put("userid", userId);
        tokenJson.put("nonce", "");
        tokenJson.put("timestamp", timestamp);
        tokenJson.put("gslb", new String[]{gslb});
        tokenJson.put("token", rtcAuth);
        return Base64.encodeBase64String(JSON.toJSONBytes(tokenJson));
    }

    /**
     * 字符串签名
     *
     * @param str 输入源
     * @return 返回签名
     */
    private static String getSHA256(String str) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes("UTF-8"));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encodestr;
    }

    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    public DescribeAiAgentRes describeAIAgentInstance(String aiAgentInstanceId, String userId) {
        Params params = new Params()
                // 接口名称
                .setAction("DescribeAIAgentInstance")
                // 接口版本
                .setVersion("2020-11-09")
                // 接口协议
                .setProtocol("HTTPS")
                // 接口 HTTP 方法
                .setMethod("POST")
                .setAuthType("AK")
                .setStyle("HTTPS")
                // 接口 PATH
                .setPathname("/")
                // 接口请求体内容格式
                .setReqBodyType("json")
                // 接口响应体内容格式
                .setBodyType("json");
        // runtime options
        java.util.Map<String, Object> queries = new java.util.HashMap<>();
        queries.put("InstanceId", aiAgentInstanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String requestId = StringUtils.EMPTY;
        String message = StringUtils.EMPTY;
        String errCode = StringUtils.EMPTY;
        int code = 500;
        try {
            com.aliyun.teaopenapi.models.OpenApiRequest request = new com.aliyun.teaopenapi.models.OpenApiRequest()
                    .setQuery(com.aliyun.openapiutil.Client.query(queries));
            long start = System.currentTimeMillis();
            log.info("describeAiAgentInstance, queries:{}", JSONObject.toJSONString(queries));
            Map<String, ?> response = teaClient.callApi(params, request, runtime);
            log.info("describeAiAgentInstance, response:{}, cost:{}ms", JSONObject.toJSONString(response), (System.currentTimeMillis() - start));
            if (response != null) {
                if (response.containsKey("statusCode")) {
                    Integer statusCode = (Integer) response.get("statusCode");
                    if (200 == statusCode) {
                        Map<String, Object> body = (Map<String, Object>) response.get("body");
                        Map<String, Object> instance = (Map<String, Object>) body.get("Instance");

                        String callLogUrl = (String) instance.get("CallLogUrl");
                        String runtimeConfig = JSONObject.toJSONString(instance.get("RuntimeConfig"));
                        String status = (String) instance.get("Status");
                        String template_config = JSONObject.toJSONString(instance.get("TemplateConfig"));
                        String user_data = (String) instance.get("UserData");
                        requestId = (String) body.get("RequestId");

                        return DescribeAiAgentRes.builder()
                                .callLogUrl(callLogUrl)
                                .runtimeConfig(runtimeConfig)
                                .status(status)
                                .templateConfig(template_config)
                                .userData(user_data)
                                .code(200)
                                .message("success")
                                .requestId(requestId)
                                .build();
                    }
                }
            }
        } catch (TeaException e) {
            log.error("describeAiAgentInstance Tea error. e:{}", e.getMessage());
            requestId = e.getData().get("RequestId").toString();
            message = e.getMessage();
            code = e.getStatusCode();
            errCode = e.getCode();
        } catch (Exception e) {
            message = e.getMessage();
            log.error("describeAiAgentInstance error. e:{}", e.getMessage());
        }
        return DescribeAiAgentRes.builder().code(code).message(message).requestId(requestId).errorCode(errCode).build();
    }
}

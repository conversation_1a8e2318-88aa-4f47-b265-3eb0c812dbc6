package com.rolling.biz.service.aliyun;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.rolling.biz.model.beans.DingDeptInfo;
import com.rolling.biz.model.beans.DingUserInfo;
import com.rolling.biz.service.system.DomainService;
import com.rolling.biz.suport.spi.DingtalkSpi;
import com.rolling.library.utils.JacksonHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class DingtalkService {
    private final static String KEY_TOKEN = "rtc:dingtalk:token";
    @Value("${dingtalk.corp_id}")
    String corpId;
    @Value("${dingtalk.client_id}")
    String clientId;
    @Value("${dingtalk.client_secret}")
    String clientSecret;
    @Value("${dingtalk.agent_id}")
    Long agentId;
    private final DingtalkSpi dingtalkSpi;
    private final DomainService domainService;
    private final StringRedisTemplate stringRedisTemplate;
    private static String NOTICE_TEXT = ResourceUtil.readUtf8Str("meta/notice.txt");

    public String getToken() {
        String token = stringRedisTemplate.opsForValue().get(KEY_TOKEN);
        if (StringUtils.isBlank(token)) {
            Map<String, Object> result = dingtalkSpi.getToken(clientId, clientSecret);
            log.info("===getToken res: {}", JacksonHelper.toJson(result));
            token = MapUtil.getStr(result, "access_token");
            if (StringUtils.isNotBlank(token)) {
                stringRedisTemplate.opsForValue().set(KEY_TOKEN, token, 3600, TimeUnit.SECONDS);
            }
        }
        return token;
    }

    public String getUserId(String code) {
        String token = getToken();
        Map<String, Object> result = dingtalkSpi.getUserInfo(token, Map.of("code", code));
        log.info("===getUserInfo res: {}", JacksonHelper.toJson(result));
        Integer errcode = MapUtil.getInt(result, "errcode");
        if (errcode == 0) {
            Map<String, Object> dataMap = MapUtil.get(result, "result", Map.class);
            return MapUtil.getStr(dataMap, "userid");
        }
        return null;
    }

    public DingDeptInfo getDeptDetail(Long deptId) {
        String token = getToken();
        Map<String, Object> result = dingtalkSpi.getDeptDetail(token, deptId);
        log.info("===getDeptDetail res: {}", JacksonHelper.toJson(result));
        Integer errcode = MapUtil.getInt(result, "errcode");
        if (errcode == 0) {
            Map<String, Object> dataMap = MapUtil.get(result, "result", Map.class);
            return DingDeptInfo.builder()
                    .deptId(MapUtil.getLong(dataMap, "dept_id"))
                    .deptName(MapUtil.getStr(dataMap, "name"))
                    .build();
        }
        return null;
    }

    public DingUserInfo getUserDetail(String userId) {
        String token = getToken();
        Map<String, Object> result = dingtalkSpi.getUserDetail(token, userId);
        log.info("===getUserDetail res: {}", JacksonHelper.toJson(result));
        Integer errcode = MapUtil.getInt(result, "errcode");
        if (errcode == 0) {
            Map<String, Object> dataMap = MapUtil.get(result, "result", Map.class);
            DingUserInfo userInfo = DingUserInfo.builder()
                    .name(MapUtil.getStr(dataMap, "name"))
                    .avatar(MapUtil.getStr(dataMap, "avatar"))
                    .userId(MapUtil.getStr(dataMap, "userid"))
                    .unionId(MapUtil.getStr(dataMap, "unionid"))
                    .title(MapUtil.getStr(dataMap, "title"))
                    .jobNumber(MapUtil.getStr(dataMap, "job_number"))
                    .build();
            List<Map<String, Object>> deptOrderList = MapUtil.get(dataMap, "dept_order_list", List.class);
            // 根据 order 字段，倒序排序
            deptOrderList = deptOrderList.stream()
                    .sorted((o1, o2) -> MapUtil.getInt(o2, "order") - MapUtil.getInt(o1, "order"))
                    .collect(Collectors.toList());
            if (deptOrderList.size() > 0) {
                Map<String, Object> deptMap = deptOrderList.get(0);
                userInfo.setDeptId(MapUtil.getLong(deptMap, "dept_id"));
                DingDeptInfo deptInfo = getDeptDetail(userInfo.getDeptId());
                Optional.ofNullable(deptInfo).ifPresent(obj -> userInfo.setDeptName(obj.getDeptName()));
            }
            return userInfo;
        }
        return null;
    }

    public void sendMessage(List<String> userIds) {
        String token = getToken();
        if (StringUtils.isBlank(token)) {
            log.warn("===dingtalk token not found===");
            return;
        }
        String actionUrl = domainService.getBaseUrl() + "/pgw/index";
        Map<String, Object> payload = new HashMap<>();
        payload.put("agent_id", agentId);
        payload.put("userid_list", StrUtil.join(",", userIds));  // 接收者的userid列表，最大用户列表长度100
        payload.put("msg", MapUtil.<String, Object>builder()
                .put("msgtype", "action_card")
                .put("action_card", MapUtil.<String, Object>builder()
                        .put("markdown", NOTICE_TEXT)
                        .put("title", "访谈任务即将截止")
                        .put("btn_orientation", "1")
                        .put("btn_json_list", Arrays.asList(Map.of("title", "一个按钮", "action_url", actionUrl)))
                        .build())
                .build());
        Map<String, Object> result = dingtalkSpi.sendMessage(token, payload);
        log.info("===sendMessage res: {}", JacksonHelper.toJson(result));
    }

    public String getCorpId() {
        return corpId;
    }

    public String getClientId() {
        return clientId;
    }
}

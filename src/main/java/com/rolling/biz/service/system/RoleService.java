package com.rolling.biz.service.system;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.mapper.system.RoleMapper;
import com.rolling.biz.mapper.system.RolePermissionMapper;
import com.rolling.biz.mapper.system.UserRoleMapper;
import com.rolling.biz.model.entity.system.Role;
import com.rolling.biz.model.entity.system.RolePermission;
import com.rolling.biz.model.entity.system.UserRole;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RoleService extends BaseServiceImpl<RoleMapper, Role> {
    private final UserRoleMapper userRoleMapper;
    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public void deleteAfterWithTransaction(Role role) {
        deleteUserRole(role);
        deleteRolePermission(role);
    }

    public void deleteUserRole(Role role) {
        userRoleMapper.deleteByQuery(QueryWrapper.create().eq(UserRole::getRoleId, role.getId()));
    }

    public void deleteRolePermission(Role role) {
        rolePermissionMapper.deleteByQuery(QueryWrapper.create().eq(RolePermission::getRoleId, role.getId()));
    }

    public List<RolePermission> listRolePermission(List<Long> roleIds) {
        return rolePermissionMapper.selectListByQuery(QueryWrapper.create().in(RolePermission::getRoleId, roleIds));
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveRolePermission(Role role, List<String> permissions) {
        deleteRolePermission(role);
        if (permissions.size() > 0) {
            List<RolePermission> list = new ArrayList<>(permissions.size());
            for (String permission : permissions) {
                RolePermission entity = new RolePermission();
                entity.setRoleId(role.getId());
                entity.setPermission(permission);
                list.add(entity);
            }
            rolePermissionMapper.insertBatch(list);
        }
    }
}

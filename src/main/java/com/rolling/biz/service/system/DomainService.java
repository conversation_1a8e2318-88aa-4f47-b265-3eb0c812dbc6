package com.rolling.biz.service.system;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class DomainService {
    @Value("${server.domain}")
    private String domain;
    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Value("${report.postUrl}")
    private String postReportUrl;
    @Value("${report.userUrl}")
    private String userReportUrl;

    public String getDomain() {
        return domain;
    }

    public String getContextPath() {
        return contextPath;
    }

    public String getBaseUrl() {
        return domain + contextPath;
    }

    public String getReportBaseUrl(String type) {
        if ("user".equals(type)) {
            return userReportUrl;
        } else if ("task".equals(type)) {
            return postReportUrl;
        }
        return "";
    }
}

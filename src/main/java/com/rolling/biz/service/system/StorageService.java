package com.rolling.biz.service.system;

import com.rolling.biz.suport.oss.OssService;
import com.rolling.biz.suport.tos.TosService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

@Component
@RequiredArgsConstructor
public class StorageService {
    @Value("${oss.enable}")
    private Boolean ossEnable;
    @Value("${tos.enable}")
    private Boolean tosEnable;
    private final OssService ossService;
    private final TosService tosService;

    private String getCdnUrl() {
        String cdnUrl = "";
        if (ossEnable) {
            cdnUrl = ossService.getCdnUrl();
        } else if (tosEnable) {
            cdnUrl = tosService.getCdnUrl();
        }
        return cdnUrl;
    }

    public String getUrl(String storageKey) {
        return String.format("%s/%s", getCdnUrl(), storageKey);
    }

    public String putObject(MultipartFile file) {
        String storageKey = null;
        if (ossEnable) {
            storageKey = ossService.putObject(file);
        } else if (tosEnable) {
            storageKey = tosService.putObject(file);
        }
        return storageKey;
    }

    public ByteArrayOutputStream getObject(String storageKey) {
        if (ossEnable) {
            return ossService.getObject(storageKey);
        } else if (tosEnable) {
            return tosService.getObject(storageKey);
        }
        return null;
    }
}

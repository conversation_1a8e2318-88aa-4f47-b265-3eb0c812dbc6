package com.rolling.biz.service.system;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.mapper.system.RoleMapper;
import com.rolling.biz.mapper.system.UserMapper;
import com.rolling.biz.mapper.system.UserRoleMapper;
import com.rolling.biz.model.entity.system.Role;
import com.rolling.biz.model.entity.system.User;
import com.rolling.biz.model.entity.system.UserRole;
import com.rolling.library.service.impl.BaseServiceImpl;
import com.rolling.library.utils.TenantHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UserService extends BaseServiceImpl<UserMapper, User> {
    private final RoleMapper roleMapper;
    private final UserRoleMapper userRoleMapper;
    private final static Cache<String, User> TOKEN_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .maximumSize(100)
            .build();

    public User getLoginUser(Long id, String token) {
        User user = TOKEN_CACHE.getIfPresent(token);
        if (Objects.nonNull(user)) {
            return user;
        }
        User entity = TenantHelper.withoutTenant(() -> getById(id, false));
        if (Objects.nonNull(entity)) {
            TOKEN_CACHE.put(token, entity);
        }
        return entity;
    }

    public User findByUsername(String username) {
        return TenantHelper.withoutTenant(() -> getOne(QueryWrapper.create().eq(User::getPhone, username).or(User::getEmail).eq(username)));
    }

    @Override
    public void deleteAfterWithTransaction(User user) {
        deleteUserRole(user);
    }

    public void deleteUserRole(User user) {
        userRoleMapper.deleteByQuery(QueryWrapper.create().eq(UserRole::getUserId, user.getId()));
    }

    public List<Role> getRoleList(User user) {
        List<UserRole> userRoles = userRoleMapper.selectListByQuery(QueryWrapper.create().eq(UserRole::getUserId, user.getId()));
        if (!CollectionUtils.isEmpty(userRoles)) {
            List<Long> roleIds = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
            return roleMapper.selectListByQuery(QueryWrapper.create().in(Role::getId, roleIds));
        }
        return new ArrayList<>(0);
    }

    public Map<Long, List<Role>> getRoleMap(List<Long> userIds) {
        List<UserRole> userRoles = userRoleMapper.selectListByQuery(QueryWrapper.create().in(UserRole::getUserId, userIds));
        if (userRoles.size() > 0) {
            List<Long> roleIds = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
            List<Role> roleList = roleMapper.selectListByQuery(QueryWrapper.create().in(Role::getId, roleIds));
            Map<Long, Role> roleMap = roleList.stream().collect(Collectors.toMap(Role::getId, role -> role));
            Map<Long, List<Role>> userRoleMap = userRoles.stream().collect(Collectors.groupingBy(UserRole::getUserId, Collectors.mapping(obj -> roleMap.get(obj.getRoleId()), Collectors.toList())));
            return userRoleMap;
        }
        return new HashMap<>(0);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveUserRoles(User user, List<Long> roleIds) {
        deleteUserRole(user);
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        List<Role> roles = roleMapper.selectListByQuery(QueryWrapper.create().in(Role::getId, roleIds));
        if (CollectionUtils.isEmpty(roles)) {
            return;
        }
        List<UserRole> list = new ArrayList<>(roles.size());
        for (Role role : roles) {
            UserRole userRole = new UserRole();
            userRole.setUserId(user.getId());
            userRole.setRoleId(role.getId());
            list.add(userRole);
        }
        userRoleMapper.insertBatch(list);
    }
}

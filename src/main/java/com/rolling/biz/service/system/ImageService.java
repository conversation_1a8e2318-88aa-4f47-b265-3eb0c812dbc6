package com.rolling.biz.service.system;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.mapper.system.ImageMapper;
import com.rolling.biz.model.entity.system.ImageInfo;
import com.rolling.library.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class ImageService extends BaseServiceImpl<ImageMapper, ImageInfo> {
    public ImageInfo getByUuid(String uuid) {
        return getOne(QueryWrapper.create().eq(ImageInfo::getUuid, uuid));
    }

    public boolean deleteByUuid(String uuid) {
        return remove(QueryWrapper.create().eq(ImageInfo::getUuid, uuid));
    }
}

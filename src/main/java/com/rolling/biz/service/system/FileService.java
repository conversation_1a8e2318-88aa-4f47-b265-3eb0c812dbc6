package com.rolling.biz.service.system;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.mapper.system.FileMapper;
import com.rolling.biz.model.entity.system.FileInfo;
import com.rolling.library.exception.AppExceptionAssert;
import com.rolling.library.service.impl.BaseServiceImpl;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class FileService extends BaseServiceImpl<FileMapper, FileInfo> {
    public FileInfo getByUuid(String uuid) {
        FileInfo fileInfo = getOne(QueryWrapper.create().eq(FileInfo::getUuid, uuid));
        AppExceptionAssert.pass(Objects.nonNull(fileInfo), HttpStatus.NOT_FOUND.getReasonPhrase());
        return fileInfo;
    }

    public boolean deleteByUuid(String uuid) {
        return remove(QueryWrapper.create().eq(FileInfo::getUuid, uuid));
    }
}

package com.rolling.biz.service.llm;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.entity.app.ModelInfo;
import com.rolling.biz.service.app.ModelInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * 配置提供商：火山引擎
 */
@Service("volcano")
@RequiredArgsConstructor
public class VolcanoConfigService implements ProviderConfigService {
    @Value("${volcengine.speech-app-id}")
    private String speechAppId;
    private final ModelInfoService modelInfoService;

    public Map<String, Object> getTTSConfig(Robot robot) {
        Robot.TtsConfig ttsConfig = robot.getTtsConfig();
        ModelInfo modelInfo = modelInfoService.getTtsInfo(ttsConfig.getProvider(), ttsConfig.getVoiceType());
        Map<String, Object> providerParams = new HashMap<>();
        providerParams.put("app", MapBuilder.create(MapUtil.<String, Object>newHashMap(2))
                .put("appid", speechAppId)
                .put("cluster", MapUtil.getStr(modelInfo.getProviderParams(), "cluster", "volcano_tts"))
                .build());
        providerParams.put("audio", MapBuilder.create(MapUtil.<String, Object>newHashMap(3))
                .put("voice_type", ttsConfig.getVoiceType())
                .put("speed_ratio", ttsConfig.getSpeedRatio())
                .put("volume_ratio", ttsConfig.getVolumeRatio())
                .build());
        Map<String, Object> configInfo = new HashMap<>();
        configInfo.put("IgnoreBracketText", Arrays.asList(1, 2, 3, 4, 5));
        configInfo.put("Provider", ttsConfig.getProvider());
        configInfo.put("ProviderParams", providerParams);
        return configInfo;
    }
}

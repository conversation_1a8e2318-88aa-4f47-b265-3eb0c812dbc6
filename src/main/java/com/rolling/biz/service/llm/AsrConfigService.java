package com.rolling.biz.service.llm;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import com.rolling.biz.model.entity.app.Robot;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class AsrConfigService {
    @Value("${volcengine.speech-app-id}")
    private String speechAppId;
    @Value("${volcengine.asr_bigmodel_token}")
    private String asrBigModelToken;
    private final static String BIGMODEL_NAME = "bigmodel";

    public Map<String, Object> getASRConfig(Robot robot) {
        Robot.AsrConfig asrConfig = robot.getAsrConfig();
        String provider = asrConfig.getProvider();
        Map<String, Object> providerParams = new HashMap<>();
        if (provider.contains(BIGMODEL_NAME)) {
            providerParams.put("Mode", "bigmodel");
            providerParams.put("AppId", speechAppId);
            providerParams.put("AccessToken", asrBigModelToken);
        } else {
            providerParams.put("Mode", "smallmodel");
            providerParams.put("AppId", speechAppId);
        }
        Integer silenceTime = Optional.ofNullable(asrConfig.getSilenceTime()).orElse(2500);
        return MapBuilder.<String, Object>create()
                .put("Provider", "volcano")
                .put("ProviderParams", providerParams)
                .put("VolumeGain", 0.1)
                .put("VADConfig", MapBuilder.create(MapUtil.<String, Object>newHashMap(1))
                        .put("SilenceTime", silenceTime)
                        .build())
                .build();
    }
}

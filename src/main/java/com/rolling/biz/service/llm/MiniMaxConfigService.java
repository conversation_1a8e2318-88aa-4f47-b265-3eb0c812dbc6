package com.rolling.biz.service.llm;

import cn.hutool.core.map.MapUtil;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.entity.app.ModelInfo;
import com.rolling.biz.service.app.ModelInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 配置提供商：minimax
 */
@Service("minimax")
@RequiredArgsConstructor
public class MiniMaxConfigService implements ProviderConfigService {

    private final ModelInfoService modelInfoService;

    public Map<String, Object> getTTSConfig(Robot robot) {
        Robot.TtsConfig ttsConfig = robot.getTtsConfig();
        ModelInfo modelInfo = modelInfoService.getTtsInfo(ttsConfig.getProvider(), ttsConfig.getVoiceType());
        Map<String, Object> params = modelInfo.getProviderParams();
        Map<String, Object> providerParams = new HashMap<>();
        providerParams.put("Authorization", modelInfo.getApiKey());
        providerParams.put("Groupid", MapUtil.getStr(params, "groupId"));
        providerParams.put("model", MapUtil.getStr(params, "model"));
        providerParams.put("URL", modelInfo.getApiUrl());
        providerParams.put("stream", false);
        providerParams.put("voice_setting", Map.of(
                "voice_id", ttsConfig.getVoiceType(),
                "speed", ttsConfig.getSpeedRatio(),
                "vol", ttsConfig.getVolumeRatio(),
                "pitch", 1.0
        ));
        Map<String, Object> configInfo = new HashMap<>();
        configInfo.put("IgnoreBracketText", Arrays.asList(1, 2, 3, 4, 5));
        configInfo.put("Provider", ttsConfig.getProvider());
        configInfo.put("ProviderParams", providerParams);
        return configInfo;
    }
}

package com.rolling.biz.service.app;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.rolling.biz.mapper.app.TaskMapper;
import com.rolling.biz.model.entity.app.TaskContact;
import com.rolling.biz.model.entity.app.TaskInfo;
import com.rolling.biz.service.aliyun.DingtalkService;
import com.rolling.library.service.impl.BaseServiceImpl;
import com.rolling.library.utils.CollectionHelper;
import com.rolling.library.utils.StringHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskInfoService extends BaseServiceImpl<TaskMapper, TaskInfo> {
    private final static Cache<String, TaskInfo> CACHE = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(100)
            .build();
    private final TaskContactService taskContactService;
    private final DingtalkService dingtalkService;

    public TaskInfo getByUuid(String uuid, boolean throwEx) {
        return getOne(QueryWrapper.create().eq(TaskInfo::getUuid, uuid), throwEx);
    }

    public TaskInfo getFromCache(String uuid) {
        TaskInfo taskInfo = CACHE.getIfPresent(uuid);
        if (Objects.isNull(taskInfo)) {
            taskInfo = getByUuid(uuid, false);
            if (Objects.nonNull(taskInfo)) {
                CACHE.put(uuid, taskInfo);
            }
        }
        return taskInfo;
    }

    public Map<String, Object> batchGetCount(List<Long> robotIds, String status) {
        QueryWrapper queryWrapper = QueryWrapper.create().select(
                QueryMethods.column(TaskInfo::getRobotId),
                QueryMethods.count(QueryMethods.column(TaskInfo::getRobotId)).as("count")
        ).groupBy(
                QueryMethods.column(TaskInfo::getRobotId)
        );
        CollectionHelper.ifPresent(robotIds, values -> queryWrapper.in(TaskInfo::getRobotId, values));
        StringHelper.ifPresent(status, value -> queryWrapper.eq(TaskInfo::getStatus, value));
        List<Row> rows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        return rows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toMap(row -> String.valueOf(row.get("robotId")), row -> row.get("count")));
    }

    @Override
    public void deleteAfterWithTransaction(TaskInfo task) {
        taskContactService.removeByTask(task.getId());
    }

    public void sendNotice(TaskInfo taskInfo) {
        long maxId = 0L;
        boolean isFirst = true;
        while (isFirst || maxId > 0) {
            isFirst = false;
            List<TaskContact> taskContactList = taskContactService.getMapper().listNotInterviewRecord(taskInfo.getId(), maxId);
            if (taskContactList.size() > 0) {
                List<String> userIds = taskContactList.stream().map(TaskContact::getExternalId).collect(Collectors.toList());
                log.info("===sendNotice taskId: {} and userIds: {}", taskInfo.getId(), userIds);
                // dingtalkService.sendMessage(userIds);
                maxId = taskContactList.get(taskContactList.size() - 1).getId();
            } else {
                maxId = 0L;
            }
        }
    }
}

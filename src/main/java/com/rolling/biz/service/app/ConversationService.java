package com.rolling.biz.service.app;

import cn.hutool.core.map.MapUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.rolling.biz.mapper.app.ConversationMapper;
import com.rolling.biz.model.entity.app.Conversation;
import com.rolling.library.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
public class ConversationService extends BaseServiceImpl<ConversationMapper, Conversation> {
    public List<Map<String, Object>> groupingByContactId(Long taskId, List<Long> contactIds) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(Conversation::getContactId),
                        QueryMethods.min(Conversation::getCreateTime).as("begin"),
                        QueryMethods.max(Conversation::getCreateTime).as("end")
                ).groupBy(
                        QueryMethods.column(Conversation::getContactId)
                );
        queryWrapper.eq(Conversation::getTaskId, taskId);
        queryWrapper.in(Conversation::getContactId, contactIds);
        List<Row> rows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        List<Map<String, Object>> dataList = rows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toList());
        return dataList;
    }

    public List<Map<String, Object>> groupingByTaskId(List<Long> taskIdList, String userId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(Conversation::getTaskId),
                        QueryMethods.count(Conversation::getId).as("count")
                ).groupBy(
                        QueryMethods.column(Conversation::getTaskId)
                );
        queryWrapper.in(Conversation::getTaskId, taskIdList);
        queryWrapper.eq(Conversation::getUserId, userId);
        List<Row> rows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        List<Map<String, Object>> dataList = rows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toList());
        return dataList;
    }

    public void groupingByTaskId(Long taskId, Consumer<List<String>> consumer) {
        Long maxId = 0L;
        Boolean isFirst = Boolean.TRUE;
        while (isFirst || maxId > 0L) {
            isFirst = Boolean.FALSE;
            List<Map<String, Object>> dataList = getMapper().groupingByTaskId(taskId, maxId);
            if (dataList.size() > 0) {
                consumer.accept(dataList.stream().map(dataMap -> MapUtil.getStr(dataMap, "room_id")).collect(Collectors.toList()));
                maxId = MapUtil.getLong(dataList.get(dataList.size() - 1), "max_id");
            } else {
                maxId = 0L;
            }
        }
    }

    public List<Conversation> list(Long taskId, List<String> roomIds) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(Conversation::getTaskId, taskId);
        queryWrapper.in(Conversation::getRoomId, roomIds);
        queryWrapper.orderBy(Conversation::getId);
        return list(queryWrapper);
    }

    public String getText(List<Conversation> conversations) {
        if (conversations.size() > 0) {
            List<String> contentList = conversations.stream().map(obj -> {
                String userType = obj.getUserType().equals("robot") ? "AI访谈官" : "员工";
                return String.format("%s：%s", userType, obj.getContent());
            }).collect(Collectors.toList());
            return String.join("\n", contentList);
        }
        return "无";
    }
}

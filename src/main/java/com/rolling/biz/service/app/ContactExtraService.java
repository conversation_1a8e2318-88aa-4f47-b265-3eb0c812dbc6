package com.rolling.biz.service.app;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.mapper.app.ContactExtraMapper;
import com.rolling.biz.model.entity.app.ContactExtra;
import com.rolling.biz.model.entity.app.Employee;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContactExtraService extends BaseServiceImpl<ContactExtraMapper, ContactExtra> {

    private final EmployeeService employeeService;

    public ContactExtra getByJobNumber(String jobNumber) {
        return getOne(QueryWrapper.create().eq(ContactExtra::getJobNumber, jobNumber));
    }

    public String getJdText(String postNumber) {
        String jdText = "无";
        List<Employee> employeeList = employeeService.list(QueryWrapper.create().eq(Employee::getPostNumber, postNumber));
        if (ObjectUtils.isEmpty(employeeList)) {
            return jdText;
        }
        List<String> jobNumberList = employeeList.stream().map(Employee::getJobNumber).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(jobNumberList)) {
            return jdText;
        }
        if (jobNumberList.size() > 0) {
            List<ContactExtra> extraList = list(QueryWrapper.create().in(ContactExtra::getJobNumber, jobNumberList));
            if (ObjectUtils.isNotEmpty(extraList)) {
                List<String> jdTextList = new ArrayList<>();
                for (ContactExtra extra : extraList) {
                    if (StringUtils.isNotEmpty(extra.getJd())) {
                        jdTextList.add(extra.getJd());
                    }
                }
                if (jdTextList.size() > 0) {
                    jdText = String.join("\n", jdTextList);
                }
            }
        }
        return jdText;
    }
}

package com.rolling.biz.service.app;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class ThrottleService {
    @Value("${throttle.limit}")
    private long limit;
    private final StringRedisTemplate stringRedisTemplate;
    private final static String ACTIVE_USER_KEY = "rtc:session:user:";
    private final static int ACTIVE_EXPIRE = 2;

    public boolean limit() {
        Long count = 0L;
        String pattern = ACTIVE_USER_KEY + "*";
        try (var cursor = stringRedisTemplate.scan(ScanOptions.scanOptions().match(pattern).build())) {
            while (cursor.hasNext()) {
                cursor.next();
                count = count + 1;
            }
        }
        return count >= limit;
    }

    public void renew(String sessionId) {
        stringRedisTemplate.opsForValue().set(ACTIVE_USER_KEY + sessionId, sessionId, ACTIVE_EXPIRE, TimeUnit.MINUTES);
    }

    public void remove(String sessionId) {
        stringRedisTemplate.delete(ACTIVE_USER_KEY + sessionId);
    }
}

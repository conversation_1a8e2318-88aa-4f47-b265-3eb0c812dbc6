package com.rolling.biz.service.app;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.rolling.biz.constants.app.TtsProviderEnum;
import com.rolling.biz.model.entity.app.ModelInfo;
import com.rolling.library.utils.BeanHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ModelInfoService {
    private final static List<ModelInfo> asrList = JSONUtil.toList(ResourceUtil.readUtf8Str("meta/asr.json"), ModelInfo.class);
    private final static List<ModelInfo> ttsList = JSONUtil.toList(ResourceUtil.readUtf8Str("meta/tts.json"), ModelInfo.VoiceInfo.class).stream().flatMap(obj -> {
        List<ModelInfo> modelInfos = new ArrayList<>();
        obj.getVoices().forEach(voice -> {
            ModelInfo modelInfo = BeanHelper.copyProperties(obj, ModelInfo.class);
            Map<String, Object> providerParams = new HashMap<>(modelInfo.getProviderParams());
            providerParams.put("voiceType", MapUtil.getStr(voice, "voiceId"));
            providerParams.put("voiceTypeName", MapUtil.getStr(voice, "voiceName"));
            if (modelInfo.getProvider().equals(TtsProviderEnum.VOLCANO.getCode())) {
                providerParams.put("cluster", MapUtil.getStr(voice, "cluster", MapUtil.getStr(providerParams, "cluster")));
            }
            modelInfo.setProviderParams(providerParams);
            modelInfos.add(modelInfo);
        });
        return modelInfos.stream();
    }).collect(Collectors.toList());

    public List<ModelInfo> getTtsList() {
        return ttsList;
    }

    public List<ModelInfo> getAsrList() {
        return asrList;
    }

    public ModelInfo getTtsInfo(String provider, String voiceType) {
        for (ModelInfo modelInfo : ttsList) {
            String type = MapUtil.getStr(modelInfo.getProviderParams(), "voiceType");
            if (provider.equals(modelInfo.getProvider()) && voiceType.equals(type)) {
                return modelInfo;
            }
        }
        return null;
    }
}

package com.rolling.biz.service.app;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.rolling.biz.mapper.app.ReportLogMapper;
import com.rolling.biz.model.entity.app.ReportLog;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ReportLogService extends ServiceImpl<ReportLogMapper, ReportLog> {

    public String getText(String userId) {
        List<Map<String, String>> dataList = new ArrayList<>();
        List<ReportLog> list = list(QueryWrapper.create().eq(ReportLog::getCreatorId, userId).orderBy(ReportLog::getCreateTime, Boolean.FALSE).limit(100));
        for (ReportLog log : list) {
            // 根据event对象生成markdown文本
            dataList.add(MapUtil.<String, String>builder()
                    .put("创建时间", DateUtil.format(Date.from(log.getCreateTime()), DatePattern.NORM_DATETIME_PATTERN))
                    .put("创建人", log.getCreatorName())
                    .put("周报备注", log.getRemark())
                    .put("周报内容", log.getContents())
                    .build());
        }
        if (dataList.size() > 0) {
            Set<String> keys = dataList.get(0).keySet();
            List<String> fieldList = new ArrayList<>(keys);
            StringBuilder markdown = new StringBuilder();
            // 表头
            for (String field : fieldList) {
                markdown.append("| ").append(field).append(" ");
            }
            markdown.append("|\n");
            // 表头与内容分隔符
            for (String field : fieldList) {
                markdown.append("|:--");
            }
            markdown.append("|\n");
            // 内容
            for (Map<String, String> map : dataList) {
                for (String field : fieldList) {
                    String value = map.getOrDefault(field, "");
                    value = value.replaceAll("\n", "");
                    value = value.replaceAll("\r", "");
                    markdown.append("| ").append(value).append(" ");
                }
                markdown.append("|\n");
            }
            return markdown.toString();
        }
        return "无";
    }
}

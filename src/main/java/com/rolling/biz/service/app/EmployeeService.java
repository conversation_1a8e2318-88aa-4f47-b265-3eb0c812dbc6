package com.rolling.biz.service.app;

import cn.hutool.core.convert.Convert;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.row.Row;
import com.rolling.biz.mapper.app.EmployeeMapper;
import com.rolling.biz.model.entity.app.Employee;
import com.rolling.biz.model.request.app.EmployeePageReq;
import com.rolling.library.service.impl.BaseServiceImpl;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class EmployeeService extends BaseServiceImpl<EmployeeMapper, Employee> {

    private final EmployeeMapper employeeMapper;

    public Map batchGetEmployee(EmployeePageReq pageReq) {
        pageReq.setOrder(StringUtils.isEmpty(pageReq.getOrder()) ? "DESC" : pageReq.getOrder());
        pageReq.setSort(StringUtils.isEmpty(pageReq.getSort()) ? "id" : pageReq.getSort());
        pageReq.setPage((pageReq.getPage() - 1) * pageReq.getPageSize());

        Map resp = new HashMap();
        List<Employee> employeeList = employeeMapper.batchGetEmployee(pageReq);
        int total = employeeMapper.countByTotal(pageReq);
        resp.put("records", employeeList);
        resp.put("total", total);
        return resp;
    }
}

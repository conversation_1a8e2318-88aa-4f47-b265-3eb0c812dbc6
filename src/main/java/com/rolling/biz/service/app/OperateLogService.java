package com.rolling.biz.service.app;

import cn.hutool.core.map.MapUtil;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.constants.system.ModuleEnum;
import com.rolling.biz.mapper.app.OperateLogMapper;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.library.context.UserContextHolder;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class OperateLogService extends BaseServiceImpl<OperateLogMapper, OperateLog> {

    private final RabbitMQTemplate rabbitMQTemplate;

    public boolean sendMessage(OperateLog entity) {

        if (entity.getModuleId() == null) {
            Optional.ofNullable(UserContextHolder.getContext()).ifPresent(context -> {
                entity.setModuleId(context.getUserId());
                entity.setModuleName(context.getUserName());
            });
        }
        rabbitMQTemplate.sendMessage(RabbitQueues.OPERATE_LOG_EXCHANGE, RabbitQueues.OPERATE_LOG_KEY, MapUtil.<String, Object>builder()
                .put("operateLog", entity)
                .build());
        return true;
    }
}

package com.rolling.biz.service.app;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.rolling.biz.constants.app.ContactStatusEnum;
import com.rolling.biz.mapper.app.TaskContactMapper;
import com.rolling.biz.model.entity.app.TaskContact;
import com.rolling.biz.model.request.app.StatContactReq;
import com.rolling.library.service.impl.BaseServiceImpl;
import com.rolling.library.utils.CollectionHelper;
import com.rolling.library.utils.DateHelper;
import com.rolling.library.utils.StringHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskContactService extends BaseServiceImpl<TaskContactMapper, TaskContact> {
    private final static Cache<String, TaskContact> CACHE = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();

    public TaskContact getOneByExternalId(Long taskId, String externalId, boolean throwEx) {
        return getOne(QueryWrapper.create().eq(TaskContact::getTaskId, taskId).eq(TaskContact::getExternalId, externalId), throwEx);
    }

    public TaskContact getFromCache(Long taskId, String externalId) {
        String key = StrUtil.format("rtc:{}:{}", taskId, externalId);
        TaskContact contact = CACHE.getIfPresent(key);
        if (Objects.isNull(contact)) {
            contact = getOneByExternalId(taskId, externalId, false);
            if (Objects.nonNull(contact)) {
                CACHE.put(key, contact);
            }
        }
        return contact;
    }

    public Map<String, Object> batchGetTodayCount(List<Long> robotIds, String status) {
        QueryWrapper queryWrapper = QueryWrapper.create().select(
                QueryMethods.column(TaskContact::getRobotId),
                QueryMethods.count(QueryMethods.column(TaskContact::getRobotId)).as("count")
        ).groupBy(
                QueryMethods.column(TaskContact::getRobotId)
        );
        CollectionHelper.ifPresent(robotIds, values -> queryWrapper.in(TaskContact::getRobotId, values));
        StringHelper.ifPresent(status, value -> queryWrapper.eq(TaskContact::getStatus, value));
        LocalDate now = LocalDate.now(ZoneId.of("Asia/Shanghai"));
        String dateBegin = StrUtil.format("{}T16:00:00Z", now.minusDays(1).format(DateHelper.FORMATTER));
        String dateEnd = StrUtil.format("{}T16:00:00Z", now.format(DateHelper.FORMATTER));
        queryWrapper.ge(TaskContact::getUpdateTime, Instant.parse(dateBegin));
        queryWrapper.lt(TaskContact::getUpdateTime, Instant.parse(dateEnd));
        List<Row> rows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        return rows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toMap(row -> String.valueOf(row.get("robotId")), row -> row.get("count")));
    }

    public Map<String, Object> getStatusCount(StatContactReq req) {
        QueryWrapper totalWrapper = QueryWrapper.create();
        QueryWrapper queryWrapper = QueryWrapper.create().select(
                QueryMethods.column(TaskContact::getStatus),
                QueryMethods.count(QueryMethods.column(TaskContact::getId)).as("count")
        ).groupBy(
                QueryMethods.column(TaskContact::getStatus)
        );
        Optional.ofNullable(req.getRobotId()).ifPresent(value -> {
            queryWrapper.eq(TaskContact::getRobotId, value);
            totalWrapper.eq(TaskContact::getRobotId, value);
        });
        List<Row> rows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        Map<String, Object> countMap = rows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toMap(row -> String.valueOf(row.get("status")), row -> row.get("count")));
        Map<String, Object> result = new HashMap<>();
        for (ContactStatusEnum statusEnum : ContactStatusEnum.values()) {
            result.put(statusEnum.getCode(), countMap.getOrDefault(statusEnum.getCode(), 0L));
        }
        result.put("total", getMapper().selectCountByQuery(totalWrapper));
        return result;
    }

    public Map<String, Object> getStatusCountByTaskIds(StatContactReq req) {
        List<Long> taskIds = Arrays.stream(req.getTaskId().split(StrUtil.COMMA)).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
        QueryWrapper totalWrapper = QueryWrapper.create().select(
                QueryMethods.column(TaskContact::getTaskId),
                QueryMethods.count(QueryMethods.column(TaskContact::getId)).as("count")
        ).groupBy(
                QueryMethods.column(TaskContact::getTaskId)
        );
        QueryWrapper queryWrapper = QueryWrapper.create().select(
                QueryMethods.column(TaskContact::getTaskId),
                QueryMethods.column(TaskContact::getStatus),
                QueryMethods.count(QueryMethods.column(TaskContact::getId)).as("count")
        ).groupBy(
                QueryMethods.column(TaskContact::getTaskId),
                QueryMethods.column(TaskContact::getStatus)
        );
        queryWrapper.in(TaskContact::getTaskId, taskIds);
        totalWrapper.in(TaskContact::getTaskId, taskIds);

        Optional.ofNullable(req.getRobotId()).ifPresent(value -> {
            queryWrapper.eq(TaskContact::getRobotId, value);
            totalWrapper.eq(TaskContact::getRobotId, value);
        });

        List<Row> totalRows = getMapper().selectListByQueryAs(totalWrapper, Row.class);
        Map<String, Object> totalCountMap = totalRows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toMap(row -> String.valueOf(row.get("taskId")), row -> row.get("count")));

        List<Row> queryRows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        Map<String, List<Map<String, Object>>> queryCountMap = queryRows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.groupingBy(row -> String.valueOf(row.get("taskId"))));

        Map<String, Object> result = new HashMap<>();
        for (String key : totalCountMap.keySet()) {
            Map<String, Object> data = MapUtil.builder(MapUtil.<String, Object>newHashMap(1)).put("total", totalCountMap.get(key)).build();
            Map<String, Object> countMap = queryCountMap.get(key).stream().collect(Collectors.toMap(obj -> String.valueOf(obj.get("status")), obj -> obj.get("count")));
            for (ContactStatusEnum statusEnum : ContactStatusEnum.values()) {
                data.put(statusEnum.getCode(), countMap.getOrDefault(statusEnum.getCode(), 0L));
            }
            result.put(key, data);
        }
        return result;
    }

    public void removeByTask(Long taskId) {
        remove(QueryWrapper.create().eq(TaskContact::getTaskId, taskId));
    }

    public List<TaskContact> list(Long taskId, List<String> userIds) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(TaskContact::getTaskId, taskId);
        queryWrapper.in(TaskContact::getExternalId, userIds);
        return list(queryWrapper);
    }
}

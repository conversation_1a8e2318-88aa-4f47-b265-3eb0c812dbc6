package com.rolling.biz.service.app;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.rolling.biz.mapper.app.CalendarEventMapper;
import com.rolling.biz.model.entity.app.CalendarEvent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class CalendarEventService extends ServiceImpl<CalendarEventMapper, CalendarEvent> {
    public String getText(String unionId) {
        List<Map<String, String>> dataList = new ArrayList<>();
        List<CalendarEvent> list = list(QueryWrapper.create().eq(CalendarEvent::getUserId, unionId).orderBy(CalendarEvent::getStartTime, Boolean.FALSE).limit(100));
        for (CalendarEvent event : list) {
            // 根据event对象生成markdown文本
            dataList.add(MapUtil.<String, String>builder()
                    .put("开始时间", DateUtil.format(Date.from(event.getStartTime()), DatePattern.NORM_DATETIME_PATTERN))
                    .put("结束时间", DateUtil.format(Date.from(event.getEndTime()), DatePattern.NORM_DATETIME_PATTERN))
                    .put("是否全天日程", event.getIsAllDay())
                    .put("是否重复日程", event.getIsRepeat())
                    .put("组织人", event.getOrganizer())
                    .put("参与人", event.getAttendees())
                    .put("主题", event.getSummary())
                    .put("描述", event.getDescription())
                    .build());
        }
        if (dataList.size() > 0) {
            Set<String> keys = dataList.get(0).keySet();
            List<String> fieldList = new ArrayList<>(keys);
            StringBuilder markdown = new StringBuilder();
            // 表头
            for (String field : fieldList) {
                markdown.append("| ").append(field).append(" ");
            }
            markdown.append("|\n");
            // 表头与内容分隔符
            for (String field : fieldList) {
                markdown.append("|:--");
            }
            markdown.append("|\n");
            // 内容
            for (Map<String, String> map : dataList) {
                for (String field : fieldList) {
                    String value = map.getOrDefault(field, "");
                    if (StringUtils.isBlank(value)) {
                        value = "";
                    }
                    value = value.replaceAll("\n", "");
                    value = value.replaceAll("\r", "");
                    markdown.append("| ").append(value).append(" ");
                }
                markdown.append("|\n");
            }
            return markdown.toString();
        }
        return "无";
    }
}

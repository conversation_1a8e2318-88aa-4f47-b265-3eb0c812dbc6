package com.rolling.biz.service.app;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
public class WebsocketService {
    private final static Cache<String, WebSocketSession> SESSION_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(1, TimeUnit.HOURS)
            .maximumSize(100)
            .build();


    public void saveSession(String clientId, WebSocketSession session) {
        SESSION_CACHE.put(clientId, session);
    }

    public void removeSession(String clientId) {
        SESSION_CACHE.invalidate(clientId);
    }

    public void pingSession(String clientId) {
        SESSION_CACHE.getIfPresent(clientId);
    }

    public void sendMessage(String clientId, String message) {
        WebSocketSession session = SESSION_CACHE.getIfPresent(clientId);
        if (Objects.nonNull(session)) {
            try {
                session.sendMessage(new TextMessage(message));
            } catch (Exception ex) {
                log.warn("===send ws message faild: {}", ex.getMessage());
            }
        }
    }
}

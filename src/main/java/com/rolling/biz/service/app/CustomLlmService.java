package com.rolling.biz.service.app;

import com.rolling.biz.model.entity.app.LlmInfo;
import com.rolling.biz.suport.spi.CustomLlmSpi;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class CustomLlmService {
    private final CustomLlmSpi customLlmSpi;
    private final LlmInfoService llmInfoService;

    public Map<String, Object> chatCompletion(Long llmId, String userMessage, double temperature) {
        LlmInfo llmInfo = llmInfoService.getById(llmId, false);
        String apiUrl = llmInfo.getProviderParams().getApiUrl();
        String apiKey = llmInfo.getProviderParams().getApiKey();
        String modelName = llmInfo.getProviderParams().getModelName();
        Map<String, Object> body = new HashMap<>();
        body.put("model", modelName);
        body.put("temperature", temperature);
        body.put("stream", false);
        body.put("messages", Arrays.asList(Map.of("role", "user", "content", userMessage)));
        return customLlmSpi.chatCompletion(apiUrl, apiKey, body);
    }
}

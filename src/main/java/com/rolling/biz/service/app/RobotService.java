package com.rolling.biz.service.app;

import cn.hutool.extra.spring.SpringUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.rolling.biz.mapper.app.RobotMapper;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.entity.app.LlmInfo;
import com.rolling.biz.model.request.volc.StartVoiceChatReq;
import com.rolling.biz.model.request.volc.StopVoiceChatReq;
import com.rolling.biz.model.response.StartVoiceChatResp;
import com.rolling.biz.model.response.StopVoiceChatResp;
import com.rolling.biz.service.llm.AsrConfigService;
import com.rolling.biz.service.llm.ProviderConfigService;
import com.rolling.biz.service.volc.VolcRtcService;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class RobotService extends BaseServiceImpl<RobotMapper, Robot> {
    private final static Cache<String, Robot> CACHE = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(100)
            .build();
    private final VolcRtcService volcRtcService;
    private final LlmInfoService llmInfoService;
    private final AsrConfigService asrConfigService;

    public Robot getByCache(Long id) {
        String key = "robot::" + id;
        Robot robot = CACHE.getIfPresent(key);
        if (Objects.isNull(robot)) {
            robot = super.getById(id, false);
            Optional.ofNullable(robot).ifPresent(obj -> CACHE.put(key, obj));
        }
        return robot;
    }

    public StartVoiceChatResp startVoiceChat(Robot robot, String userId, String roomId) {
        StartVoiceChatReq.Config config = new StartVoiceChatReq.Config();

        LlmInfo llmInfo = llmInfoService.getById(robot.getLlmId(), true);
        config.setLlmConfig(llmInfoService.getLLMConfig(llmInfo, robot, userId));

        ProviderConfigService ttsConfigService = SpringUtil.getBean(robot.getTtsConfig().getProvider());
        config.setTtsConfig(ttsConfigService.getTTSConfig(robot));
        config.setAsrConfig(asrConfigService.getASRConfig(robot));

        StartVoiceChatReq.AgentConfig agentConfig = new StartVoiceChatReq.AgentConfig();
        agentConfig.setWelcomeMessage(robot.getLlmConfig().getWelcomeMessage());
        agentConfig.setTargetUserId(Arrays.asList(userId));

        StartVoiceChatReq req = new StartVoiceChatReq();
        req.setRoomId(roomId);
        req.setTaskId(userId);
        req.setConfig(config);
        req.setAgentConfig(agentConfig);
        return volcRtcService.startVoiceChat(req);
    }

    public StopVoiceChatResp stopVoiceChat(String userId, String roomId) {
        StopVoiceChatReq req = new StopVoiceChatReq();
        req.setRoomId(roomId);
        req.setTaskId(userId);
        return volcRtcService.stopVoiceChat(req);
    }
}

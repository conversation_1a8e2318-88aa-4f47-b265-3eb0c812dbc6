package com.rolling.biz.service.app;

import com.mybatisflex.core.query.QueryWrapper;
import com.rolling.biz.mapper.app.FolderContentMapper;
import com.rolling.biz.model.entity.app.FolderContent;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class FolderContentService extends BaseServiceImpl<FolderContentMapper, FolderContent> {

    private final FolderContentMapper folderContentMapper;

    public void addToFolder(Long folderId, List<Long> contentIds) {
        if (!CollectionUtils.isEmpty(contentIds)) {
            List<FolderContent> entities = new ArrayList<>(contentIds.size());
            for (Long contentId : contentIds) {
                entities.add(FolderContent.builder()
                        .folderId(folderId)
                        .contentId(contentId)
                        .build());
            }
            saveBatchIgnore(entities);
        }
    }

    public void removeFromFolder(Long folderId, List<Long> contentIds) {
        QueryWrapper queryWrapper = query();
        queryWrapper.eq(FolderContent::getFolderId, folderId);
        if (!CollectionUtils.isEmpty(contentIds)) {
            queryWrapper.in(FolderContent::getContentId, contentIds);
        }
        remove(queryWrapper);
    }

    public List<FolderContent> findByAllContentId(String type, List<Long> contentIds) {
        return folderContentMapper.findByAllContentId(type, contentIds);
    }
}

package com.rolling.biz.service.app;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.rolling.biz.constants.app.EvalTypeEnum;
import com.rolling.biz.constants.app.TaskStatusEnum;
import com.rolling.biz.mapper.app.EvalTaskMapper;
import com.rolling.biz.model.entity.app.EvalTask;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class EvalTaskService extends BaseServiceImpl<EvalTaskMapper, EvalTask> {

    public Map<String, Object> batchGetTaskStatus(List<Long> evalAppIds) {
        QueryWrapper queryWrapper = QueryWrapper.create().select(
                        QueryMethods.column(EvalTask::getEvalAppId),
                        QueryMethods.count(QueryMethods.column(EvalTask::getId)).as("count")
        ).eq(EvalTask::getStatus, TaskStatusEnum.PROCESSING.getCode()).in(EvalTask::getEvalAppId, evalAppIds)
        .groupBy(
                        QueryMethods.column(EvalTask::getEvalAppId)
        );
        List<Row> rows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        return rows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toMap(row -> String.valueOf(row.get("evalAppId")), row -> row.get("count")));
    }

    public Map<String, Object> batchTotalTodayCompleted(List<Long> evalAppIds) {
        QueryWrapper queryWrapper = QueryWrapper.create().select(
                        QueryMethods.column(EvalTask::getEvalAppId),
                        QueryMethods.count(QueryMethods.column(EvalTask::getId)).as("count")
        ).gt(EvalTask::getUpdateTime, DateUtil.beginOfDay(new Date()).offset(DateField.HOUR, -8))
        .in(EvalTask::getEvalAppId, evalAppIds)
        .groupBy(QueryMethods.column(EvalTask::getEvalAppId));
        List<Row> rows = getMapper().selectListByQueryAs(queryWrapper, Row.class);
        return rows.stream().map(row -> row.toCamelKeysMap()).collect(Collectors.toMap(row -> String.valueOf(row.get("evalAppId")), row -> row.get("count")));
    }

    public List<Long> batchGetByStatus(String status) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(EvalTask::getStatus, status);
        List<EvalTask> taskList = getMapper().selectListByQuery(queryWrapper);
        return taskList.stream().map(EvalTask::getEvalAppId).collect(Collectors.toSet()).stream().toList();
    }

    public Map<String, String> statusAnalysis(String type, List<Long> taskIds) {

        List<String> externalIds = taskIds.stream().map(String::valueOf).collect(Collectors.toList());
        QueryWrapper queryWrapper = QueryWrapper.create();

        queryWrapper.select(
                QueryMethods.column(EvalTask::getExternalId).as("externalId"),
                QueryMethods.column(EvalTask::getStatus).as("status"),
                QueryMethods.count(QueryMethods.column(EvalTask::getId)).as("count")
        );
        queryWrapper.eq(EvalTask::getTaskType, type);
        queryWrapper.in(EvalTask::getExternalId, externalIds);
        queryWrapper.groupBy(
                QueryMethods.column(EvalTask::getExternalId),
                QueryMethods.column(EvalTask::getStatus)
        );
        List<Row> rowList = getMapper().selectListByQueryAs(queryWrapper, Row.class);

        Map<String, Map<String, Integer>> statusMap = new HashMap<>();
        for (Row row : rowList) {
            String externalId = Convert.toStr(row.get("externalId"));
            String status = Convert.toStr(row.get("status"));
            int count = Convert.toInt(row.get("count"));
            Map<String, Integer> totalMap = statusMap.get(externalId) == null ? new HashMap<>() : statusMap.get(externalId);
            totalMap.put(status, (totalMap.get(status) == null ? 0 : totalMap.get(status)) + count);
            statusMap.put(externalId, totalMap);
        }
        Map<String, String> resp = new HashMap<>();
        for (Long taskId : taskIds) {
            Map<String, Integer> totalMap = statusMap.get(String.valueOf(taskId));
            if (totalMap == null) {
                resp.put(String.valueOf(taskId), "initial");
            } else {
                if (totalMap.get("processing") != null) {
                    resp.put(String.valueOf(taskId), "processing");
                } else {
                    resp.put(String.valueOf(taskId), "completed");
                }
            }
        }
        return resp;
    }
}

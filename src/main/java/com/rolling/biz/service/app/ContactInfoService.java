package com.rolling.biz.service.app;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.rolling.biz.mapper.app.ContactInfoMapper;
import com.rolling.biz.model.beans.DingUserInfo;
import com.rolling.biz.model.entity.app.ContactInfo;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContactInfoService extends BaseServiceImpl<ContactInfoMapper, ContactInfo> {
    private final static Cache<String, ContactInfo> CACHE = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();

    public void saveUserInfo(DingUserInfo userInfo) {
        ContactInfo entity = new ContactInfo();
        entity.setName(userInfo.getName());
        entity.setUserId(userInfo.getUserId());
        entity.setUnionId(userInfo.getUnionId());
        entity.setJobNumber(userInfo.getJobNumber());
        entity.setTitle(userInfo.getTitle());
        if (Objects.nonNull(userInfo.getDeptId())) {
            entity.setDeptId(String.valueOf(userInfo.getDeptId()));
        }
        entity.setDeptName(userInfo.getDeptName());
        getMapper().upsertBatch(Arrays.asList(entity));
    }
}

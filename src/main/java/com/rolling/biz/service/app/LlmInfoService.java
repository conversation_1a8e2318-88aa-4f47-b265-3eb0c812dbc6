package com.rolling.biz.service.app;

import cn.hutool.core.map.MapUtil;
import com.rolling.biz.constants.app.LlmProviderEnum;
import com.rolling.biz.mapper.app.LlmInfoMapper;
import com.rolling.biz.model.entity.app.ContactExtra;
import com.rolling.biz.model.entity.app.Robot;
import com.rolling.biz.model.entity.app.LlmInfo;
import com.rolling.library.service.impl.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class LlmInfoService extends BaseServiceImpl<LlmInfoMapper, LlmInfo> {
    private final ContactExtraService contactExtraService;

    public Map<String, Object> getLLMConfig(LlmInfo llmInfo, Robot robot, String userId) {
        ContactExtra extra = Optional.ofNullable(contactExtraService.getByJobNumber(userId)).orElse(new ContactExtra());
        String jd = Optional.ofNullable(extra.getJd()).orElse("无");
        String qa = Optional.ofNullable(extra.getQa()).orElse("无");
        Robot.LlmConfig llmConfig = robot.getLlmConfig();
        String systemMessage = llmConfig.getSystemMessage();
        if (StringUtils.isNotBlank(systemMessage)) {
            systemMessage = systemMessage.replace("{{job}}", jd);
            systemMessage = systemMessage.replace("{{question}}", qa);
        }
        Map<String, Object> config = MapUtil.newHashMap();
        config.put("Mode", llmInfo.getProvider());
        config.put("MaxTokens", 1024);
        config.put("Temperature", llmConfig.getTemperature());
        config.put("WelcomeSpeech", llmConfig.getWelcomeMessage());
        config.put("SystemMessages", Arrays.asList(systemMessage));
        config.put("HistoryLength", Objects.nonNull(llmConfig.getHistoryLength()) ? llmConfig.getHistoryLength() : 100);
        if (LlmProviderEnum.CUSTOM_LLM.getCode().equals(llmInfo.getProvider())) {
            config.put("ModelName", llmInfo.getProviderParams().getModelName());
            config.put("APIKey", llmInfo.getProviderParams().getApiKey());
            config.put("URL", llmInfo.getProviderParams().getApiUrl());
        } else {
            if (llmInfo.getProvider().equals(LlmProviderEnum.ARKV3.getCode())) {
                if (llmInfo.getEndPointId().startsWith("bot-")) {
                    config.put("BotId", llmInfo.getEndPointId());
                } else {
                    config.put("EndPointId", llmInfo.getEndPointId());
                }
            }
        }
        return config;
    }
}

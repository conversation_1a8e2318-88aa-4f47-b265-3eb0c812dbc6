package com.rolling.biz.service.volc;

import com.alibaba.fastjson.JSON;
import com.rolling.biz.constants.app.VolcApiConst;
import com.rolling.biz.constants.app.VolcRtcConfig;
import com.rolling.biz.model.entity.app.AccessToken;
import com.rolling.biz.model.request.volc.*;
import com.rolling.biz.model.response.*;
import com.rolling.library.enums.CustomHttpStatus;
import com.rolling.library.exception.AppException;
import com.volcengine.error.SdkError;
import com.volcengine.helper.Utils;
import com.volcengine.model.response.RawResponse;
import com.volcengine.service.BaseServiceImpl;
import com.volcengine.service.rtc.RtcConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class VolcRtcService extends BaseServiceImpl {
    @Value("${volcengine.access-key}")
    private String accessKey;
    @Value("${volcengine.secret-key}")
    private String secretKey;
    @Value("${volcengine.rtc-region}")
    private String region;
    @Value("${volcengine.rtc-app-id}")
    private String rtcAppId;
    @Value("${volcengine.rtc-app-key}")
    private String rtcAppKey;

    public VolcRtcService() {
        super(RtcConfig.serviceInfo, VolcRtcConfig.apiInfoList);
    }

    @PostConstruct
    public void init() {
        super.setAccessKey(accessKey);
        super.setSecretKey(secretKey);
        super.setRegion(region);
    }

    public String getRtcAppId() {
        return rtcAppId;
    }

    /**
     * 获取Token用于鉴权
     *
     * @param roomId RTC房间ID
     * @param userId RTC用户ID
     * @return
     */
    public String getToken(String roomId, String userId) {
        AccessToken accessToken = new AccessToken(rtcAppId, rtcAppKey, roomId, userId);
        accessToken.ExpireTime(AccessToken.Utils.getTimestamp() + 7200);
        accessToken.AddPrivilege(AccessToken.Privileges.PrivSubscribeStream, 0);
        accessToken.AddPrivilege(AccessToken.Privileges.PrivPublishStream, AccessToken.Utils.getTimestamp() + 7200);
        return accessToken.Serialize();
    }

    /**
     * 获取离线通话房间列表
     */
    public ListRoomInfoResp listRoomInfo(ListRoomInfoReq req) {
        req.setAppId(rtcAppId);
        RawResponse response = query(VolcApiConst.ListRoomInfo, Utils.mapToPairList(Utils.paramsToMap(req)));
        if (response.getCode() != SdkError.SUCCESS.getNumber()) {
            throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), response.getException().getMessage());
        }
        return JSON.parseObject(response.getData(), ListRoomInfoResp.class);
    }

    /**
     * 启动智能体
     */
    public StartVoiceChatResp startVoiceChat(StartVoiceChatReq req) {
        req.setAppId(rtcAppId);
        RawResponse response = json(VolcApiConst.StartVoiceChat, null, JSON.toJSONString(req));
        if (response.getCode() != SdkError.SUCCESS.getNumber()) {
            throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), response.getException().getMessage());
        }
        return JSON.parseObject(response.getData(), StartVoiceChatResp.class);
    }

    /**
     * 更新智能体
     */
    public UpdateVoiceChatResp updateVoiceChat(UpdateVoiceChatReq req) {
        req.setAppId(rtcAppId);
        RawResponse response = json(VolcApiConst.UpdateVoiceChat, null, JSON.toJSONString(req));
        if (response.getCode() != SdkError.SUCCESS.getNumber()) {
            throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), response.getException().getMessage());
        }
        return JSON.parseObject(response.getData(), UpdateVoiceChatResp.class);
    }

    /**
     * 关闭智能体
     */
    public StopVoiceChatResp stopVoiceChat(StopVoiceChatReq req) {
        req.setAppId(rtcAppId);
        RawResponse response = json(VolcApiConst.StopVoiceChat, null, JSON.toJSONString(req));
        if (response.getCode() != SdkError.SUCCESS.getNumber()) {
            throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), response.getException().getMessage());
        }
        return JSON.parseObject(response.getData(), StopVoiceChatResp.class);
    }
}

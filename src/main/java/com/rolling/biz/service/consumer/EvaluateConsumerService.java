package com.rolling.biz.service.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mybatisflex.core.row.Row;
import com.rolling.biz.constants.app.EvalTypeEnum;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.constants.app.TaskStatusEnum;
import com.rolling.biz.mapper.app.ConversationMapper;
import com.rolling.biz.mapper.app.EmployeeMapper;
import com.rolling.biz.model.entity.app.*;
import com.rolling.biz.service.app.*;
import com.rolling.library.enums.CustomHttpStatus;
import com.rolling.library.exception.AppException;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class EvaluateConsumerService {

    private final ConversationService conversationService;
    private final RabbitMQTemplate rabbitMQTemplate;
    private final TaskInfoService taskInfoService;
    private final EvalAppService evalAppService;
    private final CalendarEventService calendarEventService;
    private final EmployeeService employeeService;
    private final ReportLogService reportLogService;
    private final CustomLlmService customLlmService;
    private final EvalTaskService evalTaskService;
    private final ContactExtraService contactExtraService;
    private final RobotService robotService;
    private final EmployeeMapper employeeMapper;
    private final ConversationMapper conversationMapper;

    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange(RabbitQueues.EVALUATE_EXCHANGE),
            value = @Queue(RabbitQueues.EVALUATE_QUEUE),
            key = RabbitQueues.EVALUATE_ROUTING_KEY
    ))
    private void receiveMessage(String message) {
        log.info("===receive evaluate message: {}", message);
        rabbitMQTemplate.preprocessMessage(message, dataMap -> processTask(dataMap));
    }

    private void processTask(Map<String, Object> dataMap) {
        String type = dataMap.get("type").toString();
        if (type.equals(EvalTypeEnum.USER.getCode())) {
            processUserAssign(dataMap);
        } else if (type.equals(EvalTypeEnum.TASK.getCode())) {
            processTaskAssign(dataMap);
        }
    }

    private void processTaskAssign(Map<String, Object> dataMap) {
        String taskUuid = dataMap.get("taskId").toString();
        String postNumber = dataMap.get("postNumber").toString();

        TaskInfo taskInfo = taskInfoService.getByUuid(taskUuid, false);
        if (ObjectUtils.isEmpty(taskInfo)) {
            return;
        }

        List<String> postNumberList = employeeMapper.groupByPostNumber(taskInfo.getId());
        if (!postNumberList.contains(postNumber)) {
            return;
        }

        if (StringUtils.isEmpty(taskInfo.getEvalAppIds())) {
            return;
        }
        List<Long> evalAppIds = Arrays.asList(taskInfo.getEvalAppIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
        List<EvalApp> evalAppList = evalAppService.list(QueryWrapper.create().in(EvalApp::getId, evalAppIds));

        for (EvalApp evalApp : evalAppList) {
            EvalTask evalTask = new EvalTask();
            evalTask.setStatus(TaskStatusEnum.PROCESSING.getCode());
            evalTask.setEvalAppId(Convert.toLong(evalApp.getId()));
            evalTask.setExternalId(String.valueOf(taskInfo.getId()));
            evalTask.setTaskType(EvalTypeEnum.TASK.getCode());
            JSONObject taskDef = new JSONObject();
            taskDef.put("taskId", taskInfo.getId());
            taskDef.put("postNumber", postNumber);
            evalTask.setTaskDef(taskDef.toString());
            evalTaskService.save(evalTask);

            try {
                if (StringUtils.isEmpty(evalApp.getCode()) || StringUtils.isEmpty(evalApp.getSystemMessage())) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason("systemMessage or code is not found");
                    continue;
                }
                String contentValue = "";
                try {
                    // 替换参数变量
                    String systemPrompt = completionTaskPersonPrompt(evalApp.getSystemMessage(), postNumber, String.valueOf(taskInfo.getId()));
                    // 大模型分析数据
                    var result = customLlmService.chatCompletion(evalApp.getLlmId(), systemPrompt, evalApp.getTemperature());
                    // 解析分析结果
                    contentValue = getChatCompletionContent(result, evalApp.getCode());
                } catch (Exception e) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason(e.getMessage());
                    throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), e.getMessage());
                }

                if (StringUtils.isEmpty(contentValue)) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason("chat completion is error");
                    continue;
                }

                // 保存分析结果
                try {
                    insertPositionEvalAppReport(postNumber, String.valueOf(taskInfo.getId()), evalApp.getCode(), contentValue);
                } catch (Exception e) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason(e.getMessage());
                    throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), e.getMessage());
                }
                evalTask.setStatus(TaskStatusEnum.COMPLETED.getCode());
            } catch (Exception e) {
                e.printStackTrace();
                evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                evalTask.setFailedReason(e.getMessage());
            } finally {
                evalTaskService.updateById(evalTask);
            }
        }
    }

    private void processUserAssign(Map<String, Object> dataMap) {
        String taskId = dataMap.get("taskId").toString();
        String userId = dataMap.get("userId").toString();

        TaskInfo taskInfo = taskInfoService.getByUuid(taskId, false);
        if (Objects.isNull(taskInfo)) {
            return;
        }
        Robot robot = robotService.getById(taskInfo.getId());
        String evalAppIds = robot.getEvalAppIds();
        if (StringUtils.isEmpty(evalAppIds)) {
            return;
        }
        Map<String, Object> roomMap = conversationMapper.findRoomIdByMax(userId, taskInfo.getId());
        String roomId = Convert.toStr(roomMap.get("roomId"));

        Employee employee = employeeService.getOne(employeeService.query().eq(Employee::getJobNumber, userId));

        List<String> evalAppIdList = Arrays.asList(evalAppIds.split(","));
        for (String evalAppId : evalAppIdList) {

            EvalApp evalApp = evalAppService.getById(Convert.toLong(evalAppId), false);
            if (Objects.isNull(evalApp)) {
                continue;
            }

            EvalTask evalTask = new EvalTask();
            evalTask.setStatus(TaskStatusEnum.PROCESSING.getCode());
            evalTask.setEvalAppId(Convert.toLong(evalAppId));
            evalTask.setTaskType(EvalTypeEnum.USER.getCode());
            evalTask.setExternalId(String.valueOf(taskInfo.getId()));
            JSONObject taskDef = new JSONObject();
            taskDef.put("taskId", taskId);
            taskDef.put("robotId", robot.getId());
            taskDef.put("jobNumber", userId);
            taskDef.put("roomId", roomId);
            evalTask.setTaskDef(taskDef.toString());
            evalTaskService.save(evalTask);

            try {
                if (StringUtils.isEmpty(evalApp.getSystemMessage()) || StringUtils.isEmpty(evalApp.getCode())) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason("system message or code is not found");
                    continue;
                }

                String contentValue = "";
                try {
                    // 替换参数变量
                    String systemPrompt = completionUserPersonPrompt(evalApp.getSystemMessage(), employee, userId, taskInfo.getId(), roomId);
                    // 大模型分析数据
                    var result = customLlmService.chatCompletion(evalApp.getLlmId(), systemPrompt, evalApp.getTemperature());
                    // 解析分析结果
                    contentValue = getChatCompletionContent(result, evalApp.getCode());
                } catch (Exception e) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason(e.getMessage());
                    throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), e.getMessage());
                }

                if (StringUtils.isEmpty(contentValue)) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason("chat completion is error");
                    continue;
                }
                // 保存分析结果
                try {
                    String positionId = Objects.isNull(employee) ? null : employee.getPostNumber();
                    insertStaffEvalAppReport(userId, positionId, String.valueOf(taskInfo.getId()), evalApp.getCode(), contentValue);

                    if ("staff_work_responsibilities".equals(evalApp.getCode())) {
                        // 个人工作事项
                        insertStaffWorkResponsibilities(positionId, userId, taskInfo.getId());
                    } else if ("low_value_tasks".equals(evalApp.getCode())) {
                        // 员工低价值工作详情
                        insertLowValueTasks(positionId, userId, taskInfo.getId());
                    } else if ("staff_collaboration_info".equals(evalApp.getCode())) {
                        // 员工协作关系详情
                        insertStaffCollaborationInfo(positionId, userId, taskInfo.getId());
                    } else if ("staff_work_status".equals(evalApp.getCode())) {
                        // 员工工作状态详情
                        insertStaffWorkStatus(positionId, userId, taskInfo.getId());
                    } else if ("staff_keyword_cloud".equals(evalApp.getCode())) {
                        // 员工访谈词云详情
                        insertStaffKeywordCloud(positionId, userId, taskInfo.getId());
                    }
                } catch (Exception e) {
                    evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                    evalTask.setFailedReason(e.getMessage());
                    throw new AppException(CustomHttpStatus.BUSINESS_ERROR.getValue(), e.getMessage());
                }
                evalTask.setStatus(TaskStatusEnum.COMPLETED.getCode());
            } catch (Exception e) {
                e.printStackTrace();
                evalTask.setStatus(TaskStatusEnum.FAILED.getCode());
                evalTask.setFailedReason(e.getMessage());
            } finally {
                evalTaskService.updateById(evalTask);
            }
        }
    }

    // 替换提示词参数
    private String completionUserPersonPrompt(String systemPrompt, Employee employee, String userId, Long taskInfoId, String roomId) {

        try {
            // 访谈原文
            if (matchReplaceEnable(systemPrompt, "{{访谈原文}}") || matchReplaceEnable(systemPrompt, "{{访谈内容}}")) {
                String talkText = "无";
                if (StringUtils.isNotEmpty(roomId)) {
                    talkText = conversationService.getText(conversationService.list(conversationService.query()
                            .eq(Conversation::getTaskId, taskInfoId)
                            .eq(Conversation::getUserId, userId)
                            .eq(Conversation::getRoomId, roomId)));
                }
                systemPrompt = systemPrompt.replace("{{访谈原文}}", talkText).replace("{{访谈内容}}", talkText);
            }

            // 会议日程
            if (matchReplaceEnable(systemPrompt, "{{会议日程}}")) {
                String calendarText = "无";
                if (employee != null && !Objects.isNull(employee.getUnionId())) {
                    calendarText = calendarEventService.getText(employee.getUnionId());
                }
                systemPrompt = systemPrompt.replace("{{会议日程}}", calendarText);
            }

            // 周报内容
            if (matchReplaceEnable(systemPrompt, "{{周报内容}}")) {
                String reportLogText = reportLogService.getText(userId);
                systemPrompt = systemPrompt.replace("{{周报内容}}", reportLogText);
            }

            // {{岗位原始JD}}
            if (matchReplaceEnable(systemPrompt, "{{岗位原始JD}}")) {
                String jdText = "无";
                ContactExtra extra = contactExtraService.getByJobNumber(userId);
                if (!Objects.isNull(extra) && StringUtils.isNotEmpty(extra.getJd())) {
                    jdText = extra.getJd();
                }
                systemPrompt = systemPrompt.replace("{{岗位原始JD}}", jdText);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("=== replace prompt params error: {}", e.getMessage());
        }
        return systemPrompt;
    }

    private String completionTaskPersonPrompt(String systemPrompt, String postNumber, String taskId) {

        // 岗位ID
        if (matchReplaceEnable(systemPrompt, "{{postNumber}}")) {
            systemPrompt = systemPrompt.replace("{{postNumber}}", postNumber);
        }

        // 所有岗位ID下JD
        if (matchReplaceEnable(systemPrompt, "{{postNumberJd}}")) {
            String jdText = contactExtraService.getJdText(postNumber);
            systemPrompt = systemPrompt.replace("{{postNumberJd}}", jdText);
        }

        // {{岗位ID下所有的个人岗内/岗外职责智能体输出，提取：work_name，description}}
        if (matchReplaceEnable(systemPrompt, "{{workNameAndDescription}}")) {
            String workDescText = batchGetJdData(postNumber, taskId);
            systemPrompt = systemPrompt.replace("{{workNameAndDescription}}", workDescText);
        }
        return systemPrompt;
    }

    private boolean matchReplaceEnable(String content, String str) {
        return content.indexOf(str) > 0 ? true : false;
    }

    // 获取结果
    private String getChatCompletionContent(Map<String, Object> result, String code) {
        if (Objects.isNull(result)) {
            return null;
        }
        String contentValue = "";
        JSONArray respArray = (JSONArray) result.get("choices");
        if (!Objects.isNull(respArray) && respArray.size() > 0) {
            JSONObject message = respArray.getJSONObject(0).getJSONObject("message");
            if (!Objects.isNull(message.get("content"))) {
                contentValue = message.get("content").toString();
            }
        }
        List<String> jsonCodes = CollUtil.newArrayList("staff_work_responsibilities", "low_value_tasks", "staff_collaboration_info", "staff_work_status", "staff_keyword_cloud", "no_mention_of_work_items");

        if (jsonCodes.contains(code)) {
            if (code.equals("staff_work_status")) {
                contentValue = contentValue.substring(contentValue.indexOf("{"), contentValue.lastIndexOf("}") + 1);
            } else {
                contentValue = contentValue.substring(contentValue.indexOf("["), contentValue.lastIndexOf("]") + 1);
            }
            if (JSONUtil.isTypeJSON(contentValue)) {
                return contentValue;
            }
        }
        return contentValue;
    }

    // jd集合
    private String batchGetJdData(String positionId, String interviewId) {
        StringBuilder sb = new StringBuilder();
        try {
            sb.append("SELECT CASE WHEN COUNT(p.field_value) = 0 THEN '无该岗位相关数据' ELSE COALESCE(");
            sb.append("NULLIF(GROUP_CONCAT(DISTINCT CONCAT(item.work_name, '：', item.description) ORDER BY item.work_name SEPARATOR '；'), ''), '无工作事项')");
            sb.append("END AS work_items_list ");
            sb.append("FROM");
            sb.append("(SELECT '").append(positionId).append("' AS position_id, '").append(interviewId).append("' AS interview_id) AS dummy ");
            sb.append("LEFT JOIN ");
            sb.append("staff_properties p ON p.position_id = dummy.position_id AND p.interview_id = dummy.interview_id AND p.field_name = 'staff_work_responsibilities' ");
            sb.append("LEFT JOIN JSON_TABLE(");
            sb.append("p.field_value, '$[*]' COLUMNS (work_name VARCHAR(100) PATH '$.work_name',description VARCHAR(500) PATH '$.description')");
            sb.append(") AS item ON p.field_name = 'staff_work_responsibilities'");
            List<Row> rowList = Db.selectListBySql(sb.toString());
            if (Objects.isNull(rowList) || rowList.size() == 0) {
                return "无该岗位相关数据";
            }
            return rowList.get(0).get("work_items_list").toString();
        } catch(Exception e) {
            e.printStackTrace();
            log.error("jd sql: {}", sb.toString());
        }
        return "无该岗位相关数据";
    }

    // 保存个人分析结果
    private void insertStaffEvalAppReport(String userId, String positionId, String interviewId, String code, String value) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO staff_properties (user_id, position_id, interview_id, field_name, field_value) VALUES (");
        sql.append("'").append(userId).append("', "); // 用户ID
        sql.append("'").append(positionId).append("', "); // 岗位ID
        sql.append("'").append(interviewId).append("', "); // 访谈ID
        sql.append("'").append(code).append("', ");
        sql.append("'").append(value).append("') "); // // 分析结果
        sql.append("ON DUPLICATE KEY UPDATE field_value = VALUES(field_value);");
        Db.insertBySql(sql.toString());
    }

    // 保存岗位分析结果
    private void insertPositionEvalAppReport(String postNumber, String taskId, String fieldName, String fieldValue) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO position_properties (position_id, interview_id, field_name, field_value) VALUES(");
        sql.append("'").append(postNumber).append("', ");
        sql.append("'").append(taskId).append("', ");
        sql.append("'").append(fieldName).append("', ");
        sql.append("'").append(fieldValue).append("') ");
        sql.append("ON DUPLICATE KEY UPDATE field_value = VALUES(field_value);");
        Db.insertBySql(sql.toString());
    }

    // 个人工作事项
    private void insertStaffWorkResponsibilities(String postNumber, String jobNumber, long interviewId) {
        String searchSql = """
                SELECT
                    t1.user_id AS '员工工号',
                    t1.position_id AS '岗位编号',
                    t1.interview_id AS '访谈编号',
                    resp.work_name AS '工作事项',
                    -- 处理各种格式的时间占比值
                    CASE
                        WHEN ROUND(
                                     CASE
                                         WHEN resp.percentage_str LIKE '%%\\%%' THEN REPLACE(resp.percentage_str, '%', '') / 100.0
                                         WHEN resp.percentage_str REGEXP '^[0-9]+$' THEN resp.percentage_str / 100.0
                                         ELSE resp.percentage_str
                                         END, 2) = 0 THEN 0.01
                        ELSE ROUND(
                                CASE
                                    WHEN resp.percentage_str LIKE '%%\\%%' THEN REPLACE(resp.percentage_str, '%', '') / 100.0
                                    WHEN resp.percentage_str REGEXP '^[0-9]+$' THEN resp.percentage_str / 100.0
                                    ELSE resp.percentage_str
                                    END, 2)
                        END AS '时间占比',
                    resp.description AS '工作事项描述',
                    ROUND(
                            CASE
                                WHEN resp.replacement_rate_str LIKE '%%\\%%' THEN REPLACE(resp.replacement_rate_str, '%', '') / 100.0
                                WHEN resp.replacement_rate_str REGEXP '^[0-9]+$' THEN resp.replacement_rate_str / 100.0
                                ELSE resp.replacement_rate_str
                                END, 2) AS '工作事项AI替代率',
                    resp.argument AS '替代理由',
                    CASE
                        WHEN resp.responsibility_type = '岗内职责' THEN '与工作职责匹配的工作内容'
                        WHEN resp.responsibility_type = '岗外职责' THEN '岗位职责外的工作内容'
                        WHEN resp.responsibility_type = '是' THEN '与工作职责匹配的工作内容'
                        WHEN resp.responsibility_type = '否' THEN '岗位职责外的工作内容'
                        ELSE '与工作职责匹配的工作内容'
                        END AS '职责种类'
                FROM
                    staff_properties t1
                        JOIN (
                        SELECT
                            user_id,
                            position_id,
                            interview_id,
                            jt.work_name,
                            jt.description,
                            jt.argument,
                            jt.responsibility_type,
                            jt.percentage_str,
                            jt.replacement_rate_str
                        FROM
                            staff_properties,
                            JSON_TABLE(
                                    field_value,
                                    '$[*]' COLUMNS (
                                        work_name VARCHAR(255) PATH '$.work',
                                        percentage_str VARCHAR(50) PATH '$.time', 
                                        description TEXT PATH '$.description',
                                        replacement_rate_str VARCHAR(50) PATH '$.rate', 
                                        responsibility_type VARCHAR(50) PATH '$.type',
                                        argument VARCHAR(500) PATH '$.argument'
                                        )
                                ) AS jt
                        WHERE
                                field_name = 'staff_work_responsibilities'
                          AND JSON_VALID(field_value) = 1
                    ) AS resp ON t1.user_id = resp.user_id
                        AND t1.position_id = resp.position_id
                        AND t1.interview_id = resp.interview_id
                WHERE t1.field_name = 'staff_work_responsibilities' AND t1.user_id=? AND t1.position_id=? AND t1.interview_id=?
                """;
        List<Row> rows = Db.selectListBySql(searchSql, jobNumber, postNumber, interviewId);

        String deleteSql = "DELETE FROM `prod_个人工作事项` WHERE `岗位编号`=? AND `员工工号`=? AND `访谈编号`=?";
        Db.deleteBySql(deleteSql, postNumber, jobNumber, interviewId);

        for (Row row : rows) {
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("INSERT INTO `prod_个人工作事项` (`岗位编号`, `工作事项`, `时间占比`, `工作事项描述`, `职责种类`, `访谈编号`, `工作事项AI替代率`, `替代理由`, `员工工号`) ");
            insertSql.append("VALUES (?,?,?,?,?,?,?,?,?) ");
            Db.insertBySql(insertSql.toString(),
                    postNumber,
                    Convert.toStr(row.get("工作事项")),
                    Convert.toStr(row.get("时间占比")),
                    Convert.toStr(row.get("工作事项描述")),
                    Convert.toStr(row.get("职责种类")),
                    interviewId,
                    Convert.toStr(row.get("工作事项AI替代率")),
                    Convert.toStr(row.get("替代理由")),
                    jobNumber
            );
        }
    }

    // 员工低价值工作详情
    private void insertLowValueTasks(String postNumber, String jobNumber, long interviewId) {
        String searchSql = """
                SELECT
                    user_id AS 员工工号,
                    position_id AS 岗位ID,
                    interview_id AS 访谈ID,
                    SUBSTRING_INDEX(SUBSTRING_INDEX(field_value, '"task_sort": "', -1), '"', 1) AS 低价值任务分类,
                    SUBSTRING_INDEX(SUBSTRING_INDEX(field_value, '"task_name": "', -1), '"', 1) AS 低价值工作标签,
                    SUBSTRING_INDEX(SUBSTRING_INDEX(field_value, '"employee_description": "', -1), '"', 1) AS 低价值任务员工描述
                FROM
                    staff_properties
                WHERE field_name = 'low_value_tasks' AND user_id=? AND position_id=? AND interview_id=?
                """;
        List<Row> rows = Db.selectListBySql(searchSql, jobNumber, postNumber, interviewId);

        String deleteSql = "DELETE FROM `prod_员工低价值工作详情` WHERE `岗位ID`=? AND `访谈ID`=? AND `员工工号`=?";
        Db.deleteBySql(deleteSql, postNumber, interviewId, jobNumber);

        for (Row row : rows) {
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("INSERT INTO `prod_员工低价值工作详情` (`岗位ID`, `访谈ID`, `低价值任务分类`, `低价值工作标签`, `低价值任务员工描述`, `员工工号`) VALUES (?,?,?,?,?,?)");
            Db.insertBySql(insertSql.toString(),
                    Convert.toStr(row.get("岗位ID")),
                    Convert.toStr(row.get("访谈ID")),
                    Convert.toStr(row.get("低价值任务分类")),
                    Convert.toStr(row.get("低价值工作标签")),
                    Convert.toStr(row.get("低价值任务员工描述")),
                    Convert.toStr(row.get("员工工号"))
            );
        }
    }

    // 员工协作关系详情
    private void insertStaffCollaborationInfo(String postNumber, String jobNumber, long interviewId) {
        String searchSql = """
                SELECT
                    sp.user_id AS '员工工号',
                    sp.position_id AS '岗位ID',
                    sp.interview_id AS '访谈ID',
                    j.department AS '协作部门',
                    j.frequency AS '协作频率',
                    CASE j.frequency
                        WHEN '高' THEN 3
                        WHEN '中' THEN 2
                        WHEN '低' THEN 1
                        WHEN 'high' THEN 3
                        WHEN 'medium' THEN 2
                        WHEN 'low' THEN 1
                        ELSE NULL
                    END AS '协作频率数值化'
                                
                FROM
                    staff_properties sp,
                    JSON_TABLE(
                        CASE
                            WHEN JSON_VALID(sp.field_value) = 1 THEN sp.field_value
                            ELSE '[]'
                        END,
                        '$[*]' COLUMNS (
                            department VARCHAR(100) PATH '$.department',
                            frequency VARCHAR(100) PATH '$.frequency'
                        )
                    ) AS j
                WHERE sp.field_name = 'staff_collaboration_info' AND sp.user_id = ? AND sp.position_id = ? AND sp.interview_id = ?
                """;
        List<Row> rows = Db.selectListBySql(searchSql, jobNumber, postNumber, interviewId);

        String deleteSql = "DELETE FROM `prod_员工协作关系详情` WHERE `岗位ID`=? AND `访谈ID`=? AND `员工工号`=?";
        Db.deleteBySql(deleteSql, postNumber, interviewId, jobNumber);

        for (Row row : rows) {
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("INSERT INTO `prod_员工协作关系详情` (`岗位ID`, `访谈ID`, `协作部门`, `协作频率`, `协作频率数值化`, `员工工号`) VALUES (?,?,?,?,?,?)");
            Db.insertBySql(insertSql.toString(),
                    Convert.toStr(row.get("岗位ID")),
                    Convert.toStr(row.get("访谈ID")),
                    Convert.toStr(row.get("协作部门")),
                    Convert.toStr(row.get("协作频率")),
                    Convert.toStr(row.get("协作频率数值化")),
                    Convert.toStr(row.get("员工工号"))
            );
        }
    }

    // 员工工作状态详情
    private void insertStaffWorkStatus(String postNumber, String jobNumber, long interviewId) {
        String searchSql = """
                SELECT
                    t1.user_id AS '员工工号',
                    t1.position_id AS '岗位编号',
                    t1.interview_id AS '访谈编号',
                    IFNULL(
                        CASE WHEN JSON_VALID(t1.field_value) = 1
                             THEN
                                CASE JSON_UNQUOTE(JSON_EXTRACT(t1.field_value, '$.pressure_level'))
                                    WHEN '压力大' THEN '😡压力大'
                                    WHEN '压力中' THEN '😨压力中'
                                    WHEN '压力小' THEN '😃压力小'
                                    ELSE JSON_UNQUOTE(JSON_EXTRACT(t1.field_value, '$.pressure_level'))
                                END
                             ELSE NULL
                        END, '数据缺失'
                    ) AS '工作压力',
                    IFNULL(
                        CASE WHEN JSON_VALID(t1.field_value) = 1
                             THEN
                                CASE JSON_UNQUOTE(JSON_EXTRACT(t1.field_value, '$.overtime'))
                                    WHEN '从不加班' THEN '😃从不加班'
                                    WHEN '偶尔加班' THEN '😨偶尔加班'
                                    WHEN '经常加班' THEN '😡经常加班'
                                    ELSE JSON_UNQUOTE(JSON_EXTRACT(t1.field_value, '$.overtime'))
                                END
                             ELSE NULL
                        END, '数据缺失'
                    ) AS '加班情况',
                    IFNULL(
                        CASE WHEN JSON_VALID(t1.field_value) = 1
                             THEN
                                CASE JSON_UNQUOTE(JSON_EXTRACT(t1.field_value, '$.status'))
                                    WHEN '状态良好' THEN '😁状态良好'
                                    WHEN '状态一般' THEN '😨状态一般'
                                    WHEN '状态不佳' THEN '😡状态不佳'
                                    ELSE JSON_UNQUOTE(JSON_EXTRACT(t1.field_value, '$.status'))
                                END
                             ELSE NULL
                        END, '数据缺失'
                    ) AS '员工状态',
                    CASE
                        WHEN t2.field_value IS NULL THEN NULL
                        WHEN t2.field_value REGEXP '^[0-9]+(\\\\.[0-9]+)?$'
                             THEN ROUND(CAST(t2.field_value AS DECIMAL(10,2)), 2)
                        ELSE NULL
                    END AS '员工饱和度'
                FROM
                    staff_properties t1
                LEFT JOIN
                    staff_properties t2 ON t1.user_id = t2.user_id
                    AND t1.position_id = t2.position_id
                    AND t1.interview_id = t2.interview_id
                    AND t2.field_name = 'staff_work_saturation'
                WHERE t1.field_name = 'staff_work_status' AND t1.user_id = ? AND t1.position_id = ? AND t1.interview_id = ?
                """;
        List<Row> rows = Db.selectListBySql(searchSql, jobNumber, postNumber, interviewId);

        String deleteSql = "DELETE FROM `prod_员工工作状态详情` WHERE `访谈编号`=? AND `岗位编号`=? AND `员工工号`=?";
        Db.deleteBySql(deleteSql, interviewId, postNumber, jobNumber);

        for (Row row : rows) {
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("INSERT INTO `prod_员工工作状态详情` (`员工状态`, `岗位编号`, `工作压力`, `加班情况`, `员工饱和度`, `访谈编号`, `员工工号`) VALUES (?,?,?,?,?,?,?)");
            Db.insertBySql(insertSql.toString(),
                    Convert.toStr(row.get("员工状态")),
                    Convert.toStr(row.get("岗位编号")),
                    Convert.toStr(row.get("工作压力")),
                    Convert.toStr(row.get("加班情况")),
                    Convert.toStr(row.get("员工饱和度")),
                    Convert.toStr(row.get("访谈编号")),
                    Convert.toStr(row.get("员工工号"))
            );
        }
    }

    // 员工访谈词云详情
    private void insertStaffKeywordCloud(String postNumber, String jobNumber, long interviewId) {
        String searchSql = """
                SELECT
                    pp.position_id AS '岗位ID',
                    pp.user_id AS '员工ID',
                    pp.interview_id AS '访谈ID',
                    CASE
                        WHEN JSON_VALID(pp.field_value) = 0 THEN '无效的JSON数据'
                        WHEN JSON_EXTRACT(kw.keyword_item, '$.keyword') IS NULL THEN '未指定关键词'
                        ELSE JSON_UNQUOTE(JSON_EXTRACT(kw.keyword_item, '$.keyword'))
                    END AS '关键词',
                    CASE
                        WHEN JSON_VALID(pp.field_value) = 0 THEN NULL
                        WHEN JSON_EXTRACT(kw.keyword_item, '$.frequency') IS NULL THEN NULL
                        ELSE CAST(JSON_EXTRACT(kw.keyword_item, '$.frequency') AS UNSIGNED)
                    END AS '频率'
                FROM
                    staff_properties pp
                LEFT JOIN JSON_TABLE(
                    CASE WHEN JSON_VALID(pp.field_value) = 1
                         THEN pp.field_value
                         ELSE '[]'
                    END,
                    '$[*]' COLUMNS (
                        keyword_item JSON PATH '$'
                    )
                ) AS kw ON JSON_VALID(pp.field_value) = 1
                WHERE pp.field_name = 'staff_keyword_cloud' AND pp.user_id = ? AND pp.position_id = ? AND pp.interview_id = ?
                    AND (
                        JSON_VALID(pp.field_value) = 1
                        OR (
                            pp.user_id IS NOT NULL
                            AND pp.interview_id IS NOT NULL
                        )
                    );
                """;
        List<Row> rows = Db.selectListBySql(searchSql, jobNumber, postNumber, interviewId);

        String deleteSql = "DELETE FROM `prod_员工访谈词云详情` WHERE `岗位ID`=? AND `访谈ID`=? AND `员工ID`=?";
        Db.deleteBySql(deleteSql, postNumber, interviewId, jobNumber);

        for (Row row : rows) {
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("INSERT INTO `prod_员工访谈词云详情` (`岗位ID`, `访谈ID`, `关键词`, `频率`, `员工ID`) VALUES (?,?,?,?,?)");
            Db.insertBySql(insertSql.toString(),
                    Convert.toStr(row.get("岗位ID")),
                    Convert.toStr(row.get("访谈ID")),
                    Convert.toStr(row.get("关键词")),
                    Convert.toStr(row.get("频率")),
                    Convert.toStr(row.get("员工ID"))
            );
        }
    }
}
package com.rolling.biz.service.consumer;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.model.entity.app.VoiceChatEvent;
import com.rolling.biz.model.request.volc.UpdateVoiceChatReq;
import com.rolling.biz.service.app.VoiceChatEventService;
import com.rolling.biz.service.volc.VolcRtcService;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
public class VoiceChatEventConsumerService {
    private final VolcRtcService volcRtcService;
    private final RabbitMQTemplate rabbitMQTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    private final VoiceChatEventService voiceChatEventService;

    @RabbitListener(
            concurrency = "1-2",
            bindings = @QueueBinding(
                    exchange = @Exchange(value = RabbitQueues.VOICE_CHAT_EVENT_EXCHANGE),
                    value = @Queue(name = RabbitQueues.VOICE_CHAT_EVENT_QUEUE),
                    key = RabbitQueues.VOICE_CHAT_EVENT_ROUTING_KEY
            )
    )
    private void receiveMessage(String message) {
        rabbitMQTemplate.preprocessMessage(message, eventData -> {
            try {
                VoiceChatEvent event = new VoiceChatEvent();
                event.setAppId(MapUtil.getStr(eventData, "AppId"));
                event.setRoomId(MapUtil.getStr(eventData, "RoomId"));
                event.setTaskId(MapUtil.getStr(eventData, "TaskId"));
                event.setUserId(MapUtil.getStr(eventData, "UserId"));
                event.setRoundId(MapUtil.getInt(eventData, "RoundID"));
                event.setCreateTime(Instant.ofEpochMilli(MapUtil.getLong(eventData, "EventTime")));
                event.setEventType(MapUtil.getInt(eventData, "EventType"));
                event.setRunStage(MapUtil.getStr(eventData, "RunStage"));

                Map<String, Object> errorInfoMap = MapUtil.get(eventData, "ErrorInfo", Map.class);
                VoiceChatEvent.ErrorInfo errorInfo = new VoiceChatEvent.ErrorInfo();
                errorInfo.setErrorCode(MapUtil.getInt(errorInfoMap, "ErrorCode"));
                errorInfo.setReason(MapUtil.getStr(errorInfoMap, "Reason", ""));
                event.setErrorInfo(errorInfo);
                if (event.getEventType() == 1 && "llm".equals(event.getRunStage())) {
                    if (errorInfo.getReason().contains("context canceled")) {
                        return;
                    }
                    executeCommand(event.getRoomId(), event.getTaskId());
                }
                voiceChatEventService.saveBatchIgnore(Arrays.asList(event));
            } catch (Exception ex) {
                log.error("===receive message error: {}", ex.getMessage(), ex);
            }
        });
    }

    private void executeCommand(String roomId, String taskId) {
        String key = StrUtil.format("rtc:etts:{}:{}", roomId, taskId);
        try {
            String value = stringRedisTemplate.opsForValue().get(key);
            if (StringUtils.isBlank(value)) {
                stringRedisTemplate.opsForValue().set(key, roomId + ":" + taskId, 30, TimeUnit.SECONDS);
                volcRtcService.updateVoiceChat(UpdateVoiceChatReq.builder()
                        .roomId(roomId)
                        .taskId(taskId)
                        .command("ExternalTextToSpeech")
                        .message("抱歉，刚刚您说的没听清，可以重新再说一遍吗？")
                        .interruptMode(3)
                        .build());
            }
        } catch (Exception ex) {
            log.error("===updateVoiceChat error: {}", ex.getMessage(), ex);
        }
    }
}

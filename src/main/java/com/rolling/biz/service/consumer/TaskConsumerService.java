package com.rolling.biz.service.consumer;

import com.rolling.biz.constants.app.*;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class TaskConsumerService {
    private final RabbitMQTemplate rabbitMQTemplate;

    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange(RabbitQueues.TASK_EXCHANGE),
            value = @Queue(RabbitQueues.TASK_DISPATCH_QUEUE),
            key = RabbitQueues.TASK_DISPATCH_KEY
    ))
    private void receiveMessage(String message) {
        log.info("===receive message: {}", message);
        rabbitMQTemplate.preprocessMessage(message, dataMap -> processTask(dataMap));
    }

    private void processTask(Map<String, Object> dataMap) {
    }
}

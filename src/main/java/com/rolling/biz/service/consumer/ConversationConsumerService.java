package com.rolling.biz.service.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.model.entity.app.Conversation;
import com.rolling.biz.model.entity.app.TaskInfo;
import com.rolling.biz.model.request.app.SendMessageReq;
import com.rolling.biz.service.app.ConversationService;
import com.rolling.biz.service.app.TaskInfoService;
import com.rolling.biz.service.app.ThrottleService;
import com.rolling.biz.service.app.WebsocketService;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import com.rolling.library.utils.JacksonHelper;
import com.rolling.library.utils.StringHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class ConversationConsumerService {
    private final TaskInfoService taskInfoService;
    private final ThrottleService throttleService;
    private final RabbitMQTemplate rabbitMQTemplate;
    private final WebsocketService websocketService;
    private final ConversationService conversationService;

    private final static String ROBOT_NAME = "robot";
    private final static String VOICE_CHAT = "voiceChat";

    @RabbitListener(
            concurrency = "2-5",
            bindings = @QueueBinding(
                    exchange = @Exchange(value = RabbitQueues.CONVERSATION_EXCHANGE, type = ExchangeTypes.TOPIC),
                    value = @Queue(name = RabbitQueues.CONVERSATION_QUEUE),
                    key = "*"
            )
    )
    private void receiveMessage(String message) {
        log.info("===receive message: {}", message);
        rabbitMQTemplate.preprocessMessage(message, dataMap -> {
            SendMessageReq req = BeanUtil.mapToBean(dataMap, SendMessageReq.class, CopyOptions.create().ignoreNullValue());
            String[] strs = req.getUserId().split("_");
            String userType = strs[0];
            String userId = strs[0];
            if (strs[0].startsWith(VOICE_CHAT)) {
                userType = ROBOT_NAME;
                userId = strs[1];
            }
            StringHelper.ifPresent(userId, value -> throttleService.renew(value));
            TaskInfo taskInfo = taskInfoService.getFromCache(req.getTaskId());
            if (Objects.nonNull(taskInfo)) {
                Conversation entity = new Conversation();
                entity.setContent(req.getContent());
                entity.setTaskId(taskInfo.getId());
                entity.setRoomId(req.getRoomId());
                entity.setUserType(userType);
                entity.setUserId(userId);
                entity.setCreateTime(Instant.ofEpochMilli(req.getPushTime()));
                conversationService.save(entity);
                websocketService.sendMessage(userId, JacksonHelper.toJson(entity));
            }
        });
    }
}

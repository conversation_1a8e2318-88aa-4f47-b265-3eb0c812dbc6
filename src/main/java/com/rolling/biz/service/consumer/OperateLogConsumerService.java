package com.rolling.biz.service.consumer;

import cn.hutool.core.bean.BeanUtil;
import com.rolling.biz.constants.app.RabbitQueues;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.library.rabbitmq.RabbitMQTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class OperateLogConsumerService {

    private final RabbitMQTemplate rabbitMQTemplate;
    private final OperateLogService operateLogService;

    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange(RabbitQueues.OPERATE_LOG_EXCHANGE),
            value = @Queue(RabbitQueues.OPERATE_LOG_QUEUE),
            key = RabbitQueues.OPERATE_LOG_KEY
    ))
    private void receiveMessage(String message) {
        log.info("===receive evaluate message: {}", message);
        rabbitMQTemplate.preprocessMessage(message, dataMap -> processOperateLog(dataMap));
    }

    private void processOperateLog(Map<String, Object> dataMap) {
        if (Objects.isNull(dataMap.get("operateLog"))) {
            return;
        }
        OperateLog operateLog = BeanUtil.toBean(dataMap.get("operateLog"), OperateLog.class);
        operateLogService.getMapper().insert(operateLog);
    }
}

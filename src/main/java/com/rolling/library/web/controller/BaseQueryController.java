package com.rolling.library.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.rolling.library.model.entity.BaseEntity;
import com.rolling.library.model.request.BasePageReq;
import com.rolling.library.model.response.PageResp;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.service.IBaseService;
import com.rolling.library.utils.SpringReflectionHelper;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


public abstract class BaseQueryController<S extends IBaseService<T>, T extends BaseEntity, PR extends BasePageReq> {
    private S service;

    protected S getService() {
        if (Objects.isNull(service)) {
            Class<S> clazz = (Class<S>) SpringReflectionHelper.resolveTypeArguments(getClass(), BaseQueryController.class)[0];
            service = SpringUtil.getBean(clazz);
        }
        return service;
    }

    protected void bindQueryWrapper(PR req, QueryWrapper queryWrapper) {

    }

    protected void processPageData(PR req, Page<T> page) {

    }

    protected void handleRecords(List<T> records, PR req) {

    }

    protected void handleRecord(T record) {

    }

    @GetMapping
    public ResultResp page(@Valid PR req) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StrUtil.isNotBlank(req.getSort())) {
            final String column = StringUtil.camelToUnderline(req.getSort());
            final Boolean asc = StrUtil.equalsIgnoreCase(req.getOrder(), SqlConsts.ASC.trim()) ? Boolean.TRUE : Boolean.FALSE;
            queryWrapper.orderBy(column, asc);
        }
        if (StrUtil.isNotBlank(req.getIds())) {
            List<Long> idList = Arrays.stream(req.getIds().split(StrUtil.COMMA)).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            queryWrapper.in(QueryMethods.column(BaseEntity::getId).getName(), idList);
        }
        bindQueryWrapper(req, queryWrapper);
        Page<T> page = getService().page(Page.of(req.getPage(), req.getPageSize()), queryWrapper);
        processPageData(req, page);
        handleRecords(page.getRecords(), req);
        return PageResp.success(page);
    }

    @GetMapping("/{id}")
    public ResultResp get(@PathVariable Long id) {
        T entity = getService().getById(id, Boolean.TRUE);
        handleRecord(entity);
        return ResultResp.success(entity);
    }
}


package com.rolling.library.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.convert.Convert;
import com.rolling.biz.constants.system.OperateEnum;
import com.rolling.biz.model.entity.app.OperateLog;
import com.rolling.biz.service.app.OperateLogService;
import com.rolling.library.context.UserContextHolder;
import com.rolling.library.model.entity.BaseEntity;
import com.rolling.library.model.request.BaseReq;
import com.rolling.library.model.request.BasePageReq;
import com.rolling.library.model.response.ResultResp;
import com.rolling.library.service.IBaseService;
import com.rolling.library.utils.BeanHelper;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;


public abstract class BaseCrudController<S extends IBaseService<T>, T extends BaseEntity, PR extends BasePageReq, CR extends BaseReq, UR extends BaseReq> extends BaseQueryController<S, T, PR> {
    public abstract OperateLogService getOperateLogService();

    protected void saveBefore(T entity, CR req) {
    }

    protected void updateBefore(T record, T entity, UR req) {
    }

    protected void deleteBefore(T entity) {
    }

    protected boolean enableOperateLog() {
        return true;
    }

    protected void bindOperateLog(OperateLog log, T entity) {
    }

    @PostMapping
    public ResultResp save(@RequestBody @Valid CR req) {
        T entity = BeanHelper.copyProperties(req, getService().getEntityClass());
        saveBefore(entity, req);
        boolean success = getService().save(entity);
        if (success) {
            getService().saveAfterWithoutTransaction(entity);
            saveOperateLog(OperateEnum.CREATE.getCode(), entity, null);
        }
        return ResultResp.success(success, entity);
    }

    @PutMapping("/{id}")
    public ResultResp update(@PathVariable Long id, @RequestBody @Valid UR req) {
        T record = getService().getById(id, Boolean.TRUE);
        T entity = BeanHelper.copyProperties(req, getService().getEntityClass());
        updateBefore(record, entity, req);
        boolean success = getService().updateById(record, entity);
        if (success) {
            getService().updateAfterWithoutTransaction(record, entity);
            saveOperateLog(OperateEnum.UPDATE.getCode(), entity, record);
        }
        return ResultResp.success(success, entity);
    }

    @DeleteMapping("/{id}")
    public ResultResp delete(@PathVariable Long id) {
        T entity = getService().getById(id, Boolean.TRUE);
        deleteBefore(entity);
        boolean success = getService().removeById(entity);
        if (success) {
            getService().deleteAfterWithoutTransaction(entity);
            saveOperateLog(OperateEnum.DELETE.getCode(), entity, null);
        }
        return ResultResp.success(success, id);
    }

    private void saveOperateLog(String operate, T entity, T record) {
        boolean enable = enableOperateLog();
        if (!enable) {
            return;
        }

        OperateLog operateLog = new OperateLog();
        operateLog.setOperate(operate);
        Optional.ofNullable(UserContextHolder.getContext()).ifPresent(context -> {
            operateLog.setCreatorId(context.getUserId());
            operateLog.setCreatorName(context.getUserName());
        });

        if (Objects.nonNull(entity)) {
            Map map = BeanUtil.toBean(entity, Map.class);
            operateLog.setModuleId(Convert.toLong(map.get("id")));
        }
        if (Objects.nonNull(record)) {
            BeanUtil.copyProperties(entity, record, CopyOptions.create().ignoreNullValue());
            bindOperateLog(operateLog, record);
        } else {
            bindOperateLog(operateLog, entity);
        }
        getOperateLogService().sendMessage(operateLog);
    }
}

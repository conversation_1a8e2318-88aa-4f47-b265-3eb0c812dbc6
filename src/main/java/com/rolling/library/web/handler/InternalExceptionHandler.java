package com.rolling.library.web.handler;

import com.rolling.library.exception.AppException;
import com.rolling.library.model.response.ResultResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ClassUtils;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.List;

@Slf4j
@RestControllerAdvice(annotations = {RestController.class})
public class InternalExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler({AppException.class})
    public ResponseEntity<ResultResp> handleBusinessException(AppException exception) {
        log.warn(String.format("%s caught: {}", ClassUtils.getQualifiedName(exception.getClass())), exception.getMessage());
        return ResponseEntity.status(HttpStatus.OK).body(ResultResp.fail(exception.getCode(), exception.getMessage()));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResultResp> handleException(Exception exception) {
        log.error("unexpected exception caught: {}", exception.getMessage(), exception);
        return ResponseEntity.status(HttpStatus.OK).body(ResultResp.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), exception.getMessage()));
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        List<FieldError> fieldErrors = ex.getFieldErrors();
        String message = fieldErrors.get(0).getField() + fieldErrors.get(0).getDefaultMessage();
        return ResponseEntity.status(HttpStatus.OK).body(ResultResp.fail(status.value(), message));
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        return ResponseEntity.status(HttpStatus.OK).body(ResultResp.fail(status.value(), ex.getMessage()));
    }
}

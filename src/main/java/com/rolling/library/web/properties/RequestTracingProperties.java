package com.rolling.library.web.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@ConfigurationProperties(prefix = "rolling.web.interceptor.request-tracing")
public class RequestTracingProperties {
    private Set<String> include = Collections.singleton("/**");
    private Set<String> exclude = new HashSet<>();
}

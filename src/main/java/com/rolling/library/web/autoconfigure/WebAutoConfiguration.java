package com.rolling.library.web.autoconfigure;

import com.rolling.library.utils.JacksonHelper;
import com.rolling.library.web.handler.InternalExceptionHandler;
import com.rolling.library.web.controller.HealthCheckController;
import com.rolling.library.web.interceptor.RequestLoggingHandlerInterceptor;
import com.rolling.library.web.interceptor.UserResolverHandlerInterceptor;
import com.rolling.library.web.interceptor.RequestTracingHandlerInterceptor;
import com.rolling.library.web.properties.RequestLoggingProperties;
import com.rolling.library.web.properties.RequestTracingProperties;
import com.rolling.library.web.properties.UserResolverProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;


@Configuration
@EnableConfigurationProperties({
        RequestTracingProperties.class,
        RequestLoggingProperties.class,
        UserResolverProperties.class,
})
public class WebAutoConfiguration {
    @Bean
    @ConditionalOnMissingBean
    InternalExceptionHandler internalExceptionHandler() {
        return new InternalExceptionHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    RequestTracingHandlerInterceptor requestTracingHandlerInterceptor(RequestTracingProperties properties) {
        return new RequestTracingHandlerInterceptor(properties);
    }

    @Bean
    @ConditionalOnMissingBean
    RequestLoggingHandlerInterceptor loggingHandlerInterceptor(RequestLoggingProperties properties) {
        return new RequestLoggingHandlerInterceptor(properties);
    }

    @Bean
    @ConditionalOnMissingBean
    UserResolverHandlerInterceptor userResolverHandlerInterceptor(UserResolverProperties properties) {
        return new UserResolverHandlerInterceptor(properties);
    }

    @Bean
    @ConditionalOnMissingBean
    MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
        return new MappingJackson2HttpMessageConverter(JacksonHelper.getMapper());
    }

    @Bean
    @ConditionalOnMissingBean
    HealthCheckController healthCheckController() {
        return new HealthCheckController();
    }
}

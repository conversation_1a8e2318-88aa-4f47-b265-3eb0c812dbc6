package com.rolling.library.web.autoconfigure;

import com.rolling.library.web.interceptor.CustomHandlerInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ClassUtils;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebMvcConfiguration implements WebMvcConfigurer {
    private final List<CustomHandlerInterceptor> interceptors;
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        for (CustomHandlerInterceptor interceptor : interceptors) {
            registry.addInterceptor(interceptor)
                    .excludePathPatterns(Optional.ofNullable(interceptor.getExcludePathPatterns()).map(ArrayList::new).orElse(new ArrayList<>()))
                    .addPathPatterns(Optional.ofNullable(interceptor.getIncludePathPatterns()).map(ArrayList::new).orElse(new ArrayList<>()))
                    .order(interceptor.getOrder());
            log.info("{} registered with order {}", ClassUtils.getQualifiedName(interceptor.getClass()), interceptor.getOrder());
        }
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/").setViewName("redirect:/ui/ai_virtual_seat.html");
    }
}

package com.rolling.library.web.interceptor;

import com.rolling.library.constant.HandlerInterceptorOrder;
import com.rolling.library.constant.LogConstants;
import com.rolling.library.context.UserContext;
import com.rolling.library.context.UserContextHolder;
import com.rolling.library.web.properties.UserResolverProperties;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

@Slf4j
public class UserResolverHandlerInterceptor implements CustomHandlerInterceptor {
    private final UserResolverProperties properties;
    public UserResolverHandlerInterceptor(UserResolverProperties properties) {
        this.properties = properties;
    }
    @Override
    public int getOrder() {
        return HandlerInterceptorOrder.ORDER_USER_RESOLVER;
    }

    @Override
    public Set<String> getIncludePathPatterns() {
        return properties.getInclude();
    }

    @Override
    public Set<String> getExcludePathPatterns() {
        return properties.getExclude();
    }

    private String decodeValue(String value) {
        String result = value;
        try {
            result = URLDecoder.decode(result, "UTF-8");
        } catch (UnsupportedEncodingException e) {
        }
        return result;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (UserContextHolder.getUserId() != null) {
            log.debug("UserId retrieved via ThreadLocal: {}", UserContextHolder.getUserId());
            MDC.put(LogConstants.USER_ID, String.valueOf(UserContextHolder.getUserId()));
        } else {
            String userId = Optional.ofNullable(request.getHeader(LogConstants.USER_ID)).map(id -> {
                log.debug("UserId retrieved via HTTP Header '{}': {}", LogConstants.USER_ID, id);
                return id;
            }).orElseGet(() -> Optional.ofNullable(request.getParameter(LogConstants.USER_ID)).map(id -> {
                log.debug("UserId retrieved via HTTP Parameter '{}': {}", LogConstants.USER_ID, id);
                return id;
            }).orElse(null));

            String userName = Optional.ofNullable(request.getHeader(LogConstants.USER_NAME)).map(name -> {
                String decodedName = decodeValue(name);
                log.debug("UserName retrieved via HTTP Header '{}': {}", LogConstants.USER_NAME, decodedName);
                return decodedName;
            }).orElseGet(() -> Optional.ofNullable(request.getParameter(LogConstants.USER_NAME)).map(name -> {
                String decodedName = decodeValue(name);
                log.debug("UserName retrieved via HTTP Parameter '{}': {}", LogConstants.USER_NAME, decodedName);
                return decodedName;
            }).orElse(null));

            Set<String> roles = new HashSet<>();
            Enumeration<String> rolesEnums = request.getHeaders(LogConstants.USER_ROLE);
            if (rolesEnums != null) {
                while (rolesEnums.hasMoreElements()) {
                    String role = rolesEnums.nextElement();
                    roles.add(role);
                }
            }

            if (StringUtils.hasText(userId)) {
                try {
                    UserContextHolder.setContext(UserContext.builder().userId(Long.parseLong(userId)).userName(userName).roles(roles).build());
                    MDC.put(LogConstants.USER_ID, userId);
                } catch (Exception ex) {
                    throw ex;
                }
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserContextHolder.clearContext();
        MDC.remove(LogConstants.USER_ID);
    }
}

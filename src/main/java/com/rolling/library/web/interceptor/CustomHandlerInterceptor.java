package com.rolling.library.web.interceptor;

import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Collections;
import java.util.Set;

public interface CustomHandlerInterceptor extends HandlerInterceptor, Ordered {

    default Set<String> getIncludePathPatterns() {
        return Collections.singleton("/**");
    }

    default Set<String> getExcludePathPatterns() {
        return null;
    }
}

package com.rolling.library.web.interceptor;

import com.rolling.library.constant.HandlerInterceptorOrder;
import com.rolling.library.constant.LogConstants;
import com.rolling.library.web.properties.RequestTracingProperties;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;

import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public class RequestTracingHandlerInterceptor implements CustomHandlerInterceptor {
    private final RequestTracingProperties properties;

    public RequestTracingHandlerInterceptor(RequestTracingProperties properties) {
        this.properties = properties;
    }

    @Override
    public int getOrder() {
        return HandlerInterceptorOrder.ORDER_TRACING;
    }

    @Override
    public Set<String> getIncludePathPatterns() {
        return properties.getInclude();
    }

    @Override
    public Set<String> getExcludePathPatterns() {
        return properties.getExclude();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = Optional.ofNullable(request.getHeader(LogConstants.TRACE_ID)).orElseGet(() -> request.getParameter(LogConstants.TRACE_ID));
        if (!StringUtils.hasText(traceId)) {
            traceId = UUID.randomUUID().toString().replaceAll("-", "");
        }
        MDC.put(LogConstants.TRACE_ID, traceId);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        clean();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        clean();
    }

    private void clean() {
        MDC.remove(LogConstants.TRACE_ID);
    }
}

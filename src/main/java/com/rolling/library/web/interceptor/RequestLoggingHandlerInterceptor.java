package com.rolling.library.web.interceptor;

import com.rolling.library.constant.HandlerInterceptorOrder;
import com.rolling.library.web.properties.RequestLoggingProperties;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Set;

public class RequestLoggingH<PERSON>lerInterceptor implements CustomHandlerInterceptor {
    private final Log log = LogFactory.getLog(getClass());
    private final RequestLoggingProperties properties;
    public RequestLoggingHandlerInterceptor(RequestLoggingProperties properties) {
        this.properties = properties;
    }
    private static final String DEFAULT_MESSAGE_PREFIX = "Request [";
    private static final String DEFAULT_MESSAGE_SUFFIX = "]";

    @Override
    public Set<String> getIncludePathPatterns() {
        return properties.getInclude();
    }

    @Override
    public Set<String> getExcludePathPatterns() {
        return properties.getExclude();
    }

    @Override
    public int getOrder() {
        return HandlerInterceptorOrder.ORDER_LOGGING;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        StringBuilder msg = new StringBuilder();
        msg.append(DEFAULT_MESSAGE_PREFIX);
        msg.append(request.getMethod()).append(' ');
        msg.append(request.getRequestURI());
        msg.append(DEFAULT_MESSAGE_SUFFIX);
        log.info(msg.toString());
        return true;
    }
}

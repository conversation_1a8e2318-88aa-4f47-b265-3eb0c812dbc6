package com.rolling.library.exception;

import com.rolling.library.enums.CustomHttpStatus;

public class AppExceptionAssert {
    public static void pass(boolean condition, String message) {
        pass(condition, CustomHttpStatus.BUSINESS_ERROR.getValue(), message);
    }

    public static void pass(boolean condition, int code, String message) {
        if (!condition) {
            throw new AppException(code, message);
        }
    }
}

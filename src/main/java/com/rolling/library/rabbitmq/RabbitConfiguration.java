package com.rolling.library.rabbitmq;

import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfiguration {
    @Value("${spring.application.name}")
    private String serviceName;

    @Bean
    @ConditionalOnMissingBean(RabbitMQTemplate.class)
    public RabbitMQTemplate rabbitMQTemplate(RabbitTemplate rabbitTemplate) {
        return new RabbitMQTemplate(serviceName, rabbitTemplate);
    }

    @Bean
    SimpleRabbitListenerContainerFactory batchRabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setBatchSize(50);
        factory.setBatchListener(true);
        factory.setReceiveTimeout(5000L);
        factory.setBatchReceiveTimeout(5000L);
        factory.setConsumerBatchEnabled(Boolean.TRUE);
        factory.setConnectionFactory(connectionFactory);
        return factory;
    }
}

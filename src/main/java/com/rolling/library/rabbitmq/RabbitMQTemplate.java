package com.rolling.library.rabbitmq;

import cn.hutool.core.util.IdUtil;
import com.rolling.library.constant.LogConstants;
import com.rolling.library.utils.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
public class RabbitMQTemplate {
    private String serviceName;
    private RabbitTemplate rabbitTemplate;

    public RabbitMQTemplate(String serviceName, RabbitTemplate rabbitTemplate) {
        this.serviceName = serviceName;
        this.rabbitTemplate = rabbitTemplate;
    }

    public void sendMessage(String exchange, Map<String, Object> data) {
        sendMessage(exchange, "x", data);
    }

    public void sendMessage(String exchange, String routingKey, Map<String, Object> data) {
        data.put(LogConstants.UPSTREAM, serviceName);
        data.put(LogConstants.TRACE_ID, Optional.ofNullable(MDC.get(LogConstants.TRACE_ID)).orElse(IdUtil.fastSimpleUUID()));
        rabbitTemplate.convertAndSend(exchange, routingKey, JacksonHelper.toJson(data));
    }

    public void preprocessMessage(String message, Consumer<Map<String, Object>> consumer) {
        try {
            Map data = JacksonHelper.toBean(message, Map.class);
            Optional.ofNullable(data.get(LogConstants.UPSTREAM)).ifPresent(value -> MDC.put(LogConstants.UPSTREAM, String.valueOf(value)));
            Optional.ofNullable(data.get(LogConstants.TRACE_ID)).ifPresent(value -> MDC.put(LogConstants.TRACE_ID, String.valueOf(value)));
            consumer.accept(data);
        } catch (Exception ex) {
            log.error("preprocess message failed: {}", ex.getMessage(), ex);
        } finally {
            MDC.remove(LogConstants.UPSTREAM);
            MDC.remove(LogConstants.TRACE_ID);
        }
    }

    public void preprocessMessages(List<String> messages, Consumer<List<Map>> consumer) {
        try {
            List<Map> dataList = messages.stream().map(message -> JacksonHelper.toBean(message, Map.class)).collect(Collectors.toList());
            Map data = dataList.get(dataList.size() - 1);
            Optional.ofNullable(data.get(LogConstants.TRACE_ID)).ifPresent(value -> MDC.put(LogConstants.TRACE_ID, String.valueOf(value)));
            Optional.ofNullable(data.get(LogConstants.UPSTREAM)).ifPresent(value -> MDC.put(LogConstants.UPSTREAM, String.valueOf(value)));
            consumer.accept(dataList);
        } catch (Exception ex) {
            log.error("===preprocess messages failed: {}", ex.getMessage(), ex);
        } finally {
            MDC.remove(LogConstants.UPSTREAM);
            MDC.remove(LogConstants.TRACE_ID);
        }
    }
}

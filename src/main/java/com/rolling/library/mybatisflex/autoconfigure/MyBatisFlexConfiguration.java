package com.rolling.library.mybatisflex.autoconfigure;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.tenant.TenantFactory;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import com.rolling.library.context.TenantContextHolder;
import com.rolling.library.model.entity.BaseEntity;
import com.rolling.library.mybatisflex.listener.InsertFillListener;
import com.rolling.library.mybatisflex.listener.UpdateFillListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyBatisFlexConfiguration implements MyBatisFlexCustomizer {
    @Override
    public void customize(FlexGlobalConfig globalConfig) {
        // 开启审计功能
        AuditManager.setAuditEnable(true);
        // 设置SQL审计收集器
        AuditManager.setMessageCollector(new ConsoleMessageCollector());
        // 数据填充监听器
        globalConfig.registerInsertListener(new InsertFillListener(), BaseEntity.class);
        globalConfig.registerUpdateListener(new UpdateFillListener(), BaseEntity.class);
    }

    @Bean
    @ConditionalOnMissingBean
    TenantFactory tenantFactory() {
        return () -> new Object[]{TenantContextHolder.getTenantId()};
    }
}

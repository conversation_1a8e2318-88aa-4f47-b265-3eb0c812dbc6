package com.rolling.library.mybatisflex.listener;

import com.mybatisflex.annotation.InsertListener;
import com.rolling.library.context.UserContextHolder;
import com.rolling.library.model.entity.BaseEntity;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

public class InsertFillListener implements InsertListener {
    @Override
    public void onInsert(Object object) {
        if (object instanceof BaseEntity) {
            BaseEntity entity = (BaseEntity) object;
            if (Objects.isNull(entity.getCreateTime())) {
                entity.setCreateTime(Instant.now());
            }
            if (Objects.isNull(entity.getUpdateTime())) {
                entity.setUpdateTime(Instant.now());
            }
            Optional.ofNullable(UserContextHolder.getContext()).ifPresent(context -> {
                entity.setCreatorId(context.getUserId());
                entity.setCreatorName(context.getUserName());
                entity.setUpdaterId(context.getUserId());
                entity.setUpdaterName(context.getUserName());
            });
        }
    }
}

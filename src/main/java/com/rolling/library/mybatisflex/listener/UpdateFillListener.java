package com.rolling.library.mybatisflex.listener;

import com.mybatisflex.annotation.UpdateListener;
import com.rolling.library.context.UserContextHolder;
import com.rolling.library.model.entity.BaseEntity;

import java.time.Instant;
import java.util.Optional;

public class UpdateFillListener implements UpdateListener {
    @Override
    public void onUpdate(Object object) {
        if (object instanceof BaseEntity) {
            BaseEntity entity = (BaseEntity) object;
            entity.setUpdateTime(Instant.now());
            Optional.ofNullable(UserContextHolder.getContext()).ifPresent(context -> {
                entity.setUpdaterId(context.getUserId());
                entity.setUpdaterName(context.getUserName());
            });
        }
    }
}

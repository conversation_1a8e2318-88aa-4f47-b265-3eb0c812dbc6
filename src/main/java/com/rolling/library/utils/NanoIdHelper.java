package com.rolling.library.utils;

import cn.hutool.core.lang.id.NanoId;

public class NanoIdHelper {
    private static final char[] ALPHABET = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();
    public static final int SIZE = 21;

    public static String randomNanoId() {
        return NanoId.randomNanoId(null, ALPHABET, SIZE);
    }

    public static String randomNanoId(int size) {
        return NanoId.randomNanoId(null, ALPHABET, size);
    }
}

package com.rolling.library.utils;

import com.mybatisflex.core.tenant.TenantManager;
import com.rolling.library.context.TenantContextHolder;

import java.util.function.Supplier;

public class TenantHelper {

    public static <T> T withTenant(Long tenantId, Supplier<T> supplier) {
        Long previousTenantId = TenantContextHolder.getTenantId();
        TenantContextHolder.setTenantId(tenantId);
        try {
            return supplier.get();
        } finally {
            TenantContextHolder.setTenantId(previousTenantId);
        }
    }

    public static <T> T withoutTenant(Supplier<T> supplier) {
        Long previousTenantId = TenantContextHolder.getTenantId();
        TenantContextHolder.clearContext();
        try {
            return TenantManager.withoutTenantCondition(supplier);
        } finally {
            TenantContextHolder.setTenantId(previousTenantId);
        }
    }
}

package com.rolling.library.utils;

import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.function.Consumer;

public class CollectionHelper {
    public static void ifPresent(Collection<?> collection, Consumer<Collection<?>> consumer) {
        if (!CollectionUtils.isEmpty(collection)) {
            consumer.accept(collection);
        }
    }

    public static void ifPresent(Map<?, ?> map, Consumer<Map<?, ?>> consumer) {
        if (!CollectionUtils.isEmpty(map)) {
            consumer.accept(map);
        }
    }
}

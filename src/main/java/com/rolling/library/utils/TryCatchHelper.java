package com.rolling.library.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

@Slf4j
public class TryCatchHelper {
    public static <R> R supplier(Supplier<R> supplier, Function<Exception, R> exceptionFunc) {
        try {
            return supplier.get();
        } catch (Exception ex) {
            return exceptionFunc.apply(ex);
        }
    }

    public static void consumer(Runnable action, Consumer<Exception> exceptionConsumer) {
        try {
            action.run();
        } catch (Exception ex) {
            exceptionConsumer.accept(ex);
        }
    }

    public static void logAndIgnoreException(Runnable action) {
        try {
            action.run();
        } catch (Exception ex) {
            log.warn("Error occur: "+ ex.getMessage(), ex);
        }
    }

    public static <T> T logAndIgnoreException(Callable<T> action) {
        return logAndIgnoreException(action, null);
    }

    public static <T> T logAndIgnoreException(Callable<T> action, T defaultReturn) {
        try {
            return action.call();
        } catch (Exception ex) {
            log.warn("Error occur: "+ ex.getMessage(), ex);
            return defaultReturn;
        }
    }
}

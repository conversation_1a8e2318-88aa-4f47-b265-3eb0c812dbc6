package com.rolling.library.service.impl;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.FlexConsts;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.dialect.DbType;
import com.mybatisflex.core.dialect.DialectFactory;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.table.TableInfo;
import com.mybatisflex.core.table.TableInfoFactory;
import com.mybatisflex.core.util.ArrayUtil;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.rolling.library.exception.AppExceptionAssert;
import com.rolling.library.model.entity.BaseEntity;
import com.rolling.library.service.IBaseService;
import com.rolling.library.utils.SpringReflectionHelper;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

public abstract class BaseServiceImpl<M extends BaseMapper<T>, T extends BaseEntity> extends ServiceImpl<M, T> implements IBaseService<T> {
    private Class<T> entityClass;

    @Override
    public M getMapper() {
        return (M) super.getMapper();
    }

    @Override
    public Class<T> getEntityClass() {
        if (this.entityClass == null) {
            this.entityClass = (Class<T>) SpringReflectionHelper.resolveTypeArguments(getMapper().getClass(), BaseMapper.class)[0];
        }
        return this.entityClass;
    }

    public T getById(Serializable id, boolean throwEx) {
        T entity = super.getById(id);
        if (throwEx) {
            AppExceptionAssert.pass(Objects.nonNull(entity), HttpStatus.NOT_FOUND.value(), HttpStatus.NOT_FOUND.getReasonPhrase());
        }
        return entity;
    }

    public T getOne(QueryWrapper queryWrapper, boolean throwEx) {
        T entity = super.getOne(queryWrapper);
        if (throwEx) {
            AppExceptionAssert.pass(Objects.nonNull(entity), HttpStatus.NOT_FOUND.value(), HttpStatus.NOT_FOUND.getReasonPhrase());
        }
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(T entity) {
        boolean success = super.save(entity);
        if (success) {
            saveAfterWithTransaction(entity);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(T entity) {
        boolean success = super.updateById(entity);
        if (success) {
            updateAfterWithTransaction(entity);
        }
        return success;
    }

    @Override
    public boolean updateById(T record, T entity) {
        entity.setVersion(record.getVersion());
        entity.setId(record.getId());
        return this.updateById(entity);
    }

    @Override
    public boolean saveOrUpdate(T record, T entity) {
        if (Objects.nonNull(record)) {
            entity.setId(record.getId());
            entity.setVersion(record.getVersion());
            return this.updateById(entity);
        } else {
            return this.save(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(T entity) {
        boolean success = super.removeById(entity);
        if (success) {
            deleteAfterWithTransaction(entity);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBatchIgnore(List<T> entities) {
        TableInfo tableInfo = TableInfoFactory.ofEntityClass(getEntityClass());
        for (Object entity : entities) {
            tableInfo.initVersionValueIfNecessary(entity);
            tableInfo.initTenantIdIfNecessary(entity);
            tableInfo.initLogicDeleteValueIfNecessary(entity);
            // 执行 onInsert 监听器
            tableInfo.invokeOnInsertListener(entity);
        }
        Object[] args = FlexConsts.EMPTY_ARRAY;
        for (Object entity : entities) {
            args = ArrayUtil.concat(args, tableInfo.buildInsertSqlArgs(entity, false));
        }
        String sql = DialectFactory.getDialect().forInsertEntityBatch(tableInfo, entities);
        DbType dbType = FlexGlobalConfig.getDefaultConfig().getDbType();
        if (dbType == DbType.POSTGRE_SQL) {
            sql += " ON CONFLICT DO NOTHING";
        } else {
            sql = sql.replace(SqlConsts.INSERT_INTO, SqlConsts.INSERT + " IGNORE" + SqlConsts.INTO);
        }
        return Db.insertBySql(sql, args);
    }
}

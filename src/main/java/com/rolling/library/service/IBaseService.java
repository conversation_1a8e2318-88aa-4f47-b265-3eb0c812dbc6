package com.rolling.library.service;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.service.IService;

import java.io.Serializable;
import java.util.List;

public interface IBaseService<T> extends IService<T> {
    Class<T> getEntityClass();

    T getById(Serializable id, boolean throwEx);

    T getOne(QueryWrapper queryWrapper, boolean throwEx);

    boolean updateById(T record, T entity);

    boolean saveOrUpdate(T record, T entity);

    int saveBatchIgnore(List<T> entities);

    default void saveAfterWithTransaction(T entity) {
    }

    default void saveAfterWithoutTransaction(T entity) {
    }

    default void updateAfterWithTransaction(T entity) {
    }

    default void updateAfterWithoutTransaction(T record, T entity) {
    }

    default void deleteAfterWithTransaction(T entity) {
    }

    default void deleteAfterWithoutTransaction(T entity) {
    }
}

package com.rolling.library.model.response;

import cn.hutool.core.map.MapUtil;
import com.mybatisflex.core.paginate.Page;

import java.util.Map;

public class PageResp {
    private final static String RECORDS_NAME = "records";
    private final static String TOTAL_NAME = "total";
    public static ResultResp success(Page page) {
        Map<String, Object> data = MapUtil.builder(MapUtil.<String, Object>newHashMap(2))
                .put(RECORDS_NAME, page.getRecords())
                .put(TOTAL_NAME, page.getTotalRow())
                .build();
        ResultResp response = new ResultResp();
        response.setSuccess(true);
        response.setData(data);
        return response;
    }
}

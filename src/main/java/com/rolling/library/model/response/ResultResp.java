package com.rolling.library.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.rolling.library.enums.CustomHttpStatus;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultResp<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private ErrorInfo error;
    private boolean success;
    private T data;

    public static <T> ResultResp success(T data) {
        return ResultResp.builder().success(true).data(data).build();
    }

    public static <T> ResultResp success(boolean success, T data) {
        return ResultResp.builder().success(success).data(data).build();
    }

    public static ResultResp fail() {
        CustomHttpStatus chs = CustomHttpStatus.BUSINESS_ERROR;
        return fail(chs.getValue(), chs.getReason());
    }

    public static ResultResp fail(String message) {
        return fail(CustomHttpStatus.BUSINESS_ERROR.getValue(), message);
    }

    public static ResultResp fail(int code, String message) {
        return ResultResp.builder().success(false).error(ErrorInfo.builder().code(code).message(message).build()).build();
    }
}

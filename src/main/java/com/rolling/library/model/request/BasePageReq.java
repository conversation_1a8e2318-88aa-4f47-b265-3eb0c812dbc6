package com.rolling.library.model.request;

import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BasePageReq extends BaseReq {
    /**
     * 当前页码
     */
    @Min(value = 1)
    private Integer page = 1;
    /**
     * 每页条数
     */
    private Integer pageSize = 20;
    /**
     * 关键字搜索
     */
    private String keyword;
    /**
     * 排序字段
     */
    private String sort;
    /**
     * 排序方式
     */
    private String order;
    /**
     * 主键id，多个id用英文逗号隔开
     */
    private String ids;
}

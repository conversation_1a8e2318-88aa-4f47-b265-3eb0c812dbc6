package com.rolling.library.context;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.Optional;

public class UserContextHolder {
    private static final ThreadLocal<UserContext> contextHolder = new TransmittableThreadLocal<>();

    public static void setContext(UserContext context) {
        contextHolder.set(context);
    }

    public static UserContext getContext() {
        return contextHolder.get();
    }

    public static void clearContext() {
        contextHolder.remove();
    }

    public static void setUserId(Long userId) {
        Optional<UserContext> userContext = Optional.ofNullable(contextHolder.get());
        if (userContext.isPresent()) {
            userContext.get().setUserId(userId);
        } else {
            contextHolder.set(UserContext.builder().userId(userId).build());
        }
    }

    public static Long getUserId() {
        return Optional.ofNullable(contextHolder.get()).map(UserContext::getUserId).orElse(null);
    }
}

package com.rolling.library.context;

import com.alibaba.ttl.TransmittableThreadLocal;

public class TenantContextHolder {

    private static final ThreadLocal<Long> contextHolder = new TransmittableThreadLocal<>();

    public static void setTenantId(Long tenantId) {
        contextHolder.set(tenantId);
    }

    public static Long getTenantId() {
        return contextHolder.get();
    }

    public static void clearContext() {
        contextHolder.remove();
    }
}

package com.rolling.library.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class TenantContext {
    private Long tenantId;
    private boolean tenantIgnore;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("TenantId=").append(this.tenantId);
        sb.append("TenantIgnore=").append(this.tenantIgnore);
        sb.append("]");
        return sb.toString();
    }
}

package com.rolling.library.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
public class UserContext {
    private Long userId;
    private String userName;
    private Set<String> roles;

    public void addRole(String role) {
        this.roles.add(role);
    }

    public void addRoles(Collection<String> roles) {
        this.roles.addAll(roles);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("UserId=").append(this.userId).append(", ");
        sb.append("UserName=").append(this.userName).append(", ");
        sb.append("Roles=").append(this.roles);
        sb.append("]");
        return sb.toString();
    }
}

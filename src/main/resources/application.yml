server:
  port: 8080
  domain: ${SERVER_DOMAIN:http://localhost:8080}
  servlet:
    context-path: ${CONTEXT_PREFIX:}
spring:
  application:
    name: app
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  jackson:
    date-format: yyyy-MM-dd'T'HH:mm:ss'Z'
    time-zone: UTC
  datasource:
    url: ${dataSource_url:************************************************************************************************************************************************************}
    username: ${dataSource_username:root}
    password: ${dataSource_password:root}
    hikari:
      maximum-pool-size: ${dataSource_pool_size:20}
  liquibase:
    enabled: true
    change-log: classpath:/dbmigraion/changelog.xml
  data:
    redis:
      host: ${redis_host:127.0.0.1}
      port: ${redis_port:6379}
      username: ${redis_username:}
      password: ${redis_password:}
  rabbitmq:
    host: ${rabbitmq_host:127.0.0.1}
    port: ${rabbitmq_port:5672}
    username: ${rabbitmq_username:guest}
    password: ${rabbitmq_password:guest}

mybatis-flex:
  global-config:
    print-banner: false
  mapper-locations:
    - classpath*:/mapper/*Mapper.xml

sa-token:
  timeout: -1
  active-timeout: 7200
  token-style: simple-uuid
  is-concurrent: true
  is-share: true
  is-log: true

forest:
  max-connections: 100
  connect-timeout: 120000
  read-timeout: 120000
  max-retry-count: 2
  max-retry-interval: 200
  log-enabled: true
  log-request: true

rolling:
  web:
    interceptor:
      request-logging:
        include:
          - /api/**
      request-tracing:
        include:
          - /api/**
      user-resolver:
        include:
          - /api/**
        exclude:
          - /api/user/login
          - /api/user/logout

volcengine:
  access-key: ${volcengine_access_key:AKLTNTJmZjhiY2UyNjEzNGRiN2FkMjRhODc5MDk2NDJmNzk}
  secret-key: ${volcengine_secret_key:TVdWaVpUSTRaV05rTXpBNU5EUmlZamswWlRoaFpUTXpPRGsyTVdGbU1UWQ==}
  rtc-region: ${volcengine_rtc_region:cn-north-1}
  rtc-app-id: ${volcengine_rtc_app_id:6846af71f0636601754b7093}
  rtc-app-key: ${volcengine_rtc_app_key:38dcd943d0284749ad7f1af3b882092f}
  speech-app-id: ${volcengine_speech_app_id:2305911791}
  asr_bigmodel_token: ${volcengine_asr_bigmodel_token:Zwxrt0plAKZJd-3YTOFMTiKkQv3WWlKK}

tos:
  enable: ${tos_enable:true}
  endpoint: ${tos_endpoint:tos-cn-beijing.volces.com}
  access-key: ${tos_access_key:AKLTNjBjMmNhODFlYjM3NDBmZGE3OTYyMmYxZTcyOTdhZjc}
  access-secret: ${tos_access_secret:Wm1SbU56QXdZalE0WXpZM05ETXhNbUZpT0dRd05qVTBPRE01T1dabFlXVQ==}
  bucket: ${tos_bucket:aifangtantest}
  region: ${tos_region:cn-beijing}
  prefix: ${tos_prefix:rtc}
  cdn: ${tos_cdn:https://aifangtantest.tos-cn-beijing.volces.com}

aliyun:
  access_key_id: ${aliyun_access_key_id:LTAI5tNFYjWFU5V2tGgQ6zCY}
  access_key_secret: ${aliyun_access_key_secret:******************************}
  # 实时音视频应用ID和AppKey。您可以前往 https://live.console.aliyun.com/?#/liveRtc/list 进行查看。该配置仅需在基于 ARTC SDK集成方案下进行配置
  artc:
    app_id: ${aliyun_artc_app_id:4f9f76c3-80c2-4700-b006-a0b6cbefb6e0}
    app_key: ${aliyun_artc_app_key:8008951572354daa69079d11e453436f}
    # gslb配置勿动，生成rtc token必需
    gslb: https://gw.rtn.aliyuncs.com
  aiagent:
    region: ${aliyun_aiagent_region:cn-shanghai}

oss:
  enable: ${oss_enable:false}
  endpoint: ${oss_endpoint:oss-cn-shanghai.aliyuncs.com}
  access-key: ${oss_access_key:LTAI4GDe6QJd5q9ToaByi7AD}
  access-secret: ${oss_access_secret:******************************}
  bucket: ${oss_bucket:edesoft}
  prefix: ${oss_prefix:cindi/upload}
  cdn: ${oss_cdn:https://cdn.edesoft.com}

dingtalk:
  corp_id: ${dingtalk_corp_id:dingc60f3ee02b83212535c2f4657eb6378f}
  client_id: ${dingtalk_client_id:dingyy1ih2l7185thbx7}
  client_secret: ${dingtalk_client_secret:YFQRwpjnloNpPPAL3Tl-b5pI6d9PpU2ZZO68Vepz6o3qBS4w3gbKamHbi4yLwA8Z}
  agent_id: ${dingtalk_agent_id:3910067249}

throttle:
  limit: ${throttle_limit:15}

report:
  postUrl: http://106.15.48.191:8100/#/de-link/fBDKVSkj
  userUrl: http://106.15.48.191:8100/#/de-link/sK35Tf1Y
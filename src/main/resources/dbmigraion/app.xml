<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">

    <changeSet id="create table operate_log" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="operate_log"/>
            </not>
        </preConditions>
        <createTable tableName="operate_log">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="operate" type="varchar(32)"/>
            <column name="module" type="varchar(32)"/>
            <column name="module_id" type="bigint"/>
            <column name="module_code" type="varchar(64)"/>
            <column name="module_name" type="varchar(64)"/>
            <column name="content" type="varchar(1024)"/>
        </createTable>
    </changeSet>

    <changeSet id="create table image_info" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="image_info"/>
            </not>
        </preConditions>
        <createTable tableName="image_info">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="uuid" type="varchar(32)"/>
            <column name="original_file_name" type="varchar(255)"/>
            <column name="file_name" type="varchar(255)"/>
            <column name="file_size" type="bigint"/>
            <column name="content_type" type="varchar(20)"/>
            <column name="content" type="longblob"/>
        </createTable>
    </changeSet>

    <changeSet id="alter table image_info add column storage_key" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="image_info" columnName="storage_key"/>
            </not>
        </preConditions>
        <addColumn tableName="image_info">
            <column name="storage_key" type="varchar(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="create table file_info" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="file_info"/>
            </not>
        </preConditions>
        <createTable tableName="file_info">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="uuid" type="varchar(32)"/>
            <column name="original_file_name" type="varchar(255)"/>
            <column name="file_name" type="varchar(255)"/>
            <column name="file_size" type="bigint"/>
            <column name="content" type="longblob"/>
            <column name="content_type" type="varchar(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="alter table file_info add column storage_key" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="file_info" columnName="storage_key"/>
            </not>
        </preConditions>
        <addColumn tableName="file_info">
            <column name="storage_key" type="varchar(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="create table robot" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="robot"/>
            </not>
        </preConditions>
        <createTable tableName="robot">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="name" type="varchar(32)"/>
            <column name="desc" type="varchar(1024)"/>
            <column name="avatar" type="varchar(255)"/>
            <column name="disabled" type="bit(1)" defaultValueNumeric="0"/>
            <column name="llm_config" type="longtext"/>
            <column name="tts_config" type="longtext"/>
            <column name="asr_config" type="text"/>
        </createTable>
    </changeSet>

    <changeSet id="alter table robot add column outboundbot_config" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="robot" columnName="outboundbot_config"/>
            </not>
        </preConditions>
        <addColumn tableName="robot">
            <column name="outboundbot_config" type="text"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table robot add column llm_id" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="robot" columnName="llm_id"/>
            </not>
        </preConditions>
        <addColumn tableName="robot">
            <column name="llm_id" type="bigint"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table robot add column platform,external_agent_id" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="robot" columnName="platform"/>
                <columnExists tableName="robot" columnName="external_agent_id"/>
            </not>
        </preConditions>
        <addColumn tableName="robot">
            <column name="platform" type="varchar(20)"/>
            <column name="external_agent_id" type="varchar(60)"/>
        </addColumn>
    </changeSet>

    <changeSet id="create table task_info" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="task_info"/>
            </not>
        </preConditions>
        <createTable tableName="task_info">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="name" type="varchar(64)"/>
            <column name="type" type="varchar(32)"/>
            <column name="desc" type="varchar(1024)"/>
            <column name="status" type="varchar(32)"/>
            <column name="failed_reason" type="varchar(1024)"/>
            <column name="robot_id" type="bigint"/>
            <column name="caller_number" type="varchar(255)"/>
        </createTable>
    </changeSet>

    <changeSet id="alter table task_info add column begin_time,end_time" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="task_info" columnName="begin_time"/>
                <columnExists tableName="task_info" columnName="end_time"/>
            </not>
        </preConditions>
        <addColumn tableName="task_info">
            <column name="begin_time" type="datetime"/>
            <column name="end_time" type="datetime"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table task_info add column uuid,bg_url" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="task_info" columnName="uuid"/>
                <columnExists tableName="task_info" columnName="bg_url"/>
            </not>
        </preConditions>
        <addColumn tableName="task_info">
            <column name="uuid" type="varchar(32)"/>
            <column name="bg_url" type="varchar(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table task_info add column inbound_config" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="task_info" columnName="inbound_config"/>
            </not>
        </preConditions>
        <addColumn tableName="task_info">
            <column name="inbound_config" type="text"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table task_info add column platform,external_id" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="task_info" columnName="platform"/>
                <columnExists tableName="task_info" columnName="external_id"/>
            </not>
        </preConditions>
        <addColumn tableName="task_info">
            <column name="platform" type="varchar(20)"/>
            <column name="external_id" type="varchar(100)"/>
        </addColumn>
    </changeSet>

    <changeSet id="create table task_contact" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="task_contact"/>
            </not>
        </preConditions>
        <createTable tableName="task_contact">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="task_id" type="bigint"/>
            <column name="name" type="varchar(32)"/>
            <column name="phone" type="varchar(32)"/>
            <column name="status" type="varchar(20)"/>
            <column name="robot_id" type="bigint"/>
            <column name="external_id" type="varchar(32)"/>
        </createTable>
        <createIndex tableName="task_contact" indexName="ux_task_contact_query" unique="true">
            <column name="task_id"/>
            <column name="external_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="create table conversation" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="conversation"/>
            </not>
        </preConditions>
        <createTable tableName="conversation">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="content" type="text"/>
            <column name="user_id" type="varchar(32)"/>
            <column name="task_id" type="bigint"/>
            <column name="contact_id" type="bigint"/>
        </createTable>
    </changeSet>

    <changeSet id="alter table conversation add column room_id" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="conversation" columnName="room_id"/>
            </not>
        </preConditions>
        <addColumn tableName="conversation">
            <column name="room_id" type="varchar(64)"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table conversation add column user_type" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="conversation" columnName="user_type"/>
            </not>
        </preConditions>
        <addColumn tableName="conversation">
            <column name="user_type" type="varchar(32)"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table conversation add index idx_conversation_query" author="tao.huang">
        <preConditions>
            <not>
                <indexExists tableName="conversation" indexName="idx_conversation_query"/>
            </not>
        </preConditions>
        <createIndex tableName="conversation" indexName="idx_conversation_query">
            <column name="task_id"/>
            <column name="user_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="create table caller_info" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="caller_info"/>
            </not>
        </preConditions>
        <createTable tableName="caller_info">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="caller_number" type="varchar(32)"/>
        </createTable>
    </changeSet>

    <changeSet id="create table llm_info" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="llm_info"/>
            </not>
        </preConditions>
        <createTable tableName="llm_info">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="name" type="varchar(100)"/>
            <column name="icon" type="varchar(1024)"/>
            <column name="provider" type="varchar(20)"/>
            <column name="provider_params" type="text"/>
            <column name="end_point_id" type="varchar(32)"/>
            <column name="remark" type="varchar(1024)"/>
        </createTable>
    </changeSet>

    <changeSet id="create table contact_info" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="contact_info"/>
            </not>
        </preConditions>
        <createTable tableName="contact_info">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="name" type="varchar(20)"/>
            <column name="phone" type="varchar(20)"/>
            <column name="title" type="varchar(32)"/>
            <column name="user_id" type="varchar(32)"/>
            <column name="union_id" type="varchar(32)"/>
            <column name="job_number" type="varchar(32)"/>
            <column name="dept_id" type="varchar(20)"/>
            <column name="dept_name" type="varchar(32)"/>
        </createTable>
        <createIndex tableName="contact_info" indexName="ux_contact_info_query" unique="true">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="create table contact_extra" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="contact_extra"/>
            </not>
        </preConditions>
        <createTable tableName="contact_extra">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="job_number" type="varchar(32)"/>
            <column name="name" type="varchar(32)"/>
            <column name="jd" type="text"/>
            <column name="qa" type="text"/>
        </createTable>
        <createIndex tableName="contact_extra" indexName="ux_contact_extra_query" unique="true">
            <column name="job_number"/>
        </createIndex>
    </changeSet>

    <changeSet id="create table voice_chat_event" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="voice_chat_event"/>
            </not>
        </preConditions>
        <createTable tableName="voice_chat_event">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="app_id" type="varchar(100)"/>
            <column name="room_id" type="varchar(1024)"/>
            <column name="task_id" type="varchar(32)"/>
            <column name="user_id" type="varchar(32)"/>
            <column name="round_id" type="int"/>
            <column name="event_type" type="int"/>
            <column name="run_stage" type="varchar(32)"/>
            <column name="error_info" type="text"/>
        </createTable>
        <createIndex tableName="voice_chat_event" indexName="idx_voice_chat_event_query">
            <column name="task_id"/>
            <column name="user_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="create table eval_app" author="zheng.zhang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="eval_app"/>
            </not>
        </preConditions>
        <createTable tableName="eval_app">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="name" type="varchar(100)"/>
            <column name="code" type="varchar(64)"/>
            <column name="temperature" type="Double"/>
            <column name="desc" type="varchar(128)"/>
            <column name="status" type="varchar(32)" defaultValue="offline"/>
            <column name="llm_id" type="bigint"/>
            <column name="system_message" type="text"/>
            <column name="avatar" type="varchar(1024)"/>
            <column name="disabled" type="bit(1)" defaultValueNumeric="0"/>
        </createTable>
        <createIndex tableName="eval_app" indexName="idx_eval_app_query">
            <column name="status"/>
        </createIndex>
    </changeSet>

    <changeSet id="alter table task_info add column eval_app_ids" author="zheng.zhang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="task_info" columnName="eval_app_ids"/>
            </not>
        </preConditions>
        <addColumn tableName="task_info">
            <column name="eval_app_ids" type="varchar(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="alter table robot add column eval_app_ids" author="zheng.zhang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="robot" columnName="eval_app_ids"/>
            </not>
        </preConditions>
        <addColumn tableName="robot">
            <column name="eval_app_ids" type="varchar(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="create table eval_task" author="zheng.zhang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="eval_task"/>
            </not>
        </preConditions>
        <createTable tableName="eval_task">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="eval_app_id" type="bigint"/>
            <column name="task_type" type="varchar(64)"/>
            <column name="task_def" type="varchar(1024)"/>
            <column name="status" type="varchar(32)"/>
            <column name="failed_reason" type="varchar(1024)"/>
        </createTable>
        <createIndex tableName="eval_task" indexName="idx_eval_task_type_eval_task">
            <column name="task_type"/>
            <column name="eval_app_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="create table folder" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="folder"/>
            </not>
        </preConditions>
        <createTable tableName="folder">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="name" type="varchar(32)"/>
            <column name="type" type="varchar(32)"/>
            <column name="parent_id" type="bigint"/>
        </createTable>
    </changeSet>

    <changeSet id="create table folder_content" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="folder_content"/>
            </not>
        </preConditions>
        <createTable tableName="folder_content">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="folder_id" type="bigint"/>
            <column name="content_id" type="bigint"/>
        </createTable>
        <createIndex tableName="folder_content" indexName="ux_folder_content_query" unique="true">
            <column name="folder_id"/>
            <column name="content_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="alter table eval_task add column external_id" author="zheng.zhang">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="eval_task" columnName="external_id"/>
            </not>
        </preConditions>
        <addColumn tableName="eval_task">
            <column name="external_id" type="varchar(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="create table department" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="department"/>
            </not>
        </preConditions>
        <sql>
            <![CDATA[
            CREATE TABLE `department`
            (
                `id`        bigint       NOT NULL,
                `name`      varchar(100) NOT NULL,
                `parent_id` bigint       NOT NULL,
                `pp_id`     bigint DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
            ]]>
        </sql>
    </changeSet>

    <changeSet id="create table employee" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="employee"/>
            </not>
        </preConditions>
        <sql>
            <![CDATA[
            CREATE TABLE `employee`
            (
                `id`            bigint NOT NULL AUTO_INCREMENT,
                `job_number`    varchar(32) DEFAULT NULL,
                `union_id`      varchar(32) DEFAULT NULL,
                `name`          varchar(32) DEFAULT NULL,
                `dept1`         varchar(32) DEFAULT NULL,
                `dept2`         varchar(32) DEFAULT NULL,
                `dept3`         varchar(32) DEFAULT NULL,
                `dept4`         varchar(32) DEFAULT NULL,
                `dept5`         varchar(32) DEFAULT NULL,
                `dept6`         varchar(32) DEFAULT NULL,
                `standard_post` varchar(32) DEFAULT NULL,
                `post_number`   varchar(32) DEFAULT NULL,
                `personal_post` varchar(32) DEFAULT NULL,
                `personal_rank` varchar(32) DEFAULT NULL,
                `job_content`   text,
                `line_name`     varchar(32) DEFAULT NULL,
                PRIMARY KEY (`id`),
                UNIQUE KEY `ux_employee_query` (`job_number`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
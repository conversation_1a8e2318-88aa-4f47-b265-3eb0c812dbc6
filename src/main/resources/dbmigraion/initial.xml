<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">

    <changeSet id="create table user" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user"/>
            </not>
        </preConditions>
        <createTable tableName="user">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="name" type="varchar(32)"/>
            <column name="avatar" type="varchar(255)"/>
            <column name="phone" type="varchar(20)"/>
            <column name="email" type="varchar(100)"/>
            <column name="password" type="varchar(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="create table role" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="role"/>
            </not>
        </preConditions>
        <createTable tableName="role">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="code" type="varchar(32)"/>
            <column name="name" type="varchar(32)"/>
            <column name="description" type="varchar(255)"/>
        </createTable>
    </changeSet>

    <changeSet id="create table user_role" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_role"/>
            </not>
        </preConditions>
        <createTable tableName="user_role">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="bigint"/>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="user_id" type="bigint"/>
            <column name="role_id" type="bigint"/>
        </createTable>
    </changeSet>

    <changeSet id="create table role_permission" author="tao.huang">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="role_permission"/>
            </not>
        </preConditions>
        <createTable tableName="role_permission">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="create_time" type="datetime"/>
            <column name="update_time" type="datetime"/>
            <column name="creator_id" type="bigint"/>
            <column name="creator_name" type="varchar(32)"/>
            <column name="updater_id" type="bigint"/>
            <column name="updater_name" type="varchar(32)"/>
            <column name="role_id" type="bigint"/>
            <column name="permission" type="varchar(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="insert data for user and role" author="tao.huang">
        <sql>
            <![CDATA[
            insert into user(id, version, create_time, update_time, name, avatar, phone, email, password)
            values (1, 0, now(), now(), 'admin',
                    'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
                    '18888888888', '<EMAIL>', '$2a$10$bwhM6kEtTubENZJ2QR9c9eoRE.R0oz2UiPasOFc3ITqIkqr6lTjYi');
            insert into role(id, version, create_time, update_time, code, name)
            values (1, 0, now(), now(), 'admin', '系统管理员');
            insert into user_role(version, create_time, update_time, user_id, role_id)
            values (0, now(), now(), 1, 1);
            ]]>
        </sql>
    </changeSet>

    <changeSet id="insert mengniu admin user" author="tao.huang">
        <sql>
            <![CDATA[
            insert into user(version, create_time, update_time, name, avatar, phone, email, password)
            values (0, now(), now(),
                    'admin',
                    'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
                    '13691615298',
                    '<EMAIL>',
                    '$2a$10$Pvn41.zlHjfokJmbMWM4L.AupsxN7WBa/FU4vlcxz97tM9iGwppvu');
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
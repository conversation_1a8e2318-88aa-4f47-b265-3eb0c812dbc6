<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.TaskContactMapper">

    <select id="findAllIdByStatus">
        select distinct task_id
        from task_contact
        where status = #{status}
    </select>

    <select id="findAllIdByPostNumber">
        select distinct task_id from task_contact where external_id in(
            select job_number from employee where post_number = #{postNumber}
        )
    </select>

    <select id="listNotInterviewRecord" resultType="com.rolling.biz.model.entity.app.TaskContact">
        select id,
               task_id,
               name,
               external_id
        from task_contact
        where task_id = #{taskId}
          and id > #{maxId}
          and (
                    external_id not in (select user_id from conversation where task_id = #{taskId} group by user_id) or
                    external_id not in (select user_id
                                        from (select room_id,
                                                     user_id,
                                                     TIMESTAMPDIFF(SECOND, min(create_time), max(create_time)) as seconds_diff
                                              from conversation
                                              where task_id = #{taskId}
                                              group by room_id, user_id
                                              having seconds_diff >= 500) as t)
            )
        order by id asc limit 100
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.ConversationMapper">

    <select id="groupingByTaskId" resultType="hashmap">
        select room_id, max_id
        from (select room_id, max(id) as max_id from conversation where task_id = #{taskId} group by room_id) as t
        where max_id > #{maxId}
        order by max_id
        limit 20
    </select>

    <select id="findRoomIdByMax" resultType="hashmap">
        select * from (
            select room_id as roomId, TIMESTAMPDIFF(SECOND, min(create_time), max(create_time)) as seconds_diff
            from conversation where task_id=#{taskId} and user_id = #{userId}
            group by room_id
        ) as item
        order by item.seconds_diff desc limit 1
    </select>
</mapper>

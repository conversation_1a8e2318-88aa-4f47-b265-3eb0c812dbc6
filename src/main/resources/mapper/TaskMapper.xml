<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.TaskMapper">

    <select id="stateTaskBoard" parameterType="com.rolling.biz.model.request.app.TaskPageReq" resultType="hashmap">
        SELECT
                t.id,
                t.name,
                t.status,
                IFNULL(tc.employeeNum, 0) AS employeeNum,
                IFNULL(sd.complateNum, 0) AS complateNum,
                f.folderId,
                IFNULL(sd.totalTime, 0) AS totalTime,
                IFNULL((sd.totalTime / sd.complateNum), 0) AS avgTime
        FROM task_info AS t LEFT JOIN (
            SELECT
                task_id, count(1) AS employeeNum
            FROM task_contact GROUP BY task_id
        ) AS tc ON t.id = tc.task_id LEFT JOIN (
            SELECT
                task_id, count(1) AS complateNum, SUM(totalTime) AS totalTime
            FROM (
                SELECT task_id, user_id, MAX(secondsDiff) as totalTime FROM (
                    SELECT
                        task_id, user_id, room_id, TIMESTAMPDIFF(SECOND, min(create_time), max(create_time)) AS secondsDiff
                    FROM conversation GROUP BY task_id, user_id, room_id
                    HAVING secondsDiff >= 500
                ) AS m GROUP BY task_id, user_id
            ) AS o GROUP BY task_id
        ) AS sd ON t.id = sd.task_id LEFT JOIN (
            SELECT fc.content_id, f.id AS folderId FROM folder AS f LEFT JOIN folder_content AS fc ON f.id = fc.folder_id WHERE f.type = 'task'
        ) AS f ON f.content_id = t.id
        <where>
            <if test="keyword != null">
                AND t.name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="status != null and status != '' ">
                AND t.status = ${status}
            </if>
            <if test="folderId != null">
                AND f.folderId = ${folderId}
            </if>
        </where>
        ORDER BY ${sort} ${order}
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="countTaskBoard" parameterType="com.rolling.biz.model.request.app.TaskPageReq">
        select count(1) as total from task_info as t
        left join (
            select fc.content_id, f.id from folder as f left join folder_content as fc on f.id = fc.folder_id where f.type='task'
        ) as f on t.id = f.content_id
        <where>
            <if test="keyword != null">
                AND t.name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="status != null and status != '' ">
                AND t.status = ${status}
            </if>
            <if test="folderId != null">
                AND f.folderId = ${folderId}
            </if>
        </where>
    </select>
</mapper>

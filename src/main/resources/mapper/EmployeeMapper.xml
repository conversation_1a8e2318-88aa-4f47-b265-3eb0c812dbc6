<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.EmployeeMapper">

    <select id="groupByPostNumber">
        select distinct e.post_number as postNumber from task_contact as tc
        left join employee as e on tc.external_id = e.job_number
        where tc.task_id = #{taskId}
    </select>

    <select id="batchGetEmployee" resultType="com.rolling.biz.model.entity.app.Employee" parameterType="com.rolling.biz.model.request.app.EmployeePageReq">
        SELECT
               e.id as id,
               e.job_number as jobNumber,
               e.name as name,
               e.dept1 as dept1,
               e.dept2 as dept2,
               e.dept3 as dept3,
               e.dept4 as dept4,
               e.dept5 as dept5,
               e.dept6 as dept6,
               e.standard_post as standardPost,
               e.post_number as postNumber,
               e.personal_post as personalPost,
               IFNULL(ec.taskNumber, 0) as taskNumber,
               IFNULL(ec.completeTaskNumber, 0) as completeTaskNumber
        FROM employee AS e
        LEFT JOIN (
                SELECT
                       external_id,
                       count(1) AS taskNumber,
                       SUM(CASE WHEN status='success' THEN 1 ELSE 0 END) AS completeTaskNumber
                FROM task_contact GROUP BY external_id
        ) AS ec ON e.job_number = ec.external_id
        <where>
            <if test="keyword != null">
                AND name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="postNumber != null and postNumber != '' ">
                AND post_number = ${postNumber}
            </if>
        </where>
        ORDER BY ${sort} ${order}
        LIMIT ${page}, ${pageSize}
    </select>

    <select id="countByTotal" parameterType="com.rolling.biz.model.request.app.EmployeePageReq">
        select count(1) as total from employee
        <where>
            <if test="keyword != null">
                AND name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="postNumber != null and postNumber != '' ">
                and post_number = #{postNumber}
            </if>
        </where>
    </select>

    <select id="statePostNumber" resultType="hashmap">
        SELECT m.*, IFNULL(n.taskNumber, 0) AS taskNumber, IFNULL(n.completeTaskNumber, 0) AS completeTaskNumber FROM (
            SELECT
                e.post_number as postNumber, e.standard_post AS standardPost, dept1, dept2, dept3, dept4, dept5, dept6
            FROM employee as e
            GROUP BY post_number, standard_post, dept1, dept2, dept3, dept4, dept5, dept6
        ) AS m LEFT JOIN (
            SELECT
                post_number AS postNumber,
                count(1) AS taskNumber,
                SUM(CASE WHEN status='success' THEN 1 ELSE 0 END) as completeTaskNumber
            FROM (
                SELECT tc.status, e.post_number FROM task_contact as tc
                LEFT JOIN employee AS e ON tc.external_id = e.job_number
            ) AS o GROUP BY post_number
        ) as n ON m.postNumber = n.postNumber
        <where>
            <if test="keyword != null">AND (m.standardPost LIKE CONCAT('%', #{keyword}, '%') OR m.postNumber LIKE CONCAT('%', #{keyword}, '%'))</if>
            <if test="postNumber != null and postNumber != ''">AND m.postNumber = ${postNumber}</if>
        </where>
        ORDER BY ${sort} ${order}
        LIMIT ${page}, ${pageSize}
    </select>

    <select id="countPostNumber" parameterType="com.rolling.biz.model.request.app.EmployeePageReq">
        SELECT COUNT(1) AS total FROM (
            SELECT
                e.post_number as postNumber, e.standard_post AS standardPost, dept1, dept2, dept3, dept4, dept5, dept6
            FROM employee as e GROUP BY post_number, standard_post, dept1, dept2, dept3, dept4, dept5, dept6
        ) as m
        <where>
            <if test="keyword != null">AND (m.standardPost LIKE CONCAT('%', #{keyword}, '%') OR m.postNumber LIKE CONCAT('%', #{keyword}, '%'))</if>
            <if test="postNumber != null and postNumber != ''">AND m.postNumber = ${postNumber}</if>
        </where>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.FolderContentMapper">

    <select id="findByAllContentId" resultType="com.rolling.biz.model.entity.app.FolderContent">
        SELECT fc.* FROM folder_content AS fc
        LEFT JOIN folder AS f ON fc.folder_id = f.id
        WHERE f.type = #{type} AND content_id in
        <foreach collection="contentIds" item="contentId" separator="," open="(" close=")">
            #{contentId}
        </foreach>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.ContactInfoMapper">

    <insert id="upsertBatch">
        insert into contact_info(version, create_time, update_time,
        name, phone, title, user_id, union_id, job_number, dept_id, dept_name)
        values
        <foreach collection="entities" item="item" separator=",">
            (0, now(), now(), #{item.name}, #{item.phone}, #{item.title}, #{item.userId}, #{item.unionId},
            #{item.jobNumber}, #{item.deptId}, #{item.deptName})
        </foreach>
        on duplicate key update
        name=values(name),
        phone=values(phone),
        title=values(title),
        union_id=values(union_id),
        job_number=values(job_number),
        dept_id=values(dept_id),
        dept_name=values(dept_name)
    </insert>
</mapper>

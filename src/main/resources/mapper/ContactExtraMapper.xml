<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.ContactExtraMapper">

    <insert id="upsertBatch">
        insert into contact_extra(version, create_time, update_time, job_number, name, jd, qa)
        values
        <foreach collection="entities" item="item" separator=",">
            (0, now(), now(), #{item.jobNumber}, #{item.name}, #{item.jd}, #{item.qa})
        </foreach>
        on duplicate key update jd=values(jd), qa=values(qa), name=values(name)
    </insert>
</mapper>

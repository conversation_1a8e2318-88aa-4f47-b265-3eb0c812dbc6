<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rolling.biz.mapper.app.FolderMapper">

    <select id="findByTypeAndContentId">
        SELECT f.* FROM folder AS f LEFT JOIN folder_content AS fc ON f.id = fc.folder_id WHERE f.type = #{type} AND fc.content_id = #{contentId}
    </select>
</mapper>

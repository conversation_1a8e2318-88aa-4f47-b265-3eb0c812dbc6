(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3432],{7250:(e,s,i)=>{"use strict";i.d(s,{A:()=>d});var t=i(95155);function l(){return(0,t.jsxs)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,t.jsx)("path",{d:"M20 39C30.4934 39 39 30.4934 39 20C39 9.50659 30.4934 1 20 1C9.50659 1 1 9.50659 1 20C1 30.4934 9.50659 39 20 39Z",stroke:"black",strokeOpacity:"0.1"}),(0,t.jsx)("path",{d:"M12.2461 20H28.7461",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M17.7461 25.5L12.2461 20L17.7461 14.5",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}var n=i(59276),o=i(22810),r=i(2796),a=i(76046);let{Content:c}=n.A,d=e=>{let{children:s}=e,i=(0,a.useRouter)();return(0,t.jsx)(c,{children:(0,t.jsxs)(o.A,{wrap:!1,children:[(0,t.jsx)(r.A,{style:{cursor:"pointer",width:60,minWidth:60,maxWidth:60,flex:"0 0 60px"},children:(0,t.jsx)("span",{onClick:()=>{i.back()},children:(0,t.jsx)(l,{})})}),(0,t.jsx)(r.A,{style:{flex:1,minWidth:0,whiteSpace:"nowrap"},children:s})]})})}},12537:(e,s,i)=>{"use strict";i.d(s,{T:()=>l,Y:()=>t});let t={initial:"未开始",processing:"进行中",completed:"已结束"},l={initial:"未开始",processing:"进行中",notexist:"空号",busy:"占线",success:"成功",failed:"失败"}},17289:(e,s,i)=>{"use strict";i.d(s,{A:()=>A});var t=i(95155),l=i(95988),n=i(6521),o=i(35039),r=i(59575),a=i(18123),c=i(2796),d=i(12115);function A(e){let{keyword:s,status:i,onStatusChange:A,extra:u,robotId:x}=e,[h,g]=(0,d.useState)(i),{runAsync:m}=(0,a.RI)(),{loading:I,runAsync:p}=(0,a.Ru)(),{runAsync:j,loading:k}=(0,r.oD)(),v=async e=>{let{keyword:s,status:i,robotId:t,page:l,pageSize:n}=e,o=await m({keyword:s,status:i,robotId:t,page:l,pageSize:n});if(o.success){var r;let e=((null===(r=o.data)||void 0===r?void 0:r.records)||[]).map(e=>({...e,taskStatusCount:{},robot:null}));try{let s={},i={};e.forEach(e=>{s[e.id]||(s[e.id]=e.id),i[e.robotId]||(i[e.robotId]=e.robotId)});let t=await p({taskId:Object.keys(s).join(",")}),l=await j({ids:Object.keys(i).join(",")});return e.forEach(e=>{e.taskStatusCount=t.success?t.data[e.id]:{},e.robot=l.success?l.data.records.find(s=>s.id===e.robotId):null}),{success:!0,data:{records:e,total:o.data.total}}}catch(e){return console.error("Error fetching related data:",e),{success:!1,error:{message:"获取相关数据失败"}}}}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l.A,{items:[{key:"processing",label:"进行中"},{key:"initial",label:"待启动"},{key:"completed",label:"已结束"}],onChange:e=>{g(e),null==A||A(e)}}),u,(0,t.jsx)(n.A,{getList:v,params:{keyword:s,status:h,robotId:x},refreshDeps:[h,s,x],renderItem:e=>(0,t.jsx)(c.A,{span:8,children:(0,t.jsx)(o.A,{task:e,onStart:()=>{g("processing")},statusCount:e.taskStatusCount,loading:{countLoading:I,robotLoading:k}})},e.id)})]})}},28532:(e,s,i)=>{"use strict";i.d(s,{A:()=>l});var t=i(95155);function l(){return(0,t.jsxs)("svg",{width:"18",height:"18",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",children:[(0,t.jsx)("rect",{x:"0.25",y:"0.25",width:"47.5",height:"47.5",rx:"11.75",fill:"url(#pattern0_228_7247)",stroke:"#EAEDF1",strokeWidth:"0.5"}),(0,t.jsxs)("defs",{children:[(0,t.jsx)("pattern",{id:"pattern0_228_7247",patternContentUnits:"objectBoundingBox",width:"1",height:"1",children:(0,t.jsx)("use",{xlinkHref:"#image0_228_7247",transform:"scale(0.0025)"})}),(0,t.jsx)("image",{id:"image0_228_7247",width:"400",height:"400",xlinkHref:"data:image/png;base64,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"})]})]})}},29044:(e,s,i)=>{Promise.resolve().then(i.bind(i,81408))},30259:(e,s,i)=>{"use strict";i.d(s,{FL:()=>a,gI:()=>d,hv:()=>A,zK:()=>r,o3:()=>c});var t=i(69653),l=i(35594),n=i(90603);let o={getLlmList:e=>(0,l.Jt)({url:"/api/llms",data:e?{...n.u,...e}:n.u}),createLlm:e=>(0,l.bE)({url:"/api/llms",data:e}),updateLlm:(e,s)=>(0,l.yJ)({url:"/api/llms/".concat(e),data:s}),deleteLlm:e=>(0,l.yH)({url:"/api/llms/".concat(e)}),getLlmProviders:()=>(0,l.Jt)({url:"/api/llms/providers"})},r=()=>(0,t.A)(o.getLlmList,{manual:!0}),a=()=>(0,t.A)(o.createLlm,{manual:!0}),c=()=>(0,t.A)(o.updateLlm,{manual:!0}),d=()=>(0,t.A)(o.deleteLlm,{manual:!0}),A=()=>{var e;let s=(0,t.A)(o.getLlmProviders,{});return{...s,data:(null==s?void 0:null===(e=s.data)||void 0===e?void 0:e.success)?s.data.data:[]}}},35039:(e,s,i)=>{"use strict";i.d(s,{A:()=>f,q:()=>D});var t=i(95155),l=i(71126),n=i(18123),o=i(12537),r=i(55750),a=i(46742),c=i(64787),d=i(11432),A=i(71349),u=i(22810),x=i(2796),h=i(72093),g=i(6457),m=i(78444),I=i(68773),p=i(79005),j=i(76046),k=i(96926),v=i(83761),R=i(24947),C=i(32853),B=i(51480),E=i.n(B),w=i(81488);let{Text:y}=a.A,D=e=>{switch(e){case"initial":return"#FBBC05";case"processing":return"#23E4A6";default:return"#BEBEBE"}};function f(e){var s;let{task:i,onStart:a,statusCount:B,loading:f}=e,{runAsync:Q,loading:G}=(0,n.V8)(),{runAsync:b,loading:F}=(0,n.rp)(),{name:H,desc:M,status:Y,type:S}=i,O=(0,j.useRouter)(),{message:N}=c.A.useApp(),{total:J,processing:U,success:L,initial:K,failed:T,busy:P,notexist:W}=null!=B?B:{};return(0,t.jsx)(d.Ay,{theme:{components:{Card:{colorBgContainer:"#fff"}}},children:(0,t.jsxs)(A.A,{variant:"borderless",className:E().taskInfo,children:[(0,t.jsxs)(u.A,{gutter:[16,16],children:[(0,t.jsx)(x.A,{span:24,children:"outbound"===S?(0,t.jsx)("div",{className:E().callOut,children:(0,t.jsx)(v.A,{})}):(0,t.jsx)("div",{className:E().callIn,children:(0,t.jsx)(k.A,{})})}),(0,t.jsx)(x.A,{span:24,children:(0,t.jsx)(R.A,{title:H,desc:M,badge:{color:D(Y),text:o.Y[Y]}})}),(0,t.jsx)(x.A,{span:24,children:(0,t.jsx)(h.A,{spinning:null==f?void 0:f.countLoading,children:("processing"===Y||"completed"===Y)&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y,{className:E().progressTitle,children:"outbound"===S?"外呼进度统计":"呼入进度统计"}),("outbound"===S||"out"===S)&&(0,t.jsxs)("div",{className:E().progress,style:{width:"100%"},children:[(0,t.jsx)(g.A,{title:"拨打中：".concat(U),children:(0,t.jsx)("div",{className:E().progressItem,style:{width:"".concat((0,w.O)(+U,+J),"%"),backgroundColor:"#41D189",borderRadius:"2px"}})}),(0,t.jsx)(g.A,{title:"未接通：".concat(+T+ +P+ +W),children:(0,t.jsx)("div",{className:E().progressItem,style:{width:"".concat((0,w.O)(+T+ +P+ +W,+J),"%"),backgroundColor:"#FF004D",right:"2px"}})}),(0,t.jsx)(g.A,{title:"待拨打：".concat(K),children:(0,t.jsx)("div",{className:E().progressItem,style:{width:"".concat((0,w.O)(+K,+J),"%"),backgroundColor:"#FFCE52",right:"4px"}})}),(0,t.jsx)(g.A,{title:"已完成：".concat(L),children:(0,t.jsx)("div",{className:E().progressItem,style:{width:"".concat((0,w.O)(+L,+J),"%")}})})]}),("inbound"===S||"in"===S)&&(0,t.jsxs)("div",{className:E().progress,style:{width:"100%"},children:[(0,t.jsx)(g.A,{title:"拨打中：".concat(U),children:(0,t.jsx)("div",{className:E().progressItem,style:{width:"".concat((0,w.O)(+U,+J),"%"),backgroundColor:"#FBBC05",borderRadius:"2px"}})}),(0,t.jsx)(g.A,{title:"故障：".concat(+T+ +P+ +W),children:(0,t.jsx)("div",{className:E().progressItem,style:{width:"".concat((0,w.O)(+T+ +P+ +W,+J),"%"),backgroundColor:"#FF004D",borderRadius:"2px"}})}),(0,t.jsx)(g.A,{title:"已呼入：".concat(L),children:(0,t.jsx)("div",{className:E().progressItem,style:{width:"".concat((0,w.O)(+L,+J),"%"),backgroundColor:"#41D189"}})})]})]})})}),(0,t.jsx)(x.A,{span:24,children:(0,t.jsx)(h.A,{spinning:null==f?void 0:f.robotLoading,children:(0,t.jsxs)("div",{className:E().progressInfo,children:[(0,t.jsx)("div",{children:"执行AI虚拟角色"}),(0,t.jsx)(m.A,{size:24,src:null==i?void 0:null===(s=i.robot)||void 0===s?void 0:s.avatar,icon:(0,t.jsx)(r.A,{})})]})})})]}),(0,t.jsx)(u.A,{justify:"end",children:(0,t.jsxs)(I.A,{children:["initial"===Y&&(0,t.jsx)(C.A,{btnText:"启动任务",title:"确定启动该任务",btnProps:{loading:G,type:"primary"},onOk:async()=>{let e=await Q(i.id);return e.success?(N.success("启动成功"),a(),!0):(N.error(e.error.message),!1)}}),"processing"===Y&&(0,t.jsx)(C.A,{btnText:"停止任务",title:"确定停止该任务",btnProps:{loading:F,type:"link"},onOk:async()=>{let e=await b(i.id);return e.success?(N.success("已停止"),!0):(N.error(e.error.message),!1)}}),(0,t.jsx)(p.Ay,{onClick:()=>{O.push((0,w.I)({url:l.Nv.TaskEdit,params:{id:i.id}}))},children:"详情"})]})})]})})}},51480:e=>{e.exports={taskInfo:"TaskInfo_taskInfo__12TZK",callIn:"TaskInfo_callIn__gQyaY",callOut:"TaskInfo_callOut__AxJgT",progressTitle:"TaskInfo_progressTitle__MztEI",progressInfo:"TaskInfo_progressInfo__UOctk",progress:"TaskInfo_progress__LYWIv",progressItem:"TaskInfo_progressItem__zFADJ"}},56409:e=>{e.exports={doubao:"list_doubao__sXuUq"}},81408:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>eo});var t=i(95155),l=i(91199),n=i(21614),o=i(68773),r=i(49113),a=i.n(r),c=i(12115),d=i(69653),A=i(35594);let u={getTtsModel:()=>(0,A.Jt)({url:"/api/model/tts"}),getAsrModel:()=>(0,A.Jt)({url:"/api/model/asr"})},x=()=>(0,d.A)(u.getTtsModel,{manual:!0}),h=()=>(0,d.A)(u.getAsrModel,{manual:!0});var g=i(28532),m=i(56409),I=i.n(m);function p(e){var s,i;let{data:l,loading:r,runAsync:d}=h(),A=null!==(i=(null==l?void 0:l.success)&&l.data)&&void 0!==i?i:[];return(0,c.useEffect)(()=>{d()},[]),(0,t.jsx)(n.A,{options:null===(s=a().uniqBy(A,"provider"))||void 0===s?void 0:s.map(e=>({label:(0,t.jsxs)(o.A,{children:[e.provider.startsWith("volcano")&&(0,t.jsx)("div",{className:I().doubao,children:(0,t.jsx)(g.A,{})}),e.providerName]}),value:e.provider})),loading:r,placeholder:"请选择",...e})}var j=i(35587),k=i(95988),v=i(32853),R=i(7250),C=(i(77135),i(35577)),B=i(86306),E=i(42628);let w=()=>{var e;let{data:s,loading:i,runAsync:t}=x(),l=null!==(e=(null==s?void 0:s.success)&&s.data)&&void 0!==e?e:[];(0,c.useEffect)(()=>{t()},[]);let n=y(l);return{providerList:a().uniqBy(l,"provider"),modelList:n,loading:i}},y=e=>{let s={};for(let i of e){let e=i.provider,t=i.providerParams;s[e]||(s[e]=[]),s[e].push(t)}return s};var D=i(81488),f=i(71126),Q=i(59575),G=i(85845),b=i(55750),F=i(41379),H=i(28726),M=i(41657),Y=i(46742),S=i(64787),O=i(32392),N=i(72093),J=i(22810),U=i(2796),L=i(78444),K=i(48904),T=i(6457),P=i(42426),W=i(11432),V=i(79005),z=i(89797),Z=i(5565),X=i(76046),q=i(81548),_=i.n(q),$=i(17289),ee=i(18123),es=i(95950);function ei(e){let{robotId:s}=e,{loading:i,data:l}=(0,ee.w4)(s);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(es.Ay,{loading:i,data:l,processingProps:{badgeProps:{textColor:"#23E4A6"},numberProps:{textColor:"#23E4A6"}}}),(0,t.jsx)(j.A,{title:"任务执行拦",children:(0,t.jsx)($.A,{robotId:s})})]})}let{TextArea:et}=M.A,{Title:el,Text:en}=Y.A;function eo(){return(0,t.jsx)(c.Suspense,{fallback:(0,t.jsx)("div",{children:"Loading..."}),children:(0,t.jsx)(ea,{})})}var er=function(e){return e.CONFIG="config",e.STATISTIC="statistic",e}(er||{});function ea(){var e,s;let{message:i}=S.A.useApp(),r=(0,X.useRouter)(),a=(0,X.useSearchParams)(),d=a.get("id"),A=null!==(e=a.get("platform"))&&void 0!==e?e:G.W.Volcano,{runAsync:u,loading:x}=(0,Q.uw)(),{data:h,runAsync:m,loading:y}=(0,Q.hU)(),{runAsync:Y,loading:q}=(0,Q.lJ)(),{runAsync:$,loading:ee}=(0,Q.dk)(),{modelList:es,providerList:eo,loading:er}=w(),[ea,ec]=(0,c.useState)(!0),[ed,eA]=(0,c.useState)(""),[eu,ex]=(0,c.useState)(""),[eh,eg]=(0,c.useState)("edit"),[em,eI]=(0,c.useState)(!1),[ep,ej]=(0,c.useState)(null),[ek,ev]=(0,c.useState)("config"),[eR,eC]=(0,c.useState)(),[eB]=(0,c.useState)(),eE=(null==h?void 0:h.success)?h.data:{},ew=null==eE?void 0:eE.id,[ey]=O.A.useForm(),eD=O.A.useWatch(["ttsConfig","provider"],ey),ef=O.A.useWatch(["ttsConfig","speedRatio"],ey),eQ=O.A.useWatch(["ttsConfig","volumeRatio"],ey),eG=O.A.useWatch(["llmConfig","temperature"],ey),eb=null!==(s=es[eD])&&void 0!==s?s:[];(0,c.useEffect)(()=>{!er&&d&&eE.id&&(eC(eb.find(e=>e.voiceType===eE.ttsConfig.voiceType)),ey.setFieldsValue(eE))},[er,eE.id]),(0,c.useEffect)(()=>{d&&m(d).then(e=>{if(e.success){let{name:s,desc:i,disabled:t,avatar:l}=e.data;eA(s),ex(i),eI(!t),ej(l),eg("view"),ec(!1),ey.setFieldsValue({...e.data})}})},[d]);let eF="view"===eh,eH=async e=>{let s={...e,name:ed,desc:eu,avatar:ep,disabled:em,platform:A},t=ew?await Y(s,ew):await u(s);t.success?(eg("view"),i.success("保存成功"),r.push((0,D.I)({url:f.Nv.RobotList}))):i.error(t.error.message)},eM=(0,E.B)(eE);return(0,t.jsx)(R.A,{children:(0,t.jsx)(N.A,{spinning:d&&ea&&(!h||y),children:(0,t.jsxs)(S.A,{className:_().container,children:[(0,t.jsxs)(J.A,{style:{marginBottom:"60px"},gutter:12,children:[(0,t.jsx)(U.A,{children:eF?(0,t.jsx)(L.A,{size:40,src:ep,icon:(0,t.jsx)(b.A,{})}):(0,t.jsx)(K.A,{showUploadList:!1,maxCount:1,action:"/api/image/upload",accept:"image/*",beforeUpload:e=>{let s=e.size/1024/1024<2;return s||i.error("图片大小不超过2MB"),s},onChange:async e=>{"done"===e.file.status?e.file.response.success?ej(e.file.response.data.url):i.error("".concat(e.file.name," 文件上传失败")):"error"===e.file.status&&i.error("".concat(e.file.name," 文件上传失败"))},children:(0,t.jsx)(T.A,{title:"点击更换头像",children:(0,t.jsx)(L.A,{size:40,icon:(0,t.jsx)(F.A,{}),src:ep})})})}),eF?(0,t.jsxs)(U.A,{span:23,children:[(0,t.jsxs)(J.A,{justify:"space-between",children:[(0,t.jsx)(U.A,{children:(0,t.jsxs)(o.A,{children:[(0,t.jsx)(el,{level:4,children:ed}),(0,t.jsx)(T.A,{title:eM.tooltip,children:(0,t.jsx)(P.A,{checked:em,disabled:"service"===eM.status,onChange:e=>{eI(e),Y({disabled:!e},ew).catch(s=>{i.error(s.message),eI(!e)})}})})]})}),(0,t.jsx)(U.A,{children:(0,t.jsxs)(o.A,{size:"small",children:[(0,t.jsx)(W.Ay,{theme:{components:{Button:{colorLink:"#171F2D",colorLinkHover:"#303847"}}},children:(0,t.jsx)(V.Ay,{type:"link",onClick:()=>{r.push((0,D.I)({url:f.Nv.TaskEdit,params:{robotId:ew}}))},children:"创建新任务"})}),(0,t.jsx)(V.Ay,{onClick:()=>eg("edit"),loading:q,children:"编辑"})]})})]}),(0,t.jsx)(en,{type:"secondary",children:eu})]}):(0,t.jsxs)(U.A,{span:23,children:[(0,t.jsxs)(J.A,{justify:"space-between",children:[(0,t.jsx)(U.A,{children:(0,t.jsx)(W.Ay,{theme:{components:{Input:{colorTextPlaceholder:"#000"}}},children:(0,t.jsx)(M.A,{placeholder:"请输入访谈助手名称",variant:"borderless",style:{paddingLeft:0,fontSize:"20px",fontWeight:"500"},value:ed,onChange:e=>{eA(e.target.value)}})})}),(0,t.jsx)(V.Ay,{type:"primary",loading:x,onClick:async()=>{let e={name:ed,desc:eu,avatar:ep},s=ew?await Y(e,ew):await u(e);s.success?(eg("view"),i.success("保存成功")):i.error(s.message)},children:"完成"})]}),(0,t.jsx)(et,{style:{marginTop:"20px"},placeholder:"请输入该访谈助手名称简介",autoSize:{minRows:2,maxRows:6},value:eu,onChange:e=>{ex(e.target.value)}})]})]}),d&&(0,t.jsx)(k.A,{items:[{key:"config",label:"访谈助手配置"},{key:"statistic",label:(0,t.jsxs)(o.A,{children:["任务状态",(0,t.jsx)(C.A,{color:eM.color,children:eM.label})]})}],onChange:e=>{ev(e)}}),"config"===ek&&(0,t.jsxs)(t.Fragment,{children:[!1,(0,t.jsx)(O.A,{layout:"vertical",form:ey,initialValues:{...eE},onFinish:eH,children:(0,t.jsxs)(J.A,{gutter:[24,24],align:"stretch",children:[(0,t.jsx)(U.A,{span:12,children:(0,t.jsx)(B.A,{form:ey,llmParams:eB,temperature:eG,visibleFields:A===G.W.Aliyun?["systemMessage","temperature","welcomeMessage","historyLength"]:void 0})}),A===G.W.Volcano&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(U.A,{span:12,children:(0,t.jsxs)(j.A,{className:_().section,title:"TTS设置",children:[(0,t.jsx)(O.A.Item,{name:["ttsConfig","provider"],label:"声音供应商",children:(0,t.jsx)(n.A,{loading:er,placeholder:"请选择",onChange:e=>{var s;let i=null===(s=es[e])||void 0===s?void 0:s[0];eC(i),ey.setFieldsValue({...ey.getFieldsValue(),ttsConfig:{...ey.getFieldsValue().ttsConfig,provider:e,voiceType:null==i?void 0:i.voiceType,speedRatio:null==i?void 0:i.speedRatioDefault,volumeRatio:null==i?void 0:i.volumeRatioDefault}})},options:eo.map(e=>({label:(0,t.jsxs)(o.A,{children:[e.provider.startsWith("volcano")&&(0,t.jsx)("div",{className:I().doubao,children:(0,t.jsx)(g.A,{})}),e.provider.startsWith("minimax")&&(0,t.jsx)("div",{className:I().doubao,children:(0,t.jsx)(Z.default,{src:(0,D.I)({url:"/minimax.jpeg",isPage:!1}),alt:"minimax",width:20,height:20})}),e.providerName]}),value:e.provider}))})}),(0,t.jsx)(O.A.Item,{name:["ttsConfig","voiceType"],label:"类型",children:(0,t.jsx)(n.A,{placeholder:"请选择类型",options:eb.map(e=>({label:e.voiceTypeName,value:e.voiceType})),onChange:e=>{let s=eb.find(s=>s.voiceType===e);eC(s),ey.setFieldsValue({...ey.getFieldsValue(),ttsConfig:{...ey.getFieldsValue().ttsConfig,voiceType:e,speedRatio:null==s?void 0:s.speedRatioDefault,volumeRatio:null==s?void 0:s.volumeRatioDefault}})}})}),(0,t.jsxs)(J.A,{justify:"space-between",children:[(0,t.jsx)(U.A,{span:8,children:(0,t.jsx)(O.A.Item,{name:["ttsConfig","speedRatio"],label:"速度",children:(0,t.jsxs)(J.A,{gutter:12,align:"middle",children:[(0,t.jsx)(U.A,{span:22,children:(0,t.jsx)(z.A,{value:ef,min:null==eR?void 0:eR.speedRatioMin,max:null==eR?void 0:eR.speedRatioMax,step:.1,onChange:e=>{ey.setFieldValue(["ttsConfig","speedRatio"],e)}})}),(0,t.jsx)(U.A,{span:2,children:null==eR?void 0:eR.speedRatioMax})]})})}),(0,t.jsx)(U.A,{span:8,children:(0,t.jsx)(O.A.Item,{name:["ttsConfig","volumeRatio"],label:"音调",children:(0,t.jsxs)(J.A,{gutter:12,align:"middle",children:[(0,t.jsx)(U.A,{span:22,children:(0,t.jsx)(z.A,{value:eQ,min:null==eR?void 0:eR.volumeRatioMin,max:null==eR?void 0:eR.volumeRatioMax,step:.1,onChange:e=>{ey.setFieldValue(["ttsConfig","volumeRatio"],e)}})}),(0,t.jsx)(U.A,{span:2,children:null==eR?void 0:eR.volumeRatioMax})]})})})]}),(0,t.jsx)(J.A,{style:{marginTop:"20px"},children:(0,t.jsx)(U.A,{span:4,children:(0,t.jsx)(V.Ay,{type:"primary",icon:(0,t.jsx)(H.A,{}),children:"播放声音"})})})]})}),(0,t.jsx)(U.A,{span:12,children:(0,t.jsx)(j.A,{className:_().section,title:"ASR设定",children:(0,t.jsx)(O.A.Item,{name:["asrConfig","provider"],label:"ASR供应商",children:(0,t.jsx)(p,{})})})})]}),A===G.W.Aliyun&&(0,t.jsx)(U.A,{span:12,children:(0,t.jsx)(j.A,{className:_().section,title:"平台设置",children:(0,t.jsx)(O.A.Item,{name:"externalAgentId",label:"智能体ID",children:(0,t.jsx)(M.A,{placeholder:"请输入智能体ID"})})})}),(0,t.jsx)(U.A,{span:12,children:(0,t.jsx)(j.A,{className:_().section,title:"任务指派",children:(0,t.jsx)(O.A.Item,{name:"evalAppIds",label:"绑定分析助手",children:(0,t.jsx)(l.A,{multiple:!0})})})})]})}),(0,t.jsxs)(J.A,{justify:d?"space-between":"start",align:"middle",style:{marginTop:"20px"},children:[(0,t.jsxs)(o.A,{size:16,children:[(0,t.jsx)(V.Ay,{type:"primary",onClick:ey.submit,loading:q||x,children:"保存"}),(0,t.jsx)(V.Ay,{onClick:()=>{r.push((0,D.I)({url:f.Nv.RobotList}))},children:"取消"})]}),d&&(0,t.jsx)(v.A,{btnText:"删除访谈助手",title:"确定删除该访谈助手？",onOk:async()=>{let e=await $(ew);return e.success?(i.success("删除成功"),r.push((0,D.I)({url:f.Nv.RobotList})),!0):(i.error(e.error.message),!1)},btnProps:{loading:ee},description:"访谈助手一旦删除, 无法恢复"})]})]}),"statistic"===ek&&(0,t.jsx)(ei,{robotId:d})]})})})}},81548:e=>{e.exports={container:"page_container__hGi22",section:"page_section__vdazD"}},86306:(e,s,i)=>{"use strict";i.d(s,{A:()=>R});var t=i(95155),l=i(41657),n=i(32392),o=i(22810),r=i(2796),a=i(89797),c=i(18198),d=i(35587),A=i(30259),u=i(21614),x=i(68773),h=i(78444),g=i(12115),m=i(28532),I=i(56409),p=i.n(I);function j(e){var s;let{data:i,loading:l,runAsync:n}=(0,A.zK)(),o=null!==(s=(null==i?void 0:i.success)&&i.data.records)&&void 0!==s?s:[];return(0,g.useEffect)(()=>{n()},[]),(0,t.jsx)(u.A,{options:null==o?void 0:o.map(e=>({...e,label:(0,t.jsxs)(x.A,{children:[e.icon?(0,t.jsx)(h.A,{src:e.icon,size:18}):(0,t.jsx)(t.Fragment,{children:e.provider.startsWith("ArkV3")&&(0,t.jsx)("div",{className:p().doubao,children:(0,t.jsx)(m.A,{})})}),e.name]}),value:e.id})),loading:l,placeholder:"请选择",...e})}let{TextArea:k}=l.A,v=["llmId","systemMessage","temperature","welcomeMessage","historyLength"],R=e=>{var s,i,A;let{form:u,llmParams:x,visibleFields:h,temperature:g}=e,m=h||v;return(0,t.jsxs)(d.A,{className:"section",title:"LLM设定",children:[m.includes("llmId")&&(0,t.jsx)(n.A.Item,{name:"llmId",label:"基础模型",children:(0,t.jsx)(j,{})}),m.includes("systemMessage")&&(0,t.jsx)(n.A.Item,{name:["llmConfig","systemMessage"],label:"提示词",children:(0,t.jsx)(k,{placeholder:"请输入提示词",rows:4})}),m.includes("temperature")&&(0,t.jsx)(o.A,{children:(0,t.jsx)(r.A,{span:12,children:(0,t.jsx)(n.A.Item,{name:["llmConfig","temperature"],label:"温度",tooltip:"温度 (Temperature)：控制输出的随机性。高温→更多创意；低温→保守（例：0.2=精准回答，1.0=平衡，2.0=天马行空）",children:(0,t.jsxs)(o.A,{gutter:12,align:"middle",children:[(0,t.jsx)(r.A,{span:22,children:(0,t.jsx)(a.A,{value:g,min:null!==(s=null==x?void 0:x.temperatureMin)&&void 0!==s?s:0,max:null!==(i=null==x?void 0:x.temperatureMax)&&void 0!==i?i:1,step:.1,onChange:e=>{u.setFieldValue(["llmConfig","temperature"],e)}})}),(0,t.jsx)(r.A,{span:2,children:null!==(A=null==x?void 0:x.temperatureMax)&&void 0!==A?A:"1.0"})]})})})}),m.includes("welcomeMessage")&&(0,t.jsx)(n.A.Item,{name:["llmConfig","welcomeMessage"],label:"欢迎语",children:(0,t.jsx)(l.A,{placeholder:"请输入欢迎语"})}),m.includes("historyLength")&&(0,t.jsx)(n.A.Item,{name:["llmConfig","historyLength"],label:"对话记忆轮数",children:(0,t.jsx)(c.A,{placeholder:"请输入对话记忆轮数",style:{width:"200px"},min:3})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[838,6772,3740,4935,2211,6222,2602,3520,9907,3288,1509,1349,9786,4439,5585,4787,2392,2342,4156,4338,5799,8198,6613,9797,8260,3889,8441,6587,7358],()=>s(29044)),_N_E=e.O()}]);
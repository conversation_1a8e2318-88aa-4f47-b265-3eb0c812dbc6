"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3288],{43288:(e,t,a)=>{a.d(t,{A:()=>z});var n=a(12115),c=a(4617),i=a.n(c),l=a(31049),o=a(70527);let s=e=>{let{prefixCls:t,className:a,style:c,size:l,shape:o}=e,s=i()({["".concat(t,"-lg")]:"large"===l,["".concat(t,"-sm")]:"small"===l}),r=i()({["".concat(t,"-circle")]:"circle"===o,["".concat(t,"-square")]:"square"===o,["".concat(t,"-round")]:"round"===o}),g=n.useMemo(()=>"number"==typeof l?{width:l,height:l,lineHeight:"".concat(l,"px")}:{},[l]);return n.createElement("span",{className:i()(t,s,r,a),style:Object.assign(Object.assign({},g),c)})};var r=a(5144),g=a(1086),d=a(56204);let u=new r.Mo("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),m=e=>({height:e,lineHeight:(0,r.zA)(e)}),b=e=>Object.assign({width:e},m(e)),h=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:u,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),p=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},m(e)),O=e=>{let{skeletonAvatarCls:t,gradientFromColor:a,controlHeight:n,controlHeightLG:c,controlHeightSM:i}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:a},b(n)),["".concat(t).concat(t,"-circle")]:{borderRadius:"50%"},["".concat(t).concat(t,"-lg")]:Object.assign({},b(c)),["".concat(t).concat(t,"-sm")]:Object.assign({},b(i))}},k=e=>{let{controlHeight:t,borderRadiusSM:a,skeletonInputCls:n,controlHeightLG:c,controlHeightSM:i,gradientFromColor:l,calc:o}=e;return{[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:a},p(t,o)),["".concat(n,"-lg")]:Object.assign({},p(c,o)),["".concat(n,"-sm")]:Object.assign({},p(i,o))}},j=e=>Object.assign({width:e},m(e)),v=e=>{let{skeletonImageCls:t,imageSizeBase:a,gradientFromColor:n,borderRadiusSM:c,calc:i}=e;return{[t]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:n,borderRadius:c},j(i(a).mul(2).equal())),{["".concat(t,"-path")]:{fill:"#bfbfbf"},["".concat(t,"-svg")]:Object.assign(Object.assign({},j(a)),{maxWidth:i(a).mul(4).equal(),maxHeight:i(a).mul(4).equal()}),["".concat(t,"-svg").concat(t,"-svg-circle")]:{borderRadius:"50%"}}),["".concat(t).concat(t,"-circle")]:{borderRadius:"50%"}}},f=(e,t,a)=>{let{skeletonButtonCls:n}=e;return{["".concat(a).concat(n,"-circle")]:{width:t,minWidth:t,borderRadius:"50%"},["".concat(a).concat(n,"-round")]:{borderRadius:t}}},C=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},m(e)),w=e=>{let{borderRadiusSM:t,skeletonButtonCls:a,controlHeight:n,controlHeightLG:c,controlHeightSM:i,gradientFromColor:l,calc:o}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:t,width:o(n).mul(2).equal(),minWidth:o(n).mul(2).equal()},C(n,o))},f(e,n,a)),{["".concat(a,"-lg")]:Object.assign({},C(c,o))}),f(e,c,"".concat(a,"-lg"))),{["".concat(a,"-sm")]:Object.assign({},C(i,o))}),f(e,i,"".concat(a,"-sm")))},E=e=>{let{componentCls:t,skeletonAvatarCls:a,skeletonTitleCls:n,skeletonParagraphCls:c,skeletonButtonCls:i,skeletonInputCls:l,skeletonImageCls:o,controlHeight:s,controlHeightLG:r,controlHeightSM:g,gradientFromColor:d,padding:u,marginSM:m,borderRadius:p,titleHeight:j,blockRadius:f,paragraphLiHeight:C,controlHeightXS:E,paragraphMarginTop:x}=e;return{[t]:{display:"table",width:"100%",["".concat(t,"-header")]:{display:"table-cell",paddingInlineEnd:u,verticalAlign:"top",[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:d},b(s)),["".concat(a,"-circle")]:{borderRadius:"50%"},["".concat(a,"-lg")]:Object.assign({},b(r)),["".concat(a,"-sm")]:Object.assign({},b(g))},["".concat(t,"-content")]:{display:"table-cell",width:"100%",verticalAlign:"top",[n]:{width:"100%",height:j,background:d,borderRadius:f,["+ ".concat(c)]:{marginBlockStart:g}},[c]:{padding:0,"> li":{width:"100%",height:C,listStyle:"none",background:d,borderRadius:f,"+ li":{marginBlockStart:E}}},["".concat(c,"> li:last-child:not(:first-child):not(:nth-child(2))")]:{width:"61%"}},["&-round ".concat(t,"-content")]:{["".concat(n,", ").concat(c," > li")]:{borderRadius:p}}},["".concat(t,"-with-avatar ").concat(t,"-content")]:{[n]:{marginBlockStart:m,["+ ".concat(c)]:{marginBlockStart:x}}},["".concat(t).concat(t,"-element")]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},w(e)),O(e)),k(e)),v(e)),["".concat(t).concat(t,"-block")]:{width:"100%",[i]:{width:"100%"},[l]:{width:"100%"}},["".concat(t).concat(t,"-active")]:{["\n        ".concat(n,",\n        ").concat(c," > li,\n        ").concat(a,",\n        ").concat(i,",\n        ").concat(l,",\n        ").concat(o,"\n      ")]:Object.assign({},h(e))}}},x=(0,g.OF)("Skeleton",e=>{let{componentCls:t,calc:a}=e;return[E((0,d.oX)(e,{skeletonAvatarCls:"".concat(t,"-avatar"),skeletonTitleCls:"".concat(t,"-title"),skeletonParagraphCls:"".concat(t,"-paragraph"),skeletonButtonCls:"".concat(t,"-button"),skeletonInputCls:"".concat(t,"-input"),skeletonImageCls:"".concat(t,"-image"),imageSizeBase:a(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:"linear-gradient(90deg, ".concat(e.gradientFromColor," 25%, ").concat(e.gradientToColor," 37%, ").concat(e.gradientFromColor," 63%)"),skeletonLoadingMotionDuration:"1.4s"}))]},e=>{let{colorFillContent:t,colorFill:a}=e;return{color:t,colorGradientEnd:a,gradientFromColor:t,gradientToColor:a,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}},{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),y=(e,t)=>{let{width:a,rows:n=2}=t;return Array.isArray(a)?a[e]:n-1===e?a:void 0},q=e=>{let{prefixCls:t,className:a,style:c,rows:l=0}=e,o=Array.from({length:l}).map((t,a)=>n.createElement("li",{key:a,style:{width:y(a,e)}}));return n.createElement("ul",{className:i()(t,a),style:c},o)},N=e=>{let{prefixCls:t,className:a,width:c,style:l}=e;return n.createElement("h3",{className:i()(t,a),style:Object.assign({width:c},l)})};function A(e){return e&&"object"==typeof e?e:{}}let R=e=>{let{prefixCls:t,loading:a,className:c,rootClassName:o,style:r,children:g,avatar:d=!1,title:u=!0,paragraph:m=!0,active:b,round:h}=e,{getPrefixCls:p,direction:O,className:k,style:j}=(0,l.TP)("skeleton"),v=p("skeleton",t),[f,C,w]=x(v);if(a||!("loading"in e)){let e,t;let a=!!d,l=!!u,g=!!m;if(a){let t=Object.assign(Object.assign({prefixCls:"".concat(v,"-avatar")},l&&!g?{size:"large",shape:"square"}:{size:"large",shape:"circle"}),A(d));e=n.createElement("div",{className:"".concat(v,"-header")},n.createElement(s,Object.assign({},t)))}if(l||g){let e,c;if(l){let t=Object.assign(Object.assign({prefixCls:"".concat(v,"-title")},function(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}(a,g)),A(u));e=n.createElement(N,Object.assign({},t))}if(g){let e=Object.assign(Object.assign({prefixCls:"".concat(v,"-paragraph")},function(e,t){let a={};return e&&t||(a.width="61%"),!e&&t?a.rows=3:a.rows=2,a}(a,l)),A(m));c=n.createElement(q,Object.assign({},e))}t=n.createElement("div",{className:"".concat(v,"-content")},e,c)}let p=i()(v,{["".concat(v,"-with-avatar")]:a,["".concat(v,"-active")]:b,["".concat(v,"-rtl")]:"rtl"===O,["".concat(v,"-round")]:h},k,c,o,C,w);return f(n.createElement("div",{className:p,style:Object.assign(Object.assign({},j),r)},e,t))}return null!=g?g:null};R.Button=e=>{let{prefixCls:t,className:a,rootClassName:c,active:r,block:g=!1,size:d="default"}=e,{getPrefixCls:u}=n.useContext(l.QO),m=u("skeleton",t),[b,h,p]=x(m),O=(0,o.A)(e,["prefixCls"]),k=i()(m,"".concat(m,"-element"),{["".concat(m,"-active")]:r,["".concat(m,"-block")]:g},a,c,h,p);return b(n.createElement("div",{className:k},n.createElement(s,Object.assign({prefixCls:"".concat(m,"-button"),size:d},O))))},R.Avatar=e=>{let{prefixCls:t,className:a,rootClassName:c,active:r,shape:g="circle",size:d="default"}=e,{getPrefixCls:u}=n.useContext(l.QO),m=u("skeleton",t),[b,h,p]=x(m),O=(0,o.A)(e,["prefixCls","className"]),k=i()(m,"".concat(m,"-element"),{["".concat(m,"-active")]:r},a,c,h,p);return b(n.createElement("div",{className:k},n.createElement(s,Object.assign({prefixCls:"".concat(m,"-avatar"),shape:g,size:d},O))))},R.Input=e=>{let{prefixCls:t,className:a,rootClassName:c,active:r,block:g,size:d="default"}=e,{getPrefixCls:u}=n.useContext(l.QO),m=u("skeleton",t),[b,h,p]=x(m),O=(0,o.A)(e,["prefixCls"]),k=i()(m,"".concat(m,"-element"),{["".concat(m,"-active")]:r,["".concat(m,"-block")]:g},a,c,h,p);return b(n.createElement("div",{className:k},n.createElement(s,Object.assign({prefixCls:"".concat(m,"-input"),size:d},O))))},R.Image=e=>{let{prefixCls:t,className:a,rootClassName:c,style:o,active:s}=e,{getPrefixCls:r}=n.useContext(l.QO),g=r("skeleton",t),[d,u,m]=x(g),b=i()(g,"".concat(g,"-element"),{["".concat(g,"-active")]:s},a,c,u,m);return d(n.createElement("div",{className:b},n.createElement("div",{className:i()("".concat(g,"-image"),a),style:o},n.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:"".concat(g,"-image-svg")},n.createElement("title",null,"Image placeholder"),n.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:"".concat(g,"-image-path")})))))},R.Node=e=>{let{prefixCls:t,className:a,rootClassName:c,style:o,active:s,children:r}=e,{getPrefixCls:g}=n.useContext(l.QO),d=g("skeleton",t),[u,m,b]=x(d),h=i()(d,"".concat(d,"-element"),{["".concat(d,"-active")]:s},m,a,c,b);return u(n.createElement("div",{className:h},n.createElement("div",{className:i()("".concat(d,"-image"),a),style:o},r)))};let z=R}}]);
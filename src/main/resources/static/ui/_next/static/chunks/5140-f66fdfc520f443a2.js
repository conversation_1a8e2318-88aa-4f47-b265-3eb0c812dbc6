"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5140],{10170:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(95155),n=r(41657);function o(){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,a.jsx)("path",{d:"M8.75033 15.8334C12.6623 15.8334 15.8337 12.6621 15.8337 8.75008C15.8337 4.83808 12.6623 1.66675 8.75033 1.66675C4.83833 1.66675 1.66699 4.83808 1.66699 8.75008C1.66699 12.6621 4.83833 15.8334 8.75033 15.8334Z",stroke:"black",strokeWidth:"1.25",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M11.1066 5.97629C10.5034 5.37308 9.67008 5 8.74958 5C7.82912 5 6.99579 5.37308 6.39258 5.97629",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M13.8428 13.8423L17.3783 17.3778",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}function l(e){return(0,a.jsx)(n.A,{placeholder:"搜索",prefix:(0,a.jsx)(o,{}),style:{width:"300px"},...e})}},35140:(e,t,r)=>{r.r(t),r.d(t,{TableTheme:()=>b,default:()=>k,sortIcon:()=>g});var a=r(95155),n=r(95988),o=r(54031),l=r(36564),s=r(81488),d=r(71126),i=r(50759),u=r(44549),c=r(59276),p=r(28041),m=r(46742),h=r(68874),v=r(76046),x=r(12115);let{Content:y}=c.A,g=()=>(0,a.jsx)("img",{src:(0,s.I)({url:"/sort.svg",isPage:!1}),alt:"sort",style:{width:14,height:14}}),b={headerBg:"#171F2D",headerColor:"#fff",headerSortHoverBg:"#171F2D",headerSortActiveBg:"#171F2D"},j=[{title:"员工工号",dataIndex:"jobNumber",render:(e,t)=>{var r;return null!==(r=t.id)&&void 0!==r?r:"-"}},{title:"姓名",dataIndex:"name",render:(e,t)=>{var r;return null!==(r=t.name)&&void 0!==r?r:"-"}},{title:"一级部门",dataIndex:"dept1",render:(e,t)=>{var r;return null!==(r=t.dept1)&&void 0!==r?r:"-"}},{title:"标准岗位",dataIndex:"standardPost",render:(e,t)=>{var r;return null!==(r=t.standardPost)&&void 0!==r?r:"-"}},{title:"岗位号码",dataIndex:"postNumber",render:(e,t)=>{var r;return null!==(r=t.postNumber)&&void 0!==r?r:"-"}},{title:"个人岗位",dataIndex:"personalPost",render:(e,t)=>{var r;return null!==(r=t.personalPost)&&void 0!==r?r:"-"}},{title:"相关访谈数",dataIndex:"taskNumber",sorter:!0,sortIcon:g,render:(e,t)=>{var r;return null!==(r=t.taskNumber)&&void 0!==r?r:"-"}},{title:"已完成访谈数",dataIndex:"completeTaskNumber",sorter:!0,sortIcon:g,render:(e,t)=>{var r;return null!==(r=t.completeTaskNumber)&&void 0!==r?r:"-"}},{title:" ",dataIndex:"opt",render:()=>(0,a.jsx)(u.A,{})}],A=[{title:"一级部门",dataIndex:"dept1",render:(e,t)=>{var r;return null!==(r=t.dept1)&&void 0!==r?r:"-"}},{title:"标岗",dataIndex:"standardPost",render:(e,t)=>{var r;return null!==(r=t.standardPost)&&void 0!==r?r:"-"}},{title:"岗位ID",dataIndex:"postNumber",render:(e,t)=>{var r;return null!==(r=t.postNumber)&&void 0!==r?r:"-"}},{title:"相关访谈总量",dataIndex:"taskNumber",sorter:!0,sortIcon:g,render:(e,t)=>{var r;return null!==(r=t.taskNumber)&&void 0!==r?r:"-"}},{title:"已完成访谈数",dataIndex:"completeTaskNumber",sorter:!0,sortIcon:g,render:(e,t)=>{var r;return null!==(r=t.completeTaskNumber)&&void 0!==r?r:"-"}},{title:" ",dataIndex:"opt",render:()=>(0,a.jsx)(u.A,{})}];function k(){let[e,t]=(0,x.useState)("employee"),[r,u]=(0,x.useState)({}),[c,g]=(0,x.useState)({current:1,pageSize:10}),[b,k]=(0,x.useState)(0),{runAsync:S,loading:f}=(0,i.YJ)(),{runAsync:C,loading:I}=(0,i.FO)(),[N,T]=(0,x.useState)(""),[P,w]=(0,x.useState)(""),[E,L]=(0,x.useState)(""),F=(0,v.useRouter)(),[z,D]=(0,x.useState)([]),[J,R]=(0,x.useState)([]),B="employee"===e;return(0,x.useEffect)(()=>{let t={page:c.current,pageSize:c.pageSize,keyword:E,dept:N,postNumber:P};r.field&&r.order&&(t.sort=r.field,t.order="ascend"===r.order?"asc":"desc"),B?S(t).then(e=>{e.success?(D(e.data.records),k(e.data.total)):"error"in e&&p.Ay.error(e.error.message)}):"position"===e&&C(t).then(e=>{e.success?(R(e.data.records),k(e.data.total)):"error"in e&&p.Ay.error(e.error.message)})},[c,r,e,E,N,P,S,C]),(0,a.jsxs)(y,{children:[(0,a.jsx)(l.A,{title:d.u4[d.GT.Report].header.title,description:d.u4[d.GT.Report].header.description,onSearch:e=>{L(e),g(e=>({...e,current:1}))},extraFilter:[(0,a.jsx)(o.A,{value:N,onChange:T,options:[],placeholder:"全部部门"},"department"),(0,a.jsx)(o.A,{value:P,onChange:w,options:[],placeholder:"全部标岗"},"standardPosition")],middleContent:(0,a.jsx)(n.A,{items:[{key:"employee",label:"员工分析报告"},{key:"position",label:"岗位分析报告"}],activeKey:e,onChange:e=>{t(e),g({current:1,pageSize:10}),u({})}})}),(0,a.jsx)(m.A.Title,{level:5,style:{marginTop:"24px"},children:B?"全部员工（".concat(b,"）"):"全部岗位（".concat(b,"）")}),(0,a.jsx)(h.A,{rowKey:B?"id":"postNumber",size:"small",columns:B?j:A,dataSource:B?z:J,loading:B?f:I,pagination:{...c,total:b,showQuickJumper:!0,showSizeChanger:!1,showTotal:e=>"共 ".concat(e," 条")},onRow:e=>({onClick:()=>{F.push((0,s.I)({url:d.Nv.EmployeeTaskReport,params:B?{type:"user",employeeId:e.id}:{type:"task",standardPost:e.standardPost,postNumber:e.postNumber,dept1:e.dept1,dept2:e.dept2,dept3:e.dept3}}))},style:{cursor:B?"pointer":void 0}}),onChange:(e,t,r)=>{Array.isArray(r)||(g({current:e.current,pageSize:e.pageSize}),u({field:r.field,order:r.order}))},scroll:{x:1e3},style:{marginTop:24}})]})}},35594:(e,t,r)=>{r.d(t,{Jt:()=>l,bE:()=>s,yH:()=>i,yJ:()=>d});var a=r(43932),n=r.n(a);function o(e,t){return Promise.resolve(n().ajax({method:e,url:t.path||t.url,data:"GET"===e?t.data:JSON.stringify(t.data),contentType:"application/json;charset=UTF-8"}))}let l=e=>o("GET",e),s=e=>o("POST",e),d=e=>o("PUT",e),i=e=>o("DELETE",e)},36564:(e,t,r)=>{r.d(t,{A:()=>v});var a=r(95155),n=r(96030),o=r(46742),l=r(22810),s=r(2796),d=r(11432),i=r(79005),u=r(68773),c=r(12115),p=r(10170);let{Title:m,Text:h}=o.A;function v(e){let{title:t,description:r,btn:o,onSearch:v,restSearch:x,customBtn:y,extraFilter:g,middleContent:b}=e,[j,A]=(0,c.useState)(""),{text:k,onClick:S,loading:f}=null!=o?o:{};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.A,{justify:"space-between",style:{marginBottom:"24px"},align:"middle",children:[(0,a.jsxs)(s.A,{children:[(0,a.jsx)(m,{level:2,children:t}),(0,a.jsx)(h,{type:"secondary",children:r})]}),(0,a.jsxs)(d.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:[y,o&&(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(n.A,{}),onClick:S,loading:f,children:k})]})]}),b,(0,a.jsxs)(u.A,{size:"middle",children:[v&&(0,a.jsx)(p.A,{onChange:e=>{A(e.target.value)},onPressEnter:()=>{v(j)},...x}),g]})]})}},50759:(e,t,r)=>{r.d(t,{dA:()=>d,YJ:()=>l,FO:()=>s});var a=r(69653),n=r(35594);let o={getEmployeeList:e=>(0,n.Jt)({url:"/api/employee",data:e}),getPositionReportList:e=>(0,n.Jt)({path:"/api/employee/postNumber",data:e}),getEmployeeDetail:e=>(0,n.Jt)({url:"/api/employee/".concat(e)})},l=()=>(0,a.A)(o.getEmployeeList,{manual:!0}),s=()=>(0,a.A)(o.getPositionReportList,{manual:!0}),d=()=>(0,a.A)(o.getEmployeeDetail,{manual:!0})},54031:(e,t,r)=>{r.d(t,{A:()=>d});var a=r(95155),n=r(10593),o=r(9365),l=r(7663),s=r(68773);r(12115);let d=e=>{var t,r;let{value:d,onChange:i,options:u,placeholder:c}=e,p=[{label:c,value:""},{label:(0,a.jsx)(o.A,{style:{margin:0}}),value:"--",disabled:!0},...u].map(e=>({key:e.value,label:e.label,disabled:e.disabled}));return(0,a.jsx)(l.A,{trigger:["click"],menu:{items:p,selectable:!0,selectedKeys:[d],onClick:e=>{let{key:t}=e;return i(t)}},children:(0,a.jsxs)(s.A,{style:{color:d?"#69b1ff":"#000",cursor:"pointer"},children:[null!==(r=null===(t=p.find(e=>e.key===d))||void 0===t?void 0:t.label)&&void 0!==r?r:c,(0,a.jsx)(n.A,{})]})})}},81488:(e,t,r)=>{r.d(t,{I:()=>a,O:()=>n});let a=e=>{let{url:t,params:r,isPage:a=!0}=e,n=t;return n="/ui".concat(n),a&&(n="".concat(n,".html")),r&&(n="".concat(n,"?").concat(new URLSearchParams(r).toString())),n},n=(e,t)=>t?(e/t*100).toFixed(2):"-"},95988:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(95155),n=r(11432),o=r(99907);function l(e){let{children:t,...r}=e;return(0,a.jsx)(n.Ay,{theme:{components:{Tabs:{inkBarColor:"#171F2D",itemHoverColor:"#000",itemSelectedColor:"#000"}}},children:(0,a.jsx)(o.A,{...r,children:t})})}}}]);
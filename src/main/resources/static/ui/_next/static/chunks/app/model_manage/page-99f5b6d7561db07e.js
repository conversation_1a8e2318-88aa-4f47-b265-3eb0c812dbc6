(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5462],{3132:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>B});var l=t(95155),n=t(32853),s=t(36564),i=t(6521),a=t(71126),o=t(30259),d=t(96030),c=t(41379),u=t(85407),m=t(12115);let p={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888.3 693.2c-42.5-24.6-94.3-18-129.2 12.8l-53-30.7V523.6c0-15.7-8.4-30.3-22-38.1l-136-78.3v-67.1c44.2-15 76-56.8 76-106.1 0-61.9-50.1-112-112-112s-112 50.1-112 112c0 49.3 31.8 91.1 76 106.1v67.1l-136 78.3c-13.6 7.8-22 22.4-22 38.1v151.6l-53 30.7c-34.9-30.8-86.8-37.4-129.2-12.8-53.5 31-71.7 99.4-41 152.9 30.8 53.5 98.9 71.9 152.2 41 42.5-24.6 62.7-73 53.6-118.8l48.7-28.3 140.6 81c6.8 3.9 14.4 5.9 22 5.9s15.2-2 22-5.9L674.5 740l48.7 28.3c-9.1 45.7 11.2 94.2 53.6 118.8 53.3 30.9 121.5 12.6 152.2-41 30.8-53.6 12.6-122-40.7-152.9zm-673 138.4a47.6 47.6 0 01-65.2-17.6c-13.2-22.9-5.4-52.3 17.5-65.5a47.6 47.6 0 0165.2 17.6c13.2 22.9 5.4 52.3-17.5 65.5zM522 463.8zM464 234a48.01 48.01 0 0196 0 48.01 48.01 0 01-96 0zm170 446.2l-122 70.3-122-70.3V539.8l122-70.3 122 70.3v140.4zm239.9 133.9c-13.2 22.9-42.4 30.8-65.2 17.6-22.8-13.2-30.7-42.6-17.5-65.5s42.4-30.8 65.2-17.6c22.9 13.2 30.7 42.5 17.5 65.5z"}}]},name:"deployment-unit",theme:"outlined"};var h=t(84021),x=m.forwardRef(function(e,r){return m.createElement(h.A,(0,u.A)({},e,{ref:r,icon:p}))}),A=t(59276),j=t(46742),v=t(64787),y=t(32392),g=t(21614),f=t(41657),k=t(48904),b=t(6457),L=t(78444),P=t(2796),w=t(71349),C=t(68773),E=t(22810),T=t(3293),F=t.n(T);let{Content:M}=A.A,{Text:z,Title:I}=j.A,S={ArkV3:[{name:"接入点ID",field:"endPointId"}],CustomLLM:[{name:"第三方模型名称",field:["providerParams","modelName"]},{name:"第三方模型URL",field:["providerParams","apiUrl"],inVisible:!0},{name:"第三方模型APIkey",field:["providerParams","apiKey"],inVisible:!0}]};function B(){let{message:e}=v.A.useApp(),[r,t]=(0,m.useState)(""),{runAsync:u}=(0,o.zK)(),{runAsync:p,loading:h}=(0,o.FL)(),{runAsync:A,loading:j}=(0,o.gI)(),{runAsync:T,loading:z}=(0,o.o3)(),[B,D]=(0,m.useState)(0),[O]=y.A.useForm(),_=y.A.useWatch("icon",O),H=y.A.useWatch("provider",O),{data:J,loading:N}=(0,o.hv)(),U=(r,t)=>{var s;let i="create"===r;return(0,l.jsx)(n.A,{btnText:i?"创建新模型":"编辑模型",title:i?"创建新模型":"编辑模型",onOk:async()=>{let r=O.getFieldsValue(),l=i?await p(r):await T(t.id,r);return l.success?(e.success("创建成功"),D(e=>e+1),!0):(e.error(l.error.message),!1)},btnExtraEvent:()=>{i?O.resetFields():O.setFieldsValue(t)},btnProps:{loading:h||z,type:i?"primary":"link",icon:i?(0,l.jsx)(d.A,{}):null},modalProps:{width:700},children:(0,l.jsxs)(y.A,{form:O,wrapperCol:{span:18},labelCol:{span:6},children:[(0,l.jsx)(y.A.Item,{label:"模型提供商",name:"provider",children:(0,l.jsx)(g.A,{options:null!==(s=J.map(e=>({label:e.desc,value:e.code})))&&void 0!==s?s:[],loading:N})}),(0,l.jsx)(y.A.Item,{label:"模型名称",name:"name",children:(0,l.jsx)(f.A,{})}),H&&S[H].map(e=>(0,l.jsx)(y.A.Item,{label:e.name,name:e.field,children:(0,l.jsx)(f.A,{})},e.field)),(0,l.jsx)(y.A.Item,{label:"模型图标",name:"icon",children:(0,l.jsx)(k.A,{showUploadList:!1,maxCount:1,action:"/api/image/upload",accept:"image/*",beforeUpload:r=>{let t=r.size/1024/1024<2;return t||e.error("图片大小不超过2MB"),t},onChange:async r=>{"done"===r.file.status?r.file.response.success?O.setFieldsValue({icon:r.file.response.data.url}):e.error("".concat(r.file.name," 图片上传失败")):"error"===r.file.status&&e.error("".concat(r.file.name," 图片上传失败"))},children:(0,l.jsx)(b.A,{title:"点击上传图片",children:(0,l.jsx)(L.A,{size:40,icon:(0,l.jsx)(c.A,{}),src:_})})})}),(0,l.jsx)(y.A.Item,{label:"备注",name:"remark",children:(0,l.jsx)(f.A,{})})]})})};return(0,l.jsxs)(M,{children:[(0,l.jsx)(s.A,{title:a.u4[a.GT.ModelManage].header.title,description:a.u4[a.GT.ModelManage].header.description,customBtn:U("create"),onSearch:t}),(0,l.jsx)(i.A,{getList:u,gutter:[16,16],renderItem:r=>{var t;return(0,l.jsx)(P.A,{span:8,children:(0,l.jsx)(w.A,{actions:[U("edit",r),(0,l.jsx)(n.A,{btnText:"删除",title:"删除模型",description:"模型一旦删除，无法恢复",btnProps:{loading:j,type:"link"},onOk:async()=>{let t=await A(r.id);return t.success?(e.success("删除成功"),D(e=>e+1),!0):(e.error(t.error.message),!1)}},"delete")],children:(0,l.jsxs)(C.A,{direction:"vertical",size:"small",style:{display:"flex"},children:[(0,l.jsx)(E.A,{align:"middle",style:{marginBottom:"8px"},children:(0,l.jsxs)(C.A,{children:[(0,l.jsx)(L.A,{size:30,icon:(0,l.jsx)(x,{}),src:r.icon}),(0,l.jsx)(I,{level:5,style:{marginBottom:"0"},children:r.name})]})}),(0,l.jsx)(V,{label:"模型提供商",value:null===(t=J.find(e=>e.code===r.provider))||void 0===t?void 0:t.desc}),r.provider&&S[r.provider].map(e=>{if(e.inVisible)return null;let t=F()(e.field)?e.field.reduce((e,r)=>e[r],r):r[e.field];return(0,l.jsx)(V,{label:e.name,value:t},e.field)}),(0,l.jsx)(V,{label:"备注",value:r.remark})]})})},r.id)},params:{keyword:r},refreshDeps:[r,B]})]})}let V=e=>{let{label:r,value:t}=e;return(0,l.jsx)(E.A,{gutter:8,wrap:!1,children:(0,l.jsxs)(z,{ellipsis:{tooltip:!0},children:[r,": ",null!=t?t:"-"]})})}},3293:e=>{var r=Array.isArray;e.exports=r},3301:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var l=t(95155),n=t(11432),s=t(79005);function i(e){let{children:r,...t}=e;return(0,l.jsx)(n.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847",defaultBg:"none",defaultHoverBg:"none",colorLink:"#171F2D",colorLinkHover:"#303847"}}},children:(0,l.jsx)(s.Ay,{...t,children:r})})}},6521:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var l=t(95155),n=t(72093),s=t(22810),i=t(2796),a=t(53096),o=t(46742),d=t(12115),c=t(3301);function u(e){let{renderItem:r,pageSize:t=12,getList:u,params:m,refreshDeps:p,gutter:h=[24,24],maxHeight:x}=e,[A,j]=(0,d.useState)(!1),[v,y]=(0,d.useState)(1),[g,f]=(0,d.useState)(!0),[k,b]=(0,d.useState)([]),L=async e=>{if(!u)return;j(!0);let r=await u({page:e,pageSize:t,...m});r.success&&(b(1===e?r.data.records:[...k,...r.data.records]),f(e*t<r.data.total)),j(!1)};return(0,d.useEffect)(()=>{(v>1||!p)&&L(v)},[v]),(0,d.useEffect)(()=>{(null==p?void 0:p.length)>0&&(f(!0),y(1),L(1))},[null==p?void 0:p.join(",")]),(0,l.jsxs)(n.A,{spinning:A,children:[(0,l.jsx)(s.A,{gutter:h,style:{marginTop:"24px",maxHeight:x,overflow:"auto"},children:k.length?k.map(e=>r(e)):(0,l.jsx)(i.A,{span:24,children:(0,l.jsx)(a.A,{image:a.A.PRESENTED_IMAGE_SIMPLE})})}),k.length>0&&(0,l.jsx)(s.A,{justify:"center",style:{marginTop:24},children:(0,l.jsx)(i.A,{children:g?(0,l.jsx)(c.A,{loading:A,onClick:()=>y(v+1),type:"link",children:"加载更多"}):(0,l.jsx)(o.A.Text,{type:"secondary",children:"已经到底了"})})})]})}},10170:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var l=t(95155),n=t(41657);function s(){return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,l.jsx)("path",{d:"M8.75033 15.8334C12.6623 15.8334 15.8337 12.6621 15.8337 8.75008C15.8337 4.83808 12.6623 1.66675 8.75033 1.66675C4.83833 1.66675 1.66699 4.83808 1.66699 8.75008C1.66699 12.6621 4.83833 15.8334 8.75033 15.8334Z",stroke:"black",strokeWidth:"1.25",strokeLinejoin:"round"}),(0,l.jsx)("path",{d:"M11.1066 5.97629C10.5034 5.37308 9.67008 5 8.74958 5C7.82912 5 6.99579 5.37308 6.39258 5.97629",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),(0,l.jsx)("path",{d:"M13.8428 13.8423L17.3783 17.3778",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}function i(e){return(0,l.jsx)(n.A,{placeholder:"搜索",prefix:(0,l.jsx)(s,{}),style:{width:"300px"},...e})}},30259:(e,r,t)=>{"use strict";t.d(r,{FL:()=>o,gI:()=>c,hv:()=>u,zK:()=>a,o3:()=>d});var l=t(69653),n=t(35594),s=t(90603);let i={getLlmList:e=>(0,n.Jt)({url:"/api/llms",data:e?{...s.u,...e}:s.u}),createLlm:e=>(0,n.bE)({url:"/api/llms",data:e}),updateLlm:(e,r)=>(0,n.yJ)({url:"/api/llms/".concat(e),data:r}),deleteLlm:e=>(0,n.yH)({url:"/api/llms/".concat(e)}),getLlmProviders:()=>(0,n.Jt)({url:"/api/llms/providers"})},a=()=>(0,l.A)(i.getLlmList,{manual:!0}),o=()=>(0,l.A)(i.createLlm,{manual:!0}),d=()=>(0,l.A)(i.updateLlm,{manual:!0}),c=()=>(0,l.A)(i.deleteLlm,{manual:!0}),u=()=>{var e;let r=(0,l.A)(i.getLlmProviders,{});return{...r,data:(null==r?void 0:null===(e=r.data)||void 0===e?void 0:e.success)?r.data.data:[]}}},32853:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var l=t(95155),n=t(79005),s=t(12115),i=t(47956);let a=e=>{let{title:r,btnProps:t,btnExtraEvent:a,onOk:o,btnText:d,description:c,children:u,modalProps:m}=e,[p,h]=(0,s.useState)(!1);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.Ay,{loading:null==t?void 0:t.loading,onClick:()=>{h(!0),null==a||a()},...t,children:d}),p&&(0,l.jsx)(i.A,{title:r,open:p,onCancel:()=>h(!1),onOk:async()=>{await (null==o?void 0:o())&&h(!1)},description:c,...m,children:u})]})}},35594:(e,r,t)=>{"use strict";t.d(r,{Jt:()=>i,bE:()=>a,yH:()=>d,yJ:()=>o});var l=t(43932),n=t.n(l);function s(e,r){return Promise.resolve(n().ajax({method:e,url:r.path||r.url,data:"GET"===e?r.data:JSON.stringify(r.data),contentType:"application/json;charset=UTF-8"}))}let i=e=>s("GET",e),a=e=>s("POST",e),o=e=>s("PUT",e),d=e=>s("DELETE",e)},36564:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var l=t(95155),n=t(96030),s=t(46742),i=t(22810),a=t(2796),o=t(11432),d=t(79005),c=t(68773),u=t(12115),m=t(10170);let{Title:p,Text:h}=s.A;function x(e){let{title:r,description:t,btn:s,onSearch:x,restSearch:A,customBtn:j,extraFilter:v,middleContent:y}=e,[g,f]=(0,u.useState)(""),{text:k,onClick:b,loading:L}=null!=s?s:{};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(i.A,{justify:"space-between",style:{marginBottom:"24px"},align:"middle",children:[(0,l.jsxs)(a.A,{children:[(0,l.jsx)(p,{level:2,children:r}),(0,l.jsx)(h,{type:"secondary",children:t})]}),(0,l.jsxs)(o.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:[j,s&&(0,l.jsx)(d.Ay,{type:"primary",icon:(0,l.jsx)(n.A,{}),onClick:b,loading:L,children:k})]})]}),y,(0,l.jsxs)(c.A,{size:"middle",children:[x&&(0,l.jsx)(m.A,{onChange:e=>{f(e.target.value)},onPressEnter:()=>{x(g)},...A}),v]})]})}},47956:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var l=t(95155),n=t(46742),s=t(21382),i=t(22810),a=t(11432),o=t(68773),d=t(79005);let{Text:c}=n.A,u=e=>{let{open:r,title:t,onOk:n,onCancel:u,description:m,children:p,cancelText:h="取消",okText:x="确定",...A}=e;return(0,l.jsxs)(s.A,{title:t,open:r,onCancel:u,onOk:n,footer:(0,l.jsx)(i.A,{justify:"end",children:(0,l.jsx)(a.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:(0,l.jsxs)(o.A,{children:[h&&(0,l.jsx)(d.Ay,{onClick:u,style:{border:"none"},children:h}),x&&(0,l.jsx)(d.Ay,{type:"primary",onClick:n,children:x})]})})}),...A,children:[m&&(0,l.jsx)(c,{type:"secondary",children:m}),p]})}},70792:(e,r,t)=>{Promise.resolve().then(t.bind(t,3132))},90603:(e,r,t)=>{"use strict";t.d(r,{u:()=>l}),t(35594);let l={order:"desc",sort:"updateTime"}}},e=>{var r=r=>e(e.s=r);e.O(0,[838,3740,2211,6222,2602,3520,9907,3288,1509,1349,9786,5585,4787,2392,2342,4338,1126,8441,6587,7358],()=>r(70792)),_N_E=e.O()}]);
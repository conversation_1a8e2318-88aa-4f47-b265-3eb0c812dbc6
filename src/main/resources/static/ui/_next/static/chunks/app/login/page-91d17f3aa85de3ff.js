(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{6187:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=e=>({[e.componentCls]:{["".concat(e.antCls,"-motion-collapse-legacy")]:{overflow:"hidden","&-active":{transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}},["".concat(e.antCls,"-motion-collapse")]:{overflow:"hidden",transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}}})},27527:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>g});var r=n(95155),s=n(81488),o=n(71126),a=n(32392),i=n(28041),c=n(22810),l=n(71349),u=n(41657),d=n(79005),h=n(76046),m=n(12115);function g(){let[e]=a.A.useForm(),t=(0,h.useRouter)(),[n,g]=(0,m.useState)(!1),p=async e=>{try{g(!0);let r=await fetch("/api/user/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await r.json();if(a.success)i.Ay.success("登录成功"),t.push((0,s.I)({url:o.Nv.RobotList}));else{var n;g(!1),i.Ay.error((null===(n=a.error)||void 0===n?void 0:n.message)||"登录失败")}}catch(e){g(!1),i.Ay.error("登录失败")}finally{}};return(0,r.jsx)(c.A,{align:"middle",style:{width:"100vw",height:"100vh",background:"url(".concat((0,s.I)({url:"/LoginBg.png",isPage:!1}),") center center / cover no-repeat")},children:(0,r.jsx)(l.A,{style:{marginLeft:"300px",width:"500px"},styles:{body:{padding:"24px"},header:{padding:"0",paddingLeft:"14px"}},title:(0,r.jsx)("img",{src:(0,s.I)({url:"/logo.png",isPage:!1}),style:{height:"80px"}}),children:(0,r.jsxs)(a.A,{form:e,layout:"vertical",onFinish:p,children:[(0,r.jsx)(a.A.Item,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名"}],children:(0,r.jsx)(u.A,{placeholder:"请输入用户名"})}),(0,r.jsx)(a.A.Item,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码"}],children:(0,r.jsx)(u.A.Password,{placeholder:"请输入密码"})}),(0,r.jsx)(a.A.Item,{children:(0,r.jsx)(d.Ay,{type:"primary",block:!0,onClick:e.submit,loading:n,children:"登录"})})]})})})}},28041:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>j});var r=n(39014),s=n(12115),o=n(89842),a=n(31049),i=n(11432),c=n(24330),l=n(1177),u=n(31617),d=n(62155);let h=null,m=e=>e(),g=[],p={};function f(){let{getContainer:e,duration:t,rtl:n,maxCount:r,top:s}=p,o=(null==e?void 0:e())||document.body;return{getContainer:()=>o,duration:t,rtl:n,maxCount:r,top:s}}let y=s.forwardRef((e,t)=>{let{messageConfig:n,sync:r}=e,{getPrefixCls:i}=(0,s.useContext)(a.QO),c=p.prefixCls||i("message"),l=(0,s.useContext)(o.B),[d,h]=(0,u.y)(Object.assign(Object.assign(Object.assign({},n),{prefixCls:c}),l.message));return s.useImperativeHandle(t,()=>{let e=Object.assign({},d);return Object.keys(e).forEach(t=>{e[t]=function(){return r(),d[t].apply(d,arguments)}}),{instance:e,sync:r}}),h}),v=s.forwardRef((e,t)=>{let[n,r]=s.useState(f),o=()=>{r(f)};s.useEffect(o,[]);let a=(0,i.cr)(),c=a.getRootPrefixCls(),l=a.getIconPrefixCls(),u=a.getTheme(),d=s.createElement(y,{ref:t,sync:o,messageConfig:n});return s.createElement(i.Ay,{prefixCls:c,iconPrefixCls:l,theme:u},a.holderRender?a.holderRender(d):d)});function b(){if(!h){let e=document.createDocumentFragment(),t={fragment:e};h=t,m(()=>{(0,c.K)()(s.createElement(v,{ref:e=>{let{instance:n,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=r,b())})}}),e)});return}h.instance&&(g.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":m(()=>{let t=h.instance.open(Object.assign(Object.assign({},p),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":m(()=>{null==h||h.instance.destroy(e.key)});break;default:m(()=>{var n;let s=(n=h.instance)[t].apply(n,(0,r.A)(e.args));null==s||s.then(e.resolve),e.setCloseFn(s)})}}),g=[])}let x={open:function(e){let t=(0,d.E)(t=>{let n;let r={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return g.push(r),()=>{n?m(()=>{n()}):r.skipped=!0}});return b(),t},destroy:e=>{g.push({type:"destroy",key:e}),b()},config:function(e){p=Object.assign(Object.assign({},p),e),m(()=>{var e;null===(e=null==h?void 0:h.sync)||void 0===e||e.call(h)})},useMessage:u.A,_InternalPanelDoNotUseOrYouWillBeFired:l.Ay};["success","info","warning","error","loading"].forEach(e=>{x[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(e,t){(0,i.cr)();let n=(0,d.E)(n=>{let r;let s={type:e,args:t,resolve:n,setCloseFn:e=>{r=e}};return g.push(s),()=>{r?m(()=>{r()}):s.skipped=!0}});return b(),n}(e,n)}});let j=x},76046:(e,t,n)=>{"use strict";var r=n(66658);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useServerInsertedHTML")&&n.d(t,{useServerInsertedHTML:function(){return r.useServerInsertedHTML}})},81488:(e,t,n)=>{"use strict";n.d(t,{I:()=>r,O:()=>s});let r=e=>{let{url:t,params:n,isPage:r=!0}=e,s=t;return s="/ui".concat(s),r&&(s="".concat(s,".html")),n&&(s="".concat(s,"?").concat(new URLSearchParams(n).toString())),s},s=(e,t)=>t?(e/t*100).toFixed(2):"-"},95758:(e,t,n)=>{Promise.resolve().then(n.bind(n,27527))}},e=>{var t=t=>e(e.s=t);e.O(0,[838,2211,6222,9907,3288,1349,2392,1126,8441,6587,7358],()=>t(95758)),_N_E=e.O()}]);
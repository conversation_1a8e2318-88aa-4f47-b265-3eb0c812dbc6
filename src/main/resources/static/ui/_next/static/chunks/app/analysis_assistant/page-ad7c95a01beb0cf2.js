(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6562],{3301:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var n=s(95155),a=s(11432),r=s(79005);function l(e){let{children:t,...s}=e;return(0,n.jsx)(a.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847",defaultBg:"none",defaultHoverBg:"none",colorLink:"#171F2D",colorLinkHover:"#303847"}}},children:(0,n.jsx)(r.Ay,{...s,children:t})})}},6521:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var n=s(95155),a=s(72093),r=s(22810),l=s(2796),i=s(53096),o=s(46742),c=s(12115),d=s(3301);function u(e){let{renderItem:t,pageSize:s=12,getList:u,params:A,refreshDeps:h,gutter:p=[24,24],maxHeight:x}=e,[j,g]=(0,c.useState)(!1),[y,m]=(0,c.useState)(1),[v,f]=(0,c.useState)(!0),[b,_]=(0,c.useState)([]),k=async e=>{if(!u)return;g(!0);let t=await u({page:e,pageSize:s,...A});t.success&&(_(1===e?t.data.records:[...b,...t.data.records]),f(e*s<t.data.total)),g(!1)};return(0,c.useEffect)(()=>{(y>1||!h)&&k(y)},[y]),(0,c.useEffect)(()=>{(null==h?void 0:h.length)>0&&(f(!0),m(1),k(1))},[null==h?void 0:h.join(",")]),(0,n.jsxs)(a.A,{spinning:j,children:[(0,n.jsx)(r.A,{gutter:p,style:{marginTop:"24px",maxHeight:x,overflow:"auto"},children:b.length?b.map(e=>t(e)):(0,n.jsx)(l.A,{span:24,children:(0,n.jsx)(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE})})}),b.length>0&&(0,n.jsx)(r.A,{justify:"center",style:{marginTop:24},children:(0,n.jsx)(l.A,{children:v?(0,n.jsx)(d.A,{loading:j,onClick:()=>m(y+1),type:"link",children:"加载更多"}):(0,n.jsx)(o.A.Text,{type:"secondary",children:"已经到底了"})})})]})}},10170:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var n=s(95155),a=s(41657);function r(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,n.jsx)("path",{d:"M8.75033 15.8334C12.6623 15.8334 15.8337 12.6621 15.8337 8.75008C15.8337 4.83808 12.6623 1.66675 8.75033 1.66675C4.83833 1.66675 1.66699 4.83808 1.66699 8.75008C1.66699 12.6621 4.83833 15.8334 8.75033 15.8334Z",stroke:"black",strokeWidth:"1.25",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M11.1066 5.97629C10.5034 5.37308 9.67008 5 8.74958 5C7.82912 5 6.99579 5.37308 6.39258 5.97629",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M13.8428 13.8423L17.3783 17.3778",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}function l(e){return(0,n.jsx)(a.A,{placeholder:"搜索",prefix:(0,n.jsx)(r,{}),style:{width:"300px"},...e})}},18263:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var n=s(95155),a=s(63056),r=s(54031),l=s(36564),i=s(6521),o=s(81488),c=s(71126),d=s(42592),u=s(59276),A=s(2796),h=s(76046),p=s(12115);let{Content:x}=u.A,j=[{label:"在线",value:"online"},{label:"离线",value:"offline"},{label:"空闲",value:"free"}];function g(){let e=(0,h.useRouter)(),{loading:t,runAsync:s}=(0,d._O)(),[u,g]=(0,p.useState)(""),[y,m]=(0,p.useState)("");return(0,n.jsxs)(x,{children:[(0,n.jsx)(l.A,{title:c.u4[c.GT.AnalysisAssistant].header.title,description:c.u4[c.GT.AnalysisAssistant].header.description,btn:{text:"创建新分析助手",onClick:()=>{e.push((0,o.I)({url:c.Nv.AnalysisAssistantEdit}))},loading:t},onSearch:e=>{g(e)},extraFilter:[(0,n.jsx)(r.A,{value:y,onChange:m,options:j,placeholder:"全部状态"},"status")]}),(0,n.jsx)(i.A,{gutter:[16,16],renderItem:e=>(0,n.jsx)(A.A,{span:6,children:(0,n.jsx)(a.A,{assistant:e})},e.id),getList:s,params:{keyword:u,status:y||void 0},refreshDeps:[u,y]})]})}},24947:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var n=s(95155),a=s(46742),r=s(22810),l=s(2796),i=s(3387),o=s(45100),c=s(95458),d=s.n(c);let{Title:u,Text:A}=a.A;function h(e){let{title:t,badge:s,popProps:a,desc:c}=e;return(0,n.jsxs)("div",{className:d().container,children:[(0,n.jsxs)(r.A,{gutter:8,style:{flexWrap:"nowrap",paddingRight:"44px"},children:[(0,n.jsx)(l.A,{children:(0,n.jsx)(i.A,{...a,children:(0,n.jsx)(o.A,{className:d().tag,color:s.color,children:s.text})})}),(0,n.jsx)(l.A,{children:(0,n.jsx)(u,{level:5,className:d().name,children:t})})]}),(0,n.jsx)(A,{type:"secondary",className:d().description,children:c})]})}},30996:(e,t,s)=>{Promise.resolve().then(s.bind(s,18263))},35594:(e,t,s)=>{"use strict";s.d(t,{Jt:()=>l,bE:()=>i,yH:()=>c,yJ:()=>o});var n=s(43932),a=s.n(n);function r(e,t){return Promise.resolve(a().ajax({method:e,url:t.path||t.url,data:"GET"===e?t.data:JSON.stringify(t.data),contentType:"application/json;charset=UTF-8"}))}let l=e=>r("GET",e),i=e=>r("POST",e),o=e=>r("PUT",e),c=e=>r("DELETE",e)},36564:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var n=s(95155),a=s(96030),r=s(46742),l=s(22810),i=s(2796),o=s(11432),c=s(79005),d=s(68773),u=s(12115),A=s(10170);let{Title:h,Text:p}=r.A;function x(e){let{title:t,description:s,btn:r,onSearch:x,restSearch:j,customBtn:g,extraFilter:y,middleContent:m}=e,[v,f]=(0,u.useState)(""),{text:b,onClick:_,loading:k}=null!=r?r:{};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(l.A,{justify:"space-between",style:{marginBottom:"24px"},align:"middle",children:[(0,n.jsxs)(i.A,{children:[(0,n.jsx)(h,{level:2,children:t}),(0,n.jsx)(p,{type:"secondary",children:s})]}),(0,n.jsxs)(o.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:[g,r&&(0,n.jsx)(c.Ay,{type:"primary",icon:(0,n.jsx)(a.A,{}),onClick:_,loading:k,children:b})]})]}),m,(0,n.jsxs)(d.A,{size:"middle",children:[x&&(0,n.jsx)(A.A,{onChange:e=>{f(e.target.value)},onPressEnter:()=>{x(v)},...j}),y]})]})}},42592:(e,t,s)=>{"use strict";s.d(t,{_O:()=>l,m2:()=>i,Lt:()=>c,HB:()=>d,Fj:()=>o});var n=s(69653),a=s(35594);let r={getAssistantList:e=>(0,a.Jt)({url:"/api/evalapp",data:e}),createAssistant:e=>(0,a.bE)({url:"/api/evalapp",data:e}),updateAssistant:(e,t)=>(0,a.yJ)({url:"/api/evalapp/".concat(t),data:e}),getAssistantDetail:e=>(0,a.Jt)({url:"/api/evalapp/".concat(e)}),deleteAssistant:e=>(0,a.yH)({url:"/api/evalapp/".concat(e)})},l=()=>(0,n.A)(r.getAssistantList,{manual:!0}),i=()=>(0,n.A)(r.createAssistant,{manual:!0}),o=()=>(0,n.A)(r.updateAssistant,{manual:!0}),c=()=>(0,n.A)(r.deleteAssistant,{manual:!0}),d=()=>(0,n.A)(r.getAssistantDetail,{manual:!0})},54031:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var n=s(95155),a=s(10593),r=s(9365),l=s(7663),i=s(68773);s(12115);let o=e=>{var t,s;let{value:o,onChange:c,options:d,placeholder:u}=e,A=[{label:u,value:""},{label:(0,n.jsx)(r.A,{style:{margin:0}}),value:"--",disabled:!0},...d].map(e=>({key:e.value,label:e.label,disabled:e.disabled}));return(0,n.jsx)(l.A,{trigger:["click"],menu:{items:A,selectable:!0,selectedKeys:[o],onClick:e=>{let{key:t}=e;return c(t)}},children:(0,n.jsxs)(i.A,{style:{color:o?"#69b1ff":"#000",cursor:"pointer"},children:[null!==(s=null===(t=A.find(e=>e.key===o))||void 0===t?void 0:t.label)&&void 0!==s?s:u,(0,n.jsx)(a.A,{})]})})}},63056:(e,t,s)=>{"use strict";s.d(t,{A:()=>k});var n=s(95155),a=s(71126),r=s(42592),l=s(55750),i=s(71349),o=s(22810),c=s(2796),d=s(78444),u=s(6457),A=s(42426),h=s(28041),p=s(92895),x=s(68773),j=s(79005),g=s(76046),y=s(12115),m=s(24947),v=s(81909),f=s.n(v),b=s(81488);let _=e=>{switch(e){case"online":return{color:"#23E4A6",text:"在线",tooltip:"工作中不支持修改"};case"offline":return{color:"#BEBEBE",text:"离线"};case"free":return{color:"#FBBC05",text:"空闲"};default:return{color:"#BEBEBE",text:"未知"}}},k=e=>{let{assistant:t,cardProps:s,onDetail:v,readonly:k=!1,selectable:C,selected:E,onSelect:B}=e,{avatar:w,name:N,desc:I,todayCompletedcount:T=0,id:S}=t,[L,R]=(0,y.useState)(t),{runAsync:P,loading:F}=(0,r.Fj)(),D=L.disabled,H=L.status,J=(0,g.useRouter)();return(0,n.jsxs)(i.A,{variant:"borderless",className:f().RobotInfoCard,...s,children:[(0,n.jsxs)(o.A,{justify:"space-between",align:"middle",style:{marginBottom:32},children:[(0,n.jsx)(c.A,{children:(0,n.jsx)(d.A,{size:64,src:w,alt:N,icon:(0,n.jsx)(l.A,{})})}),!k&&(0,n.jsx)(c.A,{children:(0,n.jsx)(u.A,{title:_(t.status).tooltip,children:(0,n.jsx)(A.A,{loading:F,checked:!D,disabled:"online"===H,onChange:async e=>{(await P({disabled:!e},S)).success?R({...L,disabled:!e,status:e?"free":"offline"}):h.Ay.error("操作失败")}})})}),C&&(0,n.jsx)(c.A,{children:(0,n.jsx)(p.A,{checked:E,onChange:e=>B(e.target.checked),className:f().checkbox})})]}),(0,n.jsx)(m.A,{title:N,badge:_(t.status),desc:I||""}),(0,n.jsx)("div",{className:f().processed_container,children:(0,n.jsxs)(o.A,{justify:"space-between",children:[(0,n.jsxs)(c.A,{className:f().processed,children:["今日分析量: ",T]}),!k&&(0,n.jsx)(c.A,{children:(0,n.jsxs)(x.A,{size:"small",className:f().button_group,children:[(0,n.jsx)(j.Ay,{onClick:()=>{J.push((0,b.I)({url:a.Nv.AnalysisAssistantCopy,params:{id:S}}))},type:"link",children:"复制"}),(0,n.jsx)(j.Ay,{type:"link",onClick:()=>{v?v():J.push((0,b.I)({url:a.Nv.AnalysisAssistantEdit,params:{id:S}}))},children:"详情"})]})})]})})]})}},81488:(e,t,s)=>{"use strict";s.d(t,{I:()=>n,O:()=>a});let n=e=>{let{url:t,params:s,isPage:n=!0}=e,a=t;return a="/ui".concat(a),n&&(a="".concat(a,".html")),s&&(a="".concat(a,"?").concat(new URLSearchParams(s).toString())),a},a=(e,t)=>t?(e/t*100).toFixed(2):"-"},81909:e=>{e.exports={RobotInfoCard:"RobotInfoCard_RobotInfoCard__AJN0j",processed:"RobotInfoCard_processed__yfChI",checkbox:"RobotInfoCard_checkbox__h5IC8",selected:"RobotInfoCard_selected__8ZE6H",button_group:"RobotInfoCard_button_group__QJivR"}},95458:e=>{e.exports={container:"BadgeTitle_container__3vD4X",name:"BadgeTitle_name__dkR9K",description:"BadgeTitle_description__Qy0lS",tag:"BadgeTitle_tag__zKgzy"}}},e=>{var t=t=>e(e.s=t);e.O(0,[838,3740,2211,6222,2602,3520,9907,3288,1509,1349,4439,6933,7663,4156,5225,1126,8441,6587,7358],()=>t(30996)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_fdf202",variable:"__variable_fdf202"}},6187:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=e=>({[e.componentCls]:{["".concat(e.antCls,"-motion-collapse-legacy")]:{overflow:"hidden","&-active":{transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}},["".concat(e.antCls,"-motion-collapse")]:{overflow:"hidden",transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}}})},28041:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>j});var r=n(39014),o=n(12115),a=n(89842),s=n(31049),i=n(11432),l=n(24330),c=n(1177),u=n(31617),d=n(62155);let f=null,h=e=>e(),m=[],p={};function v(){let{getContainer:e,duration:t,rtl:n,maxCount:r,top:o}=p,a=(null==e?void 0:e())||document.body;return{getContainer:()=>a,duration:t,rtl:n,maxCount:r,top:o}}let y=o.forwardRef((e,t)=>{let{messageConfig:n,sync:r}=e,{getPrefixCls:i}=(0,o.useContext)(s.QO),l=p.prefixCls||i("message"),c=(0,o.useContext)(a.B),[d,f]=(0,u.y)(Object.assign(Object.assign(Object.assign({},n),{prefixCls:l}),c.message));return o.useImperativeHandle(t,()=>{let e=Object.assign({},d);return Object.keys(e).forEach(t=>{e[t]=function(){return r(),d[t].apply(d,arguments)}}),{instance:e,sync:r}}),f}),g=o.forwardRef((e,t)=>{let[n,r]=o.useState(v),a=()=>{r(v)};o.useEffect(a,[]);let s=(0,i.cr)(),l=s.getRootPrefixCls(),c=s.getIconPrefixCls(),u=s.getTheme(),d=o.createElement(y,{ref:t,sync:a,messageConfig:n});return o.createElement(i.Ay,{prefixCls:l,iconPrefixCls:c,theme:u},s.holderRender?s.holderRender(d):d)});function b(){if(!f){let e=document.createDocumentFragment(),t={fragment:e};f=t,h(()=>{(0,l.K)()(o.createElement(g,{ref:e=>{let{instance:n,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=r,b())})}}),e)});return}f.instance&&(m.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":h(()=>{let t=f.instance.open(Object.assign(Object.assign({},p),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":h(()=>{null==f||f.instance.destroy(e.key)});break;default:h(()=>{var n;let o=(n=f.instance)[t].apply(n,(0,r.A)(e.args));null==o||o.then(e.resolve),e.setCloseFn(o)})}}),m=[])}let x={open:function(e){let t=(0,d.E)(t=>{let n;let r={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return m.push(r),()=>{n?h(()=>{n()}):r.skipped=!0}});return b(),t},destroy:e=>{m.push({type:"destroy",key:e}),b()},config:function(e){p=Object.assign(Object.assign({},p),e),h(()=>{var e;null===(e=null==f?void 0:f.sync)||void 0===e||e.call(f)})},useMessage:u.A,_InternalPanelDoNotUseOrYouWillBeFired:c.Ay};["success","info","warning","error","loading"].forEach(e=>{x[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(e,t){(0,i.cr)();let n=(0,d.E)(n=>{let r;let o={type:e,args:t,resolve:n,setCloseFn:e=>{r=e}};return m.push(o),()=>{r?h(()=>{r()}):o.skipped=!0}});return b(),n}(e,n)}});let j=x},30347:()=>{},32876:(e,t,n)=>{Promise.resolve().then(n.bind(n,41631)),Promise.resolve().then(n.bind(n,91915)),Promise.resolve().then(n.t.bind(n,4147,23)),Promise.resolve().then(n.t.bind(n,38489,23)),Promise.resolve().then(n.t.bind(n,30347,23)),Promise.resolve().then(n.bind(n,33980)),Promise.resolve().then(n.bind(n,73528))},33980:(e,t,n)=>{"use strict";n.d(t,{default:()=>j});var r=n(95155),o=n(81488),a=n(71126),s=n(35594),i=n(59276),l=n(28041),c=n(11432),u=n(66933),d=n(72093),f=n(3387),h=n(79005),m=n(78444),p=n(76046),v=n(12115),y=n(39187),g=n.n(y);let{Sider:b,Header:x}=i.A,j=e=>{let{children:t}=e,n=(0,p.useRouter)(),y=(0,p.usePathname)(),[j,A]=(0,v.useState)(null),[O,S]=(0,v.useState)(!0),_=!1,C=()=>{_||(_=!0,n.push((0,o.I)({url:a.Nv.Login})))},E=()=>[a.GT.AIVirtualSeat,a.GT.Task,a.GT.UserPermission,a.GT.EmployeeInfo,a.GT.AnalysisAssistant,a.GT.TaskBoard,a.GT.Report,a.GT.ModelManage].includes(T);(0,v.useEffect)(()=>{E()&&(0,s.Jt)({path:"/api/user/current"}).then(e=>{if(e.success)A(e.data);else{var t,n;(null===(t=e.error)||void 0===t?void 0:t.code)===401?(l.Ay.error("登录已过期，请重新登录"),C()):l.Ay.error((null===(n=e.error)||void 0===n?void 0:n.message)||"获取用户信息失败")}}).catch(e=>{var t,n,r,o;(null==e?void 0:null===(n=e.responseJSON)||void 0===n?void 0:null===(t=n.error)||void 0===t?void 0:t.code)===401?C():l.Ay.error((null==e?void 0:null===(o=e.responseJSON)||void 0===o?void 0:null===(r=o.error)||void 0===r?void 0:r.message)||"获取用户信息失败")}).finally(()=>S(!1))},[y]);let P=async()=>{try{await (0,s.bE)({path:"/api/user/logout"})}finally{n.push((0,o.I)({url:a.Nv.Login}))}},T=y.replace("/ui","").replace(".html","").replace(/\?.*$/,"").split("/")[1];return y.includes("login")?t:(E()||C(),(0,r.jsxs)(i.A,{style:{height:"100vh"},children:[(0,r.jsxs)(b,{width:320,style:{background:"#fff"},children:[(0,r.jsx)("div",{style:{height:"10%",position:"relative",top:"-10px",left:"24px"},children:(0,r.jsx)("img",{src:(0,o.I)({url:"/logo.png",isPage:!1}),height:"70",style:{position:"relative",top:"120px",left:"20px"}})}),(0,r.jsx)("div",{className:g().menuContainer,children:(0,r.jsx)(c.Ay,{theme:{components:{Menu:{itemSelectedBg:"#171F2D",itemSelectedColor:"#fff"}}},children:(0,r.jsx)(u.A,{className:g().navigationMenu,style:{flex:1,padding:"60% 24px 0 24px"},mode:"vertical",selectedKeys:[T],items:Object.keys(a.u4).map(e=>{let{label:t,icon:n}=a.u4[e];return{label:t,key:e,icon:(0,r.jsx)(n,{theme:T===e?"dark":"light"})}}),onClick:e=>{n.push((0,o.I)({url:"/".concat(e.key)}))}})})})]}),(0,r.jsxs)(i.A,{style:{overflow:"auto"},children:[(0,r.jsx)(x,{style:{background:"#f8f8fa",display:"flex",justifyContent:"flex-end",paddingLeft:"16px"},children:O?(0,r.jsx)(d.A,{size:"small"}):j?(0,r.jsx)(f.A,{placement:"bottomRight",content:(0,r.jsx)(h.Ay,{type:"text",danger:!0,block:!0,onClick:P,children:"退出登录"}),trigger:"click",children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer"},children:[(0,r.jsx)(m.A,{src:j.avatar,size:36,style:{marginRight:8}}),(0,r.jsx)("span",{style:{fontWeight:500},children:j.name})]})}):null}),(0,r.jsx)(c.Ay,{theme:{components:{Table:{headerBg:"#171F2D",headerColor:"#fff",headerSortHoverBg:"#171F2D",headerSortActiveBg:"#171F2D"}}},children:j?t:(0,r.jsx)(d.A,{})})]})]}))}},35594:(e,t,n)=>{"use strict";n.d(t,{Jt:()=>s,bE:()=>i,yH:()=>c,yJ:()=>l});var r=n(43932),o=n.n(r);function a(e,t){return Promise.resolve(o().ajax({method:e,url:t.path||t.url,data:"GET"===e?t.data:JSON.stringify(t.data),contentType:"application/json;charset=UTF-8"}))}let s=e=>a("GET",e),i=e=>a("POST",e),l=e=>a("PUT",e),c=e=>a("DELETE",e)},38489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_e80179",variable:"__variable_e80179"}},39187:e=>{e.exports={navigationMenu:"Navigation_navigationMenu__ayGda",menuContainer:"Navigation_menuContainer__np3W6"}},41631:(e,t,n)=>{"use strict";n.d(t,{ConfigProvider:()=>r.Ay});var r=n(11432)},73528:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(95155),o=n(5144),a=n(24330),s=n(12669);(0,a.L)(function(e,t){t._reactRoot||(t._reactRoot=(0,s.createRoot)(t));var n=t._reactRoot;return n.render(e),function(){return new Promise(function(e){setTimeout(function(){n.unmount(),e()},0)})}});var i=n(64787);let l=e=>{let{children:t}=e;return(0,r.jsx)(o.N7,{children:(0,r.jsx)(i.A,{children:t})})}},76046:(e,t,n)=>{"use strict";var r=n(66658);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useServerInsertedHTML")&&n.d(t,{useServerInsertedHTML:function(){return r.useServerInsertedHTML}})},81488:(e,t,n)=>{"use strict";n.d(t,{I:()=>r,O:()=>o});let r=e=>{let{url:t,params:n,isPage:r=!0}=e,o=t;return o="/ui".concat(o),r&&(o="".concat(o,".html")),n&&(o="".concat(o,"?").concat(new URLSearchParams(n).toString())),o},o=(e,t)=>t?(e/t*100).toFixed(2):"-"},91915:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(12115),o=n(5144),a=n(76046);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}let l=function(e){var t,n=(function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(function(){return(0,o.VC)()}))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,s,i=[],l=!0,c=!1;try{a=(n=n.call(e)).next;for(;!(l=(r=a.call(n)).done)&&(i.push(r.value),i.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw o}}return i}}(t,1)||function(e,t){if(e){if("string"==typeof e)return i(e,1);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}}(t,1)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],l=(0,r.useRef)(!1);return(0,a.useServerInsertedHTML)(function(){var e=(0,o.Jb)(n,{plain:!0});return l.current?null:(l.current=!0,r.createElement("style",{id:"antd-cssinjs","data-rc-order":"prepend","data-rc-priority":"-1000",dangerouslySetInnerHTML:{__html:e}}))}),r.createElement(o.N7,s({},e,{cache:n}))}}},e=>{var t=t=>e(e.s=t);e.O(0,[7896,838,5820,3740,2211,2602,3288,1509,5585,6933,4787,1126,8441,6587,7358],()=>t(32876)),_N_E=e.O()}]);
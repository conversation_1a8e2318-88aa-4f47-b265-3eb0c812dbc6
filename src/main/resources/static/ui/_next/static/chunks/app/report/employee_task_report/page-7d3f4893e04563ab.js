(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[585],{7250:(t,e,a)=>{"use strict";a.d(e,{A:()=>d});var n=a(95155);function r(){return(0,n.jsxs)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M20 39C30.4934 39 39 30.4934 39 20C39 9.50659 30.4934 1 20 1C9.50659 1 1 9.50659 1 20C1 30.4934 9.50659 39 20 39Z",stroke:"black",strokeOpacity:"0.1"}),(0,n.jsx)("path",{d:"M12.2461 20H28.7461",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M17.7461 25.5L12.2461 20L17.7461 14.5",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}var o=a(59276),i=a(22810),s=a(2796),c=a(76046);let{Content:l}=o.A,d=t=>{let{children:e}=t,a=(0,c.useRouter)();return(0,n.jsx)(l,{children:(0,n.jsxs)(i.A,{wrap:!1,children:[(0,n.jsx)(s.A,{style:{cursor:"pointer",width:60,minWidth:60,maxWidth:60,flex:"0 0 60px"},children:(0,n.jsx)("span",{onClick:()=>{a.back()},children:(0,n.jsx)(r,{})})}),(0,n.jsx)(s.A,{style:{flex:1,minWidth:0,whiteSpace:"nowrap"},children:e})]})})}},9365:(t,e,a)=>{"use strict";a.d(e,{A:()=>m});var n=a(12115),r=a(4617),o=a.n(r),i=a(31049),s=a(5144),c=a(70695),l=a(1086),d=a(56204);let u=t=>{let{componentCls:e,sizePaddingEdgeHorizontal:a,colorSplit:n,lineWidth:r,textPaddingInline:o,orientationMargin:i,verticalMarginInline:l}=t;return{[e]:Object.assign(Object.assign({},(0,c.dF)(t)),{borderBlockStart:"".concat((0,s.zA)(r)," solid ").concat(n),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,s.zA)(r)," solid ").concat(n)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,s.zA)(t.dividerHorizontalGutterMargin)," 0")},["&-horizontal".concat(e,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,s.zA)(t.dividerHorizontalWithTextGutterMargin)," 0"),color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(n),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,s.zA)(r)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(e,"-with-text-start")]:{"&::before":{width:"calc(".concat(i," * 100%)")},"&::after":{width:"calc(100% - ".concat(i," * 100%)")}},["&-horizontal".concat(e,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(i," * 100%)")},"&::after":{width:"calc(".concat(i," * 100%)")}},["".concat(e,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:o},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:"".concat((0,s.zA)(r)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(e,"-dashed")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:"".concat((0,s.zA)(r)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(e,"-dotted")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(e,"-with-text")]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},["&-horizontal".concat(e,"-with-text-start").concat(e,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(e,"-inner-text")]:{paddingInlineStart:a}},["&-horizontal".concat(e,"-with-text-end").concat(e,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(e,"-inner-text")]:{paddingInlineEnd:a}}})}},p=(0,l.OF)("Divider",t=>[u((0,d.oX)(t,{dividerHorizontalWithTextGutterMargin:t.margin,dividerHorizontalGutterMargin:t.marginLG,sizePaddingEdgeHorizontal:0}))],t=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS}),{unitless:{orientationMargin:!0}});var h=function(t,e){var a={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(a[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(t);r<n.length;r++)0>e.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(a[n[r]]=t[n[r]]);return a};let m=t=>{let{getPrefixCls:e,direction:a,className:r,style:s}=(0,i.TP)("divider"),{prefixCls:c,type:l="horizontal",orientation:d="center",orientationMargin:u,className:m,rootClassName:g,children:k,dashed:b,variant:f="solid",plain:v,style:x}=t,y=h(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),A=e("divider",c),[S,j,w]=p(A),I=!!k,T=n.useMemo(()=>"left"===d?"rtl"===a?"end":"start":"right"===d?"rtl"===a?"start":"end":d,[a,d]),z="start"===T&&null!=u,C="end"===T&&null!=u,E=o()(A,r,j,w,"".concat(A,"-").concat(l),{["".concat(A,"-with-text")]:I,["".concat(A,"-with-text-").concat(T)]:I,["".concat(A,"-dashed")]:!!b,["".concat(A,"-").concat(f)]:"solid"!==f,["".concat(A,"-plain")]:!!v,["".concat(A,"-rtl")]:"rtl"===a,["".concat(A,"-no-default-orientation-margin-start")]:z,["".concat(A,"-no-default-orientation-margin-end")]:C},m,g),M=n.useMemo(()=>"number"==typeof u?u:/^\d+$/.test(u)?Number(u):u,[u]);return S(n.createElement("div",Object.assign({className:E,style:Object.assign(Object.assign({},s),x)},y,{role:"separator"}),k&&"vertical"!==l&&n.createElement("span",{className:"".concat(A,"-inner-text"),style:{marginInlineStart:z?M:void 0,marginInlineEnd:C?M:void 0}},k)))}},12537:(t,e,a)=>{"use strict";a.d(e,{T:()=>r,Y:()=>n});let n={initial:"未开始",processing:"进行中",completed:"已结束"},r={initial:"未开始",processing:"进行中",notexist:"空号",busy:"占线",success:"成功",failed:"失败"}},18123:(t,e,a)=>{"use strict";a.d(e,{AK:()=>c,K:()=>s,Mj:()=>l,Mp:()=>p,RI:()=>o,Ru:()=>k,V8:()=>d,Yn:()=>h,ZY:()=>i,c9:()=>b,rp:()=>u,w4:()=>g});var n=a(69653),r=a(79471);let o=()=>(0,n.A)(r.A.getTaskList,{manual:!0}),i=()=>(0,n.A)(r.A.createTask,{manual:!0}),s=()=>(0,n.A)(r.A.updateTask,{manual:!0}),c=()=>(0,n.A)(r.A.deleteTask,{manual:!0}),l=()=>(0,n.A)(r.A.getTaskDetail,{manual:!0}),d=()=>(0,n.A)(r.A.startTask,{manual:!0}),u=()=>(0,n.A)(r.A.stopTask,{manual:!0}),p=()=>(0,n.A)(r.A.notice,{manual:!0}),h=()=>(0,n.A)(r.A.rerunReport,{manual:!0}),m={busy:"0",failed:"0",initial:"0",notexist:"0",processing:"0",success:"0",total:"0"},g=t=>{var e;let a=(0,n.A)(()=>r.A.getTaskStatusCount({robotId:t}));return{...a,data:(null==a?void 0:null===(e=a.data)||void 0===e?void 0:e.success)?a.data.data:m}},k=()=>{var t;let e=(0,n.A)(r.A.getTaskStatusCount,{manual:!0});return{...e,data:(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.success)?e.data.data:{}}},b=()=>(0,n.A)(r.A.getContactList,{manual:!0})},48362:(t,e,a)=>{"use strict";a.d(e,{dz:()=>i,jX:()=>s,t3:()=>r,vV:()=>o});var n=a(35594);function r(t){return(0,n.Jt)({url:"/api/folder",data:t})}function o(t){return(0,n.bE)({url:"/api/folder",data:t})}function i(t){return(0,n.Jt)({url:"/api/folder/".concat(t)})}function s(t){return(0,n.bE)({url:"/api/folder/addToFolder",data:t})}},68734:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>I});var n=a(95155),r=a(7250),o=a(54031),i=a(36564),s=a(50759),c=a(69814),l=a(18123),d=a(79471),u=a(12537),p=a(79005),h=a(28041),m=a(6457),g=a(9365),k=a(46742),b=a(68874),f=a(13691),v=a.n(f),x=a(76046),y=a(12115),A=a(35140);let S={initial:{text:"未访谈",color:"#fbc02d"},processing:{text:"进行中",color:"#23e4a6"},completed:{text:"已结束",color:"#4096ff"}},j=Object.entries(u.Y).map(t=>{let[e,a]=t;return{label:a,value:e}}),w=Object.entries(u.T).map(t=>{let[e,a]=t;return{label:a,value:e}});function I(){var t,e,a;let u=(0,x.useSearchParams)(),f=u.get("type")||"user",I="user"===f,T=u.get("employeeId"),z=u.get("postNumber"),{data:C,runAsync:E}=(0,s.dA)(),M=(null==C?void 0:C.success)?C.data:{},O={standardPost:u.get("standardPost"),postNumber:z,dept1:null!==(t=u.get("dept1"))&&void 0!==t?t:"-",dept2:null!==(e=u.get("dept2"))&&void 0!==e?e:"-",dept3:null!==(a=u.get("dept3"))&&void 0!==a?a:"-"},[P,N]=(0,y.useState)(""),[L,H]=(0,y.useState)(""),[W,Y]=(0,y.useState)(""),{runAsync:B}=(0,c.wh)(),[R,D]=(0,y.useState)([]),[F,J]=(0,y.useState)(void 0);(0,y.useEffect)(()=>{B(void 0).then(t=>{t.success&&D(t.data.records.map(t=>({label:t.name||"-",value:t.id})))})},[B]);let[_,G]=(0,y.useState)([]),[X,K]=(0,y.useState)(!1),{runAsync:V,loading:Z}=(0,l.Yn)();(0,y.useEffect)(()=>{"position"===f&&z&&(K(!0),d.A.getTaskList({postNumber:z,keyword:P,contactStatus:W||void 0,folderId:F}).then(t=>{K(!1),t.success?G(t.data.records):G([])}))},[f,z,P,W,F]),(0,y.useEffect)(()=>{I&&T&&(K(!0),E(T),d.A.getTaskList({employeeId:T,keyword:P,status:L||void 0,folderId:F}).then(t=>{K(!1),t.success?G(t.data.records):G([])}))},[f,T,P,L,F]);let $=(0,y.useMemo)(()=>[{title:"开始时间",dataIndex:"beginTime",key:"beginTime",render:t=>t?v()(t).format("YYYY-MM-DD HH:mm:ss"):"-",sorter:!0,sortIcon:A.sortIcon},{title:"结束时间",dataIndex:"endTime",key:"endTime",render:t=>t?v()(t).format("YYYY-MM-DD HH:mm:ss"):"-",sorter:!0,sortIcon:A.sortIcon},{title:"任务名称",dataIndex:"name",key:"name",render:t=>t||"-"},{title:"任务组",dataIndex:"groupName",key:"groupName",render:t=>t||"-"},{title:"任务状态",dataIndex:"status",key:"status",render:t=>{let e=S[t];return e?(0,n.jsx)("span",{style:{color:e.color,fontWeight:600},children:e.text}):"-"},sorter:!0,sortIcon:A.sortIcon},{title:"操作",dataIndex:"opt",key:"opt",render:(t,e)=>"completed"===e.taskStatus?(0,n.jsx)(p.Ay,{type:"link",children:"查看报告"}):(0,n.jsx)(p.Ay,{type:"link",loading:Z,disabled:"processing"===e.taskStatus,onClick:async()=>{let t=I?{type:"user",userId:T,taskId:String(e.id)}:{type:"task",postNumber:z,taskId:String(e.id)};(await V(t)).success?h.Ay.success("生成成功"):h.Ay.error("生成失败")},children:"生成报告"})}],[I,T,z,V,Z]),q=[{title:"员工工号",key:"jobNumber"},{title:"一级部门名称/二级部门名称/三级部门名称",key:"dept",render:t=>"".concat((null==t?void 0:t.dept1)||"-","/").concat((null==t?void 0:t.dept2)||"-","/").concat((null==t?void 0:t.dept3)||"-")},{title:"标准岗位",key:"standardPost"},{title:"岗位号码",key:"postNumber"},{title:"个人岗位名称",key:"personalPost"},{title:"个人职级描述",key:"personalRank"}];return(0,n.jsxs)(r.A,{children:[(0,n.jsx)(i.A,{title:I?"".concat((null==M?void 0:M.name)||"-","访谈任务列表"):"".concat(null==O?void 0:O.standardPost,"访谈任务列表"),description:I?(0,n.jsx)(n.Fragment,{children:q.map((t,e)=>{var a;return(0,n.jsxs)("span",{children:[(0,n.jsx)(m.A,{title:t.title,children:t.render?t.render(M):null!==(a=M[t.key])&&void 0!==a?a:"-"}),e!==q.length-1&&(0,n.jsx)(g.A,{type:"vertical"})]},t.key)})}):(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(m.A,{title:"一级部门名称/二级部门名称/三级部门名称",children:[O.dept1,"/",O.dept2,"/",O.dept3]})}),onSearch:N,extraFilter:[(0,n.jsx)(o.A,{value:L,onChange:t=>H(t),options:j,placeholder:"全部访谈任务状态"},"status"),(0,n.jsx)(o.A,{value:W,onChange:t=>Y(t),options:w,placeholder:"全部执行状态"},"contactStatus"),(0,n.jsx)(o.A,{value:F,onChange:J,options:R,placeholder:"全部任务分组"},"folderId")]}),(0,n.jsx)(k.A.Title,{level:5,style:{margin:"16px 0"},children:"".concat(I?null==M?void 0:M.name:null==O?void 0:O.standardPost,"的全部访谈任务 (").concat(_.length,")")}),(0,n.jsx)(b.A,{rowKey:"id",columns:$,dataSource:_,loading:X,pagination:{pageSize:10},scroll:{x:1e3},size:"small"})]})}},69814:(t,e,a)=>{"use strict";a.d(e,{Ih:()=>s,p_:()=>i,wh:()=>o});var n=a(69653),r=a(48362);function o(){return(0,n.A)(r.t3,{manual:!0})}function i(){return(0,n.A)(r.dz,{manual:!0})}function s(){return(0,n.A)(r.jX,{manual:!0})}},76046:(t,e,a)=>{"use strict";var n=a(66658);a.o(n,"usePathname")&&a.d(e,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(e,{useRouter:function(){return n.useRouter}}),a.o(n,"useSearchParams")&&a.d(e,{useSearchParams:function(){return n.useSearchParams}}),a.o(n,"useServerInsertedHTML")&&a.d(e,{useServerInsertedHTML:function(){return n.useServerInsertedHTML}})},79471:(t,e,a)=>{"use strict";a.d(e,{A:()=>o});var n=a(35594),r=a(90603);let o={getTaskList:t=>{let{contactStatus:e,employeeId:a,...o}=t||{},i={...r.u,...o};return a&&(i.employeeId=a,e&&(i.contactStatus=e)),(null==t?void 0:t.postNumber)&&(i.postNumber=t.postNumber),(0,n.Jt)({url:"/api/tasks",data:i})},createTask:t=>(0,n.bE)({url:"/api/tasks",data:t}),updateTask:(t,e)=>(0,n.yJ)({url:"/api/tasks/".concat(e),data:t}),getTaskDetail:t=>(0,n.Jt)({url:"/api/tasks/".concat(t)}),deleteTask:t=>(0,n.yH)({url:"/api/tasks/".concat(t)}),startTask:t=>(0,n.bE)({url:"/api/tasks/".concat(t,"/start")}),stopTask:t=>(0,n.bE)({url:"/api/tasks/".concat(t,"/stop")}),notice:t=>(0,n.bE)({url:"/api/tasks/".concat(t,"/notice")}),getTaskStatusCount:t=>(0,n.Jt)({url:"/api/contacts/status/count",data:t}),getContactList:t=>(0,n.Jt)({url:"/api/contacts",data:t}),addContact:(t,e)=>(0,n.bE)({url:"/api/contacts/".concat(t,"/save"),data:e}),removeContact:(t,e)=>(0,n.yH)({url:"/api/contacts/".concat(t,"/remove"),data:e}),importContacts:(t,e)=>{let a=new FormData;return a.append("file",e),fetch("/api/contacts/".concat(t,"/import"),{method:"POST",body:a}).then(t=>t.json())},rerunReport:t=>(0,n.bE)({url:"/api/evalapp/repeat/run",data:t})}},89263:(t,e,a)=>{Promise.resolve().then(a.bind(a,68734))},90603:(t,e,a)=>{"use strict";a.d(e,{u:()=>n}),a(35594);let n={order:"desc",sort:"updateTime"}}},t=>{var e=e=>t(t.s=e);t.O(0,[838,3740,586,2211,6222,2602,3520,9907,9786,4439,6933,7663,3840,8874,1126,5140,8441,6587,7358],()=>e(89263)),_N_E=t.O()}]);
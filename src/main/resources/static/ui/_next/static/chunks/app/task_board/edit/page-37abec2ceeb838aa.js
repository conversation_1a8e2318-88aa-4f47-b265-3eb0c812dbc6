(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2066],{7250:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var n=t(95155);function r(){return(0,n.jsxs)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M20 39C30.4934 39 39 30.4934 39 20C39 9.50659 30.4934 1 20 1C9.50659 1 1 9.50659 1 20C1 30.4934 9.50659 39 20 39Z",stroke:"black",strokeOpacity:"0.1"}),(0,n.jsx)("path",{d:"M12.2461 20H28.7461",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M17.7461 25.5L12.2461 20L17.7461 14.5",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}var i=t(59276),l=t(22810),a=t(2796),o=t(76046);let{Content:c}=i.A,d=e=>{let{children:s}=e,t=(0,o.useRouter)();return(0,n.jsx)(c,{children:(0,n.jsxs)(l.A,{wrap:!1,children:[(0,n.jsx)(a.A,{style:{cursor:"pointer",width:60,minWidth:60,maxWidth:60,flex:"0 0 60px"},children:(0,n.jsx)("span",{onClick:()=>{t.back()},children:(0,n.jsx)(r,{})})}),(0,n.jsx)(a.A,{style:{flex:1,minWidth:0,whiteSpace:"nowrap"},children:s})]})})}},10170:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var n=t(95155),r=t(41657);function i(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,n.jsx)("path",{d:"M8.75033 15.8334C12.6623 15.8334 15.8337 12.6621 15.8337 8.75008C15.8337 4.83808 12.6623 1.66675 8.75033 1.66675C4.83833 1.66675 1.66699 4.83808 1.66699 8.75008C1.66699 12.6621 4.83833 15.8334 8.75033 15.8334Z",stroke:"black",strokeWidth:"1.25",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M11.1066 5.97629C10.5034 5.37308 9.67008 5 8.74958 5C7.82912 5 6.99579 5.37308 6.39258 5.97629",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M13.8428 13.8423L17.3783 17.3778",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}function l(e){return(0,n.jsx)(r.A,{placeholder:"搜索",prefix:(0,n.jsx)(i,{}),style:{width:"300px"},...e})}},32383:()=>{},39244:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var n=t(95155),r=t(59575),i=t(68773),l=t(21614),a=t(82133),o=t(22810),c=t(79005),d=t(2796),u=t(12115),h=t(42628);let x=e=>{let{value:s,onChange:t,drawerTitle:x="绑定访谈助手",readonly:j=!1}=e,[p,A]=(0,u.useState)(!1),[g,m]=(0,u.useState)(s),{data:f,runAsync:v}=(0,r.oD)();(0,u.useEffect)(()=>{m(s)},[s]),(0,u.useEffect)(()=>{v()},[]);let y=(0,u.useMemo)(()=>(null==f?void 0:f.success)?f.data.records:[],[f]),k=e=>{m(e)},C=y.find(e=>e.id===g);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.A,{children:(0,n.jsx)(l.A,{style:{width:240},placeholder:"请选择访谈助手",value:C?C.id:void 0,open:!1,onClick:()=>A(!0),options:y.map(e=>({label:e.name,value:e.id})),disabled:j})}),(0,n.jsx)(a.A,{open:p,onClose:()=>A(!1),title:x,width:700,footer:j?null:(0,n.jsx)(o.A,{justify:"end",children:(0,n.jsxs)(i.A,{children:[(0,n.jsx)(c.Ay,{type:"primary",onClick:()=>{t&&g&&t(g),A(!1)},disabled:!g,children:"确定"}),(0,n.jsx)(c.Ay,{onClick:()=>A(!1),children:"取消"})]})}),children:(0,n.jsx)(o.A,{gutter:[16,16],children:y.map(e=>(0,n.jsx)(d.A,{span:8,children:(0,n.jsx)(h.A,{robot:e,selectable:!j,selected:g===e.id,onSelect:()=>k(e.id),readonly:!0,cardProps:{style:{borderColor:g===e.id?"#1677ff":void 0,boxShadow:g===e.id?"0 0 0 2px #1677ff22":void 0}}})},e.id))})})]})}},40997:(e,s,t)=>{"use strict";t.r(s),t.d(s,{CallInWithLabel:()=>eM,CallOutWithLabel:()=>eB,default:()=>e_});var n=t(95155),r=t(35587),i=t(95988),l=t(32853),a=t(77135),o=t(35577),c=t(81488),d=t(71126),u=t(96926),h=t(83761),x=t(18123),j=t(97139),p=t(96030),A=t(59276),g=t(41657),m=t(46742),f=t(64787),v=t(32392),y=t(21382),k=t(72093),C=t(22810),b=t(2796),w=t(68773),S=t(79005),I=t(11432),L=t(92895),T=t(18198),E=t(48904),_=t(6457),W=t(7974),F=t(55474),M=t(99189),B=t(21455),N=t.n(B),O=t(73474),Y=t.n(O),H=t(49113),D=t.n(H),P=t(91199),z=t(39244),R=t(61303),V=t(7250),Z=t(76046),U=t(12115),G=t(3301),J=t(6521),q=t(79471),K=t(41379),X=t(27656),Q=t(69653),$=t(28041),ee=t(54857),es=t(9365);let et=e=>{let{taskId:s}=e,[t,r]=(0,U.useState)(""),[i,l]=(0,U.useState)(""),[a,o]=(0,U.useState)(0),{runAsync:d}=(0,x.c9)(),{run:u,loading:h}=(0,Q.A)(e=>q.A.addContact(s,e),{manual:!0,onSuccess:e=>{if(e.success)$.Ay.success("添加成功"),r(""),l(""),o(e=>e+1);else{var s;$.Ay.error((null===(s=e.error)||void 0===s?void 0:s.message)||"添加失败")}},onError:()=>$.Ay.error("添加失败")}),{run:A,loading:m}=(0,Q.A)(e=>q.A.removeContact(s,D().pick(e,["externalId","name"])),{manual:!0,onSuccess:e=>{if(e.success)$.Ay.success("删除成功"),o(e=>e+1);else{var s;$.Ay.error((null===(s=e.error)||void 0===s?void 0:s.message)||"删除失败")}},onError:()=>$.Ay.error("删除失败")}),{run:f,loading:v}=(0,Q.A)(e=>q.A.importContacts(s,e),{manual:!0,onSuccess:e=>{e.success?($.Ay.success("导入成功"),o(e=>e+1)):$.Ay.error(e.message||"导入失败")},onError:()=>$.Ay.error("导入失败")}),y=async()=>{if(!t.trim()||!i.trim()){$.Ay.warning("请填写完整信息");return}u({name:t,externalId:i})},k=async e=>{A(e)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(w.A,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,n.jsxs)(w.A,{children:[(0,n.jsx)(g.A,{placeholder:"工号",value:i,onChange:e=>l(e.target.value),style:{width:120}}),(0,n.jsx)(g.A,{placeholder:"姓名",value:t,onChange:e=>r(e.target.value),style:{width:120}}),(0,n.jsx)(G.A,{type:"primary",icon:(0,n.jsx)(p.A,{}),onClick:y,loading:h,children:"添加"})]}),(0,n.jsxs)(w.A,{children:[(0,n.jsx)(E.A,{name:"file",accept:".xlsx,.xls",customRequest:async e=>{f(e.file),e.onSuccess&&e.onSuccess({},e.file)},showUploadList:!1,children:(0,n.jsx)(S.Ay,{icon:(0,n.jsx)(K.A,{}),loading:v,children:"Excel导入"})}),(0,n.jsx)(G.A,{icon:(0,n.jsx)(j.A,{}),href:(0,c.I)({url:"/contact_template.xlsx",isPage:!1}),style:{border:"none"},children:"下载模版"})]})]}),(0,n.jsx)(J.A,{getList:d,renderItem:e=>(0,n.jsxs)("div",{style:{width:"100%",padding:"0 10px"},children:[(0,n.jsxs)(w.A,{style:{width:"100%",display:"flex",justifyContent:"space-between"},size:"large",children:[(0,n.jsxs)(w.A,{size:"large",children:[(0,n.jsx)("span",{style:{color:"#888"},children:e.externalId}),(0,n.jsx)("span",{style:{fontWeight:500},children:e.name})]}),(0,n.jsx)(ee.A,{title:"确定删除该联系人？",onConfirm:()=>k(e),okText:"删除",cancelText:"取消",okButtonProps:{loading:m},children:(0,n.jsx)(X.A,{})},"delete-confirm")]}),(0,n.jsx)(es.A,{style:{margin:"10px 0"}})]},e.externalId),params:{taskId:s},refreshDeps:[a,s],gutter:0,maxHeight:300})]})};var en=t(89810),er=t.n(en),ei=t(95950),el=t(59575),ea=t(78444),eo=t(13691),ec=t.n(eo),ed=t(57394),eu=t(61632),eh=t(64165),ex=t(49086),ej=t(85605),ep=t(10819),eA=t(55750),eg=t(16419),em=t(71349),ef=t(82133),ev=t(53096),ey=t(7660);let{Title:ek,Text:eC}=m.A;function eb(e){let{selectedStatus:s,contact:t,task:r,robot:i}=e,{name:l,status:a,phone:c,durationText:d,externalId:u}=t,{runAsync:h}=(0,ej.H)(),[x,p]=(0,U.useState)(!1),[A,g]=(0,U.useState)([]),[v,y]=(0,U.useState)(!1),[k,I]=(0,U.useState)(!1),L=(0,U.useRef)(null),T=(0,U.useRef)(null),{message:E}=f.A.useApp();(0,U.useEffect)(()=>{if(x&&u&&"processing"===a){let{protocol:e,host:s}=window.location,t=new WebSocket("".concat("https:"===e?"wss:":"ws:","//").concat(s,"/api/ws/").concat(u));return t.onopen=()=>{console.log("WebSocket连接已建立"),y(!0)},t.onmessage=e=>{try{var s,t;let n=JSON.parse(e.data),r={id:n.id,content:null!==(s=n.content)&&void 0!==s?s:"",userId:n.userId,userType:null!==(t=n.userType)&&void 0!==t?t:"robot",createTime:n.createTime};g(e=>[...e,r])}catch(e){console.error("解析WebSocket消息失败:",e)}},t.onerror=e=>{console.error("WebSocket错误:",e),y(!1)},t.onclose=()=>{console.log("WebSocket连接已关闭"),y(!1)},L.current=t,()=>{(t.readyState===WebSocket.OPEN||t.readyState===WebSocket.CONNECTING)&&t.close()}}},[x,u]),(0,U.useEffect)(()=>{!x&&(g([]),L.current&&(L.current.readyState===WebSocket.OPEN||L.current.readyState===WebSocket.CONNECTING)&&(L.current.close(),y(!1)))},[x]);let W=()=>{T.current&&T.current.scrollIntoView({behavior:"smooth"})};(0,U.useEffect)(()=>{x&&A.length>0&&W()},[A]);let{icon:F,color:M,desc:B}=(()=>{let e=null,t="",r="";switch(s){case"success":t="#BEBEBE",r="通话已完成",e=(0,n.jsx)(ed.A,{});break;case"failed":e=(0,n.jsx)(eu.A,{}),t="#FF004D",r="notexist"===a?"空号":"busy"===a?"占线":"失败";break;case"processing":e=(0,n.jsx)(ex.A,{}),t="#23E4A6",r="正在通话中";break;case"initial":e=(0,n.jsx)(eh.A,{}),t="#FBBC05",r="通话待进行"}return{icon:e,color:t,desc:r}})(),N=e=>{let s="robot"===e.userType,t=ec()(e.createTime).format("YYYY-MM-DD HH:mm:ss");return(0,n.jsx)(C.A,{justify:s?"start":"end",style:{marginBottom:16,width:"100%"},children:(0,n.jsxs)(w.A,{align:"start",style:{maxWidth:"80%"},children:[s&&(0,n.jsx)(ea.A,{icon:(0,n.jsx)(ep.A,{}),style:{backgroundColor:"#1677ff"}}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{background:s?"#f0f2f5":"#1677ff",color:s?"#000":"#fff",padding:"8px 12px",borderRadius:"8px",borderTopLeftRadius:s?0:"8px",borderTopRightRadius:s?"8px":0,wordBreak:"break-word"},children:e.content}),(0,n.jsx)(eC,{type:"secondary",style:{fontSize:12},children:t})]}),!s&&(0,n.jsx)(ea.A,{icon:(0,n.jsx)(eA.A,{}),style:{backgroundColor:"#87d068"}})]})},e.id)},O=s===ei.nW.PROCESSING||s===ei.nW.SUCCESS,Y=async()=>{try{I(!0);let e=1,s=0,n=[];for(;;){let i=await h({contactId:t.id,taskId:r.id,page:e,pageSize:100});if(s=i.data.total,!i.data.records||0===i.data.records.length||(n=[...n,...i.data.records],100*e>=s))break;e++}let a=n.map(e=>({时间:ec()(e.createTime).format("YYYY-MM-DD HH:mm:ss"),发送者:"robot"===e.userType?"AI助手":"用户",内容:e.content,访谈助手名称:i.name,访谈助手id:i.id,任务名称:r.name,任务id:r.id,联系人名称:l,号码:c})),o=ey.Wp.book_new(),d=ey.Wp.json_to_sheet(a);ey.Wp.book_append_sheet(o,d,"聊天记录"),ey._h(o,"".concat(r.name,"_").concat(l,"_").concat(c,".xlsx")),E.success("下载成功")}catch(e){console.error("下载失败:",e),E.error("下载失败，请重试")}finally{I(!1)}};return(0,n.jsxs)(f.A,{children:[(0,n.jsxs)(em.A,{children:[(0,n.jsxs)(C.A,{justify:"space-between",children:[(0,n.jsx)(b.A,{children:(0,n.jsxs)(w.A,{size:"small",children:[F,(0,n.jsx)(_.A,{title:"客户名称/设备名称",children:(0,n.jsx)(ek,{level:5,children:l})}),(0,n.jsx)(ek,{level:5,children:"/"}),(0,n.jsx)(_.A,{title:"号码",children:(0,n.jsx)(ek,{level:5,ellipsis:!0,children:c})})]})}),(0,n.jsx)(b.A,{children:(0,n.jsx)(o.A,{color:M,children:B})})]}),(0,n.jsx)(eC,{type:"secondary",style:{marginLeft:"32px"},children:s===ei.nW.INITIAL?"暂无数据":ec()(t.createTime).format("MM/DD HH:mm")}),d&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(es.A,{type:"vertical"}),(0,n.jsx)(eC,{type:"secondary",children:d})]}),O&&(0,n.jsx)(C.A,{justify:"end",children:(0,n.jsx)(S.Ay,{onClick:()=>p(!0),children:"日志"})})]}),(0,n.jsxs)(ef.A,{open:x,onClose:()=>p(!1),title:"日志详情",width:400,children:[(0,n.jsxs)(w.A,{size:"small",style:{display:"flex",alignItems:"baseline"},children:[(0,n.jsx)(m.A.Title,{level:5,children:"历史聊天记录"}),k?(0,n.jsx)(eg.A,{}):(0,n.jsx)(_.A,{title:"下载历史聊天记录",children:(0,n.jsx)(j.A,{onClick:Y})})]}),x&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(J.A,{renderItem:N,getList:h,params:{userId:u,taskId:r.id}}),"processing"===a&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(es.A,{}),(0,n.jsxs)("div",{children:[(0,n.jsxs)(m.A.Title,{level:5,children:["实时聊天记录",v&&(0,n.jsx)(o.A,{color:"#23E4A6",style:{marginLeft:8},children:"已连接"}),!v&&(0,n.jsx)(o.A,{color:"#FF004D",style:{marginLeft:8},children:"未连接"})]}),A.length>0?(0,n.jsxs)("div",{children:[A.map(N),(0,n.jsx)("div",{ref:T})]}):(0,n.jsx)(ev.A,{description:"暂无实时对话记录"})]})]})]})]})]})}function ew(e){let{task:s,statusLabel:t}=e,{data:r,run:i,loading:a}=(0,el.hU)(),{runAsync:o,loading:c}=(0,x.V8)(),{runAsync:d}=(0,x.c9)(),[u,h]=(0,U.useState)(ei.nW.PROCESSING),{message:j}=f.A.useApp(),{robotId:p,type:A,status:g,beginTime:m}=s,v=(null==r?void 0:r.success)?r.data:null,{data:y,loading:S,runAsync:I}=(0,x.Ru)();(0,U.useEffect)(()=>{p&&i(p)},[p,i]),(0,U.useEffect)(()=>{s.id&&I({taskId:s.id,robotId:p})},[s.id,p,I]);let L="initial"===g||"completed"===g;return(0,n.jsx)("div",{children:(0,n.jsxs)(f.A,{children:[(0,n.jsxs)(C.A,{align:"middle",justify:L?"space-between":"start",children:[(0,n.jsxs)(b.A,{span:12,children:[a?(0,n.jsx)(k.A,{}):(0,n.jsxs)(w.A,{children:[(0,n.jsx)(ea.A,{src:null==v?void 0:v.avatar}),null==v?void 0:v.name]}),(0,n.jsx)(es.A,{type:"vertical"}),"inbound"===A?(0,n.jsx)(eM,{}):(0,n.jsx)(eB,{}),(0,n.jsx)(es.A,{type:"vertical"}),"initial"===g?t:"".concat(ec()(m).format("YYYY-MM-DD HH:mm:ss")," 启动")]}),L&&(0,n.jsx)(b.A,{children:(0,n.jsxs)(w.A,{children:[(0,n.jsx)(l.A,{btnText:"删除任务",title:"确定删除该任务？",description:"任务一旦删除，无法恢复",onOk:async()=>(console.log("删除任务"),!0)}),"initial"===g&&(0,n.jsx)(l.A,{btnText:"启动任务",title:"确定启动该任务",btnProps:{loading:c,type:"primary"},description:"任务一旦启动，无法终止",onOk:async()=>{let e=await o(s.id);return e.success?(j.success("启动成功"),!0):(j.error(e.error.message),!1)}})]})})]}),(0,n.jsx)(C.A,{style:{marginTop:"24px"},children:(0,n.jsx)(b.A,{span:24,children:(0,n.jsx)(ei.Ay,{loading:S,data:y[s.id],processingProps:{cardProps:"initial"===g?{bgColor:"linear-gradient(111deg, #41D189 52.04%, rgba(50, 255, 50, 0.10) 179.69%)"}:{}},selectConfig:{enable:!0,onSelect:e=>{h(e)},selectedKey:u}})})}),(0,n.jsx)(J.A,{refreshDeps:[u],getList:d,params:{status:ei.HI[u],robotId:p,taskId:s.id},gutter:[16,16],renderItem:e=>(0,n.jsx)(b.A,{span:8,children:(0,n.jsx)(eb,{selectedStatus:u,contact:e,task:s,robot:v})},e.id)})]})})}let{Content:eS}=A.A,{TextArea:eI}=g.A,{Title:eL,Text:eT}=m.A;var eE=function(e){return e.CONFIG="config",e.STATISTIC="statistic",e}(eE||{});function e_(){return(0,n.jsx)(U.Suspense,{fallback:(0,n.jsx)("div",{children:"Loading..."}),children:(0,n.jsx)(eF,{})})}let eW=e=>{switch(e){case"initial":return{color:"#FBBC05",label:"待启动"};case"processing":return{color:"#23E4A6",label:"进行中"};case"completed":return{color:"#BEBEBE",label:"已结束"}}};function eF(){var e,s,t;let{message:u}=f.A.useApp(),h=(0,Z.useSearchParams)(),A=h.get("id"),m=h.get("robotId"),[B,O]=(0,U.useState)("edit"),[H,G]=(0,U.useState)(""),[J,q]=(0,U.useState)(""),[K,X]=(0,U.useState)(60),[Q,$]=(0,U.useState)(!1),[ee,es]=(0,U.useState)("config"),[en,ei]=(0,U.useState)(!0),el=(0,Z.useRouter)(),{runAsync:ea,loading:eo}=(0,x.ZY)(),{runAsync:ec,loading:ed}=(0,x.K)(),{runAsync:eu,loading:eh}=(0,x.AK)(),{runAsync:ex}=(0,x.Mp)(),{runAsync:ej,data:ep,loading:eA}=(0,x.Mj)(),eg=(null==ep?void 0:ep.success)?ep.data:{},em=null==eg?void 0:eg.id,[ef]=v.A.useForm(),ev=null!==(e=(0,M.FH)("callerNumber",ef))&&void 0!==e?e:"",ey=(null!==(s=null==ev?void 0:ev.split(","))&&void 0!==s?s:[]).filter(Boolean),ek=(0,M.FH)("bgUrl",ef),eC=eW(null!==(t=null==eg?void 0:eg.status)&&void 0!==t?t:"initial"),eb=async e=>{var s;let t={name:H,desc:J,inboundConfig:JSON.stringify({holdTalkTimeout:K,showAudioWave:Q}),...e,callerNumber:ey.filter(e=>"on"!==e).join(","),contacts:(null!==(s=ef.getFieldValue("contacts"))&&void 0!==s?s:[]).map(e=>({externalId:e.externalId,name:e.name})),endTime:e.endTime?N()(e.endTime).utc().format():void 0},n=em?await ec(t,em):await ea(t);n.success?(O("view"),u.success("保存成功"),el.push((0,c.I)({url:d.Nv.TaskList}))):u.error(n.error.message)};return(0,U.useEffect)(()=>{N().extend(Y()),A?ej(A).then(e=>{if(e.success){let{name:s,desc:t,inboundConfig:n}=e.data;G(s),q(t),O("view"),ei(!1),ef.setFieldsValue({...e.data,endTime:e.data.endTime?N()(e.data.endTime):void 0});let r=JSON.parse(n||"{}");X(r.holdTalkTimeout||60),$(r.showAudioWave||!1)}}):m&&ef.setFieldValue("robotId",m)},[A,m]),(0,n.jsx)(V.A,{children:(0,n.jsx)(k.A,{spinning:A&&en&&(!ep||eA),children:(0,n.jsxs)(f.A,{children:[(0,n.jsx)(C.A,{style:{marginBottom:"60px"},gutter:12,children:"view"===B?(0,n.jsxs)(b.A,{span:23,children:[(0,n.jsxs)(C.A,{justify:"space-between",children:[(0,n.jsx)(b.A,{children:(0,n.jsx)(eL,{level:4,children:H})}),(0,n.jsx)(b.A,{children:(0,n.jsxs)(w.A,{children:[(0,n.jsx)(S.Ay,{onClick:()=>O("edit"),loading:ed,children:"编辑"}),(0,n.jsx)(S.Ay,{type:"primary",onClick:()=>{y.A.confirm({title:"发送钉钉任务提醒",content:"确定要通过钉钉向所有未完成访谈任务的员工发送任务提醒吗？",onOk:async()=>{console.log(await ex(em))}})},children:"发送钉钉通知"}),"statistic"===ee&&(0,n.jsx)(S.Ay,{icon:(0,n.jsx)(j.A,{}),type:"link",href:"/api/tasks/".concat(A,"/conversation/export"),children:"批量导出"})]})})]}),(0,n.jsx)(eT,{type:"secondary",children:J})]}):(0,n.jsxs)(b.A,{span:23,children:[(0,n.jsxs)(C.A,{justify:"space-between",children:[(0,n.jsx)(b.A,{children:(0,n.jsx)(I.Ay,{theme:{components:{Input:{colorTextPlaceholder:"#000"}}},children:(0,n.jsx)(g.A,{placeholder:"请输入访谈任务名称",variant:"borderless",style:{paddingLeft:0,fontSize:"20px",fontWeight:"500"},value:H,onChange:e=>{G(e.target.value)}})})}),(0,n.jsx)(S.Ay,{type:"primary",loading:eo,onClick:async()=>{if(em&&D().isEqual(H,null==eg?void 0:eg.name)&&D().isEqual(J,null==eg?void 0:eg.desc)){O("view");return}let e={name:H,desc:J},s=em?await ec(e,em):await ea(e);s.success?(O("view"),u.success("保存成功"),ej(null==s?void 0:s.data.id)):u.error(s.message)},children:"完成"})]}),(0,n.jsx)(eI,{style:{marginTop:"20px"},placeholder:"请输入访谈任务简介",autoSize:{minRows:2,maxRows:6},value:J,onChange:e=>{q(e.target.value)}})]})}),A&&(0,n.jsx)(i.A,{items:[{key:"config",label:"任务配置"},{key:"statistic",label:(0,n.jsxs)(w.A,{children:["任务追踪",(0,n.jsx)(o.A,{color:eC.color,children:eC.label})]})}],onChange:e=>{es(e)}}),"config"===ee&&(0,n.jsxs)(n.Fragment,{children:["processing"===eg.status&&(0,n.jsx)(a.A,{message:"该任务正在进行中，暂不支持编辑配置"}),"completed"===eg.status&&(0,n.jsx)(a.A,{message:"该任务已结束，暂不支持编辑配置"}),(0,n.jsx)(v.A,{layout:"vertical",form:ef,onFinish:eb,initialValues:{type:"inbound",...eg},children:(0,n.jsxs)(C.A,{gutter:[24,24],align:"stretch",children:[(0,n.jsx)(b.A,{span:24,children:(0,n.jsx)(r.A,{title:"任务分组设置",children:(0,n.jsx)(v.A.Item,{label:"任务组别选择",name:"folderId",children:(0,n.jsx)(R.A,{})})})}),(0,n.jsx)(b.A,{span:24,children:(0,n.jsx)(r.A,{title:"任务设定",children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v.A.Item,{label:"",children:(0,n.jsx)(L.A,{checked:Q,onChange:e=>$(e.target.checked),children:"是否显示声音波纹"})}),(0,n.jsx)(v.A.Item,{label:"超时未对话，则结束通话",children:(0,n.jsx)(T.A,{placeholder:"请输入",addonAfter:"秒",min:10,value:K,onChange:e=>X(e)})}),(0,n.jsx)(v.A.Item,{label:(0,n.jsxs)("span",{children:["背景图片",(0,n.jsx)("span",{style:{fontSize:"13px",color:"#999"},children:"（ 建议尺寸大小 750px \xd7 1334px ）"})]}),name:"bgUrl",children:(0,n.jsx)(E.A,{showUploadList:!1,maxCount:1,action:"/api/image/upload",accept:"image/*",className:er().bgImg,beforeUpload:e=>{let s=e.size/1024/1024<5;return s||u.error("图片大小不超过5MB"),s},onChange:async e=>{"uploading"!==e.file.status&&("done"===e.file.status?e.file.response.success?ef.setFieldValue("bgUrl",e.file.response.data.url):u.error("".concat(e.file.name," 图片上传失败")):"error"===e.file.status&&u.error("".concat(e.file.name," 图片上传失败")))},listType:"picture-card",children:ek?(0,n.jsx)(_.A,{title:"点击更换背景图片",children:(0,n.jsx)("div",{style:{height:"100%"},children:(0,n.jsx)(W.A,{src:ek,alt:"背景图片",preview:!1,style:{height:"100%"}})})}):(0,n.jsx)(_.A,{title:"点击更换背景图片",children:(0,n.jsx)(p.A,{})})})}),(0,n.jsx)(v.A.Item,{label:"预计完成时间",name:"endTime",children:(0,n.jsx)(F.A,{showTime:!0,format:"YYYY-MM-DD HH:mm:ss"})})]})})}),em&&(0,n.jsx)(b.A,{span:12,children:(0,n.jsx)(r.A,{title:"员工配置",className:er().section,children:(0,n.jsx)(et,{taskId:A})})}),(0,n.jsx)(b.A,{span:em?12:24,children:(0,n.jsxs)(r.A,{title:"任务指派",className:er().section,children:[(0,n.jsx)(v.A.Item,{name:"robotId",label:"绑定访谈助手",children:(0,n.jsx)(z.A,{value:ef.getFieldValue("robotId"),onChange:e=>ef.setFieldValue("robotId",e)})}),(0,n.jsx)(v.A.Item,{name:"evalAppIds",label:"绑定分析助手",children:(0,n.jsx)(P.A,{value:ef.getFieldValue("evalAppIds"),onChange:e=>ef.setFieldValue("evalAppIds",e),multiple:!0})})]})})]})}),(0,n.jsxs)(C.A,{justify:(null==eg?void 0:eg.status)==="initial"?"space-between":"start",style:{marginTop:"20px"},children:[(0,n.jsxs)(w.A,{size:16,children:[(0,n.jsx)(S.Ay,{type:"primary",onClick:ef.submit,loading:ed||eo,children:"保存"}),(0,n.jsx)(S.Ay,{onClick:()=>{el.push((0,c.I)({url:d.Nv.TaskList}))},children:"取消"})]}),(null==eg?void 0:eg.status)==="initial"&&(0,n.jsx)(l.A,{btnText:"删除任务",title:"确定删除该任务？",onOk:async()=>{let e=await eu(em);return e.success?(u.success("删除成功"),el.push((0,c.I)({url:d.Nv.TaskList})),!0):(u.error(e.error.message),!1)},btnProps:{loading:eh},description:"任务一旦删除，无法恢复"})]})]}),"statistic"===ee&&(0,n.jsx)(ew,{task:eg,statusLabel:eC.label})]})})})}function eM(){return(0,n.jsxs)(w.A,{children:[(0,n.jsx)("div",{className:er().callIn,children:(0,n.jsx)(u.A,{})}),(0,n.jsx)("span",{children:"网络呼入"})]})}function eB(){return(0,n.jsxs)(w.A,{children:[(0,n.jsx)("div",{className:er().callOut,children:(0,n.jsx)(h.A,{})}),(0,n.jsx)("span",{children:"网络呼出"})]})}},48362:(e,s,t)=>{"use strict";t.d(s,{dz:()=>l,jX:()=>a,t3:()=>r,vV:()=>i});var n=t(35594);function r(e){return(0,n.Jt)({url:"/api/folder",data:e})}function i(e){return(0,n.bE)({url:"/api/folder",data:e})}function l(e){return(0,n.Jt)({url:"/api/folder/".concat(e)})}function a(e){return(0,n.bE)({url:"/api/folder/addToFolder",data:e})}},49086:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M12 22C14.7614 22 17.2614 20.8807 19.0711 19.0711C20.8807 17.2614 22 14.7614 22 12C22 9.2386 20.8807 6.7386 19.0711 4.92893C17.2614 3.11929 14.7614 2 12 2C9.2386 2 6.7386 3.11929 4.92893 4.92893C3.11929 6.7386 2 9.2386 2 12C2 14.7614 3.11929 17.2614 4.92893 19.0711C6.7386 20.8807 9.2386 22 12 22Z",fill:"#23E4A6",stroke:"#23E4A6",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M9.95719 7.2417C10.1691 7.2417 10.3644 7.35662 10.4672 7.5419L11.1808 8.82722C11.2742 8.99551 11.2786 9.19907 11.1925 9.37124L10.5051 10.7461C10.5051 10.7461 10.7043 11.7702 11.538 12.6039C12.3717 13.4376 13.3924 13.6334 13.3924 13.6334L14.767 12.9461C14.9393 12.8599 15.143 12.8644 15.3114 12.958L16.6003 13.6746C16.7854 13.7776 16.9002 13.9727 16.9002 14.1845V15.6642C16.9002 16.4178 16.2003 16.9621 15.4862 16.7211C14.0198 16.2263 11.7434 15.2842 10.3006 13.8413C8.85773 12.3985 7.91559 10.1222 7.42076 8.65569C7.17984 7.94166 7.72412 7.2417 8.47769 7.2417H9.95719Z",fill:"white",stroke:"white",strokeLinejoin:"round"})]})}},50968:(e,s,t)=>{"use strict";t.d(s,{A:()=>u});var n=t(95155),r=t(48362),i=t(96030),l=t(28041),a=t(46742),o=t(41657),c=t(12115),d=t(32853);let u=e=>{let{onCreated:s,btnType:t="link",btnText:u="创建新任务组"}=e,h=(0,c.useRef)(null);return(0,n.jsxs)(d.A,{btnText:u,title:"创建新任务组",btnProps:{type:t,icon:(0,n.jsx)(i.A,{})},onOk:async()=>{var e,t,n,i;let a=null===(n=h.current)||void 0===n?void 0:null===(t=n.input)||void 0===t?void 0:null===(e=t.value)||void 0===e?void 0:e.trim();if(!a)return l.Ay.error("请输入新任务组名称"),!1;let o=await (0,r.vV)({name:a});return o.success?(l.Ay.success("创建成功"),null==s||s({id:o.data.id,name:o.data.name}),!0):"error"in o?(l.Ay.error((null==o?void 0:null===(i=o.error)||void 0===i?void 0:i.message)||"创建失败"),!1):void 0},children:[(0,n.jsx)(a.A.Text,{children:"确定添加新的任务组别"}),(0,n.jsx)(o.A,{style:{margin:"18px 0"},ref:h,placeholder:"请输入新任务组名称",maxLength:20})]})}},53974:(e,s,t)=>{Promise.resolve().then(t.bind(t,40997))},57394:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M12 22C14.7614 22 17.2614 20.8807 19.0711 19.0711C20.8807 17.2614 22 14.7614 22 12C22 9.2386 20.8807 6.7386 19.0711 4.92893C17.2614 3.11929 14.7614 2 12 2C9.2386 2 6.7386 3.11929 4.92893 4.92893C3.11929 6.7386 2 9.2386 2 12C2 14.7614 3.11929 17.2614 4.92893 19.0711C6.7386 20.8807 9.2386 22 12 22Z",fill:"#BEBEBE"}),(0,n.jsx)("path",{d:"M8 12L11 15L17 9",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}},61303:(e,s,t)=>{"use strict";t.d(s,{A:()=>p});var n=t(95155),r=t(10170),i=t(48362),l=t(41657),a=t(82133),o=t(22810),c=t(68773),d=t(79005),u=t(72093),h=t(89351),x=t(12115),j=t(50968);let p=e=>{var s;let{value:t,onChange:p}=e,[A,g]=(0,x.useState)(!1),[m,f]=(0,x.useState)(!1),[v,y]=(0,x.useState)([]),[k,C]=(0,x.useState)(""),[b,w]=(0,x.useState)(t),S=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";f(!0);try{let s=await (0,i.t3)({keyword:e,page:1,pageSize:100});s.success&&y(s.data.records||[])}finally{f(!1)}};(0,x.useEffect)(()=>{A&&S()},[A]);let I=e=>{w(e),null==p||p(e),g(!1)},L=(0,x.useMemo)(()=>[{label:"不加入任务组",value:""},...v.map(e=>({label:e.name||"-",value:e.id}))],[v]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.A,{style:{width:240,cursor:"pointer"},placeholder:"请选择任务分组",value:(null===(s=L.find(e=>e.value===b))||void 0===s?void 0:s.label)||"",readOnly:!0,onClick:()=>g(!0)}),(0,n.jsxs)(a.A,{open:A,onClose:()=>g(!1),title:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,n.jsx)("span",{children:"任务组选择"}),(0,n.jsx)(j.A,{btnType:"default",btnText:"创建新任务组",onCreated:()=>S(k)})]}),width:520,footer:(0,n.jsx)(o.A,{justify:"end",children:(0,n.jsxs)(c.A,{children:[(0,n.jsx)(d.Ay,{type:"primary",onClick:()=>{void 0!==b&&I(b)},children:"保存"}),(0,n.jsx)(d.Ay,{onClick:()=>g(!1),children:"取消"})]})}),children:[(0,n.jsx)(r.A,{value:k,onChange:e=>{C(e.target.value),S(e.target.value)},style:{width:"100%"}}),(0,n.jsx)(u.A,{spinning:m,children:(0,n.jsx)(h.Ay.Group,{style:{width:"100%"},value:b,onChange:e=>w(e.target.value),children:(0,n.jsx)("div",{style:{maxHeight:400,overflowY:"auto"},children:L.map(e=>(0,n.jsx)("div",{style:{display:"flex",alignItems:"center",height:48,borderBottom:"1px solid #f0f0f0",padding:"0 8px"},children:(0,n.jsx)(h.Ay,{value:e.value,children:e.label})},e.value))})})})]})]})}},61632:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M12 22C17.5229 22 22 17.5229 22 12C22 6.47715 17.5229 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5229 6.47715 22 12 22Z",fill:"#FF004D",stroke:"#FF004D",strokeWidth:"1.5",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M14.8284 9.17139L9.1715 14.8282",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M9.17165 9.17139L14.8285 14.8282",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}},64165:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M12 22C14.7614 22 17.2614 20.8807 19.0711 19.0711C20.8807 17.2614 22 14.7614 22 12C22 9.2386 20.8807 6.7386 19.0711 4.92893C17.2614 3.11929 14.7614 2 12 2C9.2386 2 6.7386 3.11929 4.92893 4.92893C3.11929 6.7386 2 9.2386 2 12C2 14.7614 3.11929 17.2614 4.92893 19.0711C6.7386 20.8807 9.2386 22 12 22Z",fill:"#FBBC05",stroke:"#FBBC05",strokeLinejoin:"round"}),(0,n.jsxs)("g",{clipPath:"url(#clip0_180_29384)",children:[(0,n.jsx)("path",{d:"M12 18.6668C15.6819 18.6668 18.6667 15.6821 18.6667 12.0002C18.6667 8.31826 15.6819 5.3335 12 5.3335C8.3181 5.3335 5.33334 8.31826 5.33334 12.0002C5.33334 15.6821 8.3181 18.6668 12 18.6668Z",stroke:"white",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M12.0028 8L12.0024 12.0029L14.8289 14.8294",stroke:"white",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"clip0_180_29384",children:(0,n.jsx)("rect",{width:"16",height:"16",fill:"white",transform:"translate(4 4)"})})})]})}},83686:()=>{},85605:(e,s,t)=>{"use strict";t.d(s,{H:()=>l});var n=t(69653),r=t(35594);let i={getConversationList:e=>(0,r.Jt)({url:"/api/conversations",data:e})},l=()=>(0,n.A)(i.getConversationList,{manual:!0})},89810:e=>{e.exports={container:"page_container__pYkIy",section:"page_section__qS5B_",callerNumberContent:"page_callerNumberContent__zWVPf",callIn:"page_callIn__hfaYd",callOut:"page_callOut__zM1qi",bgImg:"page_bgImg__k0Slv"}}},e=>{var s=s=>e(e.s=s);e.O(0,[6772,838,2625,3740,4935,586,3524,2211,6222,2602,3520,9907,3288,1509,1349,9786,4439,5585,4787,3840,2392,2342,4156,4338,5799,8198,6613,3864,3889,8441,6587,7358],()=>s(53974)),_N_E=e.O()}]);
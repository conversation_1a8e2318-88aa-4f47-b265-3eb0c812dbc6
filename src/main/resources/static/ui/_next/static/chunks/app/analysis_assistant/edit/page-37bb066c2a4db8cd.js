(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3379],{7250:(e,i,s)=>{"use strict";s.d(i,{A:()=>c});var t=s(95155);function n(){return(0,t.jsxs)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,t.jsx)("path",{d:"M20 39C30.4934 39 39 30.4934 39 20C39 9.50659 30.4934 1 20 1C9.50659 1 1 9.50659 1 20C1 30.4934 9.50659 39 20 39Z",stroke:"black",strokeOpacity:"0.1"}),(0,t.jsx)("path",{d:"M12.2461 20H28.7461",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M17.7461 25.5L12.2461 20L17.7461 14.5",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}var l=s(59276),a=s(22810),r=s(2796),A=s(76046);let{Content:o}=l.A,c=e=>{let{children:i}=e,s=(0,A.useRouter)();return(0,t.jsx)(o,{children:(0,t.jsxs)(a.A,{wrap:!1,children:[(0,t.jsx)(r.A,{style:{cursor:"pointer",width:60,minWidth:60,maxWidth:60,flex:"0 0 60px"},children:(0,t.jsx)("span",{onClick:()=>{s.back()},children:(0,t.jsx)(n,{})})}),(0,t.jsx)(r.A,{style:{flex:1,minWidth:0,whiteSpace:"nowrap"},children:i})]})})}},9877:(e,i,s)=>{Promise.resolve().then(s.bind(s,60760))},28532:(e,i,s)=>{"use strict";s.d(i,{A:()=>n});var t=s(95155);function n(){return(0,t.jsxs)("svg",{width:"18",height:"18",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",children:[(0,t.jsx)("rect",{x:"0.25",y:"0.25",width:"47.5",height:"47.5",rx:"11.75",fill:"url(#pattern0_228_7247)",stroke:"#EAEDF1",strokeWidth:"0.5"}),(0,t.jsxs)("defs",{children:[(0,t.jsx)("pattern",{id:"pattern0_228_7247",patternContentUnits:"objectBoundingBox",width:"1",height:"1",children:(0,t.jsx)("use",{xlinkHref:"#image0_228_7247",transform:"scale(0.0025)"})}),(0,t.jsx)("image",{id:"image0_228_7247",width:"400",height:"400",xlinkHref:"data:image/png;base64,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"})]})]})}},30259:(e,i,s)=>{"use strict";s.d(i,{FL:()=>A,gI:()=>c,hv:()=>d,zK:()=>r,o3:()=>o});var t=s(69653),n=s(35594),l=s(90603);let a={getLlmList:e=>(0,n.Jt)({url:"/api/llms",data:e?{...l.u,...e}:l.u}),createLlm:e=>(0,n.bE)({url:"/api/llms",data:e}),updateLlm:(e,i)=>(0,n.yJ)({url:"/api/llms/".concat(e),data:i}),deleteLlm:e=>(0,n.yH)({url:"/api/llms/".concat(e)}),getLlmProviders:()=>(0,n.Jt)({url:"/api/llms/providers"})},r=()=>(0,t.A)(a.getLlmList,{manual:!0}),A=()=>(0,t.A)(a.createLlm,{manual:!0}),o=()=>(0,t.A)(a.updateLlm,{manual:!0}),c=()=>(0,t.A)(a.deleteLlm,{manual:!0}),d=()=>{var e;let i=(0,t.A)(a.getLlmProviders,{});return{...i,data:(null==i?void 0:null===(e=i.data)||void 0===e?void 0:e.success)?i.data.data:[]}}},32853:(e,i,s)=>{"use strict";s.d(i,{A:()=>r});var t=s(95155),n=s(79005),l=s(12115),a=s(47956);let r=e=>{let{title:i,btnProps:s,btnExtraEvent:r,onOk:A,btnText:o,description:c,children:d,modalProps:h}=e,[x,g]=(0,l.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.Ay,{loading:null==s?void 0:s.loading,onClick:()=>{g(!0),null==r||r()},...s,children:o}),x&&(0,t.jsx)(a.A,{title:i,open:x,onCancel:()=>g(!1),onOk:async()=>{await (null==A?void 0:A())&&g(!1)},description:c,...h,children:d})]})}},35587:(e,i,s)=>{"use strict";s.d(i,{A:()=>a});var t=s(95155),n=s(11432),l=s(71349);function a(e){let{children:i,...s}=e;return(0,t.jsx)(n.Ay,{theme:{components:{Card:{headerBg:"#171F2D",colorBgContainer:"#f8f8fa",colorTextHeading:"#fff"}}},children:(0,t.jsx)(l.A,{...s,children:i})})}},35594:(e,i,s)=>{"use strict";s.d(i,{Jt:()=>a,bE:()=>r,yH:()=>o,yJ:()=>A});var t=s(43932),n=s.n(t);function l(e,i){return Promise.resolve(n().ajax({method:e,url:i.path||i.url,data:"GET"===e?i.data:JSON.stringify(i.data),contentType:"application/json;charset=UTF-8"}))}let a=e=>l("GET",e),r=e=>l("POST",e),A=e=>l("PUT",e),o=e=>l("DELETE",e)},42592:(e,i,s)=>{"use strict";s.d(i,{_O:()=>a,m2:()=>r,Lt:()=>o,HB:()=>c,Fj:()=>A});var t=s(69653),n=s(35594);let l={getAssistantList:e=>(0,n.Jt)({url:"/api/evalapp",data:e}),createAssistant:e=>(0,n.bE)({url:"/api/evalapp",data:e}),updateAssistant:(e,i)=>(0,n.yJ)({url:"/api/evalapp/".concat(i),data:e}),getAssistantDetail:e=>(0,n.Jt)({url:"/api/evalapp/".concat(e)}),deleteAssistant:e=>(0,n.yH)({url:"/api/evalapp/".concat(e)})},a=()=>(0,t.A)(l.getAssistantList,{manual:!0}),r=()=>(0,t.A)(l.createAssistant,{manual:!0}),A=()=>(0,t.A)(l.updateAssistant,{manual:!0}),o=()=>(0,t.A)(l.deleteAssistant,{manual:!0}),c=()=>(0,t.A)(l.getAssistantDetail,{manual:!0})},47956:(e,i,s)=>{"use strict";s.d(i,{A:()=>d});var t=s(95155),n=s(46742),l=s(21382),a=s(22810),r=s(11432),A=s(68773),o=s(79005);let{Text:c}=n.A,d=e=>{let{open:i,title:s,onOk:n,onCancel:d,description:h,children:x,cancelText:g="取消",okText:u="确定",...I}=e;return(0,t.jsxs)(l.A,{title:s,open:i,onCancel:d,onOk:n,footer:(0,t.jsx)(a.A,{justify:"end",children:(0,t.jsx)(r.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:(0,t.jsxs)(A.A,{children:[g&&(0,t.jsx)(o.Ay,{onClick:d,style:{border:"none"},children:g}),u&&(0,t.jsx)(o.Ay,{type:"primary",onClick:n,children:u})]})})}),...I,children:[h&&(0,t.jsx)(c,{type:"secondary",children:h}),x]})}},56409:e=>{e.exports={doubao:"list_doubao__sXuUq"}},60760:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>J});var t=s(95155),n=s(32853),l=s(7250),a=s(86306),r=s(81488),A=s(71126),o=s(42592),c=s(55750),d=s(41379),h=s(59276),x=s(41657),g=s(46742),u=s(72093),I=s(64787),k=s(32392),m=s(22810),B=s(2796),E=s(78444),R=s(48904),D=s(6457),w=s(68773),j=s(42426),C=s(79005),Q=s(11432),p=s(76046),y=s(12115),G=s(81548),H=s.n(G);let{Content:v}=h.A,{TextArea:f}=x.A,{Title:F,Text:Y}=g.A;function J(){return(0,t.jsx)(u.A,{spinning:!1,children:(0,t.jsx)(M,{})})}function M(){let{message:e}=I.A.useApp(),i=(0,p.useRouter)(),s=(0,p.useSearchParams)().get("id"),{runAsync:h,loading:g}=(0,o.m2)(),{data:G,runAsync:v,loading:J}=(0,o.HB)(),{runAsync:M,loading:U}=(0,o.Fj)(),{runAsync:S,loading:O}=(0,o.Lt)(),[L,K]=(0,y.useState)(!0),[N,P]=(0,y.useState)(""),[b,W]=(0,y.useState)(""),[z,Z]=(0,y.useState)(!1),[V,T]=(0,y.useState)(null),[X,q]=(0,y.useState)("edit"),[_]=(0,y.useState)(),[$]=k.A.useForm(),ee=k.A.useWatch(["llmConfig","temperature"],$);(0,y.useEffect)(()=>{s&&v(s).then(e=>{if(e.success){let{name:i,desc:s,avatar:t,disabled:n}=e.data;P(i),W(s),Z(!n),T(t),q("view"),K(!1),$.setFieldsValue({...e.data,llmConfig:{temperature:e.data.temperature,systemMessage:e.data.systemMessage}})}})},[s]);let ei="view"===X,es=async t=>{let n;let l={...t,name:N,desc:b,avatar:V,disabled:!z,status:z?"free":"offline",llmConfig:void 0,...t.llmConfig?t.llmConfig:{}};(s?await M(l,s):await h(l)).success?(q("view"),e.success("保存成功"),i.push((0,r.I)({url:A.Nv.AnalysisAssistantList}))):e.error("保存失败")};return(0,t.jsx)(l.A,{children:(0,t.jsx)(u.A,{spinning:s&&L&&(!G||J),children:(0,t.jsxs)(I.A,{className:H().container,children:[(0,t.jsxs)(m.A,{style:{marginBottom:"60px"},gutter:12,children:[(0,t.jsx)(B.A,{children:ei?(0,t.jsx)(E.A,{size:40,src:V,icon:(0,t.jsx)(c.A,{})}):(0,t.jsx)(R.A,{showUploadList:!1,maxCount:1,action:"/api/image/upload",accept:"image/*",beforeUpload:i=>{let s=i.size/1024/1024<2;return s||e.error("图片大小不超过2MB"),s},onChange:async i=>{"done"===i.file.status?i.file.response.success?T(i.file.response.data.url):e.error("".concat(i.file.name," 文件上传失败")):"error"===i.file.status&&e.error("".concat(i.file.name," 文件上传失败"))},children:(0,t.jsx)(D.A,{title:"点击更换头像",children:(0,t.jsx)(E.A,{size:40,icon:(0,t.jsx)(d.A,{}),src:V||void 0})})})}),ei?(0,t.jsxs)(B.A,{span:23,children:[(0,t.jsxs)(m.A,{justify:"space-between",children:[(0,t.jsx)(B.A,{children:(0,t.jsxs)(w.A,{children:[(0,t.jsx)(F,{level:4,children:N}),(0,t.jsx)(j.A,{checked:z,onChange:e=>Z(e)})]})}),(0,t.jsx)(B.A,{children:(0,t.jsx)(C.Ay,{onClick:()=>q("edit"),loading:U,children:"编辑"})})]}),(0,t.jsx)(Y,{type:"secondary",children:b})]}):(0,t.jsxs)(B.A,{span:23,children:[(0,t.jsxs)(m.A,{justify:"space-between",children:[(0,t.jsx)(B.A,{children:(0,t.jsx)(Q.Ay,{theme:{components:{Input:{colorTextPlaceholder:"#000"}}},children:(0,t.jsx)(x.A,{placeholder:"请输入分析助手名称",variant:"borderless",style:{paddingLeft:0,fontSize:"20px",fontWeight:"500"},value:N,onChange:e=>P(e.target.value)})})}),(0,t.jsx)(C.Ay,{type:"primary",loading:g||U,onClick:$.submit,children:"完成"})]}),(0,t.jsx)(f,{style:{marginTop:"20px"},placeholder:"请输入该分析助手名称简介",autoSize:{minRows:2,maxRows:6},value:b,onChange:e=>W(e.target.value)})]})]}),(0,t.jsxs)(k.A,{layout:"vertical",form:$,initialValues:{name:N,desc:b,avatar:V},onFinish:es,children:[(0,t.jsx)(m.A,{gutter:[24,24],align:"stretch",children:(0,t.jsx)(B.A,{span:24,children:(0,t.jsx)(a.A,{form:$,llmParams:_,temperature:ee,visibleFields:["llmId","systemMessage","temperature"]})})}),(0,t.jsxs)(m.A,{justify:"space-between",align:"middle",style:{marginTop:"20px"},children:[(0,t.jsxs)(w.A,{size:16,children:[(0,t.jsx)(C.Ay,{type:"primary",onClick:$.submit,loading:U||g,children:"保存"}),(0,t.jsx)(C.Ay,{onClick:()=>i.back(),children:"取消"})]}),s&&(0,t.jsx)(n.A,{btnText:"删除分析助手",title:"确定删除该分析助手？",btnProps:{loading:O},onOk:async()=>(await S(s)).success?(e.success("删除成功"),i.back(),!0):(e.error("删除失败"),!1)})]})]})]})})})}},81488:(e,i,s)=>{"use strict";s.d(i,{I:()=>t,O:()=>n});let t=e=>{let{url:i,params:s,isPage:t=!0}=e,n=i;return n="/ui".concat(n),t&&(n="".concat(n,".html")),s&&(n="".concat(n,"?").concat(new URLSearchParams(s).toString())),n},n=(e,i)=>i?(e/i*100).toFixed(2):"-"},81548:e=>{e.exports={container:"page_container__hGi22",section:"page_section__vdazD"}},86306:(e,i,s)=>{"use strict";s.d(i,{A:()=>D});var t=s(95155),n=s(41657),l=s(32392),a=s(22810),r=s(2796),A=s(89797),o=s(18198),c=s(35587),d=s(30259),h=s(21614),x=s(68773),g=s(78444),u=s(12115),I=s(28532),k=s(56409),m=s.n(k);function B(e){var i;let{data:s,loading:n,runAsync:l}=(0,d.zK)(),a=null!==(i=(null==s?void 0:s.success)&&s.data.records)&&void 0!==i?i:[];return(0,u.useEffect)(()=>{l()},[]),(0,t.jsx)(h.A,{options:null==a?void 0:a.map(e=>({...e,label:(0,t.jsxs)(x.A,{children:[e.icon?(0,t.jsx)(g.A,{src:e.icon,size:18}):(0,t.jsx)(t.Fragment,{children:e.provider.startsWith("ArkV3")&&(0,t.jsx)("div",{className:m().doubao,children:(0,t.jsx)(I.A,{})})}),e.name]}),value:e.id})),loading:n,placeholder:"请选择",...e})}let{TextArea:E}=n.A,R=["llmId","systemMessage","temperature","welcomeMessage","historyLength"],D=e=>{var i,s,d;let{form:h,llmParams:x,visibleFields:g,temperature:u}=e,I=g||R;return(0,t.jsxs)(c.A,{className:"section",title:"LLM设定",children:[I.includes("llmId")&&(0,t.jsx)(l.A.Item,{name:"llmId",label:"基础模型",children:(0,t.jsx)(B,{})}),I.includes("systemMessage")&&(0,t.jsx)(l.A.Item,{name:["llmConfig","systemMessage"],label:"提示词",children:(0,t.jsx)(E,{placeholder:"请输入提示词",rows:4})}),I.includes("temperature")&&(0,t.jsx)(a.A,{children:(0,t.jsx)(r.A,{span:12,children:(0,t.jsx)(l.A.Item,{name:["llmConfig","temperature"],label:"温度",tooltip:"温度 (Temperature)：控制输出的随机性。高温→更多创意；低温→保守（例：0.2=精准回答，1.0=平衡，2.0=天马行空）",children:(0,t.jsxs)(a.A,{gutter:12,align:"middle",children:[(0,t.jsx)(r.A,{span:22,children:(0,t.jsx)(A.A,{value:u,min:null!==(i=null==x?void 0:x.temperatureMin)&&void 0!==i?i:0,max:null!==(s=null==x?void 0:x.temperatureMax)&&void 0!==s?s:1,step:.1,onChange:e=>{h.setFieldValue(["llmConfig","temperature"],e)}})}),(0,t.jsx)(r.A,{span:2,children:null!==(d=null==x?void 0:x.temperatureMax)&&void 0!==d?d:"1.0"})]})})})}),I.includes("welcomeMessage")&&(0,t.jsx)(l.A.Item,{name:["llmConfig","welcomeMessage"],label:"欢迎语",children:(0,t.jsx)(n.A,{placeholder:"请输入欢迎语"})}),I.includes("historyLength")&&(0,t.jsx)(l.A.Item,{name:["llmConfig","historyLength"],label:"对话记忆轮数",children:(0,t.jsx)(o.A,{placeholder:"请输入对话记忆轮数",style:{width:"200px"},min:3})})]})}},90603:(e,i,s)=>{"use strict";s.d(i,{u:()=>t}),s(35594);let t={order:"desc",sort:"updateTime"}}},e=>{var i=i=>e(e.s=i);e.O(0,[6772,838,3740,2211,6222,2602,3520,9907,3288,1509,1349,9786,5585,4787,2392,2342,4156,4338,8198,9797,1126,8441,6587,7358],()=>i(9877)),_N_E=e.O()}]);
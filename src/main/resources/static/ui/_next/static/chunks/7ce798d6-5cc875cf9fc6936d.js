(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3740],{43932:function(e,t){var n,r,i;r="undefined"!=typeof window?window:this,i=function(r,i){"use strict";var o,a=[],s=Object.getPrototypeOf,u=a.slice,l=a.flat?function(e){return a.flat.call(e)}:function(e){return a.concat.apply([],e)},c=a.push,f=a.indexOf,p={},d=p.toString,h=p.hasOwnProperty,g=h.toString,v=g.call(Object),y={},m=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},x=function(e){return null!=e&&e===e.window},b=r.document,T={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var r,i,o=(n=n||b).createElement("script");if(o.text=e,t)for(r in T)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function C(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?p[d.call(e)]||"object":typeof e}var k="3.7.1",E=/HTML$/i,S=function(e,t){return new S.fn.init(e,t)};function j(e){var t=!!e&&"length"in e&&e.length,n=C(e);return!(m(e)||x(e))&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function A(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}S.fn=S.prototype={jquery:k,constructor:S,length:0,toArray:function(){return u.call(this)},get:function(e){return null==e?u.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=S.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return S.each(this,e)},map:function(e){return this.pushStack(S.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(u.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(S.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:a.sort,splice:a.splice},S.extend=S.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||m(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(S.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||S.isPlainObject(n)?n:{},i=!1,a[t]=S.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},S.extend({expando:"jQuery"+(k+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!!e&&"[object Object]"===d.call(e)&&(!(t=s(e))||"function"==typeof(n=h.call(t,"constructor")&&t.constructor)&&g.call(n)===v)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(j(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=S.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(j(Object(e))?S.merge(n,"string"==typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:f.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!E.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(j(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return l(a)},guid:1,support:y}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=a[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){p["[object "+t+"]"]=t.toLowerCase()});var D=a.pop,N=a.sort,q=a.splice,L="[\\x20\\t\\r\\n\\f]",H=RegExp("^"+L+"+|((?:^|[^\\\\])(?:\\\\.)*)"+L+"+$","g");S.contains=function(e,t){var n=t&&t.parentNode;return e===n||!!(n&&1===n.nodeType&&(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var O=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function P(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}S.escapeSelector=function(e){return(e+"").replace(O,P)},!function(){var e,t,n,i,o,s,l,p,d,g,v=c,m=S.expando,x=0,T=0,w=ee(),C=ee(),k=ee(),E=ee(),j=function(e,t){return e===t&&(o=!0),0},O="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",P="(?:\\\\[\\da-fA-F]{1,6}"+L+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",M="\\["+L+"*("+P+")(?:"+L+"*([*^$|!~]?=)"+L+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+P+"))|)"+L+"*\\]",R=":("+P+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+M+")*)|.*)\\)|)",I=RegExp(L+"+","g"),W=RegExp("^"+L+"*,"+L+"*"),F=RegExp("^"+L+"*([>+~]|"+L+")"+L+"*"),$=RegExp(L+"|>"),_=new RegExp(R),B=RegExp("^"+P+"$"),z={ID:RegExp("^#("+P+")"),CLASS:RegExp("^\\.("+P+")"),TAG:RegExp("^("+P+"|[*])"),ATTR:RegExp("^"+M),PSEUDO:RegExp("^"+R),CHILD:RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+L+"*(even|odd|(([+-]|)(\\d*)n|)"+L+"*(?:([+-]|)"+L+"*(\\d+)|))"+L+"*\\)|)","i"),bool:RegExp("^(?:"+O+")$","i"),needsContext:RegExp("^"+L+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+L+"*((?:-\\d)?\\d*)"+L+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,U=/^h\d$/i,V=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,G=/[+~]/,Y=RegExp("\\\\[\\da-fA-F]{1,6}"+L+"?|\\\\([^\\r\\n\\f])","g"),Q=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},J=function(){ea()},K=ec(function(e){return!0===e.disabled&&A(e,"fieldset")},{dir:"parentNode",next:"legend"});try{v.apply(a=u.call(b.childNodes),b.childNodes),a[b.childNodes.length].nodeType}catch(e){v={apply:function(e,t){c.apply(e,u.call(t))},call:function(e){c.apply(e,u.call(arguments,1))}}}function Z(e,t,n,r){var i,o,a,u,l,c,f,h=t&&t.ownerDocument,g=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==g&&9!==g&&11!==g)return n;if(!r&&(ea(t),t=t||s,p)){if(11!==g&&(l=V.exec(e))){if(i=l[1]){if(9===g){if(!(a=t.getElementById(i)))return n;if(a.id===i)return v.call(n,a),n}else if(h&&(a=h.getElementById(i))&&Z.contains(t,a)&&a.id===i)return v.call(n,a),n}else if(l[2])return v.apply(n,t.getElementsByTagName(e)),n;else if((i=l[3])&&t.getElementsByClassName)return v.apply(n,t.getElementsByClassName(i)),n}if(!E[e+" "]&&(!d||!d.test(e))){if(f=e,h=t,1===g&&($.test(e)||F.test(e))){for((h=G.test(e)&&eo(t.parentNode)||t)==t&&y.scope||((u=t.getAttribute("id"))?u=S.escapeSelector(u):t.setAttribute("id",u=m)),o=(c=eu(e)).length;o--;)c[o]=(u?"#"+u:":scope")+" "+el(c[o]);f=c.join(",")}try{return v.apply(n,h.querySelectorAll(f)),n}catch(t){E(e,!0)}finally{u===m&&t.removeAttribute("id")}}}return eh(e.replace(H,"$1"),t,n,r)}function ee(){var e=[];return function n(r,i){return e.push(r+" ")>t.cacheLength&&delete n[e.shift()],n[r+" "]=i}}function et(e){return e[m]=!0,e}function en(e){var t=s.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function er(e){return function(t){if("form"in t){if(t.parentNode&&!1===t.disabled)return"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||!e!==t.isDisabled&&K(t)===e;return t.disabled===e}return"label"in t&&t.disabled===e}}function ei(e){return et(function(t){return t*=1,et(function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))})})}function eo(e){return e&&void 0!==e.getElementsByTagName&&e}function ea(e){var n,r=e?e.ownerDocument||e:b;return r!=s&&9===r.nodeType&&r.documentElement&&(l=(s=r).documentElement,p=!S.isXMLDoc(s),g=l.matches||l.webkitMatchesSelector||l.msMatchesSelector,l.msMatchesSelector&&b!=s&&(n=s.defaultView)&&n.top!==n&&n.addEventListener("unload",J),y.getById=en(function(e){return l.appendChild(e).id=S.expando,!s.getElementsByName||!s.getElementsByName(S.expando).length}),y.disconnectedMatch=en(function(e){return g.call(e,"*")}),y.scope=en(function(){return s.querySelectorAll(":scope")}),y.cssHas=en(function(){try{return s.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}}),y.getById?(t.filter.ID=function(e){var t=e.replace(Y,Q);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&p){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(Y,Q);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&p){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&p)return t.getElementsByClassName(e)},d=[],en(function(e){var t;l.appendChild(e).innerHTML="<a id='"+m+"' href='' disabled='disabled'></a><select id='"+m+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||d.push("\\["+L+"*(?:value|"+O+")"),e.querySelectorAll("[id~="+m+"-]").length||d.push("~="),e.querySelectorAll("a#"+m+"+*").length||d.push(".#.+[+~]"),e.querySelectorAll(":checked").length||d.push(":checked"),(t=s.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),l.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(t=s.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||d.push("\\["+L+"*name"+L+"*="+L+"*(?:''|\"\")")}),y.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),j=function(e,t){if(e===t)return o=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n?n:1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!y.sortDetached&&t.compareDocumentPosition(e)===n?e===s||e.ownerDocument==b&&Z.contains(b,e)?-1:t===s||t.ownerDocument==b&&Z.contains(b,t)?1:i?f.call(i,e)-f.call(i,t):0:4&n?-1:1}),s}for(e in Z.matches=function(e,t){return Z(e,null,null,t)},Z.matchesSelector=function(e,t){if(ea(e),p&&!E[t+" "]&&(!d||!d.test(t)))try{var n=g.call(e,t);if(n||y.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){E(t,!0)}return Z(t,s,null,[e]).length>0},Z.contains=function(e,t){return(e.ownerDocument||e)!=s&&ea(e),S.contains(e,t)},Z.attr=function(e,n){(e.ownerDocument||e)!=s&&ea(e);var r=t.attrHandle[n.toLowerCase()],i=r&&h.call(t.attrHandle,n.toLowerCase())?r(e,n,!p):void 0;return void 0!==i?i:e.getAttribute(n)},Z.error=function(e){throw Error("Syntax error, unrecognized expression: "+e)},S.uniqueSort=function(e){var t,n=[],r=0,a=0;if(o=!y.sortStable,i=!y.sortStable&&u.call(e,0),N.call(e,j),o){for(;t=e[a++];)t===e[a]&&(r=n.push(a));for(;r--;)q.call(e,n[r],1)}return i=null,e},S.fn.uniqueSort=function(){return this.pushStack(S.uniqueSort(u.apply(this)))},(t=S.expr={cacheLength:50,createPseudo:et,match:z,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Y,Q),e[3]=(e[3]||e[4]||e[5]||"").replace(Y,Q),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Z.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Z.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return z.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&_.test(n)&&(t=eu(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Y,Q).toLowerCase();return"*"===e?function(){return!0}:function(e){return A(e,t)}},CLASS:function(e){var t=w[e+" "];return t||(t=RegExp("(^|"+L+")"+e+"("+L+"|$)"),w(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=Z.attr(r,e);return null==i?"!="===t:!t||((i+="","="===t)?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(I," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,f,p,d,h=o!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),y=!u&&!s,b=!1;if(g){if(o){for(;h;){for(f=t;f=f[h];)if(s?A(f,v):1===f.nodeType)return!1;d=h="only"===e&&!d&&"nextSibling"}return!0}if(d=[a?g.firstChild:g.lastChild],a&&y){for(b=(p=(l=(c=g[m]||(g[m]={}))[e]||[])[0]===x&&l[1])&&l[2],f=p&&g.childNodes[p];f=++p&&f&&f[h]||(b=p=0)||d.pop();)if(1===f.nodeType&&++b&&f===t){c[e]=[x,p,b];break}}else if(y&&(b=p=(l=(c=t[m]||(t[m]={}))[e]||[])[0]===x&&l[1]),!1===b)for(;(f=++p&&f&&f[h]||(b=p=0)||d.pop())&&(!((s?A(f,v):1===f.nodeType)&&++b)||(y&&((c=f[m]||(f[m]={}))[e]=[x,b]),f!==t)););return(b-=i)===r||b%r==0&&b/r>=0}}},PSEUDO:function(e,n){var r,i=t.pseudos[e]||t.setFilters[e.toLowerCase()]||Z.error("unsupported pseudo: "+e);return i[m]?i(n):i.length>1?(r=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?et(function(e,t){for(var r,o=i(e,n),a=o.length;a--;)r=f.call(e,o[a]),e[r]=!(t[r]=o[a])}):function(e){return i(e,0,r)}):i}},pseudos:{not:et(function(e){var t=[],n=[],r=ed(e.replace(H,"$1"));return r[m]?et(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:et(function(e){return function(t){return Z(e,t).length>0}}),contains:et(function(e){return e=e.replace(Y,Q),function(t){return(t.textContent||S.text(t)).indexOf(e)>-1}}),lang:et(function(e){return B.test(e||"")||Z.error("unsupported lang: "+e),e=e.replace(Y,Q).toLowerCase(),function(t){var n;do if(n=p?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(e){var t=r.location&&r.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===l},focus:function(e){return e===function(){try{return s.activeElement}catch(e){}}()&&s.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:er(!1),disabled:er(!0),checked:function(e){return A(e,"input")&&!!e.checked||A(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return U.test(e.nodeName)},input:function(e){return X.test(e.nodeName)},button:function(e){return A(e,"input")&&"button"===e.type||A(e,"button")},text:function(e){var t;return A(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ei(function(){return[0]}),last:ei(function(e,t){return[t-1]}),eq:ei(function(e,t,n){return[n<0?n+t:n]}),even:ei(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:ei(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:ei(function(e,t,n){var r;for(r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e}),gt:ei(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=function(e){return function(t){return A(t,"input")&&t.type===e}}(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=function(e){return function(t){return(A(t,"input")||A(t,"button"))&&t.type===e}}(e);function es(){}function eu(e,n){var r,i,o,a,s,u,l,c=C[e+" "];if(c)return n?0:c.slice(0);for(s=e,u=[],l=t.preFilter;s;){for(a in(!r||(i=W.exec(s)))&&(i&&(s=s.slice(i[0].length)||s),u.push(o=[])),r=!1,(i=F.exec(s))&&(r=i.shift(),o.push({value:r,type:i[0].replace(H," ")}),s=s.slice(r.length)),t.filter)(i=z[a].exec(s))&&(!l[a]||(i=l[a](i)))&&(r=i.shift(),o.push({value:r,type:a,matches:i}),s=s.slice(r.length));if(!r)break}return n?s.length:s?Z.error(e):C(e,u).slice(0)}function el(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ec(e,t,n){var r=t.dir,i=t.next,o=i||r,a=n&&"parentNode"===o,s=T++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||a)return e(t,n,i);return!1}:function(t,n,u){var l,c,f=[x,s];if(u){for(;t=t[r];)if((1===t.nodeType||a)&&e(t,n,u))return!0}else for(;t=t[r];)if(1===t.nodeType||a){if(c=t[m]||(t[m]={}),i&&A(t,i))t=t[r]||t;else if((l=c[o])&&l[0]===x&&l[1]===s)return f[2]=l[2];else if(c[o]=f,f[2]=e(t,n,u))return!0}return!1}}function ef(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function ep(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(!n||n(o,r,i))&&(a.push(o),l&&t.push(s));return a}function ed(e,r){var i,o,a,u,l=[],c=[],d=k[e+" "];if(!d){for(r||(r=eu(e)),u=r.length;u--;)(d=function e(r){for(var i,o,a,s=r.length,u=t.relative[r[0].type],l=u||t.relative[" "],c=+!!u,p=ec(function(e){return e===i},l,!0),d=ec(function(e){return f.call(i,e)>-1},l,!0),h=[function(e,t,r){var o=!u&&(r||t!=n)||((i=t).nodeType?p(e,t,r):d(e,t,r));return i=null,o}];c<s;c++)if(o=t.relative[r[c].type])h=[ec(ef(h),o)];else{if((o=t.filter[r[c].type].apply(null,r[c].matches))[m]){for(a=++c;a<s&&!t.relative[r[a].type];a++);return function e(t,n,r,i,o,a){return i&&!i[m]&&(i=e(i)),o&&!o[m]&&(o=e(o,a)),et(function(e,a,s,u){var l,c,p,d,h=[],g=[],y=a.length,m=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)Z(e,t[r],n);return n}(n||"*",s.nodeType?[s]:s,[]),x=t&&(e||!n)?ep(m,h,t,s,u):m;if(r?r(x,d=o||(e?t:y||i)?[]:a,s,u):d=x,i)for(l=ep(d,g),i(l,[],s,u),c=l.length;c--;)(p=l[c])&&(d[g[c]]=!(x[g[c]]=p));if(e){if(o||t){if(o){for(l=[],c=d.length;c--;)(p=d[c])&&l.push(x[c]=p);o(null,d=[],l,u)}for(c=d.length;c--;)(p=d[c])&&(l=o?f.call(e,p):h[c])>-1&&(e[l]=!(a[l]=p))}}else d=ep(d===a?d.splice(y,d.length):d),o?o(null,a,d,u):v.apply(a,d)})}(c>1&&ef(h),c>1&&el(r.slice(0,c-1).concat({value:" "===r[c-2].type?"*":""})).replace(H,"$1"),o,c<a&&e(r.slice(c,a)),a<s&&e(r=r.slice(a)),a<s&&el(r))}h.push(o)}return ef(h)}(r[u]))[m]?l.push(d):c.push(d);(d=k(e,(i=l.length>0,o=c.length>0,a=function(e,r,a,u,f){var d,h,g,y=0,m="0",b=e&&[],T=[],w=n,C=e||o&&t.find.TAG("*",f),k=x+=null==w?1:Math.random()||.1,E=C.length;for(f&&(n=r==s||r||f);m!==E&&null!=(d=C[m]);m++){if(o&&d){for(h=0,r||d.ownerDocument==s||(ea(d),a=!p);g=c[h++];)if(g(d,r||s,a)){v.call(u,d);break}f&&(x=k)}i&&((d=!g&&d)&&y--,e&&b.push(d))}if(y+=m,i&&m!==y){for(h=0;g=l[h++];)g(b,T,r,a);if(e){if(y>0)for(;m--;)b[m]||T[m]||(T[m]=D.call(u));T=ep(T)}v.apply(u,T),f&&!e&&T.length>0&&y+l.length>1&&S.uniqueSort(u)}return f&&(x=k,n=w),b},i?et(a):a))).selector=e}return d}function eh(e,n,r,i){var o,a,s,u,l,c="function"==typeof e&&e,f=!i&&eu(e=c.selector||e);if(r=r||[],1===f.length){if((a=f[0]=f[0].slice(0)).length>2&&"ID"===(s=a[0]).type&&9===n.nodeType&&p&&t.relative[a[1].type]){if(!(n=(t.find.ID(s.matches[0].replace(Y,Q),n)||[])[0]))return r;c&&(n=n.parentNode),e=e.slice(a.shift().value.length)}for(o=z.needsContext.test(e)?0:a.length;o--&&(s=a[o],!t.relative[u=s.type]);)if((l=t.find[u])&&(i=l(s.matches[0].replace(Y,Q),G.test(a[0].type)&&eo(n.parentNode)||n))){if(a.splice(o,1),!(e=i.length&&el(a)))return v.apply(r,i),r;break}}return(c||ed(e,f))(i,n,!p,r,!n||G.test(e)&&eo(n.parentNode)||n),r}es.prototype=t.filters=t.pseudos,t.setFilters=new es,y.sortStable=m.split("").sort(j).join("")===m,ea(),y.sortDetached=en(function(e){return 1&e.compareDocumentPosition(s.createElement("fieldset"))}),S.find=Z,S.expr[":"]=S.expr.pseudos,S.unique=S.uniqueSort,Z.compile=ed,Z.select=eh,Z.setDocument=ea,Z.tokenize=eu,Z.escape=S.escapeSelector,Z.getText=S.text,Z.isXML=S.isXMLDoc,Z.selectors=S.expr,Z.support=S.support,Z.uniqueSort=S.uniqueSort}();var M=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&S(e).is(n))break;r.push(e)}return r},R=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},I=S.expr.match.needsContext,W=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function F(e,t,n){return m(t)?S.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?S.grep(e,function(e){return e===t!==n}):"string"!=typeof t?S.grep(e,function(e){return f.call(t,e)>-1!==n}):S.filter(t,e,n)}S.filter=function(e,t,n){var r=t[0];return(n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType)?S.find.matchesSelector(r,e)?[r]:[]:S.find.matches(e,S.grep(t,function(e){return 1===e.nodeType}))},S.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(S(e).filter(function(){for(t=0;t<r;t++)if(S.contains(i[t],this))return!0}));for(t=0,n=this.pushStack([]);t<r;t++)S.find(e,i[t],n);return r>1?S.uniqueSort(n):n},filter:function(e){return this.pushStack(F(this,e||[],!1))},not:function(e){return this.pushStack(F(this,e||[],!0))},is:function(e){return!!F(this,"string"==typeof e&&I.test(e)?S(e):e||[],!1).length}});var $,_=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||$,"string"==typeof e){if((r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:_.exec(e))&&(r[1]||!t)){if(!r[1])return(i=b.getElementById(r[2]))&&(this[0]=i,this.length=1),this;if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),W.test(r[1])&&S.isPlainObject(t))for(r in t)m(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e)}return e.nodeType?(this[0]=e,this.length=1,this):m(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this)}).prototype=S.fn,$=S(b);var B=/^(?:parents|prev(?:Until|All))/,z={children:!0,contents:!0,next:!0,prev:!0};function X(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&S(e);if(!I.test(e)){for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&S.find.matchesSelector(n,e))){o.push(n);break}}return this.pushStack(o.length>1?S.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?f.call(S(e),this[0]):f.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return M(e,"parentNode")},parentsUntil:function(e,t,n){return M(e,"parentNode",n)},next:function(e){return X(e,"nextSibling")},prev:function(e){return X(e,"previousSibling")},nextAll:function(e){return M(e,"nextSibling")},prevAll:function(e){return M(e,"previousSibling")},nextUntil:function(e,t,n){return M(e,"nextSibling",n)},prevUntil:function(e,t,n){return M(e,"previousSibling",n)},siblings:function(e){return R((e.parentNode||{}).firstChild,e)},children:function(e){return R(e.firstChild)},contents:function(e){return null!=e.contentDocument&&s(e.contentDocument)?e.contentDocument:(A(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},function(e,t){S.fn[e]=function(n,r){var i=S.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=S.filter(r,i)),this.length>1&&(z[e]||S.uniqueSort(i),B.test(e)&&i.reverse()),this.pushStack(i)}});var U=/[^\x20\t\r\n\f]+/g;function V(e){return e}function G(e){throw e}function Y(e,t,n,r){var i;try{e&&m(i=e.promise)?i.call(e).done(t).fail(n):e&&m(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(e){e="string"==typeof e?(t=e,n={},S.each(t.match(U)||[],function(e,t){n[t]=!0}),n):S.extend({},e);var t,n,r,i,o,a,s=[],u=[],l=-1,c=function(){for(a=a||e.once,o=r=!0;u.length;l=-1)for(i=u.shift();++l<s.length;)!1===s[l].apply(i[0],i[1])&&e.stopOnFalse&&(l=s.length,i=!1);e.memory||(i=!1),r=!1,a&&(s=i?[]:"")},f={add:function(){return s&&(i&&!r&&(l=s.length-1,u.push(i)),function t(n){S.each(n,function(n,r){m(r)?e.unique&&f.has(r)||s.push(r):r&&r.length&&"string"!==C(r)&&t(r)})}(arguments),i&&!r&&c()),this},remove:function(){return S.each(arguments,function(e,t){for(var n;(n=S.inArray(t,s,n))>-1;)s.splice(n,1),n<=l&&l--}),this},has:function(e){return e?S.inArray(e,s)>-1:s.length>0},empty:function(){return s&&(s=[]),this},disable:function(){return a=u=[],s=i="",this},disabled:function(){return!s},lock:function(){return a=u=[],i||r||(s=i=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),r||c()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!o}};return f},S.extend({Deferred:function(e){var t=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return S.Deferred(function(n){S.each(t,function(t,r){var i=m(e[r[4]])&&e[r[4]];o[r[1]](function(){var e=i&&i.apply(this,arguments);e&&m(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(e,n,i){var o=0;function a(e,t,n,i){return function(){var s=this,u=arguments,l=function(){var r,l;if(!(e<o)){if((r=n.apply(s,u))===t.promise())throw TypeError("Thenable self-resolution");m(l=r&&("object"==typeof r||"function"==typeof r)&&r.then)?i?l.call(r,a(o,t,V,i),a(o,t,G,i)):(o++,l.call(r,a(o,t,V,i),a(o,t,G,i),a(o,t,V,t.notifyWith))):(n!==V&&(s=void 0,u=[r]),(i||t.resolveWith)(s,u))}},c=i?l:function(){try{l()}catch(r){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(r,c.error),e+1>=o&&(n!==G&&(s=void 0,u=[r]),t.rejectWith(s,u))}};e?c():(S.Deferred.getErrorHook?c.error=S.Deferred.getErrorHook():S.Deferred.getStackHook&&(c.error=S.Deferred.getStackHook()),r.setTimeout(c))}}return S.Deferred(function(r){t[0][3].add(a(0,r,m(i)?i:V,r.notifyWith)),t[1][3].add(a(0,r,m(e)?e:V)),t[2][3].add(a(0,r,m(n)?n:G))}).promise()},promise:function(e){return null!=e?S.extend(e,i):i}},o={};return S.each(t,function(e,r){var a=r[2],s=r[5];i[r[1]]=a.add,s&&a.add(function(){n=s},t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),a.add(r[3].fire),o[r[0]]=function(){return o[r[0]+"With"](this===o?void 0:this,arguments),this},o[r[0]+"With"]=a.fireWith}),i.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=u.call(arguments),o=S.Deferred(),a=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?u.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(Y(e,o.done(a(n)).resolve,o.reject,!t),"pending"===o.state()||m(i[n]&&i[n].then)))return o.then();for(;n--;)Y(i[n],a(n),o.reject);return o.promise()}});var Q=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){r.console&&r.console.warn&&e&&Q.test(e.name)&&r.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){r.setTimeout(function(){throw e})};var J=S.Deferred();function K(){b.removeEventListener("DOMContentLoaded",K),r.removeEventListener("load",K),S.ready()}S.fn.ready=function(e){return J.then(e).catch(function(e){S.readyException(e)}),this},S.extend({isReady:!1,readyWait:1,ready:function(e){if(!(!0===e?--S.readyWait:S.isReady))S.isReady=!0,!(!0!==e&&--S.readyWait>0)&&J.resolveWith(b,[S])}}),S.ready.then=J.then,"complete"!==b.readyState&&("loading"===b.readyState||b.documentElement.doScroll)?(b.addEventListener("DOMContentLoaded",K),r.addEventListener("load",K)):r.setTimeout(S.ready);var Z=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===C(n))for(s in i=!0,n)Z(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,m(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(S(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},ee=/^-ms-/,et=/-([a-z])/g;function en(e,t){return t.toUpperCase()}function er(e){return e.replace(ee,"ms-").replace(et,en)}var ei=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function eo(){this.expando=S.expando+eo.uid++}eo.uid=1,eo.prototype={cache:function(e){var t=e[this.expando];return!t&&(t={},ei(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[er(t)]=n;else for(r in t)i[er(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][er(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t)for(n=(t=Array.isArray(t)?t.map(er):((t=er(t))in r)?[t]:t.match(U)||[]).length;n--;)delete r[t[n]];(void 0===t||S.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!S.isEmptyObject(t)}};var ea=new eo,es=new eo,eu=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,el=/[A-Z]/g;function ec(e,t,n){var r,i;if(void 0===n&&1===e.nodeType){if(r="data-"+t.replace(el,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{i=n,n="true"===i||"false"!==i&&("null"===i?null:i===+i+""?+i:eu.test(i)?JSON.parse(i):i)}catch(e){}es.set(e,t,n)}else n=void 0}return n}S.extend({hasData:function(e){return es.hasData(e)||ea.hasData(e)},data:function(e,t,n){return es.access(e,t,n)},removeData:function(e,t){es.remove(e,t)},_data:function(e,t,n){return ea.access(e,t,n)},_removeData:function(e,t){ea.remove(e,t)}}),S.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=es.get(o),1===o.nodeType&&!ea.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&0===(r=a[n].name).indexOf("data-")&&ec(o,r=er(r.slice(5)),i[r]);ea.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each(function(){es.set(this,e)}):Z(this,function(t){var n;if(o&&void 0===t)return void 0!==(n=es.get(o,e))||void 0!==(n=ec(o,e))?n:void 0;this.each(function(){es.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){es.remove(this,e)})}}),S.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=ea.get(e,t),n&&(!r||Array.isArray(n)?r=ea.access(e,t,S.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),r=n.length,i=n.shift(),o=S._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){S.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ea.get(e,n)||ea.access(e,n,{empty:S.Callbacks("once memory").add(function(){ea.remove(e,[t+"queue",n])})})}}),S.fn.extend({queue:function(e,t){var n=2;return("string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n)?S.queue(this[0],e):void 0===t?this:this.each(function(){var n=S.queue(this,e,t);S._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&S.dequeue(this,e)})},dequeue:function(e){return this.each(function(){S.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=S.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=ea.get(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var ef=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ep=RegExp("^(?:([+-])=|)("+ef+")([a-z%]*)$","i"),ed=["Top","Right","Bottom","Left"],eh=b.documentElement,eg=function(e){return S.contains(e.ownerDocument,e)},ev={composed:!0};eh.getRootNode&&(eg=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(ev)===e.ownerDocument});var ey=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&eg(e)&&"none"===S.css(e,"display")};function em(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return S.css(e,t,"")},u=s(),l=n&&n[3]||(S.cssNumber[t]?"":"px"),c=e.nodeType&&(S.cssNumber[t]||"px"!==l&&+u)&&ep.exec(S.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;a--;)S.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,S.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ex={};function eb(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)(r=e[o]).style&&(n=r.style.display,t?("none"!==n||(i[o]=ea.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&ey(r)&&(i[o]=function(e){var t,n=e.ownerDocument,r=e.nodeName,i=ex[r];return i||(t=n.body.appendChild(n.createElement(r)),i=S.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),ex[r]=i),i}(r))):"none"!==n&&(i[o]="none",ea.set(r,"display",n)));for(o=0;o<a;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}S.fn.extend({show:function(){return eb(this,!0)},hide:function(){return eb(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ey(this)?S(this).show():S(this).hide()})}});var eT=/^(?:checkbox|radio)$/i,ew=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,eC=/^$|^module$|\/(?:java|ecma)script/i;e9=b.createDocumentFragment().appendChild(b.createElement("div")),(e8=b.createElement("input")).setAttribute("type","radio"),e8.setAttribute("checked","checked"),e8.setAttribute("name","t"),e9.appendChild(e8),y.checkClone=e9.cloneNode(!0).cloneNode(!0).lastChild.checked,e9.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!e9.cloneNode(!0).lastChild.defaultValue,e9.innerHTML="<option></option>",y.option=!!e9.lastChild;var ek={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function eE(e,t){var n;return(n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&A(e,t))?S.merge([e],n):n}function eS(e,t){for(var n=0,r=e.length;n<r;n++)ea.set(e[n],"globalEval",!t||ea.get(t[n],"globalEval"))}ek.tbody=ek.tfoot=ek.colgroup=ek.caption=ek.thead,ek.th=ek.td,y.option||(ek.optgroup=ek.option=[1,"<select multiple='multiple'>","</select>"]);var ej=/<|&#?\w+;/;function eA(e,t,n,r,i){for(var o,a,s,u,l,c=t.createDocumentFragment(),f=[],p=0,d=e.length;p<d;p++)if((o=e[p])||0===o){if("object"===C(o))S.merge(f,o.nodeType?[o]:o);else if(ej.test(o)){for(a=a||c.appendChild(t.createElement("div")),s=ek[(ew.exec(o)||["",""])[1].toLowerCase()]||ek._default,a.innerHTML=s[1]+S.htmlPrefilter(o)+s[2],l=s[0];l--;)a=a.lastChild;S.merge(f,a.childNodes),(a=c.firstChild).textContent=""}else f.push(t.createTextNode(o))}for(c.textContent="",p=0;o=f[p++];){if(r&&S.inArray(o,r)>-1){i&&i.push(o);continue}if(u=eg(o),a=eE(c.appendChild(o),"script"),u&&eS(a),n)for(l=0;o=a[l++];)eC.test(o.type||"")&&n.push(o)}return c}var eD=/^([^.]*)(?:\.(.+)|)/;function eN(){return!0}function eq(){return!1}function eL(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)eL(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=eq;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return S().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=S.guid++)),e.each(function(){S.event.add(this,t,i,r,n)})}function eH(e,t,n){if(!n){void 0===ea.get(e,t)&&S.event.add(e,t,eN);return}ea.set(e,t,!1),S.event.add(e,t,{namespace:!1,handler:function(e){var n,r=ea.get(this,t);if(1&e.isTrigger&&this[t]){if(r)(S.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=u.call(arguments),ea.set(this,t,r),this[t](),n=ea.get(this,t),ea.set(this,t,!1),r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else r&&(ea.set(this,t,S.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=eN)}})}S.event={global:{},add:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=ea.get(e);if(ei(e))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&S.find.matchesSelector(eh,i),n.guid||(n.guid=S.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(t){return S.event.triggered!==t.type?S.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match(U)||[""]).length;l--;)d=g=(s=eD.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d&&(f=S.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=S.event.special[d]||{},c=S.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&S.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||((p=u[d]=[]).delegateCount=0,(!f.setup||!1===f.setup.call(e,r,h,a))&&e.addEventListener&&e.addEventListener(d,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),S.event.global[d]=!0)},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=ea.hasData(e)&&ea.get(e);if(v&&(u=v.events)){for(l=(t=(t||"").match(U)||[""]).length;l--;){if(d=g=(s=eD.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),!d){for(d in u)S.event.remove(e,d+t[l],n,r,!0);continue}for(f=S.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;o--;)c=p[o],(i||g===c.origType)&&(!n||n.guid===c.guid)&&(!s||s.test(c.namespace))&&(!r||r===c.selector||"**"===r&&c.selector)&&(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||S.removeEvent(e,d,v.handle),delete u[d])}S.isEmptyObject(u)&&ea.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=Array(arguments.length),u=S.event.fix(e),l=(ea.get(this,"events")||Object.create(null))[u.type]||[],c=S.event.special[u.type]||{};for(t=1,s[0]=u;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){for(a=S.event.handlers.call(this,u,l),t=0;(i=a[t++])&&!u.isPropagationStopped();)for(u.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!u.isImmediatePropagationStopped();)(!u.rnamespace||!1===o.namespace||u.rnamespace.test(o.namespace))&&(u.handleObj=o,u.data=o.data,void 0!==(r=((S.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1)){for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(n=0,o=[],a={};n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?S(i,this).index(l)>-1:S.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(e,t){Object.defineProperty(S.Event.prototype,e,{enumerable:!0,configurable:!0,get:m(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return eT.test(t.type)&&t.click&&A(t,"input")&&eH(t,"click",!0),!1},trigger:function(e){var t=this||e;return eT.test(t.type)&&t.click&&A(t,"input")&&eH(t,"click"),!0},_default:function(e){var t=e.target;return eT.test(t.type)&&t.click&&A(t,"input")&&ea.get(t,"click")||A(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?eN:eq,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:eq,isPropagationStopped:eq,isImmediatePropagationStopped:eq,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=eN,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=eN,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=eN,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},function(e,t){function n(e){if(b.documentMode){var n=ea.get(this,"handle"),r=S.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else S.event.simulate(t,e.target,S.event.fix(e))}S.event.special[e]={setup:function(){var r;if(eH(this,e,!0),!b.documentMode)return!1;(r=ea.get(this,t))||this.addEventListener(t,n),ea.set(this,t,(r||0)+1)},trigger:function(){return eH(this,e),!0},teardown:function(){var e;if(!b.documentMode)return!1;(e=ea.get(this,t)-1)?ea.set(this,t,e):(this.removeEventListener(t,n),ea.remove(this,t))},_default:function(t){return ea.get(t.target,e)},delegateType:t},S.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ea.get(i,t);o||(b.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),ea.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ea.get(i,t)-1;o?ea.set(i,t,o):(b.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),ea.remove(i,t))}}}),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){S.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;return r&&(r===this||S.contains(this,r))||(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}}),S.fn.extend({on:function(e,t,n,r){return eL(this,e,t,n,r)},one:function(e,t,n,r){return eL(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,S(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return(!1===t||"function"==typeof t)&&(n=t,t=void 0),!1===n&&(n=eq),this.each(function(){S.event.remove(this,e,n,t)})}});var eO=/<script|<style|<link/i,eP=/checked\s*(?:[^=]|=\s*.checked.)/i,eM=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function eR(e,t){return A(e,"table")&&A(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function eI(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function eW(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function eF(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if(ea.hasData(e)&&(s=ea.get(e).events))for(i in ea.remove(t,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)S.event.add(t,i,s[i][n]);es.hasData(e)&&(o=es.access(e),a=S.extend({},o),es.set(t,a))}}function e$(e,t,n,r){t=l(t);var i,o,a,s,u,c,f=0,p=e.length,d=p-1,h=t[0],g=m(h);if(g||p>1&&"string"==typeof h&&!y.checkClone&&eP.test(h))return e.each(function(i){var o=e.eq(i);g&&(t[0]=h.call(this,i,o.html())),e$(o,t,n,r)});if(p&&(o=(i=eA(t,e[0].ownerDocument,!1,e,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(s=(a=S.map(eE(i,"script"),eI)).length;f<p;f++)u=i,f!==d&&(u=S.clone(u,!0,!0),s&&S.merge(a,eE(u,"script"))),n.call(e[f],u,f);if(s)for(c=a[a.length-1].ownerDocument,S.map(a,eW),f=0;f<s;f++)u=a[f],eC.test(u.type||"")&&!ea.access(u,"globalEval")&&S.contains(c,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?S._evalUrl&&!u.noModule&&S._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},c):w(u.textContent.replace(eM,""),u,c))}return e}function e_(e,t,n){for(var r,i=t?S.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||S.cleanData(eE(r)),r.parentNode&&(n&&eg(r)&&eS(eE(r,"script")),r.parentNode.removeChild(r));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s=e.cloneNode(!0),u=eg(e);if(!y.noCloneChecked&&(1===e.nodeType||11===e.nodeType)&&!S.isXMLDoc(e))for(r=0,a=eE(s),i=(o=eE(e)).length;r<i;r++)!function(e,t){var n=t.nodeName.toLowerCase();"input"===n&&eT.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}(o[r],a[r]);if(t){if(n)for(r=0,o=o||eE(e),a=a||eE(s),i=o.length;r<i;r++)eF(o[r],a[r]);else eF(e,s)}return(a=eE(s,"script")).length>0&&eS(a,!u&&eE(e,"script")),s},cleanData:function(e){for(var t,n,r,i=S.event.special,o=0;void 0!==(n=e[o]);o++)if(ei(n)){if(t=n[ea.expando]){if(t.events)for(r in t.events)i[r]?S.event.remove(n,r):S.removeEvent(n,r,t.handle);n[ea.expando]=void 0}n[es.expando]&&(n[es.expando]=void 0)}}}),S.fn.extend({detach:function(e){return e_(this,e,!0)},remove:function(e){return e_(this,e)},text:function(e){return Z(this,function(e){return void 0===e?S.text(this):this.empty().each(function(){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&(this.textContent=e)})},null,e,arguments.length)},append:function(){return e$(this,arguments,function(e){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&eR(this,e).appendChild(e)})},prepend:function(){return e$(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=eR(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return e$(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return e$(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(eE(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return S.clone(this,e,t)})},html:function(e){return Z(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!eO.test(e)&&!ek[(ew.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<r;n++)t=this[n]||{},1===t.nodeType&&(S.cleanData(eE(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return e$(this,arguments,function(t){var n=this.parentNode;0>S.inArray(this,e)&&(S.cleanData(eE(this)),n&&n.replaceChild(t,this))},e)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){S.fn[e]=function(e){for(var n,r=[],i=S(e),o=i.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),S(i[a])[t](n),c.apply(r,n.get());return this.pushStack(r)}});var eB=RegExp("^("+ef+")(?!px)[a-z%]+$","i"),ez=/^--/,eX=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=r),t.getComputedStyle(e)},eU=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},eV=RegExp(ed.join("|"),"i");function eG(e,t,n){var r,i,o,a,s=ez.test(t),u=e.style;return(n=n||eX(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(H,"$1")||void 0),""!==a||eg(e)||(a=S.style(e,t)),!y.pixelBoxStyles()&&eB.test(a)&&eV.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+"":a}function eY(e,t){return{get:function(){if(e()){delete this.get;return}return(this.get=t).apply(this,arguments)}}}!function(){function e(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",eh.appendChild(l).appendChild(c);var e=r.getComputedStyle(c);n="1%"!==e.top,u=12===t(e.marginLeft),c.style.right="60%",a=36===t(e.right),i=36===t(e.width),c.style.position="absolute",o=12===t(c.offsetWidth/3),eh.removeChild(l),c=null}}function t(e){return Math.round(parseFloat(e))}var n,i,o,a,s,u,l=b.createElement("div"),c=b.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===c.style.backgroundClip,S.extend(y,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),a},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),u},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,n,i;return null==s&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",eh.appendChild(e).appendChild(t).appendChild(n),s=parseInt((i=r.getComputedStyle(t)).height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight,eh.removeChild(e)),s}}))}();var eQ=["Webkit","Moz","ms"],eJ=b.createElement("div").style,eK={};function eZ(e){var t=S.cssProps[e]||eK[e];return t||(e in eJ?e:eK[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=eQ.length;n--;)if((e=eQ[n]+t)in eJ)return e}(e)||e)}var e0=/^(none|table(?!-c[ea]).+)/,e1={position:"absolute",visibility:"hidden",display:"block"},e2={letterSpacing:"0",fontWeight:"400"};function e3(e,t,n){var r=ep.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function e4(e,t,n,r,i,o){var a=+("width"===t),s=0,u=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=S.css(e,n+ed[a],!0,i)),r?("content"===n&&(u-=S.css(e,"padding"+ed[a],!0,i)),"margin"!==n&&(u-=S.css(e,"border"+ed[a]+"Width",!0,i))):(u+=S.css(e,"padding"+ed[a],!0,i),"padding"!==n?u+=S.css(e,"border"+ed[a]+"Width",!0,i):s+=S.css(e,"border"+ed[a]+"Width",!0,i));return!r&&o>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u+l}function e5(e,t,n){var r=eX(e),i=(!y.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,r),o=i,a=eG(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(eB.test(a)){if(!n)return a;a="auto"}return(!y.boxSizingReliable()&&i||!y.reliableTrDimensions()&&A(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===S.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===S.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+e4(e,t,n||(i?"border":"content"),o,r,a)+"px"}function e6(e,t,n,r,i){return new e6.prototype.init(e,t,n,r,i)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=eG(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=er(t),u=ez.test(t),l=e.style;if(u||(t=eZ(s)),a=S.cssHooks[t]||S.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];if("string"==(o=typeof n)&&(i=ep.exec(n))&&i[1]&&(n=em(e,t,i),o="number"),null!=n&&n==n)"number"!==o||u||(n+=i&&i[3]||(S.cssNumber[s]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n)}},css:function(e,t,n,r){var i,o,a,s=er(t);return(ez.test(t)||(t=eZ(s)),(a=S.cssHooks[t]||S.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=eG(e,t,r)),"normal"===i&&t in e2&&(i=e2[t]),""===n||n)?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),S.each(["height","width"],function(e,t){S.cssHooks[t]={get:function(e,n,r){if(n)return!e0.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?e5(e,t,r):eU(e,e1,function(){return e5(e,t,r)})},set:function(e,n,r){var i,o=eX(e),a=!y.scrollboxSize()&&"absolute"===o.position,s=(a||r)&&"border-box"===S.css(e,"boxSizing",!1,o),u=r?e4(e,t,r,s,o):0;return s&&a&&(u-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-e4(e,t,"border",!1,o)-.5)),u&&(i=ep.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=S.css(e,t)),e3(e,n,u)}}}),S.cssHooks.marginLeft=eY(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(eG(e,"marginLeft"))||e.getBoundingClientRect().left-eU(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),S.each({margin:"",padding:"",border:"Width"},function(e,t){S.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+ed[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(S.cssHooks[e+t].set=e3)}),S.fn.extend({css:function(e,t){return Z(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=eX(e),i=t.length;a<i;a++)o[t[a]]=S.css(e,t[a],!1,r);return o}return void 0!==n?S.style(e,t,n):S.css(e,t)},e,t,arguments.length>1)}}),S.Tween=e6,e6.prototype={constructor:e6,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(S.cssNumber[n]?"":"px")},cur:function(){var e=e6.propHooks[this.prop];return e&&e.get?e.get(this):e6.propHooks._default.get(this)},run:function(e){var t,n=e6.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):e6.propHooks._default.set(this),this}},e6.prototype.init.prototype=e6.prototype,e6.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=S.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1===e.elem.nodeType&&(S.cssHooks[e.prop]||null!=e.elem.style[eZ(e.prop)])?S.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},e6.propHooks.scrollTop=e6.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return .5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=e6.prototype.init,S.fx.step={};var e9,e8,e7,te,tt=/^(?:toggle|show|hide)$/,tn=/queueHooks$/;function tr(){return r.setTimeout(function(){e7=void 0}),e7=Date.now()}function ti(e,t){var n,r=0,i={height:e};for(t=+!!t;r<4;r+=2-t)i["margin"+(n=ed[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function to(e,t,n){for(var r,i=(ta.tweeners[t]||[]).concat(ta.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function ta(e,t,n){var r,i,o=0,a=ta.prefilters.length,s=S.Deferred().always(function(){delete u.elem}),u=function(){if(i)return!1;for(var t=e7||tr(),n=Math.max(0,l.startTime+l.duration-t),r=1-(n/l.duration||0),o=0,a=l.tweens.length;o<a;o++)l.tweens[o].run(r);return(s.notifyWith(e,[l,r,n]),r<1&&a)?n:(a||s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:S.extend({},t),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},n),originalProperties:t,originalOptions:n,startTime:e7||tr(),duration:n.duration,tweens:[],createTween:function(t,n){var r=S.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return t?(s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l,t])):s.rejectWith(e,[l,t]),this}}),c=l.props;for(function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=er(n)],Array.isArray(o=e[n])&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=S.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);o<a;o++)if(r=ta.prefilters[o].call(l,e,c,l.opts))return m(r.stop)&&(S._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return S.map(c,to,l),m(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),S.fx.timer(S.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}S.Animation=S.extend(ta,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return em(n.elem,e,ep.exec(t),n),n}]},tweener:function(e,t){m(e)?(t=e,e=["*"]):e=e.match(U);for(var n,r=0,i=e.length;r<i;r++)n=e[r],ta.tweeners[n]=ta.tweeners[n]||[],ta.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&ey(e),v=ea.get(e,"fxshow");for(r in n.queue||(null==(a=S._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,S.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],tt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;g=!0}d[r]=v&&v[r]||S.style(e,r)}if(!(!(u=!S.isEmptyObject(t))&&S.isEmptyObject(d)))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=v&&v.display)&&(l=ea.get(e,"display")),"none"===(c=S.css(e,"display"))&&(l?c=l:(eb([e],!0),l=e.style.display||l,c=S.css(e,"display"),eb([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===S.css(e,"float")&&(u||(p.done(function(){h.display=l}),null!=l||(l="none"===(c=h.display)?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1,d)u||(v?"hidden"in v&&(g=v.hidden):v=ea.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&eb([e],!0),p.done(function(){for(r in g||eb([e]),ea.remove(e,"fxshow"),d)S.style(e,r,d[r])})),u=to(g?v[r]:0,r,p),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?ta.prefilters.unshift(e):ta.prefilters.push(e)}}),S.speed=function(e,t,n){var r=e&&"object"==typeof e?S.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};return S.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in S.fx.speeds?r.duration=S.fx.speeds[r.duration]:r.duration=S.fx.speeds._default),(null==r.queue||!0===r.queue)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){m(r.old)&&r.old.call(this),r.queue&&S.dequeue(this,r.queue)},r},S.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ey).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=S.isEmptyObject(e),o=S.speed(t,n,r),a=function(){var t=ta(this,S.extend({},e),o);(i||ea.get(this,"finish"))&&t.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=S.timers,a=ea.get(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&tn.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem===this&&(null==e||o[i].queue===e)&&(o[i].anim.stop(n),t=!1,o.splice(i,1));(t||!n)&&S.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=ea.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=S.timers,a=r?r.length:0;for(n.finish=!0,S.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),S.each(["toggle","show","hide"],function(e,t){var n=S.fn[t];S.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(ti(t,!0),e,r,i)}}),S.each({slideDown:ti("show"),slideUp:ti("hide"),slideToggle:ti("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){S.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(e7=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),e7=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){!te&&(te=!0,function e(){te&&(!1===b.hidden&&r.requestAnimationFrame?r.requestAnimationFrame(e):r.setTimeout(e,S.fx.interval),S.fx.tick())}())},S.fx.stop=function(){te=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(e,t){return e=S.fx&&S.fx.speeds[e]||e,t=t||"fx",this.queue(t,function(t,n){var i=r.setTimeout(t,e);n.stop=function(){r.clearTimeout(i)}})},ts=b.createElement("input"),tu=b.createElement("select").appendChild(b.createElement("option")),ts.type="checkbox",y.checkOn=""!==ts.value,y.optSelected=tu.selected,(ts=b.createElement("input")).value="t",ts.type="radio",y.radioValue="t"===ts.value;var ts,tu,tl,tc=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return Z(this,S.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){S.removeAttr(this,e)})}}),S.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o){if(void 0===e.getAttribute)return S.prop(e,t,n);if(1===o&&S.isXMLDoc(e)||(i=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?tl:void 0)),void 0!==n){if(null===n){S.removeAttr(e,t);return}return i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n)}return i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=S.find.attr(e,t))?void 0:r}},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&A(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(U);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),tl={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),function(e,t){var n=tc[t]||S.find.attr;tc[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=tc[a],tc[a]=i,i=null!=n(e,t,r)?a:null,tc[a]=o),i}});var tf=/^(?:input|select|textarea|button)$/i,tp=/^(?:a|area)$/i;function td(e){return(e.match(U)||[]).join(" ")}function th(e){return e.getAttribute&&e.getAttribute("class")||""}function tg(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(U)||[]}S.fn.extend({prop:function(e,t){return Z(this,S.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[S.propFix[e]||e]})}}),S.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return(1===o&&S.isXMLDoc(e)||(t=S.propFix[t]||t,i=S.propHooks[t]),void 0!==n)?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):tf.test(e.nodeName)||tp.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(S.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){S.propFix[this.toLowerCase()]=this}),S.fn.extend({addClass:function(e){var t,n,r,i,o,a;return m(e)?this.each(function(t){S(this).addClass(e.call(this,t,th(this)))}):(t=tg(e)).length?this.each(function(){if(r=th(this),n=1===this.nodeType&&" "+td(r)+" "){for(o=0;o<t.length;o++)i=t[o],0>n.indexOf(" "+i+" ")&&(n+=i+" ");r!==(a=td(n))&&this.setAttribute("class",a)}}):this},removeClass:function(e){var t,n,r,i,o,a;return m(e)?this.each(function(t){S(this).removeClass(e.call(this,t,th(this)))}):arguments.length?(t=tg(e)).length?this.each(function(){if(r=th(this),n=1===this.nodeType&&" "+td(r)+" "){for(o=0;o<t.length;o++)for(i=t[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");r!==(a=td(n))&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,a=typeof e,s="string"===a||Array.isArray(e);return m(e)?this.each(function(n){S(this).toggleClass(e.call(this,n,th(this),t),t)}):"boolean"==typeof t&&s?t?this.addClass(e):this.removeClass(e):(n=tg(e),this.each(function(){if(s)for(i=0,o=S(this);i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else(void 0===e||"boolean"===a)&&((r=th(this))&&ea.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":ea.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+td(th(n))+" ").indexOf(t)>-1)return!0;return!1}});var tv=/\r/g;S.fn.extend({val:function(e){var t,n,r,i=this[0];if(!arguments.length)return i?(t=S.valHooks[i.type]||S.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(tv,""):null==n?"":n:void 0;return r=m(e),this.each(function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,S(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=S.map(i,function(e){return null==e?"":e+""})),(t=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))})}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:td(S.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!A(n.parentNode,"optgroup"))){if(t=S(n).val(),a)return t;s.push(t)}return s},set:function(e,t){for(var n,r,i=e.options,o=S.makeArray(t),a=i.length;a--;)((r=i[a]).selected=S.inArray(S.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),S.each(["radio","checkbox"],function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=S.inArray(S(e).val(),t)>-1}},y.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var ty=r.location,tm={guid:Date.now()},tx=/\?/;S.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=new r.DOMParser().parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],(!t||n)&&S.error("Invalid XML: "+(n?S.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var tb=/^(?:focusinfocus|focusoutblur)$/,tT=function(e){e.stopPropagation()};S.extend(S.event,{trigger:function(e,t,n,i){var o,a,s,u,l,c,f,p,d=[n||b],g=h.call(e,"type")?e.type:e,v=h.call(e,"namespace")?e.namespace.split("."):[];if(a=p=s=n=n||b,!(3===n.nodeType||8===n.nodeType||tb.test(g+S.event.triggered))){if(g.indexOf(".")>-1&&(g=(v=g.split(".")).shift(),v.sort()),l=0>g.indexOf(":")&&"on"+g,(e=e[S.expando]?e:new S.Event(g,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=v.join("."),e.rnamespace=e.namespace?RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:S.makeArray(t,[e]),f=S.event.special[g]||{},i||!f.trigger||!1!==f.trigger.apply(n,t)){if(!i&&!f.noBubble&&!x(n)){for(u=f.delegateType||g,tb.test(u+g)||(a=a.parentNode);a;a=a.parentNode)d.push(a),s=a;s===(n.ownerDocument||b)&&d.push(s.defaultView||s.parentWindow||r)}for(o=0;(a=d[o++])&&!e.isPropagationStopped();)p=a,e.type=o>1?u:f.bindType||g,(c=(ea.get(a,"events")||Object.create(null))[e.type]&&ea.get(a,"handle"))&&c.apply(a,t),(c=l&&a[l])&&c.apply&&ei(a)&&(e.result=c.apply(a,t),!1===e.result&&e.preventDefault());return e.type=g,!i&&!e.isDefaultPrevented()&&(!f._default||!1===f._default.apply(d.pop(),t))&&ei(n)&&l&&m(n[g])&&!x(n)&&((s=n[l])&&(n[l]=null),S.event.triggered=g,e.isPropagationStopped()&&p.addEventListener(g,tT),n[g](),e.isPropagationStopped()&&p.removeEventListener(g,tT),S.event.triggered=void 0,s&&(n[l]=s)),e.result}}},simulate:function(e,t,n){var r=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(r,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each(function(){S.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}});var tw=/\[\]$/,tC=/\r?\n/g,tk=/^(?:submit|button|image|reset|file)$/i,tE=/^(?:input|select|textarea|keygen)/i;S.param=function(e,t){var n,r=[],i=function(e,t){var n=m(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,function(){i(this.name,this.value)});else for(n in e)!function e(t,n,r,i){var o;if(Array.isArray(n))S.each(n,function(n,o){r||tw.test(t)?i(t,o):e(t+"["+("object"==typeof o&&null!=o?n:"")+"]",o,r,i)});else if(r||"object"!==C(n))i(t,n);else for(o in n)e(t+"["+o+"]",n[o],r,i)}(n,e[n],t,i);return r.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&tE.test(this.nodeName)&&!tk.test(e)&&(this.checked||!eT.test(e))}).map(function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,function(e){return{name:t.name,value:e.replace(tC,"\r\n")}}):{name:t.name,value:n.replace(tC,"\r\n")}}).get()}});var tS=/%20/g,tj=/#.*$/,tA=/([?&])_=[^&]*/,tD=/^(.*?):[ \t]*([^\r\n]*)$/mg,tN=/^(?:GET|HEAD)$/,tq=/^\/\//,tL={},tH={},tO="*/".concat("*"),tP=b.createElement("a");function tM(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(U)||[];if(m(n))for(;r=o[i++];)"+"===r[0]?(e[r=r.slice(1)||"*"]=e[r]||[]).unshift(n):(e[r]=e[r]||[]).push(n)}}function tR(e,t,n,r){var i={},o=e===tH;function a(s){var u;return i[s]=!0,S.each(e[s]||[],function(e,s){var l=s(t,n,r);return"string"!=typeof l||o||i[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),a(l),!1)}),u}return a(t.dataTypes[0])||!i["*"]&&a("*")}function tI(e,t){var n,r,i=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&S.extend(!0,e,r),e}tP.href=ty.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ty.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(ty.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":tO,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?tI(tI(e,S.ajaxSettings),t):tI(S.ajaxSettings,e)},ajaxPrefilter:tM(tL),ajaxTransport:tM(tH),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,i,o,a,s,u,l,c,f,p,d=S.ajaxSetup({},t),h=d.context||d,g=d.context&&(h.nodeType||h.jquery)?S(h):S.event,v=S.Deferred(),y=S.Callbacks("once memory"),m=d.statusCode||{},x={},T={},w="canceled",C={readyState:0,getResponseHeader:function(e){var t;if(l){if(!a)for(a={};t=tD.exec(o);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return l?o:null},setRequestHeader:function(e,t){return null==l&&(x[e=T[e.toLowerCase()]=T[e.toLowerCase()]||e]=t),this},overrideMimeType:function(e){return null==l&&(d.mimeType=e),this},statusCode:function(e){var t;if(e){if(l)C.always(e[C.status]);else for(t in e)m[t]=[m[t],e[t]]}return this},abort:function(e){var t=e||w;return n&&n.abort(t),k(0,t),this}};if(v.promise(C),d.url=((e||d.url||ty.href)+"").replace(tq,ty.protocol+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match(U)||[""],null==d.crossDomain){u=b.createElement("a");try{u.href=d.url,u.href=u.href,d.crossDomain=tP.protocol+"//"+tP.host!=u.protocol+"//"+u.host}catch(e){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=S.param(d.data,d.traditional)),tR(tL,d,t,C),l)return C;for(f in(c=S.event&&d.global)&&0==S.active++&&S.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!tN.test(d.type),i=d.url.replace(tj,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(tS,"+")):(p=d.url.slice(i.length),d.data&&(d.processData||"string"==typeof d.data)&&(i+=(tx.test(i)?"&":"?")+d.data,delete d.data),!1===d.cache&&(i=i.replace(tA,"$1"),p=(tx.test(i)?"&":"?")+"_="+tm.guid+++p),d.url=i+p),d.ifModified&&(S.lastModified[i]&&C.setRequestHeader("If-Modified-Since",S.lastModified[i]),S.etag[i]&&C.setRequestHeader("If-None-Match",S.etag[i])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&C.setRequestHeader("Content-Type",d.contentType),C.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+tO+"; q=0.01":""):d.accepts["*"]),d.headers)C.setRequestHeader(f,d.headers[f]);if(d.beforeSend&&(!1===d.beforeSend.call(h,C,d)||l))return C.abort();if(w="abort",y.add(d.complete),C.done(d.success),C.fail(d.error),n=tR(tH,d,t,C)){if(C.readyState=1,c&&g.trigger("ajaxSend",[C,d]),l)return C;d.async&&d.timeout>0&&(s=r.setTimeout(function(){C.abort("timeout")},d.timeout));try{l=!1,n.send(x,k)}catch(e){if(l)throw e;k(-1,e)}}else k(-1,"No Transport");function k(e,t,a,u){var f,p,x,b,T,w=t;!l&&(l=!0,s&&r.clearTimeout(s),n=void 0,o=u||"",C.readyState=4*(e>0),f=e>=200&&e<300||304===e,a&&(b=function(e,t,n){for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r){for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(d,C,a)),!f&&S.inArray("script",d.dataTypes)>-1&&0>S.inArray("json",d.dataTypes)&&(d.converters["text script"]=function(){}),b=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift()){if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o])){for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}}if(!0!==a){if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}}}return{state:"success",data:t}}(d,b,C,f),f?(d.ifModified&&((T=C.getResponseHeader("Last-Modified"))&&(S.lastModified[i]=T),(T=C.getResponseHeader("etag"))&&(S.etag[i]=T)),204===e||"HEAD"===d.type?w="nocontent":304===e?w="notmodified":(w=b.state,p=b.data,f=!(x=b.error))):(x=w,(e||!w)&&(w="error",e<0&&(e=0))),C.status=e,C.statusText=(t||w)+"",f?v.resolveWith(h,[p,w,C]):v.rejectWith(h,[C,w,x]),C.statusCode(m),m=void 0,c&&g.trigger(f?"ajaxSuccess":"ajaxError",[C,d,f?p:x]),y.fireWith(h,[C,w]),!c||(g.trigger("ajaxComplete",[C,d]),--S.active||S.event.trigger("ajaxStop")))}return C},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],function(e,t){S[t]=function(e,n,r,i){return m(n)&&(i=i||r,r=n,n=void 0),S.ajax(S.extend({url:e,type:t,dataType:i,data:n,success:r},S.isPlainObject(e)&&e))}}),S.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){var t;return this[0]&&(m(e)&&(e=e.call(this[0])),t=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return m(e)?this.each(function(t){S(this).wrapInner(e.call(this,t))}):this.each(function(){var t=S(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=m(e);return this.each(function(n){S(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){S(this).replaceWith(this.childNodes)}),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new r.XMLHttpRequest}catch(e){}};var tW={0:200,1223:204},tF=S.ajaxSettings.xhr();y.cors=!!tF&&"withCredentials"in tF,y.ajax=tF=!!tF,S.ajaxTransport(function(e){var t,n;if(y.cors||tF&&!e.crossDomain)return{send:function(i,o){var a,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];for(a in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)s.setRequestHeader(a,i[a]);t=function(e){return function(){t&&(t=n=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(tW[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=t(),n=s.onerror=s.ontimeout=t("error"),void 0!==s.onabort?s.onabort=n:s.onreadystatechange=function(){4===s.readyState&&r.setTimeout(function(){t&&n()})},t=t("abort");try{s.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}}),S.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),S.ajaxTransport("script",function(e){if(e.crossDomain||e.scriptAttrs){var t,n;return{send:function(r,i){t=S("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}}});var t$=[],t_=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=t$.pop()||S.expando+"_"+tm.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",function(e,t,n){var i,o,a,s=!1!==e.jsonp&&(t_.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&t_.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(t_,"$1"+i):!1!==e.jsonp&&(e.url+=(tx.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return a||S.error(i+" was not called"),a[0]},e.dataTypes[0]="json",o=r[i],r[i]=function(){a=arguments},n.always(function(){void 0===o?S(r).removeProp(i):r[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,t$.push(i)),a&&m(o)&&o(a[0]),a=o=void 0}),"script"}),y.createHTMLDocument=((o=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===o.childNodes.length),S.parseHTML=function(e,t,n){var r,i,o;return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((r=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(r)):t=b),i=W.exec(e),o=!n&&[],i)?[t.createElement(i[1])]:(i=eA([e],t,o),o&&o.length&&S(o).remove(),S.merge([],i.childNodes))},S.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return s>-1&&(r=td(e.slice(s)),e=e.slice(0,s)),m(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),a.length>0&&S.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?S("<div>").append(S.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},S.expr.pseudos.animated=function(e){return S.grep(S.timers,function(t){return e===t.elem}).length},S.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=S.css(e,"position"),c=S(e),f={};"static"===l&&(e.style.position="relative"),s=c.offset(),o=S.css(e,"top"),u=S.css(e,"left"),("absolute"===l||"fixed"===l)&&(o+u).indexOf("auto")>-1?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),m(t)&&(t=t.call(e,n,S.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),"using"in t?t.using.call(e,f):c.css(f)}},S.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){S.offset.setOffset(this,e,t)});var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===S.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&(i=S(e).offset(),i.top+=S.css(e,"borderTopWidth",!0),i.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-S.css(r,"marginTop",!0),left:t.left-i.left-S.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===S.css(e,"position");)e=e.offsetParent;return e||eh})}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;S.fn[e]=function(r){return Z(this,function(e,r,i){var o;if(x(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i},e,r,arguments.length)}}),S.each(["top","left"],function(e,t){S.cssHooks[t]=eY(y.pixelPosition,function(e,n){if(n)return n=eG(e,t),eB.test(n)?S(e).position()[t]+"px":n})}),S.each({Height:"height",Width:"width"},function(e,t){S.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){S.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(!0===i||!0===o?"margin":"border");return Z(this,function(t,n,i){var o;return x(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?S.css(t,n,s):S.style(t,n,i,s)},t,a?i:void 0,a)}})}),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){S.fn[t]=function(e){return this.on(t,e)}}),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1==arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){S.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}});var tB=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),m(e))return r=u.call(arguments,2),(i=function(){return e.apply(t||this,r.concat(u.call(arguments)))}).guid=e.guid=e.guid||S.guid++,i},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=A,S.isFunction=m,S.isWindow=x,S.camelCase=er,S.type=C,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(tB,"$1")},void 0!==(n=(function(){return S}).apply(t,[]))&&(e.exports=n);var tz=r.jQuery,tX=r.$;return S.noConflict=function(e){return r.$===S&&(r.$=tX),e&&r.jQuery===S&&(r.jQuery=tz),S},void 0===i&&(r.jQuery=r.$=S),S},"object"==typeof e.exports?e.exports=r.document?i(r,!0):function(e){if(!e.document)throw Error("jQuery requires a window with a document");return i(e)}:i(r)}}]);
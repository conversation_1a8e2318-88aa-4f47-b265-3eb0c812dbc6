"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9797],{89797:(e,t,n)=>{n.d(t,{A:()=>en});var r=n(12115),a=n(4617),o=n.n(a),l=n(85268),c=n(1568),i=n(39014),u=n(21855),s=n(59912),d=n(97262),f=n(35015),v=n(85646),g=n(30754),m=n(85407),h=n(64406),b=n(47650);function p(e,t,n,r){var a=(t-n)/(r-n),o={};switch(e){case"rtl":o.right="".concat(100*a,"%"),o.transform="translateX(50%)";break;case"btt":o.bottom="".concat(100*a,"%"),o.transform="translateY(50%)";break;case"ttb":o.top="".concat(100*a,"%"),o.transform="translateY(-50%)";break;default:o.left="".concat(100*a,"%"),o.transform="translateX(-50%)"}return o}function k(e,t){return Array.isArray(e)?e[t]:e}var A=n(23672),C=r.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),y=r.createContext({}),x=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],E=r.forwardRef(function(e,t){var n,a=e.prefixCls,i=e.value,u=e.valueIndex,s=e.onStartMove,d=e.onDelete,f=e.style,v=e.render,g=e.dragging,b=e.draggingDelete,y=e.onOffsetChange,E=e.onChangeComplete,S=e.onFocus,M=e.onMouseEnter,w=(0,h.A)(e,x),O=r.useContext(C),B=O.min,D=O.max,R=O.direction,j=O.disabled,F=O.keyboard,P=O.range,H=O.tabIndex,N=O.ariaLabelForHandle,I=O.ariaLabelledByForHandle,L=O.ariaRequired,z=O.ariaValueTextFormatterForHandle,q=O.styles,T=O.classNames,V="".concat(a,"-handle"),X=function(e){j||s(e,u)},W=p(R,i,B,D),Y={};null!==u&&(Y={tabIndex:j?null:k(H,u),role:"slider","aria-valuemin":B,"aria-valuemax":D,"aria-valuenow":i,"aria-disabled":j,"aria-label":k(N,u),"aria-labelledby":k(I,u),"aria-required":k(L,u),"aria-valuetext":null===(n=k(z,u))||void 0===n?void 0:n(i),"aria-orientation":"ltr"===R||"rtl"===R?"horizontal":"vertical",onMouseDown:X,onTouchStart:X,onFocus:function(e){null==S||S(e,u)},onMouseEnter:function(e){M(e,u)},onKeyDown:function(e){if(!j&&F){var t=null;switch(e.which||e.keyCode){case A.A.LEFT:t="ltr"===R||"btt"===R?-1:1;break;case A.A.RIGHT:t="ltr"===R||"btt"===R?1:-1;break;case A.A.UP:t="ttb"!==R?1:-1;break;case A.A.DOWN:t="ttb"!==R?-1:1;break;case A.A.HOME:t="min";break;case A.A.END:t="max";break;case A.A.PAGE_UP:t=2;break;case A.A.PAGE_DOWN:t=-2;break;case A.A.BACKSPACE:case A.A.DELETE:d(u)}null!==t&&(e.preventDefault(),y(t,u))}},onKeyUp:function(e){switch(e.which||e.keyCode){case A.A.LEFT:case A.A.RIGHT:case A.A.UP:case A.A.DOWN:case A.A.HOME:case A.A.END:case A.A.PAGE_UP:case A.A.PAGE_DOWN:null==E||E()}}});var G=r.createElement("div",(0,m.A)({ref:t,className:o()(V,(0,c.A)((0,c.A)((0,c.A)({},"".concat(V,"-").concat(u+1),null!==u&&P),"".concat(V,"-dragging"),g),"".concat(V,"-dragging-delete"),b),T.handle),style:(0,l.A)((0,l.A)((0,l.A)({},W),f),q.handle)},Y,w));return v&&(G=v(G,{index:u,prefixCls:a,value:i,dragging:g,draggingDelete:b})),G}),S=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],M=r.forwardRef(function(e,t){var n=e.prefixCls,a=e.style,o=e.onStartMove,c=e.onOffsetChange,i=e.values,u=e.handleRender,d=e.activeHandleRender,f=e.draggingIndex,v=e.draggingDelete,g=e.onFocus,p=(0,h.A)(e,S),A=r.useRef({}),C=r.useState(!1),y=(0,s.A)(C,2),x=y[0],M=y[1],w=r.useState(-1),O=(0,s.A)(w,2),B=O[0],D=O[1],R=function(e){D(e),M(!0)};r.useImperativeHandle(t,function(){return{focus:function(e){var t;null===(t=A.current[e])||void 0===t||t.focus()},hideHelp:function(){(0,b.flushSync)(function(){M(!1)})}}});var j=(0,l.A)({prefixCls:n,onStartMove:o,onOffsetChange:c,render:u,onFocus:function(e,t){R(t),null==g||g(e)},onMouseEnter:function(e,t){R(t)}},p);return r.createElement(r.Fragment,null,i.map(function(e,t){var n=f===t;return r.createElement(E,(0,m.A)({ref:function(e){e?A.current[t]=e:delete A.current[t]},dragging:n,draggingDelete:n&&v,style:k(a,t),key:t,value:e,valueIndex:t},j))}),d&&x&&r.createElement(E,(0,m.A)({key:"a11y"},j,{value:i[B],valueIndex:null,dragging:-1!==f,draggingDelete:v,render:d,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))});let w=function(e){var t=e.prefixCls,n=e.style,a=e.children,i=e.value,u=e.onClick,s=r.useContext(C),d=s.min,f=s.max,v=s.direction,g=s.includedStart,m=s.includedEnd,h=s.included,b="".concat(t,"-text"),k=p(v,i,d,f);return r.createElement("span",{className:o()(b,(0,c.A)({},"".concat(b,"-active"),h&&g<=i&&i<=m)),style:(0,l.A)((0,l.A)({},k),n),onMouseDown:function(e){e.stopPropagation()},onClick:function(){u(i)}},a)},O=function(e){var t=e.prefixCls,n=e.marks,a=e.onClick,o="".concat(t,"-mark");return n.length?r.createElement("div",{className:o},n.map(function(e){var t=e.value,n=e.style,l=e.label;return r.createElement(w,{key:t,prefixCls:o,style:n,value:t,onClick:a},l)})):null},B=function(e){var t=e.prefixCls,n=e.value,a=e.style,i=e.activeStyle,u=r.useContext(C),s=u.min,d=u.max,f=u.direction,v=u.included,g=u.includedStart,m=u.includedEnd,h="".concat(t,"-dot"),b=v&&g<=n&&n<=m,k=(0,l.A)((0,l.A)({},p(f,n,s,d)),"function"==typeof a?a(n):a);return b&&(k=(0,l.A)((0,l.A)({},k),"function"==typeof i?i(n):i)),r.createElement("span",{className:o()(h,(0,c.A)({},"".concat(h,"-active"),b)),style:k})},D=function(e){var t=e.prefixCls,n=e.marks,a=e.dots,o=e.style,l=e.activeStyle,c=r.useContext(C),i=c.min,u=c.max,s=c.step,d=r.useMemo(function(){var e=new Set;if(n.forEach(function(t){e.add(t.value)}),a&&null!==s)for(var t=i;t<=u;)e.add(t),t+=s;return Array.from(e)},[i,u,s,a,n]);return r.createElement("div",{className:"".concat(t,"-step")},d.map(function(e){return r.createElement(B,{prefixCls:t,key:e,value:e,style:o,activeStyle:l})}))},R=function(e){var t=e.prefixCls,n=e.style,a=e.start,i=e.end,u=e.index,s=e.onStartMove,d=e.replaceCls,f=r.useContext(C),v=f.direction,g=f.min,m=f.max,h=f.disabled,b=f.range,p=f.classNames,k="".concat(t,"-track"),A=(a-g)/(m-g),y=(i-g)/(m-g),x=function(e){!h&&s&&s(e,-1)},E={};switch(v){case"rtl":E.right="".concat(100*A,"%"),E.width="".concat(100*y-100*A,"%");break;case"btt":E.bottom="".concat(100*A,"%"),E.height="".concat(100*y-100*A,"%");break;case"ttb":E.top="".concat(100*A,"%"),E.height="".concat(100*y-100*A,"%");break;default:E.left="".concat(100*A,"%"),E.width="".concat(100*y-100*A,"%")}var S=d||o()(k,(0,c.A)((0,c.A)({},"".concat(k,"-").concat(u+1),null!==u&&b),"".concat(t,"-track-draggable"),s),p.track);return r.createElement("div",{className:S,style:(0,l.A)((0,l.A)({},E),n),onMouseDown:x,onTouchStart:x})},j=function(e){var t=e.prefixCls,n=e.style,a=e.values,c=e.startPoint,i=e.onStartMove,u=r.useContext(C),s=u.included,d=u.range,f=u.min,v=u.styles,g=u.classNames,m=r.useMemo(function(){if(!d){if(0===a.length)return[];var e=null!=c?c:f,t=a[0];return[{start:Math.min(e,t),end:Math.max(e,t)}]}for(var n=[],r=0;r<a.length-1;r+=1)n.push({start:a[r],end:a[r+1]});return n},[a,d,c,f]);if(!s)return null;var h=null!=m&&m.length&&(g.tracks||v.tracks)?r.createElement(R,{index:null,prefixCls:t,start:m[0].start,end:m[m.length-1].end,replaceCls:o()(g.tracks,"".concat(t,"-tracks")),style:v.tracks}):null;return r.createElement(r.Fragment,null,h,m.map(function(e,a){var o=e.start,c=e.end;return r.createElement(R,{index:a,prefixCls:t,style:(0,l.A)((0,l.A)({},k(n,a)),v.track),start:o,end:c,key:a,onStartMove:i})}))};var F=n(66105);function P(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}let H=function(e,t,n,a,o,l,c,u,f,v,g){var m=r.useState(null),h=(0,s.A)(m,2),b=h[0],p=h[1],k=r.useState(-1),A=(0,s.A)(k,2),C=A[0],x=A[1],E=r.useState(!1),S=(0,s.A)(E,2),M=S[0],w=S[1],O=r.useState(n),B=(0,s.A)(O,2),D=B[0],R=B[1],j=r.useState(n),H=(0,s.A)(j,2),N=H[0],I=H[1],L=r.useRef(null),z=r.useRef(null),q=r.useRef(null),T=r.useContext(y),V=T.onDragStart,X=T.onDragChange;(0,F.A)(function(){-1===C&&R(n)},[n,C]),r.useEffect(function(){return function(){document.removeEventListener("mousemove",L.current),document.removeEventListener("mouseup",z.current),q.current&&(q.current.removeEventListener("touchmove",L.current),q.current.removeEventListener("touchend",z.current))}},[]);var W=function(e,t,n){void 0!==t&&p(t),R(e);var r=e;n&&(r=e.filter(function(e,t){return t!==C})),c(r),X&&X({rawValues:e,deleteIndex:n?C:-1,draggingIndex:C,draggingValue:t})},Y=(0,d.A)(function(e,t,n){if(-1===e){var r=N[0],c=N[N.length-1],u=t*(o-a);u=Math.min(u=Math.max(u,a-r),o-c),u=l(r+u)-r,W(N.map(function(e){return e+u}))}else{var s=(0,i.A)(D);s[e]=N[e];var d=f(s,(o-a)*t,e,"dist");W(d.values,d.value,n)}});return[C,b,M,r.useMemo(function(){var e=(0,i.A)(n).sort(function(e,t){return e-t}),t=(0,i.A)(D).sort(function(e,t){return e-t}),r={};t.forEach(function(e){r[e]=(r[e]||0)+1}),e.forEach(function(e){r[e]=(r[e]||0)-1});var a=+!!v;return Object.values(r).reduce(function(e,t){return e+Math.abs(t)},0)<=a?D:n},[n,D,v]),function(r,a,o){r.stopPropagation();var l=o||n,c=l[a];x(a),p(c),I(l),R(l),w(!1);var i=P(r),s=i.pageX,d=i.pageY,f=!1;V&&V({rawValues:l,draggingIndex:a,draggingValue:c});var m=function(n){n.preventDefault();var r,o,l=P(n),c=l.pageX,i=l.pageY,u=c-s,m=i-d,h=e.current.getBoundingClientRect(),b=h.width,p=h.height;switch(t){case"btt":r=-m/p,o=u;break;case"ttb":r=m/p,o=u;break;case"rtl":r=-u/b,o=m;break;default:r=u/b,o=m}w(f=!!v&&Math.abs(o)>130&&g<D.length),Y(a,r,f)},h=function e(t){t.preventDefault(),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",m),q.current&&(q.current.removeEventListener("touchmove",L.current),q.current.removeEventListener("touchend",z.current)),L.current=null,z.current=null,q.current=null,u(f),x(-1),w(!1)};document.addEventListener("mouseup",h),document.addEventListener("mousemove",m),r.currentTarget.addEventListener("touchend",h),r.currentTarget.addEventListener("touchmove",m),L.current=m,z.current=h,q.current=r.currentTarget}]};var N=r.forwardRef(function(e,t){var n,a,m,h,b,p,k,A=e.prefixCls,y=void 0===A?"rc-slider":A,x=e.className,E=e.style,S=e.classNames,w=e.styles,B=e.id,R=e.disabled,F=void 0!==R&&R,P=e.keyboard,N=void 0===P||P,I=e.autoFocus,L=e.onFocus,z=e.onBlur,q=e.min,T=void 0===q?0:q,V=e.max,X=void 0===V?100:V,W=e.step,Y=void 0===W?1:W,G=e.value,_=e.defaultValue,U=e.range,K=e.count,J=e.onChange,Q=e.onBeforeChange,Z=e.onAfterChange,$=e.onChangeComplete,ee=e.allowCross,et=e.pushable,en=void 0!==et&&et,er=e.reverse,ea=e.vertical,eo=e.included,el=void 0===eo||eo,ec=e.startPoint,ei=e.trackStyle,eu=e.handleStyle,es=e.railStyle,ed=e.dotStyle,ef=e.activeDotStyle,ev=e.marks,eg=e.dots,em=e.handleRender,eh=e.activeHandleRender,eb=e.track,ep=e.tabIndex,ek=void 0===ep?0:ep,eA=e.ariaLabelForHandle,eC=e.ariaLabelledByForHandle,ey=e.ariaRequired,ex=e.ariaValueTextFormatterForHandle,eE=r.useRef(null),eS=r.useRef(null),eM=r.useMemo(function(){return ea?er?"ttb":"btt":er?"rtl":"ltr"},[er,ea]),ew=(0,r.useMemo)(function(){if(!0===U||!U)return[!!U,!1,!1,0];var e=U.editable,t=U.draggableTrack;return[!0,e,!e&&t,U.minCount||0,U.maxCount]},[U]),eO=(0,s.A)(ew,5),eB=eO[0],eD=eO[1],eR=eO[2],ej=eO[3],eF=eO[4],eP=r.useMemo(function(){return isFinite(T)?T:0},[T]),eH=r.useMemo(function(){return isFinite(X)?X:100},[X]),eN=r.useMemo(function(){return null!==Y&&Y<=0?1:Y},[Y]),eI=r.useMemo(function(){return"boolean"==typeof en?!!en&&eN:en>=0&&en},[en,eN]),eL=r.useMemo(function(){return Object.keys(ev||{}).map(function(e){var t=ev[e],n={value:Number(e)};return t&&"object"===(0,u.A)(t)&&!r.isValidElement(t)&&("label"in t||"style"in t)?(n.style=t.style,n.label=t.label):n.label=t,n}).filter(function(e){var t=e.label;return t||"number"==typeof t}).sort(function(e,t){return e.value-t.value})},[ev]),ez=(n=void 0===ee||ee,a=r.useCallback(function(e){return Math.max(eP,Math.min(eH,e))},[eP,eH]),m=r.useCallback(function(e){if(null!==eN){var t=eP+Math.round((a(e)-eP)/eN)*eN,n=function(e){return(String(e).split(".")[1]||"").length},r=Math.max(n(eN),n(eH),n(eP)),o=Number(t.toFixed(r));return eP<=o&&o<=eH?o:null}return null},[eN,eP,eH,a]),h=r.useCallback(function(e){var t=a(e),n=eL.map(function(e){return e.value});null!==eN&&n.push(m(e)),n.push(eP,eH);var r=n[0],o=eH-eP;return n.forEach(function(e){var n=Math.abs(t-e);n<=o&&(r=e,o=n)}),r},[eP,eH,eL,eN,a,m]),b=function e(t,n,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit";if("number"==typeof n){var o,l=t[r],c=l+n,u=[];eL.forEach(function(e){u.push(e.value)}),u.push(eP,eH),u.push(m(l));var s=n>0?1:-1;"unit"===a?u.push(m(l+s*eN)):u.push(m(c)),u=u.filter(function(e){return null!==e}).filter(function(e){return n<0?e<=l:e>=l}),"unit"===a&&(u=u.filter(function(e){return e!==l}));var d="unit"===a?l:c,f=Math.abs((o=u[0])-d);if(u.forEach(function(e){var t=Math.abs(e-d);t<f&&(o=e,f=t)}),void 0===o)return n<0?eP:eH;if("dist"===a)return o;if(Math.abs(n)>1){var v=(0,i.A)(t);return v[r]=o,e(v,n-s,r,a)}return o}return"min"===n?eP:"max"===n?eH:void 0},p=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e[n],o=b(e,t,n,r);return{value:o,changed:o!==a}},k=function(e){return null===eI&&0===e||"number"==typeof eI&&e<eI},[h,function(e,t,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",o=e.map(h),l=o[r],c=b(o,t,r,a);if(o[r]=c,!1===n){var i=eI||0;r>0&&o[r-1]!==l&&(o[r]=Math.max(o[r],o[r-1]+i)),r<o.length-1&&o[r+1]!==l&&(o[r]=Math.min(o[r],o[r+1]-i))}else if("number"==typeof eI||null===eI){for(var u=r+1;u<o.length;u+=1)for(var s=!0;k(o[u]-o[u-1])&&s;){var d=p(o,1,u);o[u]=d.value,s=d.changed}for(var f=r;f>0;f-=1)for(var v=!0;k(o[f]-o[f-1])&&v;){var g=p(o,-1,f-1);o[f-1]=g.value,v=g.changed}for(var m=o.length-1;m>0;m-=1)for(var A=!0;k(o[m]-o[m-1])&&A;){var C=p(o,-1,m-1);o[m-1]=C.value,A=C.changed}for(var y=0;y<o.length-1;y+=1)for(var x=!0;k(o[y+1]-o[y])&&x;){var E=p(o,1,y+1);o[y+1]=E.value,x=E.changed}}return{value:o[r],values:o}}]),eq=(0,s.A)(ez,2),eT=eq[0],eV=eq[1],eX=(0,f.A)(_,{value:G}),eW=(0,s.A)(eX,2),eY=eW[0],eG=eW[1],e_=r.useMemo(function(){var e=null==eY?[]:Array.isArray(eY)?eY:[eY],t=(0,s.A)(e,1)[0],n=void 0===t?eP:t,r=null===eY?[]:[n];if(eB){if(r=(0,i.A)(e),K||void 0===eY){var a,o=K>=0?K+1:2;for(r=r.slice(0,o);r.length<o;)r.push(null!==(a=r[r.length-1])&&void 0!==a?a:eP)}r.sort(function(e,t){return e-t})}return r.forEach(function(e,t){r[t]=eT(e)}),r},[eY,eB,eP,K,eT]),eU=function(e){return eB?e:e[0]},eK=(0,d.A)(function(e){var t=(0,i.A)(e).sort(function(e,t){return e-t});J&&!(0,v.A)(t,e_,!0)&&J(eU(t)),eG(t)}),eJ=(0,d.A)(function(e){e&&eE.current.hideHelp();var t=eU(e_);null==Z||Z(t),(0,g.Ay)(!Z,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==$||$(t)}),eQ=H(eS,eM,e_,eP,eH,eT,eK,eJ,eV,eD,ej),eZ=(0,s.A)(eQ,5),e$=eZ[0],e0=eZ[1],e1=eZ[2],e2=eZ[3],e5=eZ[4],e3=function(e,t){if(!F){var n,r,a=(0,i.A)(e_),o=0,l=0,c=eH-eP;e_.forEach(function(t,n){var r=Math.abs(e-t);r<=c&&(c=r,o=n),t<e&&(l=n)});var u=o;eD&&0!==c&&(!eF||e_.length<eF)?(a.splice(l+1,0,e),u=l+1):a[o]=e,eB&&!e_.length&&void 0===K&&a.push(e);var s=eU(a);null==Q||Q(s),eK(a),t?(null===(n=document.activeElement)||void 0===n||null===(r=n.blur)||void 0===r||r.call(n),eE.current.focus(u),e5(t,u,a)):(null==Z||Z(s),(0,g.Ay)(!Z,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==$||$(s))}},e4=r.useState(null),e6=(0,s.A)(e4,2),e7=e6[0],e9=e6[1];r.useEffect(function(){if(null!==e7){var e=e_.indexOf(e7);e>=0&&eE.current.focus(e)}e9(null)},[e7]);var e8=r.useMemo(function(){return(!eR||null!==eN)&&eR},[eR,eN]),te=(0,d.A)(function(e,t){e5(e,t),null==Q||Q(eU(e_))}),tt=-1!==e$;r.useEffect(function(){if(!tt){var e=e_.lastIndexOf(e0);eE.current.focus(e)}},[tt]);var tn=r.useMemo(function(){return(0,i.A)(e2).sort(function(e,t){return e-t})},[e2]),tr=r.useMemo(function(){return eB?[tn[0],tn[tn.length-1]]:[eP,tn[0]]},[tn,eB,eP]),ta=(0,s.A)(tr,2),to=ta[0],tl=ta[1];r.useImperativeHandle(t,function(){return{focus:function(){eE.current.focus(0)},blur:function(){var e,t=document.activeElement;null!==(e=eS.current)&&void 0!==e&&e.contains(t)&&(null==t||t.blur())}}}),r.useEffect(function(){I&&eE.current.focus(0)},[]);var tc=r.useMemo(function(){return{min:eP,max:eH,direction:eM,disabled:F,keyboard:N,step:eN,included:el,includedStart:to,includedEnd:tl,range:eB,tabIndex:ek,ariaLabelForHandle:eA,ariaLabelledByForHandle:eC,ariaRequired:ey,ariaValueTextFormatterForHandle:ex,styles:w||{},classNames:S||{}}},[eP,eH,eM,F,N,eN,el,to,tl,eB,ek,eA,eC,ey,ex,w,S]);return r.createElement(C.Provider,{value:tc},r.createElement("div",{ref:eS,className:o()(y,x,(0,c.A)((0,c.A)((0,c.A)((0,c.A)({},"".concat(y,"-disabled"),F),"".concat(y,"-vertical"),ea),"".concat(y,"-horizontal"),!ea),"".concat(y,"-with-marks"),eL.length)),style:E,onMouseDown:function(e){e.preventDefault();var t,n=eS.current.getBoundingClientRect(),r=n.width,a=n.height,o=n.left,l=n.top,c=n.bottom,i=n.right,u=e.clientX,s=e.clientY;switch(eM){case"btt":t=(c-s)/a;break;case"ttb":t=(s-l)/a;break;case"rtl":t=(i-u)/r;break;default:t=(u-o)/r}e3(eT(eP+t*(eH-eP)),e)},id:B},r.createElement("div",{className:o()("".concat(y,"-rail"),null==S?void 0:S.rail),style:(0,l.A)((0,l.A)({},es),null==w?void 0:w.rail)}),!1!==eb&&r.createElement(j,{prefixCls:y,style:ei,values:e_,startPoint:ec,onStartMove:e8?te:void 0}),r.createElement(D,{prefixCls:y,marks:eL,dots:eg,style:ed,activeStyle:ef}),r.createElement(M,{ref:eE,prefixCls:y,style:eu,values:e2,draggingIndex:e$,draggingDelete:e1,onStartMove:te,onOffsetChange:function(e,t){if(!F){var n=eV(e_,e,t);null==Q||Q(eU(e_)),eK(n.values),e9(n.value)}},onFocus:L,onBlur:z,handleRender:em,activeHandleRender:eh,onChangeComplete:eJ,onDelete:eD?function(e){if(!F&&eD&&!(e_.length<=ej)){var t=(0,i.A)(e_);t.splice(e,1),null==Q||Q(eU(t)),eK(t);var n=Math.max(0,e-1);eE.current.hideHelp(),eE.current.focus(n)}}:void 0}),r.createElement(O,{prefixCls:y,marks:eL,onClick:e3})))}),I=n(13379),L=n(52414);let z=(0,r.createContext)({});var q=n(15231),T=n(6457);let V=r.forwardRef((e,t)=>{let{open:n,draggingDelete:a}=e,o=(0,r.useRef)(null),l=n&&!a,c=(0,r.useRef)(null);function i(){I.A.cancel(c.current),c.current=null}return r.useEffect(()=>(l?c.current=(0,I.A)(()=>{var e;null===(e=o.current)||void 0===e||e.forceAlign(),c.current=null}):i(),i),[l,e.title]),r.createElement(T.A,Object.assign({ref:(0,q.K4)(o,t)},e,{open:l}))});var X=n(5144),W=n(10815),Y=n(70695),G=n(1086),_=n(56204);let U=e=>{let{componentCls:t,antCls:n,controlSize:r,dotSize:a,marginFull:o,marginPart:l,colorFillContentHover:c,handleColorDisabled:i,calc:u,handleSize:s,handleSizeHover:d,handleActiveColor:f,handleActiveOutlineColor:v,handleLineWidth:g,handleLineWidthHover:m,motionDurationMid:h}=e;return{[t]:Object.assign(Object.assign({},(0,Y.dF)(e)),{position:"relative",height:r,margin:"".concat((0,X.zA)(l)," ").concat((0,X.zA)(o)),padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:"".concat((0,X.zA)(o)," ").concat((0,X.zA)(l))},["".concat(t,"-rail")]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:"background-color ".concat(h)},["".concat(t,"-track,").concat(t,"-tracks")]:{position:"absolute",transition:"background-color ".concat(h)},["".concat(t,"-track")]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},["".concat(t,"-track-draggable")]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{["".concat(t,"-rail")]:{backgroundColor:e.railHoverBg},["".concat(t,"-track")]:{backgroundColor:e.trackHoverBg},["".concat(t,"-dot")]:{borderColor:c},["".concat(t,"-handle::after")]:{boxShadow:"0 0 0 ".concat((0,X.zA)(g)," ").concat(e.colorPrimaryBorderHover)},["".concat(t,"-dot-active")]:{borderColor:e.dotActiveBorderColor}},["".concat(t,"-handle")]:{position:"absolute",width:s,height:s,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:u(g).mul(-1).equal(),insetBlockStart:u(g).mul(-1).equal(),width:u(s).add(u(g).mul(2)).equal(),height:u(s).add(u(g).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:s,height:s,backgroundColor:e.colorBgElevated,boxShadow:"0 0 0 ".concat((0,X.zA)(g)," ").concat(e.handleColor),outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:"\n            inset-inline-start ".concat(h,",\n            inset-block-start ").concat(h,",\n            width ").concat(h,",\n            height ").concat(h,",\n            box-shadow ").concat(h,",\n            outline ").concat(h,"\n          ")},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:u(d).sub(s).div(2).add(m).mul(-1).equal(),insetBlockStart:u(d).sub(s).div(2).add(m).mul(-1).equal(),width:u(d).add(u(m).mul(2)).equal(),height:u(d).add(u(m).mul(2)).equal()},"&::after":{boxShadow:"0 0 0 ".concat((0,X.zA)(m)," ").concat(f),outline:"6px solid ".concat(v),width:d,height:d,insetInlineStart:e.calc(s).sub(d).div(2).equal(),insetBlockStart:e.calc(s).sub(d).div(2).equal()}}},["&-lock ".concat(t,"-handle")]:{"&::before, &::after":{transition:"none"}},["".concat(t,"-mark")]:{position:"absolute",fontSize:e.fontSize},["".concat(t,"-mark-text")]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},["".concat(t,"-step")]:{position:"absolute",background:"transparent",pointerEvents:"none"},["".concat(t,"-dot")]:{position:"absolute",width:a,height:a,backgroundColor:e.colorBgElevated,border:"".concat((0,X.zA)(g)," solid ").concat(e.dotBorderColor),borderRadius:"50%",cursor:"pointer",transition:"border-color ".concat(e.motionDurationSlow),pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},["&".concat(t,"-disabled")]:{cursor:"not-allowed",["".concat(t,"-rail")]:{backgroundColor:"".concat(e.railBg," !important")},["".concat(t,"-track")]:{backgroundColor:"".concat(e.trackBgDisabled," !important")},["\n          ".concat(t,"-dot\n        ")]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},["".concat(t,"-handle::after")]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:s,height:s,boxShadow:"0 0 0 ".concat((0,X.zA)(g)," ").concat(i),insetInlineStart:0,insetBlockStart:0},["\n          ".concat(t,"-mark-text,\n          ").concat(t,"-dot\n        ")]:{cursor:"not-allowed !important"}},["&-tooltip ".concat(n,"-tooltip-inner")]:{minWidth:"unset"}})}},K=(e,t)=>{let{componentCls:n,railSize:r,handleSize:a,dotSize:o,marginFull:l,calc:c}=e,i=t?"width":"height",u=t?"height":"width",s=t?"insetBlockStart":"insetInlineStart",d=t?"top":"insetInlineStart",f=c(r).mul(3).sub(a).div(2).equal(),v=c(a).sub(r).div(2).equal(),g=t?{borderWidth:"".concat((0,X.zA)(v)," 0"),transform:"translateY(".concat((0,X.zA)(c(v).mul(-1).equal()),")")}:{borderWidth:"0 ".concat((0,X.zA)(v)),transform:"translateX(".concat((0,X.zA)(e.calc(v).mul(-1).equal()),")")};return{[t?"paddingBlock":"paddingInline"]:r,[u]:c(r).mul(3).equal(),["".concat(n,"-rail")]:{[i]:"100%",[u]:r},["".concat(n,"-track,").concat(n,"-tracks")]:{[u]:r},["".concat(n,"-track-draggable")]:Object.assign({},g),["".concat(n,"-handle")]:{[s]:f},["".concat(n,"-mark")]:{insetInlineStart:0,top:0,[d]:c(r).mul(3).add(t?0:l).equal(),[i]:"100%"},["".concat(n,"-step")]:{insetInlineStart:0,top:0,[d]:r,[i]:"100%",[u]:r},["".concat(n,"-dot")]:{position:"absolute",[s]:c(r).sub(o).div(2).equal()}}},J=e=>{let{componentCls:t,marginPartWithMark:n}=e;return{["".concat(t,"-horizontal")]:Object.assign(Object.assign({},K(e,!0)),{["&".concat(t,"-with-marks")]:{marginBottom:n}})}},Q=e=>{let{componentCls:t}=e;return{["".concat(t,"-vertical")]:Object.assign(Object.assign({},K(e,!1)),{height:"100%"})}},Z=(0,G.OF)("Slider",e=>{let t=(0,_.oX)(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[U(t),J(t),Q(t)]},e=>{let t=e.controlHeightLG/4,n=e.controlHeightSM/2,r=e.lineWidth+1,a=e.lineWidth+1.5,o=e.colorPrimary,l=new W.Y(o).setA(.2).toRgbString();return{controlSize:t,railSize:4,handleSize:t,handleSizeHover:n,dotSize:8,handleLineWidth:r,handleLineWidthHover:a,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:o,handleActiveOutlineColor:l,handleColorDisabled:new W.Y(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}});function $(){let[e,t]=r.useState(!1),n=r.useRef(null),a=()=>{I.A.cancel(n.current)};return r.useEffect(()=>a,[]),[e,e=>{a(),e?t(e):n.current=(0,I.A)(()=>{t(e)})}]}var ee=n(31049),et=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let en=r.forwardRef((e,t)=>{let{prefixCls:n,range:a,className:l,rootClassName:c,style:i,disabled:u,tooltipPrefixCls:s,tipFormatter:d,tooltipVisible:f,getTooltipPopupContainer:v,tooltipPlacement:g,tooltip:m={},onChangeComplete:h,classNames:b,styles:p}=e,k=et(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:A}=e,{getPrefixCls:C,direction:y,className:x,style:E,classNames:S,styles:M,getPopupContainer:w}=(0,ee.TP)("slider"),O=r.useContext(L.A),{handleRender:B,direction:D}=r.useContext(z),R="rtl"===(D||y),[j,F]=$(),[P,H]=$(),q=Object.assign({},m),{open:T,placement:X,getPopupContainer:W,prefixCls:Y,formatter:G}=q,_=null!=T?T:f,U=(j||P)&&!1!==_,K=function(e,t){return e||null===e?e:t||null===t?t:e=>"number"==typeof e?e.toString():""}(G,d),[J,Q]=$(),en=(e,t)=>e||(t?R?"left":"right":"top"),er=C("slider",n),[ea,eo,el]=Z(er),ec=o()(l,x,S.root,null==b?void 0:b.root,c,{["".concat(er,"-rtl")]:R,["".concat(er,"-lock")]:J},eo,el);R&&!k.vertical&&(k.reverse=!k.reverse),r.useEffect(()=>{let e=()=>{(0,I.A)(()=>{H(!1)},1)};return document.addEventListener("mouseup",e),()=>{document.removeEventListener("mouseup",e)}},[]);let ei=a&&!_,eu=B||((e,t)=>{let{index:n}=t,a=e.props;function o(e,t,n){var r,o;n&&(null===(r=k[e])||void 0===r||r.call(k,t)),null===(o=a[e])||void 0===o||o.call(a,t)}let l=Object.assign(Object.assign({},a),{onMouseEnter:e=>{F(!0),o("onMouseEnter",e)},onMouseLeave:e=>{F(!1),o("onMouseLeave",e)},onMouseDown:e=>{H(!0),Q(!0),o("onMouseDown",e)},onFocus:e=>{var t;H(!0),null===(t=k.onFocus)||void 0===t||t.call(k,e),o("onFocus",e,!0)},onBlur:e=>{var t;H(!1),null===(t=k.onBlur)||void 0===t||t.call(k,e),o("onBlur",e,!0)}}),c=r.cloneElement(e,l),i=(!!_||U)&&null!==K;return ei?c:r.createElement(V,Object.assign({},q,{prefixCls:C("tooltip",null!=Y?Y:s),title:K?K(t.value):"",open:i,placement:en(null!=X?X:g,A),key:n,classNames:{root:"".concat(er,"-tooltip")},getPopupContainer:W||v||w}),c)}),es=ei?(e,t)=>{let n=r.cloneElement(e,{style:Object.assign(Object.assign({},e.props.style),{visibility:"hidden"})});return r.createElement(V,Object.assign({},q,{prefixCls:C("tooltip",null!=Y?Y:s),title:K?K(t.value):"",open:null!==K&&U,placement:en(null!=X?X:g,A),key:"tooltip",classNames:{root:"".concat(er,"-tooltip")},getPopupContainer:W||v||w,draggingDelete:t.draggingDelete}),n)}:void 0,ed=Object.assign(Object.assign(Object.assign(Object.assign({},M.root),E),null==p?void 0:p.root),i),ef=Object.assign(Object.assign({},M.tracks),null==p?void 0:p.tracks),ev=o()(S.tracks,null==b?void 0:b.tracks);return ea(r.createElement(N,Object.assign({},k,{classNames:Object.assign({handle:o()(S.handle,null==b?void 0:b.handle),rail:o()(S.rail,null==b?void 0:b.rail),track:o()(S.track,null==b?void 0:b.track)},ev?{tracks:ev}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},M.handle),null==p?void 0:p.handle),rail:Object.assign(Object.assign({},M.rail),null==p?void 0:p.rail),track:Object.assign(Object.assign({},M.track),null==p?void 0:p.track)},Object.keys(ef).length?{tracks:ef}:{}),step:k.step,range:a,className:ec,style:ed,disabled:null!=u?u:O,ref:t,prefixCls:er,handleRender:eu,activeHandleRender:es,onChangeComplete:e=>{null==h||h(e),Q(!1)}})))})}}]);
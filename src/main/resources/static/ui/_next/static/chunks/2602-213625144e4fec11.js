"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2602],{28709:(e,t,n)=>{n.d(t,{M:()=>o});let o=n(12115).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},33621:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(85407),a=n(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var r=n(84021);let c=a.forwardRef(function(e,t){return a.createElement(r.A,(0,o.A)({},e,{ref:t,icon:i}))})},44549:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(85407),a=n(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"};var r=n(84021);let c=a.forwardRef(function(e,t){return a.createElement(r.A,(0,o.A)({},e,{ref:t,icon:i}))})},59276:(e,t,n)=>{n.d(t,{A:()=>S});var o=n(39014),a=n(12115),i=n(4617),r=n.n(i),c=n(70527),l=n(31049),s=n(28709),d=n(63588),u=n(94937),g=n(67312),m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};function p(e){let{suffixCls:t,tagName:n,displayName:o}=e;return e=>a.forwardRef((o,i)=>a.createElement(e,Object.assign({ref:i,suffixCls:t,tagName:n},o)))}let f=a.forwardRef((e,t)=>{let{prefixCls:n,suffixCls:o,className:i,tagName:c}=e,s=m(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:d}=a.useContext(l.QO),u=d("layout",n),[p,f,h]=(0,g.Ay)(u),v=o?"".concat(u,"-").concat(o):u;return p(a.createElement(c,Object.assign({className:r()(n||v,i,f,h),ref:t},s)))}),h=a.forwardRef((e,t)=>{let{direction:n}=a.useContext(l.QO),[i,p]=a.useState([]),{prefixCls:f,className:h,rootClassName:v,children:b,hasSider:y,tagName:x,style:S}=e,w=m(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),z=(0,c.A)(w,["suffixCls"]),{getPrefixCls:E,className:N,style:O}=(0,l.TP)("layout"),C=E("layout",f),k=function(e,t,n){return"boolean"==typeof n?n:!!e.length||(0,d.A)(t).some(e=>e.type===u.A)}(i,b,y),[A,B,I]=(0,g.Ay)(C),j=r()(C,{["".concat(C,"-has-sider")]:k,["".concat(C,"-rtl")]:"rtl"===n},N,h,v,B,I),D=a.useMemo(()=>({siderHook:{addSider:e=>{p(t=>[].concat((0,o.A)(t),[e]))},removeSider:e=>{p(t=>t.filter(t=>t!==e))}}}),[]);return A(a.createElement(s.M.Provider,{value:D},a.createElement(x,Object.assign({ref:t,className:j,style:Object.assign(Object.assign({},O),S)},z),b)))}),v=p({tagName:"div",displayName:"Layout"})(h),b=p({suffixCls:"header",tagName:"header",displayName:"Header"})(f),y=p({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(f),x=p({suffixCls:"content",tagName:"main",displayName:"Content"})(f);v.Header=b,v.Footer=y,v.Content=x,v.Sider=u.A,v._InternalSiderContext=u.P;let S=v},67312:(e,t,n)=>{n.d(t,{Ay:()=>l,cH:()=>r,lB:()=>c});var o=n(5144),a=n(1086);let i=e=>{let{antCls:t,componentCls:n,colorText:a,footerBg:i,headerHeight:r,headerPadding:c,headerColor:l,footerPadding:s,fontSize:d,bodyBg:u,headerBg:g}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:u,"&, *":{boxSizing:"border-box"},["&".concat(n,"-has-sider")]:{flexDirection:"row",["> ".concat(n,", > ").concat(n,"-content")]:{width:0}},["".concat(n,"-header, &").concat(n,"-footer")]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},["".concat(n,"-header")]:{height:r,padding:c,color:l,lineHeight:(0,o.zA)(r),background:g,["".concat(t,"-menu")]:{lineHeight:"inherit"}},["".concat(n,"-footer")]:{padding:s,color:a,fontSize:d,background:i},["".concat(n,"-content")]:{flex:"auto",color:a,minHeight:0}}},r=e=>{let{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:a,controlHeightSM:i,marginXXS:r,colorTextLightSolid:c,colorBgContainer:l}=e,s=1.25*o;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:"0 ".concat(s,"px"),headerColor:a,footerPadding:"".concat(i,"px ").concat(s,"px"),footerBg:t,siderBg:"#001529",triggerHeight:o+2*r,triggerBg:"#002140",triggerColor:c,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:l,lightTriggerBg:l,lightTriggerColor:a}},c=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],l=(0,a.OF)("Layout",e=>[i(e)],r,{deprecatedTokens:c})},72093:(e,t,n)=>{let o;n.d(t,{A:()=>O});var a=n(12115),i=n(4617),r=n.n(i),c=n(31049),l=n(58292),s=n(66105);let d=80*Math.PI,u=e=>{let{dotClassName:t,style:n,hasCircleCls:o}=e;return a.createElement("circle",{className:r()("".concat(t,"-circle"),{["".concat(t,"-circle-bg")]:o}),r:40,cx:50,cy:50,strokeWidth:20,style:n})},g=e=>{let{percent:t,prefixCls:n}=e,o="".concat(n,"-dot"),i="".concat(o,"-holder"),c="".concat(i,"-hidden"),[l,g]=a.useState(!1);(0,s.A)(()=>{0!==t&&g(!0)},[0!==t]);let m=Math.max(Math.min(t,100),0);if(!l)return null;let p={strokeDashoffset:"".concat(d/4),strokeDasharray:"".concat(d*m/100," ").concat(d*(100-m)/100)};return a.createElement("span",{className:r()(i,"".concat(o,"-progress"),m<=0&&c)},a.createElement("svg",{viewBox:"0 0 ".concat(100," ").concat(100),role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":m},a.createElement(u,{dotClassName:o,hasCircleCls:!0}),a.createElement(u,{dotClassName:o,style:p})))};function m(e){let{prefixCls:t,percent:n=0}=e,o="".concat(t,"-dot"),i="".concat(o,"-holder"),c="".concat(i,"-hidden");return a.createElement(a.Fragment,null,a.createElement("span",{className:r()(i,n>0&&c)},a.createElement("span",{className:r()(o,"".concat(t,"-dot-spin"))},[1,2,3,4].map(e=>a.createElement("i",{className:"".concat(t,"-dot-item"),key:e})))),a.createElement(g,{prefixCls:t,percent:n}))}function p(e){let{prefixCls:t,indicator:n,percent:o}=e;return n&&a.isValidElement(n)?(0,l.Ob)(n,{className:r()(n.props.className,"".concat(t,"-dot")),percent:o}):a.createElement(m,{prefixCls:t,percent:o})}var f=n(5144),h=n(70695),v=n(1086),b=n(56204);let y=new f.Mo("antSpinMove",{to:{opacity:1}}),x=new f.Mo("antRotate",{to:{transform:"rotate(405deg)"}}),S=e=>{let{componentCls:t,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{position:"absolute",display:"none",color:e.colorPrimary,fontSize:0,textAlign:"center",verticalAlign:"middle",opacity:0,transition:"transform ".concat(e.motionDurationSlow," ").concat(e.motionEaseInOutCirc),"&-spinning":{position:"relative",display:"inline-block",opacity:1},["".concat(t,"-text")]:{fontSize:e.fontSize,paddingTop:n(n(e.dotSize).sub(e.fontSize)).div(2).add(2).equal()},"&-fullscreen":{position:"fixed",width:"100vw",height:"100vh",backgroundColor:e.colorBgMask,zIndex:e.zIndexPopupBase,inset:0,display:"flex",alignItems:"center",flexDirection:"column",justifyContent:"center",opacity:0,visibility:"hidden",transition:"all ".concat(e.motionDurationMid),"&-show":{opacity:1,visibility:"visible"},[t]:{["".concat(t,"-dot-holder")]:{color:e.colorWhite},["".concat(t,"-text")]:{color:e.colorTextLightSolid}}},"&-nested-loading":{position:"relative",["> div > ".concat(t)]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,["".concat(t,"-dot")]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:n(e.dotSize).mul(-1).div(2).equal()},["".concat(t,"-text")]:{position:"absolute",top:"50%",width:"100%",textShadow:"0 1px 2px ".concat(e.colorBgContainer)},["&".concat(t,"-show-text ").concat(t,"-dot")]:{marginTop:n(e.dotSize).div(2).mul(-1).sub(10).equal()},"&-sm":{["".concat(t,"-dot")]:{margin:n(e.dotSizeSM).mul(-1).div(2).equal()},["".concat(t,"-text")]:{paddingTop:n(n(e.dotSizeSM).sub(e.fontSize)).div(2).add(2).equal()},["&".concat(t,"-show-text ").concat(t,"-dot")]:{marginTop:n(e.dotSizeSM).div(2).mul(-1).sub(10).equal()}},"&-lg":{["".concat(t,"-dot")]:{margin:n(e.dotSizeLG).mul(-1).div(2).equal()},["".concat(t,"-text")]:{paddingTop:n(n(e.dotSizeLG).sub(e.fontSize)).div(2).add(2).equal()},["&".concat(t,"-show-text ").concat(t,"-dot")]:{marginTop:n(e.dotSizeLG).div(2).mul(-1).sub(10).equal()}}},["".concat(t,"-container")]:{position:"relative",transition:"opacity ".concat(e.motionDurationSlow),"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:"all ".concat(e.motionDurationSlow),content:'""',pointerEvents:"none"}},["".concat(t,"-blur")]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},["".concat(t,"-dot-holder")]:{width:"1em",height:"1em",fontSize:e.dotSize,display:"inline-block",transition:"transform ".concat(e.motionDurationSlow," ease, opacity ").concat(e.motionDurationSlow," ease"),transformOrigin:"50% 50%",lineHeight:1,color:e.colorPrimary,"&-hidden":{transform:"scale(0.3)",opacity:0}},["".concat(t,"-dot-progress")]:{position:"absolute",inset:0},["".concat(t,"-dot")]:{position:"relative",display:"inline-block",fontSize:e.dotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),height:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),background:"currentColor",borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:y,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0,animationDelay:"0s"},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:x,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"},"&-circle":{strokeLinecap:"round",transition:["stroke-dashoffset","stroke-dasharray","stroke","stroke-width","opacity"].map(t=>"".concat(t," ").concat(e.motionDurationSlow," ease")).join(","),fillOpacity:0,stroke:"currentcolor"},"&-circle-bg":{stroke:e.colorFillSecondary}},["&-sm ".concat(t,"-dot")]:{"&, &-holder":{fontSize:e.dotSizeSM}},["&-sm ".concat(t,"-dot-holder")]:{i:{width:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal(),height:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal()}},["&-lg ".concat(t,"-dot")]:{"&, &-holder":{fontSize:e.dotSizeLG}},["&-lg ".concat(t,"-dot-holder")]:{i:{width:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal(),height:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal()}},["&".concat(t,"-show-text ").concat(t,"-text")]:{display:"block"}})}},w=(0,v.OF)("Spin",e=>[S((0,b.oX)(e,{spinDotDefault:e.colorTextDescription}))],e=>{let{controlHeightLG:t,controlHeight:n}=e;return{contentHeight:400,dotSize:t/2,dotSizeSM:.35*t,dotSizeLG:n}}),z=[[30,.05],[70,.03],[96,.01]];var E=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let N=e=>{var t;let{prefixCls:n,spinning:i=!0,delay:l=0,className:s,rootClassName:d,size:u="default",tip:g,wrapperClassName:m,style:f,children:h,fullscreen:v=!1,indicator:b,percent:y}=e,x=E(e,["prefixCls","spinning","delay","className","rootClassName","size","tip","wrapperClassName","style","children","fullscreen","indicator","percent"]),{getPrefixCls:S,direction:N,className:O,style:C,indicator:k}=(0,c.TP)("spin"),A=S("spin",n),[B,I,j]=w(A),[D,T]=a.useState(()=>i&&!function(e,t){return!!e&&!!t&&!Number.isNaN(Number(t))}(i,l)),M=function(e,t){let[n,o]=a.useState(0),i=a.useRef(null),r="auto"===t;return a.useEffect(()=>(r&&e&&(o(0),i.current=setInterval(()=>{o(e=>{let t=100-e;for(let n=0;n<z.length;n+=1){let[o,a]=z[n];if(e<=o)return e+t*a}return e})},200)),()=>{clearInterval(i.current)}),[r,e]),r?n:t}(D,y);a.useEffect(()=>{if(i){let e=function(e,t,n){var o=void 0;return function(e,t,n){var o,a=n||{},i=a.noTrailing,r=void 0!==i&&i,c=a.noLeading,l=void 0!==c&&c,s=a.debounceMode,d=void 0===s?void 0:s,u=!1,g=0;function m(){o&&clearTimeout(o)}function p(){for(var n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];var c=this,s=Date.now()-g;function p(){g=Date.now(),t.apply(c,a)}function f(){o=void 0}!u&&(l||!d||o||p(),m(),void 0===d&&s>e?l?(g=Date.now(),r||(o=setTimeout(d?f:p,e))):p():!0!==r&&(o=setTimeout(d?f:p,void 0===d?e-s:e)))}return p.cancel=function(e){var t=(e||{}).upcomingOnly;m(),u=!(void 0!==t&&t)},p}(e,t,{debounceMode:!1!==(void 0!==o&&o)})}(l,()=>{T(!0)});return e(),()=>{var t;null===(t=null==e?void 0:e.cancel)||void 0===t||t.call(e)}}T(!1)},[l,i]);let H=a.useMemo(()=>void 0!==h&&!v,[h,v]),P=r()(A,O,{["".concat(A,"-sm")]:"small"===u,["".concat(A,"-lg")]:"large"===u,["".concat(A,"-spinning")]:D,["".concat(A,"-show-text")]:!!g,["".concat(A,"-rtl")]:"rtl"===N},s,!v&&d,I,j),L=r()("".concat(A,"-container"),{["".concat(A,"-blur")]:D}),q=null!==(t=null!=b?b:k)&&void 0!==t?t:o,F=Object.assign(Object.assign({},C),f),X=a.createElement("div",Object.assign({},x,{style:F,className:P,"aria-live":"polite","aria-busy":D}),a.createElement(p,{prefixCls:A,indicator:q,percent:M}),g&&(H||v)?a.createElement("div",{className:"".concat(A,"-text")},g):null);return B(H?a.createElement("div",Object.assign({},x,{className:r()("".concat(A,"-nested-loading"),m,I,j)}),D&&a.createElement("div",{key:"loading"},X),a.createElement("div",{className:L,key:"container"},h)):v?a.createElement("div",{className:r()("".concat(A,"-fullscreen"),{["".concat(A,"-fullscreen-show")]:D},d,I,j)},X):X)};N.setDefaultIndicator=e=>{o=e};let O=N},94937:(e,t,n)=>{n.d(t,{P:()=>z,A:()=>N});var o=n(12115),a=n(85407);let i={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};var r=n(84021),c=o.forwardRef(function(e,t){return o.createElement(r.A,(0,a.A)({},e,{ref:t,icon:i}))}),l=n(33621),s=n(44549),d=n(4617),u=n.n(d),g=n(70527),m=n(31049),p=n(28709),f=n(5144),h=n(67312),v=n(1086);let b=e=>{let{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:a,antCls:i,triggerHeight:r,triggerColor:c,triggerBg:l,headerHeight:s,zeroTriggerWidth:d,zeroTriggerHeight:u,borderRadiusLG:g,lightSiderBg:m,lightTriggerColor:p,lightTriggerBg:h,bodyBg:v}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:"all ".concat(o,", background 0s"),"&-has-trigger":{paddingBottom:r},"&-right":{order:1},["".concat(t,"-children")]:{height:"100%",marginTop:-.1,paddingTop:.1,["".concat(i,"-menu").concat(i,"-menu-inline-collapsed")]:{width:"auto"}},["&-zero-width ".concat(t,"-children")]:{overflow:"hidden"},["".concat(t,"-trigger")]:{position:"fixed",bottom:0,zIndex:1,height:r,color:c,lineHeight:(0,f.zA)(r),textAlign:"center",background:l,cursor:"pointer",transition:"all ".concat(o)},["".concat(t,"-zero-width-trigger")]:{position:"absolute",top:s,insetInlineEnd:e.calc(d).mul(-1).equal(),zIndex:1,width:d,height:u,color:c,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:"0 ".concat((0,f.zA)(g)," ").concat((0,f.zA)(g)," 0"),cursor:"pointer",transition:"background ".concat(a," ease"),"&::after":{position:"absolute",inset:0,background:"transparent",transition:"all ".concat(a),content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(d).mul(-1).equal(),borderRadius:"".concat((0,f.zA)(g)," 0 0 ").concat((0,f.zA)(g))}},"&-light":{background:m,["".concat(t,"-trigger")]:{color:p,background:h},["".concat(t,"-zero-width-trigger")]:{color:p,background:h,border:"1px solid ".concat(v),borderInlineStart:0}}}}},y=(0,v.OF)(["Layout","Sider"],e=>[b(e)],h.cH,{deprecatedTokens:h.lB});var x=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let S={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},w=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),z=o.createContext({}),E=(()=>{let e=0;return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e+=1,"".concat(t).concat(e)}})(),N=o.forwardRef((e,t)=>{let{prefixCls:n,className:a,trigger:i,children:r,defaultCollapsed:d=!1,theme:f="dark",style:h={},collapsible:v=!1,reverseArrow:b=!1,width:N=200,collapsedWidth:O=80,zeroWidthTriggerStyle:C,breakpoint:k,onCollapse:A,onBreakpoint:B}=e,I=x(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:j}=(0,o.useContext)(p.M),[D,T]=(0,o.useState)("collapsed"in e?e.collapsed:d),[M,H]=(0,o.useState)(!1);(0,o.useEffect)(()=>{"collapsed"in e&&T(e.collapsed)},[e.collapsed]);let P=(t,n)=>{"collapsed"in e||T(t),null==A||A(t,n)},{getPrefixCls:L,direction:q}=(0,o.useContext)(m.QO),F=L("layout-sider",n),[X,R,W]=y(F),G=(0,o.useRef)(null);G.current=e=>{H(e.matches),null==B||B(e.matches),D!==e.matches&&P(e.matches,"responsive")},(0,o.useEffect)(()=>{let e;function t(e){return G.current(e)}if("undefined"!=typeof window){let{matchMedia:n}=window;if(n&&k&&k in S){e=n("screen and (max-width: ".concat(S[k],")"));try{e.addEventListener("change",t)}catch(n){e.addListener(t)}t(e)}}return()=>{try{null==e||e.removeEventListener("change",t)}catch(n){null==e||e.removeListener(t)}}},[k]),(0,o.useEffect)(()=>{let e=E("ant-sider-");return j.addSider(e),()=>j.removeSider(e)},[]);let _=()=>{P(!D,"clickTrigger")},Q=(0,g.A)(I,["collapsed"]),V=D?O:N,J=w(V)?"".concat(V,"px"):String(V),K=0===parseFloat(String(O||0))?o.createElement("span",{onClick:_,className:u()("".concat(F,"-zero-width-trigger"),"".concat(F,"-zero-width-trigger-").concat(b?"right":"left")),style:C},i||o.createElement(c,null)):null,U="rtl"===q==!b,Y={expanded:U?o.createElement(s.A,null):o.createElement(l.A,null),collapsed:U?o.createElement(l.A,null):o.createElement(s.A,null)}[D?"collapsed":"expanded"],Z=null!==i?K||o.createElement("div",{className:"".concat(F,"-trigger"),onClick:_,style:{width:J}},i||Y):null,$=Object.assign(Object.assign({},h),{flex:"0 0 ".concat(J),maxWidth:J,minWidth:J,width:J}),ee=u()(F,"".concat(F,"-").concat(f),{["".concat(F,"-collapsed")]:!!D,["".concat(F,"-has-trigger")]:v&&null!==i&&!K,["".concat(F,"-below")]:!!M,["".concat(F,"-zero-width")]:0===parseFloat(J)},a,R,W),et=o.useMemo(()=>({siderCollapsed:D}),[D]);return X(o.createElement(z.Provider,{value:et},o.createElement("aside",Object.assign({className:ee},Q,{style:$,ref:t}),o.createElement("div",{className:"".concat(F,"-children")},r),v||M&&K?Z:null)))})}}]);
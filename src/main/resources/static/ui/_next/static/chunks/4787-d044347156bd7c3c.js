"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4787],{64787:(t,o,a)=>{a.d(o,{A:()=>X});var e=a(12115),n=a(4617),c=a.n(n),i=a(28415),r=a(31049),l=a(31617),s=a(35585),d=a(22946),p=a(7926),m=a(68711),u=a(4951),g=a(6140),f=a(79624),b=a(51629),h=a(92984),v=a(16419);function k(t,o){return null===o||!1===o?null:o||e.createElement(f.A,{className:"".concat(t,"-close-icon")})}h.A,u.A,g.A,b.A,v.A;let y={success:u.A,info:h.A,error:g.A,warning:b.A},O=t=>{let{prefixCls:o,icon:a,type:n,message:i,description:r,actions:l,role:s="alert"}=t,d=null;return a?d=e.createElement("span",{className:"".concat(o,"-icon")},a):n&&(d=e.createElement(y[n]||null,{className:c()("".concat(o,"-icon"),"".concat(o,"-icon-").concat(n))})),e.createElement("div",{className:c()({["".concat(o,"-with-icon")]:d}),role:s},d,e.createElement("div",{className:"".concat(o,"-message")},i),e.createElement("div",{className:"".concat(o,"-description")},r),l&&e.createElement("div",{className:"".concat(o,"-actions")},l))};var w=a(5144),j=a(78877),S=a(70695),I=a(56204),N=a(1086);let x=t=>{let{componentCls:o,notificationMarginEdge:a,animationMaxHeight:e}=t,n="".concat(o,"-notice"),c=new w.Mo("antNotificationFadeIn",{"0%":{transform:"translate3d(100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}}),i=new w.Mo("antNotificationTopFadeIn",{"0%":{top:-e,opacity:0},"100%":{top:0,opacity:1}}),r=new w.Mo("antNotificationBottomFadeIn",{"0%":{bottom:t.calc(e).mul(-1).equal(),opacity:0},"100%":{bottom:0,opacity:1}}),l=new w.Mo("antNotificationLeftFadeIn",{"0%":{transform:"translate3d(-100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}});return{[o]:{["&".concat(o,"-top, &").concat(o,"-bottom")]:{marginInline:0,[n]:{marginInline:"auto auto"}},["&".concat(o,"-top")]:{["".concat(o,"-fade-enter").concat(o,"-fade-enter-active, ").concat(o,"-fade-appear").concat(o,"-fade-appear-active")]:{animationName:i}},["&".concat(o,"-bottom")]:{["".concat(o,"-fade-enter").concat(o,"-fade-enter-active, ").concat(o,"-fade-appear").concat(o,"-fade-appear-active")]:{animationName:r}},["&".concat(o,"-topRight, &").concat(o,"-bottomRight")]:{["".concat(o,"-fade-enter").concat(o,"-fade-enter-active, ").concat(o,"-fade-appear").concat(o,"-fade-appear-active")]:{animationName:c}},["&".concat(o,"-topLeft, &").concat(o,"-bottomLeft")]:{marginRight:{value:0,_skip_check_:!0},marginLeft:{value:a,_skip_check_:!0},[n]:{marginInlineEnd:"auto",marginInlineStart:0},["".concat(o,"-fade-enter").concat(o,"-fade-enter-active, ").concat(o,"-fade-appear").concat(o,"-fade-appear-active")]:{animationName:l}}}}},A=["top","topLeft","topRight","bottom","bottomLeft","bottomRight"],E={topLeft:"left",topRight:"right",bottomLeft:"left",bottomRight:"right",top:"left",bottom:"left"},M=(t,o)=>{let{componentCls:a}=t;return{["".concat(a,"-").concat(o)]:{["&".concat(a,"-stack > ").concat(a,"-notice-wrapper")]:{[o.startsWith("top")?"top":"bottom"]:0,[E[o]]:{value:0,_skip_check_:!0}}}}},C=t=>{let o={};for(let a=1;a<t.notificationStackLayer;a++)o["&:nth-last-child(".concat(a+1,")")]={overflow:"hidden",["& > ".concat(t.componentCls,"-notice")]:{opacity:0,transition:"opacity ".concat(t.motionDurationMid)}};return Object.assign({["&:not(:nth-last-child(-n+".concat(t.notificationStackLayer,"))")]:{opacity:0,overflow:"hidden",color:"transparent",pointerEvents:"none"}},o)},B=t=>{let o={};for(let a=1;a<t.notificationStackLayer;a++)o["&:nth-last-child(".concat(a+1,")")]={background:t.colorBgBlur,backdropFilter:"blur(10px)","-webkit-backdrop-filter":"blur(10px)"};return Object.assign({},o)},L=t=>{let{componentCls:o}=t;return Object.assign({["".concat(o,"-stack")]:{["& > ".concat(o,"-notice-wrapper")]:Object.assign({transition:"transform ".concat(t.motionDurationSlow,", backdrop-filter 0s"),willChange:"transform, opacity",position:"absolute"},C(t))},["".concat(o,"-stack:not(").concat(o,"-stack-expanded)")]:{["& > ".concat(o,"-notice-wrapper")]:Object.assign({},B(t))},["".concat(o,"-stack").concat(o,"-stack-expanded")]:{["& > ".concat(o,"-notice-wrapper")]:{"&:not(:nth-last-child(-n + 1))":{opacity:1,overflow:"unset",color:"inherit",pointerEvents:"auto",["& > ".concat(t.componentCls,"-notice")]:{opacity:1}},"&:after":{content:'""',position:"absolute",height:t.margin,width:"100%",insetInline:0,bottom:t.calc(t.margin).mul(-1).equal(),background:"transparent",pointerEvents:"auto"}}}},A.map(o=>M(t,o)).reduce((t,o)=>Object.assign(Object.assign({},t),o),{}))},_=t=>{let{iconCls:o,componentCls:a,boxShadow:e,fontSizeLG:n,notificationMarginBottom:c,borderRadiusLG:i,colorSuccess:r,colorInfo:l,colorWarning:s,colorError:d,colorTextHeading:p,notificationBg:m,notificationPadding:u,notificationMarginEdge:g,notificationProgressBg:f,notificationProgressHeight:b,fontSize:h,lineHeight:v,width:k,notificationIconSize:y,colorText:O}=t,j="".concat(a,"-notice");return{position:"relative",marginBottom:c,marginInlineStart:"auto",background:m,borderRadius:i,boxShadow:e,[j]:{padding:u,width:k,maxWidth:"calc(100vw - ".concat((0,w.zA)(t.calc(g).mul(2).equal()),")"),overflow:"hidden",lineHeight:v,wordWrap:"break-word"},["".concat(j,"-message")]:{marginBottom:t.marginXS,color:p,fontSize:n,lineHeight:t.lineHeightLG},["".concat(j,"-description")]:{fontSize:h,color:O},["".concat(j,"-closable ").concat(j,"-message")]:{paddingInlineEnd:t.paddingLG},["".concat(j,"-with-icon ").concat(j,"-message")]:{marginBottom:t.marginXS,marginInlineStart:t.calc(t.marginSM).add(y).equal(),fontSize:n},["".concat(j,"-with-icon ").concat(j,"-description")]:{marginInlineStart:t.calc(t.marginSM).add(y).equal(),fontSize:h},["".concat(j,"-icon")]:{position:"absolute",fontSize:y,lineHeight:1,["&-success".concat(o)]:{color:r},["&-info".concat(o)]:{color:l},["&-warning".concat(o)]:{color:s},["&-error".concat(o)]:{color:d}},["".concat(j,"-close")]:Object.assign({position:"absolute",top:t.notificationPaddingVertical,insetInlineEnd:t.notificationPaddingHorizontal,color:t.colorIcon,outline:"none",width:t.notificationCloseButtonSize,height:t.notificationCloseButtonSize,borderRadius:t.borderRadiusSM,transition:"background-color ".concat(t.motionDurationMid,", color ").concat(t.motionDurationMid),display:"flex",alignItems:"center",justifyContent:"center","&:hover":{color:t.colorIconHover,backgroundColor:t.colorBgTextHover},"&:active":{backgroundColor:t.colorBgTextActive}},(0,S.K8)(t)),["".concat(j,"-progress")]:{position:"absolute",display:"block",appearance:"none",WebkitAppearance:"none",inlineSize:"calc(100% - ".concat((0,w.zA)(i)," * 2)"),left:{_skip_check_:!0,value:i},right:{_skip_check_:!0,value:i},bottom:0,blockSize:b,border:0,"&, &::-webkit-progress-bar":{borderRadius:i,backgroundColor:"rgba(0, 0, 0, 0.04)"},"&::-moz-progress-bar":{background:f},"&::-webkit-progress-value":{borderRadius:i,background:f}},["".concat(j,"-actions")]:{float:"right",marginTop:t.marginSM}}},z=t=>{let{componentCls:o,notificationMarginBottom:a,notificationMarginEdge:e,motionDurationMid:n,motionEaseInOut:c}=t,i="".concat(o,"-notice"),r=new w.Mo("antNotificationFadeOut",{"0%":{maxHeight:t.animationMaxHeight,marginBottom:a},"100%":{maxHeight:0,marginBottom:0,paddingTop:0,paddingBottom:0,opacity:0}});return[{[o]:Object.assign(Object.assign({},(0,S.dF)(t)),{position:"fixed",zIndex:t.zIndexPopup,marginRight:{value:e,_skip_check_:!0},["".concat(o,"-hook-holder")]:{position:"relative"},["".concat(o,"-fade-appear-prepare")]:{opacity:"0 !important"},["".concat(o,"-fade-enter, ").concat(o,"-fade-appear")]:{animationDuration:t.motionDurationMid,animationTimingFunction:c,animationFillMode:"both",opacity:0,animationPlayState:"paused"},["".concat(o,"-fade-leave")]:{animationTimingFunction:c,animationFillMode:"both",animationDuration:n,animationPlayState:"paused"},["".concat(o,"-fade-enter").concat(o,"-fade-enter-active, ").concat(o,"-fade-appear").concat(o,"-fade-appear-active")]:{animationPlayState:"running"},["".concat(o,"-fade-leave").concat(o,"-fade-leave-active")]:{animationName:r,animationPlayState:"running"},"&-rtl":{direction:"rtl",["".concat(i,"-actions")]:{float:"left"}}})},{[o]:{["".concat(i,"-wrapper")]:Object.assign({},_(t))}}]},P=t=>{let o=t.paddingMD,a=t.paddingLG;return(0,I.oX)(t,{notificationBg:t.colorBgElevated,notificationPaddingVertical:o,notificationPaddingHorizontal:a,notificationIconSize:t.calc(t.fontSizeLG).mul(t.lineHeightLG).equal(),notificationCloseButtonSize:t.calc(t.controlHeightLG).mul(.55).equal(),notificationMarginBottom:t.margin,notificationPadding:"".concat((0,w.zA)(t.paddingMD)," ").concat((0,w.zA)(t.paddingContentHorizontalLG)),notificationMarginEdge:t.marginLG,animationMaxHeight:150,notificationStackLayer:3,notificationProgressHeight:2,notificationProgressBg:"linear-gradient(90deg, ".concat(t.colorPrimaryBorderHover,", ").concat(t.colorPrimary,")")})},R=(0,N.OF)("Notification",t=>{let o=P(t);return[z(o),x(o),L(o)]},t=>({zIndexPopup:t.zIndexPopupBase+j.jH+50,width:384}));var H=function(t,o){var a={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&0>o.indexOf(e)&&(a[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,e=Object.getOwnPropertySymbols(t);n<e.length;n++)0>o.indexOf(e[n])&&Object.prototype.propertyIsEnumerable.call(t,e[n])&&(a[e[n]]=t[e[n]]);return a};let F=t=>{let{children:o,prefixCls:a}=t,n=(0,p.A)(a),[i,r,l]=R(a,n);return i(e.createElement(d.ph,{classNames:{list:c()(r,l,n)}},o))},D=(t,o)=>{let{prefixCls:a,key:n}=o;return e.createElement(F,{prefixCls:a,key:n},t)},G=e.forwardRef((t,o)=>{let{top:a,bottom:n,prefixCls:i,getContainer:l,maxCount:s,rtl:p,onAllRemoved:u,stack:g,duration:f,pauseOnHover:b=!0,showProgress:h}=t,{getPrefixCls:v,getPopupContainer:y,notification:O,direction:w}=(0,e.useContext)(r.QO),[,j]=(0,m.Ay)(),S=i||v("notification"),[I,N]=(0,d.hN)({prefixCls:S,style:t=>(function(t,o,a){let e;switch(t){case"top":e={left:"50%",transform:"translateX(-50%)",right:"auto",top:o,bottom:"auto"};break;case"topLeft":e={left:0,top:o,bottom:"auto"};break;case"topRight":e={right:0,top:o,bottom:"auto"};break;case"bottom":e={left:"50%",transform:"translateX(-50%)",right:"auto",top:"auto",bottom:a};break;case"bottomLeft":e={left:0,top:"auto",bottom:a};break;default:e={right:0,top:"auto",bottom:a}}return e})(t,null!=a?a:24,null!=n?n:24),className:()=>c()({["".concat(S,"-rtl")]:null!=p?p:"rtl"===w}),motion:()=>({motionName:"".concat(S,"-fade")}),closable:!0,closeIcon:k(S),duration:null!=f?f:4.5,getContainer:()=>(null==l?void 0:l())||(null==y?void 0:y())||document.body,maxCount:s,pauseOnHover:b,showProgress:h,onAllRemoved:u,renderNotifications:D,stack:!1!==g&&{threshold:"object"==typeof g?null==g?void 0:g.threshold:void 0,offset:8,gap:j.margin}});return e.useImperativeHandle(o,()=>Object.assign(Object.assign({},I),{prefixCls:S,notification:O})),N});var q=a(89842);let T=(0,N.OF)("App",t=>{let{componentCls:o,colorText:a,fontSize:e,lineHeight:n,fontFamily:c}=t;return{[o]:{color:a,fontSize:e,lineHeight:n,fontFamily:c,["&".concat(o,"-rtl")]:{direction:"rtl"}}}},()=>({})),W=t=>{let{prefixCls:o,children:a,className:n,rootClassName:d,message:p,notification:m,style:u,component:g="div"}=t,{direction:f,getPrefixCls:b}=(0,e.useContext)(r.QO),h=b("app",o),[v,y,w]=T(h),j=c()(y,h,n,d,w,{["".concat(h,"-rtl")]:"rtl"===f}),S=(0,e.useContext)(q.B),I=e.useMemo(()=>({message:Object.assign(Object.assign({},S.message),p),notification:Object.assign(Object.assign({},S.notification),m)}),[p,m,S.message,S.notification]),[N,x]=(0,l.A)(I.message),[A,E]=function(t){let o=e.useRef(null);return(0,i.rJ)("Notification"),[e.useMemo(()=>{let a=a=>{var n;if(!o.current)return;let{open:i,prefixCls:r,notification:l}=o.current,s="".concat(r,"-notice"),{message:d,description:p,icon:m,type:u,btn:g,actions:f,className:b,style:h,role:v="alert",closeIcon:y,closable:w}=a,j=H(a,["message","description","icon","type","btn","actions","className","style","role","closeIcon","closable"]),S=k(s,void 0!==y?y:void 0!==(null==t?void 0:t.closeIcon)?t.closeIcon:null==l?void 0:l.closeIcon);return i(Object.assign(Object.assign({placement:null!==(n=null==t?void 0:t.placement)&&void 0!==n?n:"topRight"},j),{content:e.createElement(O,{prefixCls:s,icon:m,type:u,message:d,description:p,actions:null!=f?f:g,role:v}),className:c()(u&&"".concat(s,"-").concat(u),b,null==l?void 0:l.className),style:Object.assign(Object.assign({},null==l?void 0:l.style),h),closeIcon:S,closable:null!=w?w:!!S}))},n={open:a,destroy:t=>{var a,e;void 0!==t?null===(a=o.current)||void 0===a||a.close(t):null===(e=o.current)||void 0===e||e.destroy()}};return["success","info","warning","error"].forEach(t=>{n[t]=o=>a(Object.assign(Object.assign({},o),{type:t}))}),n},[]),e.createElement(G,Object.assign({key:"notification-holder"},t,{ref:o}))]}(I.notification),[M,C]=(0,s.A)(),B=e.useMemo(()=>({message:N,notification:A,modal:M}),[N,A,M]);(0,i.rJ)("App")(!(w&&!1===g),"usage","When using cssVar, ensure `component` is assigned a valid React component string.");let L=!1===g?e.Fragment:g;return v(e.createElement(q.A.Provider,{value:B},e.createElement(q.B.Provider,{value:I},e.createElement(L,Object.assign({},!1===g?void 0:{className:j,style:u}),C,x,E,a))))};W.useApp=()=>e.useContext(q.A);let X=W}}]);
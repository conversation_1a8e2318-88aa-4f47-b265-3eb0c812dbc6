(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3520],{2796:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=n(96594).A},4768:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(85407),o=n(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"};var i=n(84021);let l=o.forwardRef(function(e,t){return o.createElement(i.A,(0,r.A)({},e,{ref:t,icon:a}))})},8273:(e,t,n)=>{var r=n(41933),o=Object.prototype,a=o.hasOwnProperty,i=o.toString,l=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,l),n=e[l];try{e[l]=void 0;var r=!0}catch(e){}var o=i.call(e);return r&&(t?e[l]=n:delete e[l]),o}},9018:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},11679:(e,t,n)=>{"use strict";n.d(t,{A:()=>c,U:()=>l});var r=n(12115),o=n(35015),a=n(11432),i=n(31049);function l(e){return t=>r.createElement(a.Ay,{theme:{token:{motion:!1,zIndexPopupBase:0}}},r.createElement(e,Object.assign({},t)))}let c=(e,t,n,a,c)=>l(l=>{let{prefixCls:s,style:u}=l,f=r.useRef(null),[d,p]=r.useState(0),[m,v]=r.useState(0),[g,y]=(0,o.A)(!1,{value:l.open}),{getPrefixCls:h}=r.useContext(i.QO),b=h(a||"select",s);r.useEffect(()=>{if(y(!0),"undefined"!=typeof ResizeObserver){let e=new ResizeObserver(e=>{let t=e[0].target;p(t.offsetHeight+8),v(t.offsetWidth)}),t=setInterval(()=>{var n;let r=c?".".concat(c(b)):".".concat(b,"-dropdown"),o=null===(n=f.current)||void 0===n?void 0:n.querySelector(r);o&&(clearInterval(t),e.observe(o))},10);return()=>{clearInterval(t),e.disconnect()}}},[]);let O=Object.assign(Object.assign({},l),{style:Object.assign(Object.assign({},u),{margin:0}),open:g,visible:g,getPopupContainer:()=>f.current});return n&&(O=n(O)),t&&Object.assign(O,{[t]:{overflow:{adjustX:!1,adjustY:!1}}}),r.createElement("div",{ref:f,style:{paddingBottom:d,position:"relative",minWidth:m}},r.createElement(e,Object.assign({},O)))})},21079:(e,t,n)=>{"use strict";var r=n(9018),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,a,i,l,c,s,u,f,d=!1;t||(t={}),i=t.debug||!1;try{if(c=r(),s=document.createRange(),u=document.getSelection(),(f=document.createElement("span")).textContent=e,f.ariaHidden="true",f.style.all="unset",f.style.position="fixed",f.style.top=0,f.style.clip="rect(0, 0, 0, 0)",f.style.whiteSpace="pre",f.style.webkitUserSelect="text",f.style.MozUserSelect="text",f.style.msUserSelect="text",f.style.userSelect="text",f.addEventListener("copy",function(n){if(n.stopPropagation(),t.format){if(n.preventDefault(),void 0===n.clipboardData){i&&console.warn("unable to use e.clipboardData"),i&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var r=o[t.format]||o.default;window.clipboardData.setData(r,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e)}t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(f),s.selectNodeContents(f),u.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");d=!0}catch(r){i&&console.error("unable to copy using execCommand: ",r),i&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),d=!0}catch(r){i&&console.error("unable to copy using clipboardData: ",r),i&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",l=n.replace(/#{\s*key\s*}/g,a),window.prompt(l,e)}}finally{u&&("function"==typeof u.removeRange?u.removeRange(s):u.removeAllRanges()),f&&document.body.removeChild(f),c()}return d}},31004:(e,t,n)=>{var r=n(77550),o=/^\s+/;e.exports=function(e){return e?e.slice(0,r(e)+1).replace(o,""):e}},32816:(e,t,n)=>{var r=n(31004),o=n(95255),a=n(83480),i=0/0,l=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,s=/^0o[0-7]+$/i,u=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(a(e))return i;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=r(e);var n=c.test(e);return n||s.test(e)?u(e.slice(2),n?2:8):l.test(e)?i:+e}},34480:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},34952:(e,t,n)=>{var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},41933:(e,t,n)=>{var r=n(62673).Symbol;e.exports=r},43600:(e,t,n)=>{var r=n(41933),o=n(8273),a=n(96798),i=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?o(e):a(e)}},46742:(e,t,n)=>{"use strict";n.d(t,{A:()=>eb});var r=n(12115),o=n(85407);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var i=n(84021),l=r.forwardRef(function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:a}))}),c=n(4617),s=n.n(c),u=n(30377),f=n(63588),d=n(66105),p=n(35015),m=n(70527),v=n(15231),g=n(88959),y=n(31049),h=n(55315),b=n(6457);let O={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var E=r.forwardRef(function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:O}))}),x=n(23672),w=n(58292),j=n(25392),S=n(70695),A=n(1086),C=n(28405),k=n(5144);let R=(e,t,n,r)=>{let{titleMarginBottom:o,fontWeightStrong:a}=r;return{marginBottom:o,color:n,fontWeight:a,fontSize:e,lineHeight:t}},T=e=>{let t={};return[1,2,3,4,5].forEach(n=>{t["\n      h".concat(n,"&,\n      div&-h").concat(n,",\n      div&-h").concat(n," > textarea,\n      h").concat(n,"\n    ")]=R(e["fontSizeHeading".concat(n)],e["lineHeightHeading".concat(n)],e.colorTextHeading,e)}),t},P=e=>{let{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,S.Y1)(e)),{userSelect:"text",["&[disabled], &".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},M=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:C.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),H=e=>{let{componentCls:t,paddingSM:n}=e;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:"calc(1em - ".concat((0,k.zA)(n),")")},["".concat(t,"-edit-content-confirm")]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},I=e=>({["".concat(e.componentCls,"-copy-success")]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},["".concat(e.componentCls,"-copy-icon-only")]:{marginInlineStart:0}}),N=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),z=e=>{let{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,["&".concat(t,"-secondary")]:{color:e.colorTextDescription},["&".concat(t,"-success")]:{color:e.colorSuccess},["&".concat(t,"-warning")]:{color:e.colorWarning},["&".concat(t,"-danger")]:{color:e.colorError,"a&:active, a&:focus":{color:e.colorErrorActive},"a&:hover":{color:e.colorErrorHover}},["&".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},T(e)),{["\n      & + h1".concat(t,",\n      & + h2").concat(t,",\n      & + h3").concat(t,",\n      & + h4").concat(t,",\n      & + h5").concat(t,"\n      ")]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),M(e)),P(e)),{["\n        ".concat(t,"-expand,\n        ").concat(t,"-collapse,\n        ").concat(t,"-edit,\n        ").concat(t,"-copy\n      ")]:Object.assign(Object.assign({},(0,S.Y1)(e)),{marginInlineStart:e.marginXXS})}),H(e)),I(e)),N()),{"&-rtl":{direction:"rtl"}})}},D=(0,A.OF)("Typography",e=>[z(e)],()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"})),B=e=>{let{prefixCls:t,"aria-label":n,className:o,style:a,direction:i,maxLength:l,autoSize:c=!0,value:u,onSave:f,onCancel:d,onEnd:p,component:m,enterIcon:v=r.createElement(E,null)}=e,g=r.useRef(null),y=r.useRef(!1),h=r.useRef(null),[b,O]=r.useState(u);r.useEffect(()=>{O(u)},[u]),r.useEffect(()=>{var e;if(null===(e=g.current)||void 0===e?void 0:e.resizableTextArea){let{textArea:e}=g.current.resizableTextArea;e.focus();let{length:t}=e.value;e.setSelectionRange(t,t)}},[]);let S=()=>{f(b.trim())},[A,C,k]=D(t),R=s()(t,"".concat(t,"-edit-content"),{["".concat(t,"-rtl")]:"rtl"===i,["".concat(t,"-").concat(m)]:!!m},o,C,k);return A(r.createElement("div",{className:R,style:a},r.createElement(j.A,{ref:g,maxLength:l,value:b,onChange:e=>{let{target:t}=e;O(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;y.current||(h.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:r,metaKey:o,shiftKey:a}=e;h.current===t&&!y.current&&!n&&!r&&!o&&!a&&(t===x.A.ENTER?(S(),null==p||p()):t===x.A.ESC&&d())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{S()},"aria-label":n,rows:1,autoSize:c}),null!==v?(0,w.Ob)(v,{className:"".concat(t,"-edit-content-confirm")}):null))};var F=n(21079),L=n.n(F),W=n(97262);let V=e=>{let{copyConfig:t,children:n}=e,[o,a]=r.useState(!1),[i,l]=r.useState(!1),c=r.useRef(null),s=()=>{c.current&&clearTimeout(c.current)},u={};t.format&&(u.format=t.format),r.useEffect(()=>s,[]);let f=(0,W.A)(e=>(function(e,t,n,r){return new(n||(n=Promise))(function(o,a){function i(e){try{c(r.next(e))}catch(e){a(e)}}function l(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(i,l)}c((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var r;null==e||e.preventDefault(),null==e||e.stopPropagation(),l(!0);try{let o="function"==typeof t.text?yield t.text():t.text;L()(o||(function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&null==e?[]:Array.isArray(e)?e:[e]})(n,!0).join("")||"",u),l(!1),a(!0),s(),c.current=setTimeout(()=>{a(!1)},3e3),null===(r=t.onCopy)||void 0===r||r.call(t,e)}catch(e){throw l(!1),e}}));return{copied:o,copyLoading:i,onClick:f}};function q(e,t){return r.useMemo(()=>{let n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]},[e])}let U=e=>{let t=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{t.current=e}),t.current},_=(e,t,n)=>(0,r.useMemo)(()=>!0===e?{title:null!=t?t:n}:(0,r.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e},[e,t,n]);var X=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let Y=r.forwardRef((e,t)=>{let{prefixCls:n,component:o="article",className:a,rootClassName:i,setContentRef:l,children:c,direction:u,style:f}=e,d=X(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:p,direction:m,className:g,style:h}=(0,y.TP)("typography"),b=l?(0,v.K4)(t,l):t,O=p("typography",n),[E,x,w]=D(O),j=s()(O,g,{["".concat(O,"-rtl")]:"rtl"===(null!=u?u:m)},a,i,x,w),S=Object.assign(Object.assign({},h),f);return E(r.createElement(o,Object.assign({className:j,style:S,ref:b},d),c))});var G=n(4768);let K={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var $=r.forwardRef(function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:K}))}),Q=n(16419);function J(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function Z(e,t,n){return!0===e||void 0===e?t:e||n&&t}let ee=e=>["string","number"].includes(typeof e),et=e=>{let{prefixCls:t,copied:n,locale:o,iconOnly:a,tooltips:i,icon:l,tabIndex:c,onCopy:u,loading:f}=e,d=J(i),p=J(l),{copied:m,copy:v}=null!=o?o:{},g=n?m:v,y=Z(d[+!!n],g),h="string"==typeof y?y:g;return r.createElement(b.A,{title:y},r.createElement("button",{type:"button",className:s()("".concat(t,"-copy"),{["".concat(t,"-copy-success")]:n,["".concat(t,"-copy-icon-only")]:a}),onClick:u,"aria-label":h,tabIndex:c},n?Z(p[1],r.createElement(G.A,null),!0):Z(p[0],f?r.createElement(Q.A,null):r.createElement($,null),!0)))};var en=n(39014);let er=r.forwardRef((e,t)=>{let{style:n,children:o}=e,a=r.useRef(null);return r.useImperativeHandle(t,()=>({isExceed:()=>{let e=a.current;return e.scrollHeight>e.clientHeight},getHeight:()=>a.current.clientHeight})),r.createElement("span",{"aria-hidden":!0,ref:a,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},n)},o)}),eo=e=>e.reduce((e,t)=>e+(ee(t)?String(t).length:1),0);function ea(e,t){let n=0,r=[];for(let o=0;o<e.length;o+=1){if(n===t)return r;let a=e[o],i=n+(ee(a)?String(a).length:1);if(i>t){let e=t-n;return r.push(String(a).slice(0,e)),r}r.push(a),n=i}return e}let ei={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function el(e){let{enableMeasure:t,width:n,text:o,children:a,rows:i,expanded:l,miscDeps:c,onEllipsis:s}=e,u=r.useMemo(()=>(0,f.A)(o),[o]),p=r.useMemo(()=>eo(u),[o]),m=r.useMemo(()=>a(u,!1),[o]),[v,g]=r.useState(null),y=r.useRef(null),h=r.useRef(null),b=r.useRef(null),O=r.useRef(null),E=r.useRef(null),[x,w]=r.useState(!1),[j,S]=r.useState(0),[A,C]=r.useState(0),[k,R]=r.useState(null);(0,d.A)(()=>{t&&n&&p?S(1):S(0)},[n,o,i,t,u]),(0,d.A)(()=>{var e,t,n,r;if(1===j)S(2),R(h.current&&getComputedStyle(h.current).whiteSpace);else if(2===j){let o=!!(null===(e=b.current)||void 0===e?void 0:e.isExceed());S(o?3:4),g(o?[0,p]:null),w(o);let a=(null===(t=b.current)||void 0===t?void 0:t.getHeight())||0;C(Math.max(a,(1===i?0:(null===(n=O.current)||void 0===n?void 0:n.getHeight())||0)+((null===(r=E.current)||void 0===r?void 0:r.getHeight())||0))+1),s(o)}},[j]);let T=v?Math.ceil((v[0]+v[1])/2):0;(0,d.A)(()=>{var e;let[t,n]=v||[0,0];if(t!==n){let r=((null===(e=y.current)||void 0===e?void 0:e.getHeight())||0)>A,o=T;n-t==1&&(o=r?t:n),g(r?[t,o]:[o,n])}},[v,T]);let P=r.useMemo(()=>{if(!t)return a(u,!1);if(3!==j||!v||v[0]!==v[1]){let e=a(u,!1);return[4,0].includes(j)?e:r.createElement("span",{style:Object.assign(Object.assign({},ei),{WebkitLineClamp:i})},e)}return a(l?u:ea(u,v[0]),x)},[l,j,v,u].concat((0,en.A)(c))),M={width:n,margin:0,padding:0,whiteSpace:"nowrap"===k?"normal":"inherit"};return r.createElement(r.Fragment,null,P,2===j&&r.createElement(r.Fragment,null,r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},M),ei),{WebkitLineClamp:i}),ref:b},m),r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},M),ei),{WebkitLineClamp:i-1}),ref:O},m),r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},M),ei),{WebkitLineClamp:1}),ref:E},a([],!0))),3===j&&v&&v[0]!==v[1]&&r.createElement(er,{style:Object.assign(Object.assign({},M),{top:400}),ref:y},a(ea(u,T),!0)),1===j&&r.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}let ec=e=>{let{enableEllipsis:t,isEllipsis:n,children:o,tooltipProps:a}=e;return(null==a?void 0:a.title)&&t?r.createElement(b.A,Object.assign({open:!!n&&void 0},a),o):o};var es=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let eu=r.forwardRef((e,t)=>{var n;let{prefixCls:o,className:a,style:i,type:c,disabled:O,children:E,ellipsis:x,editable:w,copyable:j,component:S,title:A}=e,C=es(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:k,direction:R}=r.useContext(y.QO),[T]=(0,h.A)("Text"),P=r.useRef(null),M=r.useRef(null),H=k("typography",o),I=(0,m.A)(C,["mark","code","delete","underline","strong","keyboard","italic"]),[N,z]=q(w),[D,F]=(0,p.A)(!1,{value:z.editing}),{triggerType:L=["icon"]}=z,W=e=>{var t;e&&(null===(t=z.onStart)||void 0===t||t.call(z)),F(e)},X=U(D);(0,d.A)(()=>{var e;!D&&X&&(null===(e=M.current)||void 0===e||e.focus())},[D]);let G=e=>{null==e||e.preventDefault(),W(!0)},[K,$]=q(j),{copied:Q,copyLoading:J,onClick:Z}=V({copyConfig:$,children:E}),[en,er]=r.useState(!1),[eo,ea]=r.useState(!1),[ei,eu]=r.useState(!1),[ef,ed]=r.useState(!1),[ep,em]=r.useState(!0),[ev,eg]=q(x,{expandable:!1,symbol:e=>e?null==T?void 0:T.collapse:null==T?void 0:T.expand}),[ey,eh]=(0,p.A)(eg.defaultExpanded||!1,{value:eg.expanded}),eb=ev&&(!ey||"collapsible"===eg.expandable),{rows:eO=1}=eg,eE=r.useMemo(()=>eb&&(void 0!==eg.suffix||eg.onEllipsis||eg.expandable||N||K),[eb,eg,N,K]);(0,d.A)(()=>{ev&&!eE&&(er((0,g.F)("webkitLineClamp")),ea((0,g.F)("textOverflow")))},[eE,ev]);let[ex,ew]=r.useState(eb),ej=r.useMemo(()=>!eE&&(1===eO?eo:en),[eE,eo,en]);(0,d.A)(()=>{ew(ej&&eb)},[ej,eb]);let eS=eb&&(ex?ef:ei),eA=eb&&1===eO&&ex,eC=eb&&eO>1&&ex,ek=(e,t)=>{var n;eh(t.expanded),null===(n=eg.onExpand)||void 0===n||n.call(eg,e,t)},[eR,eT]=r.useState(0),eP=e=>{var t;eu(e),ei!==e&&(null===(t=eg.onEllipsis)||void 0===t||t.call(eg,e))};r.useEffect(()=>{let e=P.current;if(ev&&ex&&e){let t=function(e){let t=document.createElement("em");e.appendChild(t);let n=e.getBoundingClientRect(),r=t.getBoundingClientRect();return e.removeChild(t),n.left>r.left||r.right>n.right||n.top>r.top||r.bottom>n.bottom}(e);ef!==t&&ed(t)}},[ev,ex,E,eC,ep,eR]),r.useEffect(()=>{let e=P.current;if("undefined"==typeof IntersectionObserver||!e||!ex||!eb)return;let t=new IntersectionObserver(()=>{em(!!e.offsetParent)});return t.observe(e),()=>{t.disconnect()}},[ex,eb]);let eM=_(eg.tooltip,z.text,E),eH=r.useMemo(()=>{if(ev&&!ex)return[z.text,E,A,eM.title].find(ee)},[ev,ex,A,eM.title,eS]);if(D)return r.createElement(B,{value:null!==(n=z.text)&&void 0!==n?n:"string"==typeof E?E:"",onSave:e=>{var t;null===(t=z.onChange)||void 0===t||t.call(z,e),W(!1)},onCancel:()=>{var e;null===(e=z.onCancel)||void 0===e||e.call(z),W(!1)},onEnd:z.onEnd,prefixCls:H,className:a,style:i,direction:R,component:S,maxLength:z.maxLength,autoSize:z.autoSize,enterIcon:z.enterIcon});let eI=()=>{let{expandable:e,symbol:t}=eg;return e?r.createElement("button",{type:"button",key:"expand",className:"".concat(H,"-").concat(ey?"collapse":"expand"),onClick:e=>ek(e,{expanded:!ey}),"aria-label":ey?T.collapse:null==T?void 0:T.expand},"function"==typeof t?t(ey):t):null},eN=()=>{if(!N)return;let{icon:e,tooltip:t,tabIndex:n}=z,o=(0,f.A)(t)[0]||(null==T?void 0:T.edit),a="string"==typeof o?o:"";return L.includes("icon")?r.createElement(b.A,{key:"edit",title:!1===t?"":o},r.createElement("button",{type:"button",ref:M,className:"".concat(H,"-edit"),onClick:G,"aria-label":a,tabIndex:n},e||r.createElement(l,{role:"button"}))):null},ez=()=>K?r.createElement(et,Object.assign({key:"copy"},$,{prefixCls:H,copied:Q,locale:T,onCopy:Z,loading:J,iconOnly:null==E})):null,eD=e=>[e&&eI(),eN(),ez()],eB=e=>[e&&!ey&&r.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),eg.suffix,eD(e)];return r.createElement(u.A,{onResize:e=>{let{offsetWidth:t}=e;eT(t)},disabled:!eb},n=>r.createElement(ec,{tooltipProps:eM,enableEllipsis:eb,isEllipsis:eS},r.createElement(Y,Object.assign({className:s()({["".concat(H,"-").concat(c)]:c,["".concat(H,"-disabled")]:O,["".concat(H,"-ellipsis")]:ev,["".concat(H,"-ellipsis-single-line")]:eA,["".concat(H,"-ellipsis-multiple-line")]:eC},a),prefixCls:o,style:Object.assign(Object.assign({},i),{WebkitLineClamp:eC?eO:void 0}),component:S,ref:(0,v.K4)(n,P,t),direction:R,onClick:L.includes("text")?G:void 0,"aria-label":null==eH?void 0:eH.toString(),title:A},I),r.createElement(el,{enableMeasure:eb&&!ex,text:E,rows:eO,width:eR,onEllipsis:eP,expanded:ey,miscDeps:[Q,ey,J,N,K,T]},(t,n)=>(function(e,t){let{mark:n,code:o,underline:a,delete:i,strong:l,keyboard:c,italic:s}=e,u=t;function f(e,t){t&&(u=r.createElement(e,{},u))}return f("strong",l),f("u",a),f("del",i),f("code",o),f("mark",n),f("kbd",c),f("i",s),u})(e,r.createElement(r.Fragment,null,t.length>0&&n&&!ey&&eH?r.createElement("span",{key:"show-content","aria-hidden":!0},t):t,eB(n)))))))});var ef=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ed=r.forwardRef((e,t)=>{var{ellipsis:n,rel:o}=e,a=ef(e,["ellipsis","rel"]);let i=Object.assign(Object.assign({},a),{rel:void 0===o&&"_blank"===a.target?"noopener noreferrer":o});return delete i.navigate,r.createElement(eu,Object.assign({},i,{ref:t,ellipsis:!!n,component:"a"}))}),ep=r.forwardRef((e,t)=>r.createElement(eu,Object.assign({ref:t},e,{component:"div"})));var em=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ev=r.forwardRef((e,t)=>{var{ellipsis:n}=e,o=em(e,["ellipsis"]);let a=r.useMemo(()=>n&&"object"==typeof n?(0,m.A)(n,["expandable","rows"]):n,[n]);return r.createElement(eu,Object.assign({ref:t},o,{ellipsis:a,component:"span"}))});var eg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ey=[1,2,3,4,5],eh=r.forwardRef((e,t)=>{let{level:n=1}=e,o=eg(e,["level"]),a=ey.includes(n)?"h".concat(n):"h1";return r.createElement(eu,Object.assign({ref:t},o,{component:a}))});Y.Text=ev,Y.Link=ed,Y.Title=eh,Y.Paragraph=ep;let eb=Y},48640:(e,t,n)=>{var r=n(70719),o=n(95255);e.exports=function(e,t,n){var a=!0,i=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(n)&&(a="leading"in n?!!n.leading:a,i="trailing"in n?!!n.trailing:i),r(e,t,{leading:a,maxWait:t,trailing:i})}},53096:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var r=n(12115),o=n(4617),a=n.n(o),i=n(55315),l=n(10815),c=n(68711),s=n(1086),u=n(56204);let f=e=>{let{componentCls:t,margin:n,marginXS:r,marginXL:o,fontSize:a,lineHeight:i}=e;return{[t]:{marginInline:r,fontSize:a,lineHeight:i,textAlign:"center",["".concat(t,"-image")]:{height:e.emptyImgHeight,marginBottom:r,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},["".concat(t,"-description")]:{color:e.colorTextDescription},["".concat(t,"-footer")]:{marginTop:n},"&-normal":{marginBlock:o,color:e.colorTextDescription,["".concat(t,"-description")]:{color:e.colorTextDescription},["".concat(t,"-image")]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:r,color:e.colorTextDescription,["".concat(t,"-image")]:{height:e.emptyImgHeightSM}}}}},d=(0,s.OF)("Empty",e=>{let{componentCls:t,controlHeightLG:n,calc:r}=e;return[f((0,u.oX)(e,{emptyImgCls:"".concat(t,"-img"),emptyImgHeight:r(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:r(n).mul(.875).equal()}))]});var p=n(31049),m=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let v=r.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,i.A)("Empty"),n=new l.Y(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return r.createElement("svg",{style:n,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},r.createElement("title",null,(null==t?void 0:t.description)||"Empty"),r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("g",{transform:"translate(24 31.67)"},r.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),r.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),r.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),r.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),r.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),r.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),r.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},r.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),r.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},null),g=r.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,i.A)("Empty"),{colorFill:n,colorFillTertiary:o,colorFillQuaternary:a,colorBgContainer:s}=e,{borderColor:u,shadowColor:f,contentColor:d}=(0,r.useMemo)(()=>({borderColor:new l.Y(n).onBackground(s).toHexString(),shadowColor:new l.Y(o).onBackground(s).toHexString(),contentColor:new l.Y(a).onBackground(s).toHexString()}),[n,o,a,s]);return r.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},r.createElement("title",null,(null==t?void 0:t.description)||"Empty"),r.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},r.createElement("ellipse",{fill:f,cx:"32",cy:"33",rx:"32",ry:"7"}),r.createElement("g",{fillRule:"nonzero",stroke:u},r.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),r.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:d}))))},null),y=e=>{let{className:t,rootClassName:n,prefixCls:o,image:l=v,description:c,children:s,imageStyle:u,style:f,classNames:y,styles:h}=e,b=m(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:O,direction:E,className:x,style:w,classNames:j,styles:S}=(0,p.TP)("empty"),A=O("empty",o),[C,k,R]=d(A),[T]=(0,i.A)("Empty"),P=void 0!==c?c:null==T?void 0:T.description,M=null;return M="string"==typeof l?r.createElement("img",{alt:"string"==typeof P?P:"empty",src:l}):l,C(r.createElement("div",Object.assign({className:a()(k,R,A,x,{["".concat(A,"-normal")]:l===g,["".concat(A,"-rtl")]:"rtl"===E},t,n,j.root,null==y?void 0:y.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},S.root),w),null==h?void 0:h.root),f)},b),r.createElement("div",{className:a()("".concat(A,"-image"),j.image,null==y?void 0:y.image),style:Object.assign(Object.assign(Object.assign({},u),S.image),null==h?void 0:h.image)},M),P&&r.createElement("div",{className:a()("".concat(A,"-description"),j.description,null==y?void 0:y.description),style:Object.assign(Object.assign({},S.description),null==h?void 0:h.description)},P),s&&r.createElement("div",{className:a()("".concat(A,"-footer"),j.footer,null==y?void 0:y.footer),style:Object.assign(Object.assign({},S.footer),null==h?void 0:h.footer)},s)))};y.PRESENTED_IMAGE_DEFAULT=v,y.PRESENTED_IMAGE_SIMPLE=g;let h=y},62673:(e,t,n)=>{var r=n(34952),o="object"==typeof self&&self&&self.Object===Object&&self,a=r||o||Function("return this")();e.exports=a},68773:(e,t,n)=>{"use strict";n.d(t,{A:()=>y});var r=n(12115),o=n(4617),a=n.n(o),i=n(63588);function l(e){return["small","middle","large"].includes(e)}function c(e){return!!e&&"number"==typeof e&&!Number.isNaN(e)}var s=n(31049),u=n(78741);let f=r.createContext({latestIndex:0}),d=f.Provider,p=e=>{let{className:t,index:n,children:o,split:a,style:i}=e,{latestIndex:l}=r.useContext(f);return null==o?null:r.createElement(r.Fragment,null,r.createElement("div",{className:t,style:i},o),n<l&&a&&r.createElement("span",{className:"".concat(t,"-split")},a))};var m=n(86257),v=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let g=r.forwardRef((e,t)=>{var n;let{getPrefixCls:o,direction:u,size:f,className:g,style:y,classNames:h,styles:b}=(0,s.TP)("space"),{size:O=null!=f?f:"small",align:E,className:x,rootClassName:w,children:j,direction:S="horizontal",prefixCls:A,split:C,style:k,wrap:R=!1,classNames:T,styles:P}=e,M=v(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[H,I]=Array.isArray(O)?O:[O,O],N=l(I),z=l(H),D=c(I),B=c(H),F=(0,i.A)(j,{keepEmpty:!0}),L=void 0===E&&"horizontal"===S?"center":E,W=o("space",A),[V,q,U]=(0,m.A)(W),_=a()(W,g,q,"".concat(W,"-").concat(S),{["".concat(W,"-rtl")]:"rtl"===u,["".concat(W,"-align-").concat(L)]:L,["".concat(W,"-gap-row-").concat(I)]:N,["".concat(W,"-gap-col-").concat(H)]:z},x,w,U),X=a()("".concat(W,"-item"),null!==(n=null==T?void 0:T.item)&&void 0!==n?n:h.item),Y=0,G=F.map((e,t)=>{var n;null!=e&&(Y=t);let o=(null==e?void 0:e.key)||"".concat(X,"-").concat(t);return r.createElement(p,{className:X,key:o,index:t,split:C,style:null!==(n=null==P?void 0:P.item)&&void 0!==n?n:b.item},e)}),K=r.useMemo(()=>({latestIndex:Y}),[Y]);if(0===F.length)return null;let $={};return R&&($.flexWrap="wrap"),!z&&B&&($.columnGap=H),!N&&D&&($.rowGap=I),V(r.createElement("div",Object.assign({ref:t,className:_,style:Object.assign(Object.assign(Object.assign({},$),y),k)},M),r.createElement(d,{value:K},G)))});g.Compact=u.Ay;let y=g},69653:(e,t,n)=>{"use strict";n.d(t,{A:()=>W});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;function a(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,a=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i}function i(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(12115));let c=function(e){return function(t,n){var r=(0,l.useRef)(!1);e(function(){return function(){r.current=!1}},[]),e(function(){if(r.current)return t();r.current=!0},n)}}(l.useEffect);var s=function(e,t){var n=t.manual,r=t.ready,o=void 0===r||r,s=t.defaultParams,u=void 0===s?[]:s,f=t.refreshDeps,d=t.refreshDepsAction,p=(0,l.useRef)(!1);return p.current=!1,c(function(){!n&&o&&(p.current=!0,e.run.apply(e,i([],a(u),!1)))},[o]),c(function(){!p.current&&(n||(p.current=!0,d?d():e.refresh()))},i([],a(void 0===f?[]:f),!1)),{onBefore:function(){if(!o)return{stopNow:!0}}}};function u(e,t){var n=(0,l.useRef)({deps:t,obj:void 0,initialized:!1}).current;return(!1===n.initialized||!function(e,t){if(e===t)return!0;for(var n=0;n<e.length;n++)if(!Object.is(e[n],t[n]))return!1;return!0}(n.deps,t))&&(n.deps=t,n.obj=e(),n.initialized=!0),n.obj}s.onInit=function(e){var t=e.ready;return{loading:!e.manual&&(void 0===t||t)}};let f=function(e){var t=(0,l.useRef)(e);return t.current=e,t},d=function(e){var t=f(e);(0,l.useEffect)(function(){return function(){t.current()}},[])};var p=new Map,m=function(e,t,n){var o=p.get(e);(null==o?void 0:o.timer)&&clearTimeout(o.timer);var a=void 0;t>-1&&(a=setTimeout(function(){p.delete(e)},t)),p.set(e,r(r({},n),{timer:a}))},v=new Map,g=function(e,t){v.set(e,t),t.then(function(t){return v.delete(e),t}).catch(function(){v.delete(e)})},y={},h=function(e,t){y[e]&&y[e].forEach(function(e){return e(t)})},b=function(e,t){return y[e]||(y[e]=[]),y[e].push(t),function(){var n=y[e].indexOf(t);y[e].splice(n,1)}};let O=function(e,t){var n=t.cacheKey,r=t.cacheTime,o=void 0===r?3e5:r,c=t.staleTime,s=void 0===c?0:c,f=t.setCache,y=t.getCache,O=(0,l.useRef)(),E=(0,l.useRef)(),x=function(e,t){f?f(t):m(e,o,t),h(e,t.data)},w=function(e,t){return(void 0===t&&(t=[]),y)?y(t):p.get(e)};return(u(function(){if(n){var t=w(n);t&&Object.hasOwnProperty.call(t,"data")&&(e.state.data=t.data,e.state.params=t.params,(-1===s||new Date().getTime()-t.time<=s)&&(e.state.loading=!1)),O.current=b(n,function(t){e.setState({data:t})})}},[]),d(function(){var e;null===(e=O.current)||void 0===e||e.call(O)}),n)?{onBefore:function(e){var t=w(n,e);return t&&Object.hasOwnProperty.call(t,"data")?-1===s||new Date().getTime()-t.time<=s?{loading:!1,data:null==t?void 0:t.data,error:void 0,returnNow:!0}:{data:null==t?void 0:t.data,error:void 0}:{}},onRequest:function(e,t){var r=v.get(n);return r&&r!==E.current||(r=e.apply(void 0,i([],a(t),!1)),E.current=r,g(n,r)),{servicePromise:r}},onSuccess:function(t,r){var o;n&&(null===(o=O.current)||void 0===o||o.call(O),x(n,{data:t,params:r,time:new Date().getTime()}),O.current=b(n,function(t){e.setState({data:t})}))},onMutate:function(t){var r;n&&(null===(r=O.current)||void 0===r||r.call(O),x(n,{data:t,params:e.state.params,time:new Date().getTime()}),O.current=b(n,function(t){e.setState({data:t})}))}}:{}};var E=n(70719),x=n.n(E);let w=function(e,t){var n=t.debounceWait,r=t.debounceLeading,o=t.debounceTrailing,c=t.debounceMaxWait,s=(0,l.useRef)(),u=(0,l.useMemo)(function(){var e={};return void 0!==r&&(e.leading=r),void 0!==o&&(e.trailing=o),void 0!==c&&(e.maxWait=c),e},[r,o,c]);return((0,l.useEffect)(function(){if(n){var t=e.runAsync.bind(e);return s.current=x()(function(e){e()},n,u),e.runAsync=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return new Promise(function(n,r){var o;null===(o=s.current)||void 0===o||o.call(s,function(){t.apply(void 0,i([],a(e),!1)).then(n).catch(r)})})},function(){var n;null===(n=s.current)||void 0===n||n.cancel(),e.runAsync=t}}},[n,u]),n)?{onCancel:function(){var e;null===(e=s.current)||void 0===e||e.cancel()}}:{}},j=function(e,t){var n=t.loadingDelay,r=t.ready,o=(0,l.useRef)();if(!n)return{};var a=function(){o.current&&clearTimeout(o.current)};return{onBefore:function(){return a(),!1!==r&&(o.current=setTimeout(function(){e.setState({loading:!0})},n)),{loading:!1}},onFinally:function(){a()},onCancel:function(){a()}}};var S=!!("undefined"!=typeof window&&window.document&&window.document.createElement);function A(){return!S||"hidden"!==document.visibilityState}var C=[];S&&window.addEventListener("visibilitychange",function(){if(A())for(var e=0;e<C.length;e++)(0,C[e])()},!1);let k=function(e,t){var n=t.pollingInterval,r=t.pollingWhenHidden,o=void 0===r||r,a=t.pollingErrorRetryCount,i=void 0===a?-1:a,s=(0,l.useRef)(),u=(0,l.useRef)(),f=(0,l.useRef)(0),d=function(){var e;s.current&&clearTimeout(s.current),null===(e=u.current)||void 0===e||e.call(u)};return(c(function(){n||d()},[n]),n)?{onBefore:function(){d()},onError:function(){f.current+=1},onSuccess:function(){f.current=0},onFinally:function(){-1===i||-1!==i&&f.current<=i?s.current=setTimeout(function(){if(o||A())e.refresh();else{var t;u.current=(t=function(){e.refresh()},C.push(t),function(){var e=C.indexOf(t);C.splice(e,1)})}},n):f.current=0},onCancel:function(){d()}}:{}};var R=[];if(S){var T=function(){if(A()&&(!S||void 0===navigator.onLine||navigator.onLine))for(var e=0;e<R.length;e++)(0,R[e])()};window.addEventListener("visibilitychange",T,!1),window.addEventListener("focus",T,!1)}let P=function(e,t){var n=t.refreshOnWindowFocus,r=t.focusTimespan,o=void 0===r?5e3:r,c=(0,l.useRef)(),s=function(){var e;null===(e=c.current)||void 0===e||e.call(c)};return(0,l.useEffect)(function(){if(n){var t,r,l,u=(t=e.refresh.bind(e),r=!1,function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r||(r=!0,t.apply(void 0,i([],a(e),!1)),setTimeout(function(){r=!1},o))});c.current=(l=function(){u()},R.push(l),function(){var e=R.indexOf(l);e>-1&&R.splice(e,1)})}return function(){s()}},[n,o]),d(function(){s()}),{}},M=function(e,t){var n=t.retryInterval,r=t.retryCount,o=(0,l.useRef)(),a=(0,l.useRef)(0),i=(0,l.useRef)(!1);return r?{onBefore:function(){i.current||(a.current=0),i.current=!1,o.current&&clearTimeout(o.current)},onSuccess:function(){a.current=0},onError:function(){if(a.current+=1,-1===r||a.current<=r){var t=null!=n?n:Math.min(1e3*Math.pow(2,a.current),3e4);o.current=setTimeout(function(){i.current=!0,e.refresh()},t)}else a.current=0},onCancel:function(){a.current=0,o.current&&clearTimeout(o.current)}}:{}};var H=n(48640),I=n.n(H);let N=function(e,t){var n=t.throttleWait,r=t.throttleLeading,o=t.throttleTrailing,c=(0,l.useRef)(),s={};return(void 0!==r&&(s.leading=r),void 0!==o&&(s.trailing=o),(0,l.useEffect)(function(){if(n){var t=e.runAsync.bind(e);return c.current=I()(function(e){e()},n,s),e.runAsync=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return new Promise(function(n,r){var o;null===(o=c.current)||void 0===o||o.call(c,function(){t.apply(void 0,i([],a(e),!1)).then(n).catch(r)})})},function(){var n;e.runAsync=t,null===(n=c.current)||void 0===n||n.cancel()}}},[n,r,o]),n)?{onCancel:function(){var e;null===(e=c.current)||void 0===e||e.cancel()}}:{}},z=function(e){var t=(0,l.useRef)(e);t.current=(0,l.useMemo)(function(){return e},[e]);var n=(0,l.useRef)();return n.current||(n.current=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t.current.apply(this,e)}),n.current},D=function(e){(0,l.useEffect)(function(){null==e||e()},[])},B=function(){var e=a((0,l.useState)({}),2)[1];return(0,l.useCallback)(function(){return e({})},[])};var F=function(){function e(e,t,n,o){void 0===o&&(o={}),this.serviceRef=e,this.options=t,this.subscribe=n,this.initState=o,this.count=0,this.state={loading:!1,params:void 0,data:void 0,error:void 0},this.state=r(r(r({},this.state),{loading:!t.manual}),o)}return e.prototype.setState=function(e){void 0===e&&(e={}),this.state=r(r({},this.state),e),this.subscribe()},e.prototype.runPluginHandler=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=this.pluginImpls.map(function(n){var r;return null===(r=n[e])||void 0===r?void 0:r.call.apply(r,i([n],a(t),!1))}).filter(Boolean);return Object.assign.apply(Object,i([{}],a(r),!1))},e.prototype.runAsync=function(){for(var e,t,n,l,c=[],s=0;s<arguments.length;s++)c[s]=arguments[s];return e=this,t=void 0,n=void 0,l=function(){var e,t,n,l,s,u,f,d,p,m,v,g,y,h,b,O,E,x,w,j,S;return function(e,t){var n,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=l(0),i.throw=l(1),i.return=l(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function l(l){return function(c){return function(l){if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&l[0]?r.return:l[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,l[1])).done)return o;switch(r=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===l[0]||2===l[0])){a=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){a.label=l[1];break}if(6===l[0]&&a.label<o[1]){a.label=o[1],o=l;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(l);break}o[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}(this,function(A){switch(A.label){case 0:if(this.count+=1,e=this.count,l=void 0!==(n=(t=this.runPluginHandler("onBefore",c)).stopNow)&&n,u=void 0!==(s=t.returnNow)&&s,f=o(t,["stopNow","returnNow"]),l)return[2,new Promise(function(){})];if(this.setState(r({loading:!0,params:c},f)),u)return[2,Promise.resolve(f.data)];null===(y=(g=this.options).onBefore)||void 0===y||y.call(g,c),A.label=1;case 1:return A.trys.push([1,3,,4]),(d=this.runPluginHandler("onRequest",this.serviceRef.current,c).servicePromise)||(d=(v=this.serviceRef).current.apply(v,i([],a(c),!1))),[4,d];case 2:if(p=A.sent(),e!==this.count)return[2,new Promise(function(){})];return this.setState({data:p,error:void 0,loading:!1}),null===(b=(h=this.options).onSuccess)||void 0===b||b.call(h,p,c),this.runPluginHandler("onSuccess",p,c),null===(E=(O=this.options).onFinally)||void 0===E||E.call(O,c,p,void 0),e===this.count&&this.runPluginHandler("onFinally",c,p,void 0),[2,p];case 3:if(m=A.sent(),e!==this.count)return[2,new Promise(function(){})];throw this.setState({error:m,loading:!1}),null===(w=(x=this.options).onError)||void 0===w||w.call(x,m,c),this.runPluginHandler("onError",m,c),null===(S=(j=this.options).onFinally)||void 0===S||S.call(j,c,void 0,m),e===this.count&&this.runPluginHandler("onFinally",c,void 0,m),m;case 4:return[2]}})},new(n||(n=Promise))(function(r,o){function a(e){try{c(l.next(e))}catch(e){o(e)}}function i(e){try{c(l.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,i)}c((l=l.apply(e,t||[])).next())})},e.prototype.run=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.runAsync.apply(this,i([],a(t),!1)).catch(function(t){e.options.onError||console.error(t)})},e.prototype.cancel=function(){this.count+=1,this.setState({loading:!1}),this.runPluginHandler("onCancel")},e.prototype.refresh=function(){this.run.apply(this,i([],a(this.state.params||[]),!1))},e.prototype.refreshAsync=function(){return this.runAsync.apply(this,i([],a(this.state.params||[]),!1))},e.prototype.mutate=function(e){var t="function"==typeof e?e(this.state.data):e;this.runPluginHandler("onMutate",t),this.setState({data:t})},e}();let L=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n=[]);var l=t.manual,c=void 0!==l&&l,s=t.ready,p=void 0===s||s,m=o(t,["manual","ready"]),v=r({manual:c,ready:p},m),g=f(e),y=B(),h=u(function(){var e=n.map(function(e){var t;return null===(t=null==e?void 0:e.onInit)||void 0===t?void 0:t.call(e,v)}).filter(Boolean);return new F(g,v,y,Object.assign.apply(Object,i([{}],a(e),!1)))},[]);return h.options=v,h.pluginImpls=n.map(function(e){return e(h,v)}),D(function(){if(!c&&p){var e=h.state.params||t.defaultParams||[];h.run.apply(h,i([],a(e),!1))}}),d(function(){h.cancel()}),{loading:h.state.loading,data:h.state.data,error:h.state.error,params:h.state.params||[],cancel:z(h.cancel.bind(h)),refresh:z(h.refresh.bind(h)),refreshAsync:z(h.refreshAsync.bind(h)),run:z(h.run.bind(h)),runAsync:z(h.runAsync.bind(h)),mutate:z(h.mutate.bind(h))}},W=function(e,t,n){return L(e,t,i(i([],a(n||[]),!1),[w,j,k,P,N,s,O,M],!1))}},70719:(e,t,n)=>{var r=n(95255),o=n(91332),a=n(32816),i=Math.max,l=Math.min;e.exports=function(e,t,n){var c,s,u,f,d,p,m=0,v=!1,g=!1,y=!0;if("function"!=typeof e)throw TypeError("Expected a function");function h(t){var n=c,r=s;return c=s=void 0,m=t,f=e.apply(r,n)}function b(e){var n=e-p,r=e-m;return void 0===p||n>=t||n<0||g&&r>=u}function O(){var e,n,r,a=o();if(b(a))return E(a);d=setTimeout(O,(e=a-p,n=a-m,r=t-e,g?l(r,u-n):r))}function E(e){return(d=void 0,y&&c)?h(e):(c=s=void 0,f)}function x(){var e,n=o(),r=b(n);if(c=arguments,s=this,p=n,r){if(void 0===d)return m=e=p,d=setTimeout(O,t),v?h(e):f;if(g)return clearTimeout(d),d=setTimeout(O,t),h(p)}return void 0===d&&(d=setTimeout(O,t)),f}return t=a(t)||0,r(n)&&(v=!!n.leading,u=(g="maxWait"in n)?i(a(n.maxWait)||0,t):u,y="trailing"in n?!!n.trailing:y),x.cancel=function(){void 0!==d&&clearTimeout(d),m=0,c=p=s=d=void 0},x.flush=function(){return void 0===d?f:E(o())},x}},77550:e=>{var t=/\s/;e.exports=function(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}},83480:(e,t,n)=>{var r=n(43600),o=n(34480);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==r(e)}},88959:(e,t,n)=>{"use strict";n.d(t,{F:()=>i});var r=n(30306),o=function(e){if((0,r.A)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some(function(e){return e in n.style})}return!1},a=function(e,t){if(!o(e))return!1;var n=document.createElement("div"),r=n.style[e];return n.style[e]=t,n.style[e]!==r};function i(e,t){return Array.isArray(e)||void 0===t?o(e):a(e,t)}},91332:(e,t,n)=>{var r=n(62673);e.exports=function(){return r.Date.now()}},95255:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},96798:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}}}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6933],{66933:(e,t,o)=>{o.d(t,{A:()=>F});var n=o(12115),c=o(88881),a=o(94937),r=o(38536),i=o(4617),l=o.n(i),s=o(97262),d=o(70527),m=o(19635),u=o(58292),g=o(31049),p=o(7926);let b=(0,n.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var v=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)0>t.indexOf(n[c])&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(o[n[c]]=e[n[c]]);return o};let h=e=>{let{prefixCls:t,className:o,dashed:a}=e,r=v(e,["prefixCls","className","dashed"]),{getPrefixCls:i}=n.useContext(g.QO),s=i("menu",t),d=l()({["".concat(s,"-item-divider-dashed")]:!!a},o);return n.createElement(c.cG,Object.assign({className:d},r))};var f=o(63588),I=o(6457);let B=e=>{var t;let{className:o,children:r,icon:i,title:s,danger:m,extra:g}=e,{prefixCls:p,firstLevel:v,direction:h,disableMenuItemTitleTooltip:B,inlineCollapsed:C}=n.useContext(b),{siderCollapsed:S}=n.useContext(a.P),x=s;void 0===s?x=v?r:"":!1===s&&(x="");let O={title:x};S||C||(O.title=null,O.open=!1);let y=(0,f.A)(r).length,z=n.createElement(c.q7,Object.assign({},(0,d.A)(e,["title","icon","danger"]),{className:l()({["".concat(p,"-item-danger")]:m,["".concat(p,"-item-only-child")]:(i?y+1:y)===1},o),title:"string"==typeof s?s:void 0}),(0,u.Ob)(i,{className:l()(n.isValidElement(i)?null===(t=i.props)||void 0===t?void 0:t.className:"","".concat(p,"-item-icon"))}),(e=>{let t=null==r?void 0:r[0],o=n.createElement("span",{className:l()("".concat(p,"-title-content"),{["".concat(p,"-title-content-with-extra")]:!!g||0===g})},r);return(!i||n.isValidElement(r)&&"span"===r.type)&&r&&e&&v&&"string"==typeof t?n.createElement("div",{className:"".concat(p,"-inline-collapsed-noicon")},t.charAt(0)):o})(C));return B||(z=n.createElement(I.A,Object.assign({},O,{placement:"rtl"===h?"left":"right",classNames:{root:"".concat(p,"-inline-collapsed-tooltip")}}),z)),z};var C=o(90948),S=o(5144),x=o(10815),O=o(70695),y=o(6187),z=o(46777),A=o(9023),j=o(1086),k=o(56204);let w=e=>{let{componentCls:t,motionDurationSlow:o,horizontalLineHeight:n,colorSplit:c,lineWidth:a,lineType:r,itemPaddingInline:i}=e;return{["".concat(t,"-horizontal")]:{lineHeight:n,border:0,borderBottom:"".concat((0,S.zA)(a)," ").concat(r," ").concat(c),boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},["".concat(t,"-item, ").concat(t,"-submenu")]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:i},["> ".concat(t,"-item:hover,\n        > ").concat(t,"-item-active,\n        > ").concat(t,"-submenu ").concat(t,"-submenu-title:hover")]:{backgroundColor:"transparent"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{transition:["border-color ".concat(o),"background ".concat(o)].join(",")},["".concat(t,"-submenu-arrow")]:{display:"none"}}}},H=e=>{let{componentCls:t,menuArrowOffset:o,calc:n}=e;return{["".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-submenu-rtl")]:{transformOrigin:"100% 0"},["".concat(t,"-rtl").concat(t,"-vertical,\n    ").concat(t,"-submenu-rtl ").concat(t,"-vertical")]:{["".concat(t,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateY(".concat((0,S.zA)(n(o).mul(-1).equal()),")")},"&::after":{transform:"rotate(45deg) translateY(".concat((0,S.zA)(o),")")}}}}},E=e=>Object.assign({},(0,O.jk)(e)),T=(e,t)=>{let{componentCls:o,itemColor:n,itemSelectedColor:c,subMenuItemSelectedColor:a,groupTitleColor:r,itemBg:i,subMenuItemBg:l,itemSelectedBg:s,activeBarHeight:d,activeBarWidth:m,activeBarBorderWidth:u,motionDurationSlow:g,motionEaseInOut:p,motionEaseOut:b,itemPaddingInline:v,motionDurationMid:h,itemHoverColor:f,lineType:I,colorSplit:B,itemDisabledColor:C,dangerItemColor:x,dangerItemHoverColor:O,dangerItemSelectedColor:y,dangerItemActiveBg:z,dangerItemSelectedBg:A,popupBg:j,itemHoverBg:k,itemActiveBg:w,menuSubMenuBg:H,horizontalItemSelectedColor:T,horizontalItemSelectedBg:N,horizontalItemBorderRadius:M,horizontalItemHoverBg:R}=e;return{["".concat(o,"-").concat(t,", ").concat(o,"-").concat(t," > ").concat(o)]:{color:n,background:i,["&".concat(o,"-root:focus-visible")]:Object.assign({},E(e)),["".concat(o,"-item")]:{"&-group-title, &-extra":{color:r}},["".concat(o,"-submenu-selected > ").concat(o,"-submenu-title")]:{color:a},["".concat(o,"-item, ").concat(o,"-submenu-title")]:{color:n,["&:not(".concat(o,"-item-disabled):focus-visible")]:Object.assign({},E(e))},["".concat(o,"-item-disabled, ").concat(o,"-submenu-disabled")]:{color:"".concat(C," !important")},["".concat(o,"-item:not(").concat(o,"-item-selected):not(").concat(o,"-submenu-selected)")]:{["&:hover, > ".concat(o,"-submenu-title:hover")]:{color:f}},["&:not(".concat(o,"-horizontal)")]:{["".concat(o,"-item:not(").concat(o,"-item-selected)")]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:w}},["".concat(o,"-submenu-title")]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:w}}},["".concat(o,"-item-danger")]:{color:x,["&".concat(o,"-item:hover")]:{["&:not(".concat(o,"-item-selected):not(").concat(o,"-submenu-selected)")]:{color:O}},["&".concat(o,"-item:active")]:{background:z}},["".concat(o,"-item a")]:{"&, &:hover":{color:"inherit"}},["".concat(o,"-item-selected")]:{color:c,["&".concat(o,"-item-danger")]:{color:y},"a, a:hover":{color:"inherit"}},["& ".concat(o,"-item-selected")]:{backgroundColor:s,["&".concat(o,"-item-danger")]:{backgroundColor:A}},["&".concat(o,"-submenu > ").concat(o)]:{backgroundColor:H},["&".concat(o,"-popup > ").concat(o)]:{backgroundColor:j},["&".concat(o,"-submenu-popup > ").concat(o)]:{backgroundColor:j},["&".concat(o,"-horizontal")]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{["> ".concat(o,"-item, > ").concat(o,"-submenu")]:{top:u,marginTop:e.calc(u).mul(-1).equal(),marginBottom:0,borderRadius:M,"&::after":{position:"absolute",insetInline:v,bottom:0,borderBottom:"".concat((0,S.zA)(d)," solid transparent"),transition:"border-color ".concat(g," ").concat(p),content:'""'},"&:hover, &-active, &-open":{background:R,"&::after":{borderBottomWidth:d,borderBottomColor:T}},"&-selected":{color:T,backgroundColor:N,"&:hover":{backgroundColor:N},"&::after":{borderBottomWidth:d,borderBottomColor:T}}}}),["&".concat(o,"-root")]:{["&".concat(o,"-inline, &").concat(o,"-vertical")]:{borderInlineEnd:"".concat((0,S.zA)(u)," ").concat(I," ").concat(B)}},["&".concat(o,"-inline")]:{["".concat(o,"-sub").concat(o,"-inline")]:{background:l},["".concat(o,"-item")]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:"".concat((0,S.zA)(m)," solid ").concat(c),transform:"scaleY(0.0001)",opacity:0,transition:["transform ".concat(h," ").concat(b),"opacity ".concat(h," ").concat(b)].join(","),content:'""'},["&".concat(o,"-item-danger")]:{"&::after":{borderInlineEndColor:y}}},["".concat(o,"-selected, ").concat(o,"-item-selected")]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:["transform ".concat(h," ").concat(p),"opacity ".concat(h," ").concat(p)].join(",")}}}}}},N=e=>{let{componentCls:t,itemHeight:o,itemMarginInline:n,padding:c,menuArrowSize:a,marginXS:r,itemMarginBlock:i,itemWidth:l,itemPaddingInline:s}=e,d=e.calc(a).add(c).add(r).equal();return{["".concat(t,"-item")]:{position:"relative",overflow:"hidden"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{height:o,lineHeight:(0,S.zA)(o),paddingInline:s,overflow:"hidden",textOverflow:"ellipsis",marginInline:n,marginBlock:i,width:l},["> ".concat(t,"-item,\n            > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{height:o,lineHeight:(0,S.zA)(o)},["".concat(t,"-item-group-list ").concat(t,"-submenu-title,\n            ").concat(t,"-submenu-title")]:{paddingInlineEnd:d}}},M=e=>{let{componentCls:t,iconCls:o,itemHeight:n,colorTextLightSolid:c,dropdownWidth:a,controlHeightLG:r,motionEaseOut:i,paddingXL:l,itemMarginInline:s,fontSizeLG:d,motionDurationFast:m,motionDurationSlow:u,paddingXS:g,boxShadowSecondary:p,collapsedWidth:b,collapsedIconSize:v}=e,h={height:n,lineHeight:(0,S.zA)(n),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({["&".concat(t,"-root")]:{boxShadow:"none"}},N(e))},["".concat(t,"-submenu-popup")]:{["".concat(t,"-vertical")]:Object.assign(Object.assign({},N(e)),{boxShadow:p})}},{["".concat(t,"-submenu-popup ").concat(t,"-vertical").concat(t,"-sub")]:{minWidth:a,maxHeight:"calc(100vh - ".concat((0,S.zA)(e.calc(r).mul(2.5).equal()),")"),padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{["".concat(t,"-inline")]:{width:"100%",["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{display:"flex",alignItems:"center",transition:["border-color ".concat(u),"background ".concat(u),"padding ".concat(m," ").concat(i)].join(","),["> ".concat(t,"-title-content")]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},["".concat(t,"-sub").concat(t,"-inline")]:{padding:0,border:0,borderRadius:0,boxShadow:"none",["& > ".concat(t,"-submenu > ").concat(t,"-submenu-title")]:h,["& ".concat(t,"-item-group-title")]:{paddingInlineStart:l}},["".concat(t,"-item")]:h}},{["".concat(t,"-inline-collapsed")]:{width:b,["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu ").concat(t,"-submenu-title")]:{["> ".concat(t,"-inline-collapsed-noicon")]:{fontSize:d,textAlign:"center"}}},["> ".concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-submenu > ").concat(t,"-submenu-title,\n          > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{insetInlineStart:0,paddingInline:"calc(50% - ".concat((0,S.zA)(e.calc(v).div(2).equal())," - ").concat((0,S.zA)(s),")"),textOverflow:"clip",["\n            ".concat(t,"-submenu-arrow,\n            ").concat(t,"-submenu-expand-icon\n          ")]:{opacity:0},["".concat(t,"-item-icon, ").concat(o)]:{margin:0,fontSize:v,lineHeight:(0,S.zA)(n),"+ span":{display:"inline-block",opacity:0}}},["".concat(t,"-item-icon, ").concat(o)]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",["".concat(t,"-item-icon, ").concat(o)]:{display:"none"},"a, a:hover":{color:c}},["".concat(t,"-item-group-title")]:Object.assign(Object.assign({},O.L9),{paddingInline:g})}}]},R=e=>{let{componentCls:t,motionDurationSlow:o,motionDurationMid:n,motionEaseInOut:c,motionEaseOut:a,iconCls:r,iconSize:i,iconMarginInlineEnd:l}=e;return{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:["border-color ".concat(o),"background ".concat(o),"padding calc(".concat(o," + 0.1s) ").concat(c)].join(","),["".concat(t,"-item-icon, ").concat(r)]:{minWidth:i,fontSize:i,transition:["font-size ".concat(n," ").concat(a),"margin ".concat(o," ").concat(c),"color ".concat(o)].join(","),"+ span":{marginInlineStart:l,opacity:1,transition:["opacity ".concat(o," ").concat(c),"margin ".concat(o),"color ".concat(o)].join(",")}},["".concat(t,"-item-icon")]:Object.assign({},(0,O.Nk)()),["&".concat(t,"-item-only-child")]:{["> ".concat(r,", > ").concat(t,"-item-icon")]:{marginInlineEnd:0}}},["".concat(t,"-item-disabled, ").concat(t,"-submenu-disabled")]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},["> ".concat(t,"-submenu-title")]:{color:"inherit !important",cursor:"not-allowed"}}}},D=e=>{let{componentCls:t,motionDurationSlow:o,motionEaseInOut:n,borderRadius:c,menuArrowSize:a,menuArrowOffset:r}=e;return{["".concat(t,"-submenu")]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:a,color:"currentcolor",transform:"translateY(-50%)",transition:"transform ".concat(o," ").concat(n,", opacity ").concat(o)},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(a).mul(.6).equal(),height:e.calc(a).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:c,transition:["background ".concat(o," ").concat(n),"transform ".concat(o," ").concat(n),"top ".concat(o," ").concat(n),"color ".concat(o," ").concat(n)].join(","),content:'""'},"&::before":{transform:"rotate(45deg) translateY(".concat((0,S.zA)(e.calc(r).mul(-1).equal()),")")},"&::after":{transform:"rotate(-45deg) translateY(".concat((0,S.zA)(r),")")}}}}},P=e=>{let{antCls:t,componentCls:o,fontSize:n,motionDurationSlow:c,motionDurationMid:a,motionEaseInOut:r,paddingXS:i,padding:l,colorSplit:s,lineWidth:d,zIndexPopup:m,borderRadiusLG:u,subMenuItemBorderRadius:g,menuArrowSize:p,menuArrowOffset:b,lineType:v,groupTitleLineHeight:h,groupTitleFontSize:f}=e;return[{"":{[o]:Object.assign(Object.assign({},(0,O.t6)()),{"&-hidden":{display:"none"}})},["".concat(o,"-submenu-hidden")]:{display:"none"}},{[o]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,O.dF)(e)),(0,O.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:n,lineHeight:0,listStyle:"none",outline:"none",transition:"width ".concat(c," cubic-bezier(0.2, 0, 0, 1) 0s"),"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",["".concat(o,"-item")]:{flex:"none"}},["".concat(o,"-item, ").concat(o,"-submenu, ").concat(o,"-submenu-title")]:{borderRadius:e.itemBorderRadius},["".concat(o,"-item-group-title")]:{padding:"".concat((0,S.zA)(i)," ").concat((0,S.zA)(l)),fontSize:f,lineHeight:h,transition:"all ".concat(c)},["&-horizontal ".concat(o,"-submenu")]:{transition:["border-color ".concat(c," ").concat(r),"background ".concat(c," ").concat(r)].join(",")},["".concat(o,"-submenu, ").concat(o,"-submenu-inline")]:{transition:["border-color ".concat(c," ").concat(r),"background ".concat(c," ").concat(r),"padding ".concat(a," ").concat(r)].join(",")},["".concat(o,"-submenu ").concat(o,"-sub")]:{cursor:"initial",transition:["background ".concat(c," ").concat(r),"padding ".concat(c," ").concat(r)].join(",")},["".concat(o,"-title-content")]:{transition:"color ".concat(c),"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},["> ".concat(t,"-typography-ellipsis-single-line")]:{display:"inline",verticalAlign:"unset"},["".concat(o,"-item-extra")]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},["".concat(o,"-item a")]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},["".concat(o,"-item-divider")]:{overflow:"hidden",lineHeight:0,borderColor:s,borderStyle:v,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),R(e)),{["".concat(o,"-item-group")]:{["".concat(o,"-item-group-list")]:{margin:0,padding:0,["".concat(o,"-item, ").concat(o,"-submenu-title")]:{paddingInline:"".concat((0,S.zA)(e.calc(n).mul(2).equal())," ").concat((0,S.zA)(l))}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:m,borderRadius:u,boxShadow:"none",transformOrigin:"0 0",["&".concat(o,"-submenu")]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},["> ".concat(o)]:Object.assign(Object.assign(Object.assign({borderRadius:u},R(e)),D(e)),{["".concat(o,"-item, ").concat(o,"-submenu > ").concat(o,"-submenu-title")]:{borderRadius:g},["".concat(o,"-submenu-title::after")]:{transition:"transform ".concat(c," ").concat(r)}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),D(e)),{["&-inline-collapsed ".concat(o,"-submenu-arrow,\n        &-inline ").concat(o,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateX(".concat((0,S.zA)(b),")")},"&::after":{transform:"rotate(45deg) translateX(".concat((0,S.zA)(e.calc(b).mul(-1).equal()),")")}},["".concat(o,"-submenu-open").concat(o,"-submenu-inline > ").concat(o,"-submenu-title > ").concat(o,"-submenu-arrow")]:{transform:"translateY(".concat((0,S.zA)(e.calc(p).mul(.2).mul(-1).equal()),")"),"&::after":{transform:"rotate(-45deg) translateX(".concat((0,S.zA)(e.calc(b).mul(-1).equal()),")")},"&::before":{transform:"rotate(45deg) translateX(".concat((0,S.zA)(b),")")}}})},{["".concat(t,"-layout-header")]:{[o]:{lineHeight:"inherit"}}}]},W=e=>{var t,o,n;let{colorPrimary:c,colorError:a,colorTextDisabled:r,colorErrorBg:i,colorText:l,colorTextDescription:s,colorBgContainer:d,colorFillAlter:m,colorFillContent:u,lineWidth:g,lineWidthBold:p,controlItemBgActive:b,colorBgTextHover:v,controlHeightLG:h,lineHeight:f,colorBgElevated:I,marginXXS:B,padding:C,fontSize:S,controlHeightSM:O,fontSizeLG:y,colorTextLightSolid:z,colorErrorHover:A}=e,j=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,k=null!==(o=e.activeBarBorderWidth)&&void 0!==o?o:g,w=null!==(n=e.itemMarginInline)&&void 0!==n?n:e.marginXXS,H=new x.Y(z).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:l,itemColor:l,colorItemTextHover:l,itemHoverColor:l,colorItemTextHoverHorizontal:c,horizontalItemHoverColor:c,colorGroupTitle:s,groupTitleColor:s,colorItemTextSelected:c,itemSelectedColor:c,subMenuItemSelectedColor:c,colorItemTextSelectedHorizontal:c,horizontalItemSelectedColor:c,colorItemBg:d,itemBg:d,colorItemBgHover:v,itemHoverBg:v,colorItemBgActive:u,itemActiveBg:b,colorSubItemBg:m,subMenuItemBg:m,colorItemBgSelected:b,itemSelectedBg:b,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:j,colorActiveBarHeight:p,activeBarHeight:p,colorActiveBarBorderSize:g,activeBarBorderWidth:k,colorItemTextDisabled:r,itemDisabledColor:r,colorDangerItemText:a,dangerItemColor:a,colorDangerItemTextHover:a,dangerItemHoverColor:a,colorDangerItemTextSelected:a,dangerItemSelectedColor:a,colorDangerItemBgActive:i,dangerItemActiveBg:i,colorDangerItemBgSelected:i,dangerItemSelectedBg:i,itemMarginInline:w,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:h,groupTitleLineHeight:f,collapsedWidth:2*h,popupBg:I,itemMarginBlock:B,itemPaddingInline:C,horizontalLineHeight:"".concat(1.15*h,"px"),iconSize:S,iconMarginInlineEnd:O-S,collapsedIconSize:y,groupTitleFontSize:S,darkItemDisabledColor:new x.Y(z).setA(.25).toRgbString(),darkItemColor:H,darkDangerItemColor:a,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:z,darkItemSelectedBg:c,darkDangerItemSelectedBg:a,darkItemHoverBg:"transparent",darkGroupTitleColor:H,darkItemHoverColor:z,darkDangerItemHoverColor:A,darkDangerItemSelectedColor:z,darkDangerItemActiveBg:a,itemWidth:j?"calc(100% + ".concat(k,"px)"):"calc(100% - ".concat(2*w,"px)")}};var q=o(78877);let L=e=>{var t;let o;let{popupClassName:a,icon:r,title:i,theme:s}=e,m=n.useContext(b),{prefixCls:g,inlineCollapsed:p,theme:v}=m,h=(0,c.Wj)();if(r){let e=n.isValidElement(i)&&"span"===i.type;o=n.createElement(n.Fragment,null,(0,u.Ob)(r,{className:l()(n.isValidElement(r)?null===(t=r.props)||void 0===t?void 0:t.className:"","".concat(g,"-item-icon"))}),e?i:n.createElement("span",{className:"".concat(g,"-title-content")},i))}else o=p&&!h.length&&i&&"string"==typeof i?n.createElement("div",{className:"".concat(g,"-inline-collapsed-noicon")},i.charAt(0)):n.createElement("span",{className:"".concat(g,"-title-content")},i);let f=n.useMemo(()=>Object.assign(Object.assign({},m),{firstLevel:!1}),[m]),[I]=(0,q.YK)("Menu");return n.createElement(b.Provider,{value:f},n.createElement(c.g8,Object.assign({},(0,d.A)(e,["icon"]),{title:o,popupClassName:l()(g,a,"".concat(g,"-").concat(s||v)),popupStyle:Object.assign({zIndex:I},e.popupStyle)})))};var X=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)0>t.indexOf(n[c])&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(o[n[c]]=e[n[c]]);return o};function Y(e){return null===e||!1===e}let _={item:B,submenu:L,divider:h},G=(0,n.forwardRef)((e,t)=>{var o;let a=n.useContext(C.h),i=a||{},{getPrefixCls:v,getPopupContainer:h,direction:f,menu:I}=n.useContext(g.QO),B=v(),{prefixCls:S,className:x,style:O,theme:E="light",expandIcon:N,_internalDisableMenuItemTitleTooltip:R,inlineCollapsed:D,siderCollapsed:q,rootClassName:L,mode:G,selectable:V,onClick:F,overflowedIndicatorPopupClassName:K}=e,Q=X(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),J=(0,d.A)(Q,["collapsedWidth"]);null===(o=i.validator)||void 0===o||o.call(i,{mode:G});let U=(0,s.A)(function(){var e;null==F||F.apply(void 0,arguments),null===(e=i.onClick)||void 0===e||e.call(i)}),Z=i.mode||G,$=null!=V?V:i.selectable,ee=null!=D?D:q,et={horizontal:{motionName:"".concat(B,"-slide-up")},inline:(0,m.A)(B),other:{motionName:"".concat(B,"-zoom-big")}},eo=v("menu",S||i.prefixCls),en=(0,p.A)(eo),[ec,ea,er]=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(0,j.OF)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:o,fontSize:n,darkItemColor:c,darkDangerItemColor:a,darkItemBg:r,darkSubMenuItemBg:i,darkItemSelectedColor:l,darkItemSelectedBg:s,darkDangerItemSelectedBg:d,darkItemHoverBg:m,darkGroupTitleColor:u,darkItemHoverColor:g,darkItemDisabledColor:p,darkDangerItemHoverColor:b,darkDangerItemSelectedColor:v,darkDangerItemActiveBg:h,popupBg:f,darkPopupBg:I}=e,B=e.calc(n).div(7).mul(5).equal(),C=(0,k.oX)(e,{menuArrowSize:B,menuHorizontalHeight:e.calc(o).mul(1.15).equal(),menuArrowOffset:e.calc(B).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:f}),S=(0,k.oX)(C,{itemColor:c,itemHoverColor:g,groupTitleColor:u,itemSelectedColor:l,subMenuItemSelectedColor:l,itemBg:r,popupBg:I,subMenuItemBg:i,itemActiveBg:"transparent",itemSelectedBg:s,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:m,itemDisabledColor:p,dangerItemColor:a,dangerItemHoverColor:b,dangerItemSelectedColor:v,dangerItemActiveBg:h,dangerItemSelectedBg:d,menuSubMenuBg:i,horizontalItemSelectedColor:l,horizontalItemSelectedBg:s});return[P(C),w(C),M(C),T(C,"light"),T(S,"dark"),H(C),(0,y.A)(C),(0,z._j)(C,"slide-up"),(0,z._j)(C,"slide-down"),(0,A.aB)(C,"zoom-big")]},W,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:o,unitless:{groupTitleLineHeight:!0}})(e,t)}(eo,en,!a),ei=l()("".concat(eo,"-").concat(E),null==I?void 0:I.className,x),el=n.useMemo(()=>{var e,t;if("function"==typeof N||Y(N))return N||null;if("function"==typeof i.expandIcon||Y(i.expandIcon))return i.expandIcon||null;if("function"==typeof(null==I?void 0:I.expandIcon)||Y(null==I?void 0:I.expandIcon))return(null==I?void 0:I.expandIcon)||null;let o=null!==(e=null!=N?N:null==i?void 0:i.expandIcon)&&void 0!==e?e:null==I?void 0:I.expandIcon;return(0,u.Ob)(o,{className:l()("".concat(eo,"-submenu-expand-icon"),n.isValidElement(o)?null===(t=o.props)||void 0===t?void 0:t.className:void 0)})},[N,null==i?void 0:i.expandIcon,null==I?void 0:I.expandIcon,eo]),es=n.useMemo(()=>({prefixCls:eo,inlineCollapsed:ee||!1,direction:f,firstLevel:!0,theme:E,mode:Z,disableMenuItemTitleTooltip:R}),[eo,ee,f,R,E]);return ec(n.createElement(C.h.Provider,{value:null},n.createElement(b.Provider,{value:es},n.createElement(c.Ay,Object.assign({getPopupContainer:h,overflowedIndicator:n.createElement(r.A,null),overflowedIndicatorPopupClassName:l()(eo,"".concat(eo,"-").concat(E),K),mode:Z,selectable:$,onClick:U},J,{inlineCollapsed:ee,style:Object.assign(Object.assign({},null==I?void 0:I.style),O),className:ei,prefixCls:eo,direction:f,defaultMotions:et,expandIcon:el,ref:t,rootClassName:l()(L,ea,i.rootClassName,er,en),_internalComponents:_})))))}),V=(0,n.forwardRef)((e,t)=>{let o=(0,n.useRef)(null),c=n.useContext(a.P);return(0,n.useImperativeHandle)(t,()=>({menu:o.current,focus:e=>{var t;null===(t=o.current)||void 0===t||t.focus(e)}})),n.createElement(G,Object.assign({ref:o},e,c))});V.Item=B,V.SubMenu=L,V.Divider=h,V.ItemGroup=c.te;let F=V},90948:(e,t,o)=>{o.d(t,{A:()=>l,h:()=>s});var n=o(12115),c=o(15231),a=o(34487),r=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)0>t.indexOf(n[c])&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(o[n[c]]=e[n[c]]);return o};let i=n.createContext(null),l=n.forwardRef((e,t)=>{let{children:o}=e,l=r(e,["children"]),s=n.useContext(i),d=n.useMemo(()=>Object.assign(Object.assign({},s),l),[s,l.prefixCls,l.mode,l.selectable,l.rootClassName]),m=(0,c.H3)(o),u=(0,c.xK)(t,m?(0,c.A9)(o):null);return n.createElement(i.Provider,{value:d},n.createElement(a.A,{space:!0},m?n.cloneElement(o,{ref:u}):o))}),s=i}}]);
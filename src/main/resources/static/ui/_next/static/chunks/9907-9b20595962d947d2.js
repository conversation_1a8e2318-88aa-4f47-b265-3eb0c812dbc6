"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9907],{99907:(t,e,n)=>{n.d(e,{A:()=>tx});var a=n(12115),o=n(79624),c=n(38536),r=n(96030),i=n(4617),l=n.n(i),d=n(85407),s=n(1568),u=n(85268),v=n(59912),f=n(21855),p=n(64406),b=n(35015),m=n(8324);let h=(0,a.createContext)(null);var g=n(39014),k=n(30377),y=n(97262),A=n(15231),w=n(13379);let x=function(t){var e=t.activeTabOffset,n=t.horizontal,o=t.rtl,c=t.indicator,r=void 0===c?{}:c,i=r.size,l=r.align,d=void 0===l?"center":l,s=(0,a.useState)(),u=(0,v.A)(s,2),f=u[0],p=u[1],b=(0,a.useRef)(),m=a.useCallback(function(t){return"function"==typeof i?i(t):"number"==typeof i?i:t},[i]);function h(){w.A.cancel(b.current)}return(0,a.useEffect)(function(){var t={};if(e){if(n){t.width=m(e.width);var a=o?"right":"left";"start"===d&&(t[a]=e[a]),"center"===d&&(t[a]=e[a]+e.width/2,t.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===d&&(t[a]=e[a]+e.width,t.transform="translateX(-100%)")}else t.height=m(e.height),"start"===d&&(t.top=e.top),"center"===d&&(t.top=e.top+e.height/2,t.transform="translateY(-50%)"),"end"===d&&(t.top=e.top+e.height,t.transform="translateY(-100%)")}return h(),b.current=(0,w.A)(function(){p(t)}),h},[e,n,o,d,m]),{style:f}};var _={width:0,height:0,left:0,top:0};function S(t,e){var n=a.useRef(t),o=a.useState({}),c=(0,v.A)(o,2)[1];return[n.current,function(t){var a="function"==typeof t?t(n.current):t;a!==n.current&&e(a,n.current),n.current=a,c({})}]}var E=n(66105);function C(t){var e=(0,a.useState)(0),n=(0,v.A)(e,2),o=n[0],c=n[1],r=(0,a.useRef)(0),i=(0,a.useRef)();return i.current=t,(0,E.o)(function(){var t;null===(t=i.current)||void 0===t||t.call(i)},[o]),function(){r.current===o&&(r.current+=1,c(r.current))}}var z={width:0,height:0,left:0,top:0,right:0};function R(t){var e;return t instanceof Map?(e={},t.forEach(function(t,n){e[n]=t})):e=t,JSON.stringify(e)}function P(t){return String(t).replace(/"/g,"TABS_DQ")}function T(t,e,n,a){return!!n&&!a&&!1!==t&&(void 0!==t||!1!==e&&null!==e)}var I=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.editable,c=t.locale,r=t.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:e,type:"button",className:"".concat(n,"-nav-add"),style:r,"aria-label":(null==c?void 0:c.addAriaLabel)||"Add tab",onClick:function(t){o.onEdit("add",{event:t})}},o.addIcon||"+"):null}),M=a.forwardRef(function(t,e){var n,o=t.position,c=t.prefixCls,r=t.extra;if(!r)return null;var i={};return"object"!==(0,f.A)(r)||a.isValidElement(r)?i.right=r:i=r,"right"===o&&(n=i.right),"left"===o&&(n=i.left),n?a.createElement("div",{className:"".concat(c,"-extra-content"),ref:e},n):null}),L=n(41763),O=n(88881),D=n(23672),B=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.id,c=t.tabs,r=t.locale,i=t.mobile,u=t.more,f=void 0===u?{}:u,p=t.style,b=t.className,m=t.editable,h=t.tabBarGutter,g=t.rtl,k=t.removeAriaLabel,y=t.onTabClick,A=t.getPopupContainer,w=t.popupClassName,x=(0,a.useState)(!1),_=(0,v.A)(x,2),S=_[0],E=_[1],C=(0,a.useState)(null),z=(0,v.A)(C,2),R=z[0],P=z[1],M=f.icon,B="".concat(o,"-more-popup"),N="".concat(n,"-dropdown"),j=null!==R?"".concat(B,"-").concat(R):null,G=null==r?void 0:r.dropdownAriaLabel,X=a.createElement(O.Ay,{onClick:function(t){y(t.key,t.domEvent),E(!1)},prefixCls:"".concat(N,"-menu"),id:B,tabIndex:-1,role:"listbox","aria-activedescendant":j,selectedKeys:[R],"aria-label":void 0!==G?G:"expanded dropdown"},c.map(function(t){var e=t.closable,n=t.disabled,c=t.closeIcon,r=t.key,i=t.label,l=T(e,c,m,n);return a.createElement(O.Dr,{key:r,id:"".concat(B,"-").concat(r),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(r),disabled:n},a.createElement("span",null,i),l&&a.createElement("button",{type:"button","aria-label":k||"remove",tabIndex:0,className:"".concat(N,"-menu-item-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:r,event:t})}},c||m.removeIcon||"\xd7"))}));function W(t){for(var e=c.filter(function(t){return!t.disabled}),n=e.findIndex(function(t){return t.key===R})||0,a=e.length,o=0;o<a;o+=1){var r=e[n=(n+t+a)%a];if(!r.disabled){P(r.key);return}}}(0,a.useEffect)(function(){var t=document.getElementById(j);t&&t.scrollIntoView&&t.scrollIntoView(!1)},[R]),(0,a.useEffect)(function(){S||P(null)},[S]);var H=(0,s.A)({},g?"marginRight":"marginLeft",h);c.length||(H.visibility="hidden",H.order=1);var K=l()((0,s.A)({},"".concat(N,"-rtl"),g)),F=i?null:a.createElement(L.A,(0,d.A)({prefixCls:N,overlay:X,visible:!!c.length&&S,onVisibleChange:E,overlayClassName:l()(K,w),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:A},f),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:H,"aria-haspopup":"listbox","aria-controls":B,id:"".concat(o,"-more"),"aria-expanded":S,onKeyDown:function(t){var e=t.which;if(!S){[D.A.DOWN,D.A.SPACE,D.A.ENTER].includes(e)&&(E(!0),t.preventDefault());return}switch(e){case D.A.UP:W(-1),t.preventDefault();break;case D.A.DOWN:W(1),t.preventDefault();break;case D.A.ESC:E(!1);break;case D.A.SPACE:case D.A.ENTER:null!==R&&y(R,t)}}},void 0===M?"More":M));return a.createElement("div",{className:l()("".concat(n,"-nav-operations"),b),style:p,ref:e},F,a.createElement(I,{prefixCls:n,locale:r,editable:m}))});let N=a.memo(B,function(t,e){return e.tabMoving}),j=function(t){var e=t.prefixCls,n=t.id,o=t.active,c=t.focus,r=t.tab,i=r.key,d=r.label,u=r.disabled,v=r.closeIcon,f=r.icon,p=t.closable,b=t.renderWrapper,m=t.removeAriaLabel,h=t.editable,g=t.onClick,k=t.onFocus,y=t.onBlur,A=t.onKeyDown,w=t.onMouseDown,x=t.onMouseUp,_=t.style,S=t.tabCount,E=t.currentPosition,C="".concat(e,"-tab"),z=T(p,v,h,u);function R(t){!u&&g(t)}var I=a.useMemo(function(){return f&&"string"==typeof d?a.createElement("span",null,d):d},[d,f]),M=a.useRef(null);a.useEffect(function(){c&&M.current&&M.current.focus()},[c]);var L=a.createElement("div",{key:i,"data-node-key":P(i),className:l()(C,(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(C,"-with-remove"),z),"".concat(C,"-active"),o),"".concat(C,"-disabled"),u),"".concat(C,"-focus"),c)),style:_,onClick:R},a.createElement("div",{ref:M,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(i),className:"".concat(C,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(i),"aria-disabled":u,tabIndex:u?null:o?0:-1,onClick:function(t){t.stopPropagation(),R(t)},onKeyDown:A,onMouseDown:w,onMouseUp:x,onFocus:k,onBlur:y},c&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(E," of ").concat(S)),f&&a.createElement("span",{className:"".concat(C,"-icon")},f),d&&I),z&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(C,"-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),h.onEdit("remove",{key:i,event:t})}},v||h.removeIcon||"\xd7"));return b?b(L):L};var G=function(t,e){var n=t.offsetWidth,a=t.offsetHeight,o=t.offsetTop,c=t.offsetLeft,r=t.getBoundingClientRect(),i=r.width,l=r.height,d=r.left,s=r.top;return 1>Math.abs(i-n)?[i,l,d-e.left,s-e.top]:[n,a,c,o]},X=function(t){var e=t.current||{},n=e.offsetWidth,a=void 0===n?0:n,o=e.offsetHeight;if(t.current){var c=t.current.getBoundingClientRect(),r=c.width,i=c.height;if(1>Math.abs(r-a))return[r,i]}return[a,void 0===o?0:o]},W=function(t,e){return t[+!e]},H=a.forwardRef(function(t,e){var n,o,c,r,i,f,p,b,m,w,E,L,O,D,B,H,K,F,q,V,Y,U,Q,J,Z,$,tt,te,tn,ta,to,tc,tr,ti,tl,td,ts,tu,tv,tf=t.className,tp=t.style,tb=t.id,tm=t.animated,th=t.activeKey,tg=t.rtl,tk=t.extra,ty=t.editable,tA=t.locale,tw=t.tabPosition,tx=t.tabBarGutter,t_=t.children,tS=t.onTabClick,tE=t.onTabScroll,tC=t.indicator,tz=a.useContext(h),tR=tz.prefixCls,tP=tz.tabs,tT=(0,a.useRef)(null),tI=(0,a.useRef)(null),tM=(0,a.useRef)(null),tL=(0,a.useRef)(null),tO=(0,a.useRef)(null),tD=(0,a.useRef)(null),tB=(0,a.useRef)(null),tN="top"===tw||"bottom"===tw,tj=S(0,function(t,e){tN&&tE&&tE({direction:t>e?"left":"right"})}),tG=(0,v.A)(tj,2),tX=tG[0],tW=tG[1],tH=S(0,function(t,e){!tN&&tE&&tE({direction:t>e?"top":"bottom"})}),tK=(0,v.A)(tH,2),tF=tK[0],tq=tK[1],tV=(0,a.useState)([0,0]),tY=(0,v.A)(tV,2),tU=tY[0],tQ=tY[1],tJ=(0,a.useState)([0,0]),tZ=(0,v.A)(tJ,2),t$=tZ[0],t0=tZ[1],t1=(0,a.useState)([0,0]),t2=(0,v.A)(t1,2),t5=t2[0],t9=t2[1],t6=(0,a.useState)([0,0]),t7=(0,v.A)(t6,2),t3=t7[0],t4=t7[1],t8=(n=new Map,o=(0,a.useRef)([]),c=(0,a.useState)({}),r=(0,v.A)(c,2)[1],i=(0,a.useRef)("function"==typeof n?n():n),f=C(function(){var t=i.current;o.current.forEach(function(e){t=e(t)}),o.current=[],i.current=t,r({})}),[i.current,function(t){o.current.push(t),f()}]),et=(0,v.A)(t8,2),ee=et[0],en=et[1],ea=(p=t$[0],(0,a.useMemo)(function(){for(var t=new Map,e=ee.get(null===(o=tP[0])||void 0===o?void 0:o.key)||_,n=e.left+e.width,a=0;a<tP.length;a+=1){var o,c,r=tP[a].key,i=ee.get(r);i||(i=ee.get(null===(c=tP[a-1])||void 0===c?void 0:c.key)||_);var l=t.get(r)||(0,u.A)({},i);l.right=n-l.left-l.width,t.set(r,l)}return t},[tP.map(function(t){return t.key}).join("_"),ee,p])),eo=W(tU,tN),ec=W(t$,tN),er=W(t5,tN),ei=W(t3,tN),el=Math.floor(eo)<Math.floor(ec+er),ed=el?eo-ei:eo-er,es="".concat(tR,"-nav-operations-hidden"),eu=0,ev=0;function ef(t){return t<eu?eu:t>ev?ev:t}tN&&tg?(eu=0,ev=Math.max(0,ec-ed)):(eu=Math.min(0,ed-ec),ev=0);var ep=(0,a.useRef)(null),eb=(0,a.useState)(),em=(0,v.A)(eb,2),eh=em[0],eg=em[1];function ek(){eg(Date.now())}function ey(){ep.current&&clearTimeout(ep.current)}b=function(t,e){function n(t,e){t(function(t){return ef(t+e)})}return!!el&&(tN?n(tW,t):n(tq,e),ey(),ek(),!0)},m=(0,a.useState)(),E=(w=(0,v.A)(m,2))[0],L=w[1],O=(0,a.useState)(0),B=(D=(0,v.A)(O,2))[0],H=D[1],K=(0,a.useState)(0),q=(F=(0,v.A)(K,2))[0],V=F[1],Y=(0,a.useState)(),Q=(U=(0,v.A)(Y,2))[0],J=U[1],Z=(0,a.useRef)(),$=(0,a.useRef)(),(tt=(0,a.useRef)(null)).current={onTouchStart:function(t){var e=t.touches[0];L({x:e.screenX,y:e.screenY}),window.clearInterval(Z.current)},onTouchMove:function(t){if(E){var e=t.touches[0],n=e.screenX,a=e.screenY;L({x:n,y:a});var o=n-E.x,c=a-E.y;b(o,c);var r=Date.now();H(r),V(r-B),J({x:o,y:c})}},onTouchEnd:function(){if(E&&(L(null),J(null),Q)){var t=Q.x/q,e=Q.y/q;if(!(.1>Math.max(Math.abs(t),Math.abs(e)))){var n=t,a=e;Z.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(a)){window.clearInterval(Z.current);return}n*=.9046104802746175,a*=.9046104802746175,b(20*n,20*a)},20)}}},onWheel:function(t){var e=t.deltaX,n=t.deltaY,a=0,o=Math.abs(e),c=Math.abs(n);o===c?a="x"===$.current?e:n:o>c?(a=e,$.current="x"):(a=n,$.current="y"),b(-a,-a)&&t.preventDefault()}},a.useEffect(function(){function t(t){tt.current.onTouchMove(t)}function e(t){tt.current.onTouchEnd(t)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",e,{passive:!0}),tL.current.addEventListener("touchstart",function(t){tt.current.onTouchStart(t)},{passive:!0}),tL.current.addEventListener("wheel",function(t){tt.current.onWheel(t)},{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",e)}},[]),(0,a.useEffect)(function(){return ey(),eh&&(ep.current=setTimeout(function(){eg(0)},100)),ey},[eh]);var eA=(te=tN?tX:tF,tr=(tn=(0,u.A)((0,u.A)({},t),{},{tabs:tP})).tabs,ti=tn.tabPosition,tl=tn.rtl,["top","bottom"].includes(ti)?(ta="width",to=tl?"right":"left",tc=Math.abs(te)):(ta="height",to="top",tc=-te),(0,a.useMemo)(function(){if(!tr.length)return[0,0];for(var t=tr.length,e=t,n=0;n<t;n+=1){var a=ea.get(tr[n].key)||z;if(Math.floor(a[to]+a[ta])>Math.floor(tc+ed)){e=n-1;break}}for(var o=0,c=t-1;c>=0;c-=1)if((ea.get(tr[c].key)||z)[to]<tc){o=c+1;break}return o>=e?[0,0]:[o,e]},[ea,ed,ec,er,ei,tc,ti,tr.map(function(t){return t.key}).join("_"),tl])),ew=(0,v.A)(eA,2),ex=ew[0],e_=ew[1],eS=(0,y.A)(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:th,e=ea.get(t)||{width:0,height:0,left:0,right:0,top:0};if(tN){var n=tX;tg?e.right<tX?n=e.right:e.right+e.width>tX+ed&&(n=e.right+e.width-ed):e.left<-tX?n=-e.left:e.left+e.width>-tX+ed&&(n=-(e.left+e.width-ed)),tq(0),tW(ef(n))}else{var a=tF;e.top<-tF?a=-e.top:e.top+e.height>-tF+ed&&(a=-(e.top+e.height-ed)),tW(0),tq(ef(a))}}),eE=(0,a.useState)(),eC=(0,v.A)(eE,2),ez=eC[0],eR=eC[1],eP=(0,a.useState)(!1),eT=(0,v.A)(eP,2),eI=eT[0],eM=eT[1],eL=tP.filter(function(t){return!t.disabled}).map(function(t){return t.key}),eO=function(t){var e=eL.indexOf(ez||th),n=eL.length;eR(eL[(e+t+n)%n])},eD=function(t){var e=t.code,n=tg&&tN,a=eL[0],o=eL[eL.length-1];switch(e){case"ArrowLeft":tN&&eO(n?1:-1);break;case"ArrowRight":tN&&eO(n?-1:1);break;case"ArrowUp":t.preventDefault(),tN||eO(-1);break;case"ArrowDown":t.preventDefault(),tN||eO(1);break;case"Home":t.preventDefault(),eR(a);break;case"End":t.preventDefault(),eR(o);break;case"Enter":case"Space":t.preventDefault(),tS(ez,t);break;case"Backspace":case"Delete":var c=eL.indexOf(ez),r=tP.find(function(t){return t.key===ez});T(null==r?void 0:r.closable,null==r?void 0:r.closeIcon,ty,null==r?void 0:r.disabled)&&(t.preventDefault(),t.stopPropagation(),ty.onEdit("remove",{key:ez,event:t}),c===eL.length-1?eO(-1):eO(1))}},eB={};tN?eB[tg?"marginRight":"marginLeft"]=tx:eB.marginTop=tx;var eN=tP.map(function(t,e){var n=t.key;return a.createElement(j,{id:tb,prefixCls:tR,key:n,tab:t,style:0===e?void 0:eB,closable:t.closable,editable:ty,active:n===th,focus:n===ez,renderWrapper:t_,removeAriaLabel:null==tA?void 0:tA.removeAriaLabel,tabCount:eL.length,currentPosition:e+1,onClick:function(t){tS(n,t)},onKeyDown:eD,onFocus:function(){eI||eR(n),eS(n),ek(),tL.current&&(tg||(tL.current.scrollLeft=0),tL.current.scrollTop=0)},onBlur:function(){eR(void 0)},onMouseDown:function(){eM(!0)},onMouseUp:function(){eM(!1)}})}),ej=function(){return en(function(){var t,e=new Map,n=null===(t=tO.current)||void 0===t?void 0:t.getBoundingClientRect();return tP.forEach(function(t){var a,o=t.key,c=null===(a=tO.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(P(o),'"]'));if(c){var r=G(c,n),i=(0,v.A)(r,4),l=i[0],d=i[1],s=i[2],u=i[3];e.set(o,{width:l,height:d,left:s,top:u})}}),e})};(0,a.useEffect)(function(){ej()},[tP.map(function(t){return t.key}).join("_")]);var eG=C(function(){var t=X(tT),e=X(tI),n=X(tM);tQ([t[0]-e[0]-n[0],t[1]-e[1]-n[1]]);var a=X(tB);t9(a),t4(X(tD));var o=X(tO);t0([o[0]-a[0],o[1]-a[1]]),ej()}),eX=tP.slice(0,ex),eW=tP.slice(e_+1),eH=[].concat((0,g.A)(eX),(0,g.A)(eW)),eK=ea.get(th),eF=x({activeTabOffset:eK,horizontal:tN,indicator:tC,rtl:tg}).style;(0,a.useEffect)(function(){eS()},[th,eu,ev,R(eK),R(ea),tN]),(0,a.useEffect)(function(){eG()},[tg]);var eq=!!eH.length,eV="".concat(tR,"-nav-wrap");return tN?tg?(ts=tX>0,td=tX!==ev):(td=tX<0,ts=tX!==eu):(tu=tF<0,tv=tF!==eu),a.createElement(k.A,{onResize:eG},a.createElement("div",{ref:(0,A.xK)(e,tT),role:"tablist","aria-orientation":tN?"horizontal":"vertical",className:l()("".concat(tR,"-nav"),tf),style:tp,onKeyDown:function(){ek()}},a.createElement(M,{ref:tI,position:"left",extra:tk,prefixCls:tR}),a.createElement(k.A,{onResize:eG},a.createElement("div",{className:l()(eV,(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(eV,"-ping-left"),td),"".concat(eV,"-ping-right"),ts),"".concat(eV,"-ping-top"),tu),"".concat(eV,"-ping-bottom"),tv)),ref:tL},a.createElement(k.A,{onResize:eG},a.createElement("div",{ref:tO,className:"".concat(tR,"-nav-list"),style:{transform:"translate(".concat(tX,"px, ").concat(tF,"px)"),transition:eh?"none":void 0}},eN,a.createElement(I,{ref:tB,prefixCls:tR,locale:tA,editable:ty,style:(0,u.A)((0,u.A)({},0===eN.length?void 0:eB),{},{visibility:eq?"hidden":null})}),a.createElement("div",{className:l()("".concat(tR,"-ink-bar"),(0,s.A)({},"".concat(tR,"-ink-bar-animated"),tm.inkBar)),style:eF}))))),a.createElement(N,(0,d.A)({},t,{removeAriaLabel:null==tA?void 0:tA.removeAriaLabel,ref:tD,prefixCls:tR,tabs:eH,className:!eq&&es,tabMoving:!!eh})),a.createElement(M,{ref:tM,position:"right",extra:tk,prefixCls:tR})))}),K=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.className,c=t.style,r=t.id,i=t.active,d=t.tabKey,s=t.children;return a.createElement("div",{id:r&&"".concat(r,"-panel-").concat(d),role:"tabpanel",tabIndex:i?0:-1,"aria-labelledby":r&&"".concat(r,"-tab-").concat(d),"aria-hidden":!i,style:c,className:l()(n,i&&"".concat(n,"-active"),o),ref:e},s)}),F=["renderTabBar"],q=["label","key"];let V=function(t){var e=t.renderTabBar,n=(0,p.A)(t,F),o=a.useContext(h).tabs;return e?e((0,u.A)((0,u.A)({},n),{},{panes:o.map(function(t){var e=t.label,n=t.key,o=(0,p.A)(t,q);return a.createElement(K,(0,d.A)({tab:e,key:n,tabKey:n},o))})}),H):a.createElement(H,n)};var Y=n(72261),U=["key","forceRender","style","className","destroyInactiveTabPane"];let Q=function(t){var e=t.id,n=t.activeKey,o=t.animated,c=t.tabPosition,r=t.destroyInactiveTabPane,i=a.useContext(h),v=i.prefixCls,f=i.tabs,b=o.tabPane,m="".concat(v,"-tabpane");return a.createElement("div",{className:l()("".concat(v,"-content-holder"))},a.createElement("div",{className:l()("".concat(v,"-content"),"".concat(v,"-content-").concat(c),(0,s.A)({},"".concat(v,"-content-animated"),b))},f.map(function(t){var c=t.key,i=t.forceRender,s=t.style,v=t.className,f=t.destroyInactiveTabPane,h=(0,p.A)(t,U),g=c===n;return a.createElement(Y.Ay,(0,d.A)({key:c,visible:g,forceRender:i,removeOnLeave:!!(r||f),leavedClassName:"".concat(m,"-hidden")},o.tabPaneMotion),function(t,n){var o=t.style,r=t.className;return a.createElement(K,(0,d.A)({},h,{prefixCls:m,id:e,tabKey:c,animated:b,active:g,style:(0,u.A)((0,u.A)({},s),o),className:l()(v,r),ref:n}))})})))};n(30754);var J=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],Z=0,$=a.forwardRef(function(t,e){var n=t.id,o=t.prefixCls,c=void 0===o?"rc-tabs":o,r=t.className,i=t.items,g=t.direction,k=t.activeKey,y=t.defaultActiveKey,A=t.editable,w=t.animated,x=t.tabPosition,_=void 0===x?"top":x,S=t.tabBarGutter,E=t.tabBarStyle,C=t.tabBarExtraContent,z=t.locale,R=t.more,P=t.destroyInactiveTabPane,T=t.renderTabBar,I=t.onChange,M=t.onTabClick,L=t.onTabScroll,O=t.getPopupContainer,D=t.popupClassName,B=t.indicator,N=(0,p.A)(t,J),j=a.useMemo(function(){return(i||[]).filter(function(t){return t&&"object"===(0,f.A)(t)&&"key"in t})},[i]),G="rtl"===g,X=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(t=!1===e?{inkBar:!1,tabPane:!1}:!0===e?{inkBar:!0,tabPane:!1}:(0,u.A)({inkBar:!0},"object"===(0,f.A)(e)?e:{})).tabPaneMotion&&void 0===t.tabPane&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}(w),W=(0,a.useState)(!1),H=(0,v.A)(W,2),K=H[0],F=H[1];(0,a.useEffect)(function(){F((0,m.A)())},[]);var q=(0,b.A)(function(){var t;return null===(t=j[0])||void 0===t?void 0:t.key},{value:k,defaultValue:y}),Y=(0,v.A)(q,2),U=Y[0],$=Y[1],tt=(0,a.useState)(function(){return j.findIndex(function(t){return t.key===U})}),te=(0,v.A)(tt,2),tn=te[0],ta=te[1];(0,a.useEffect)(function(){var t,e=j.findIndex(function(t){return t.key===U});-1===e&&(e=Math.max(0,Math.min(tn,j.length-1)),$(null===(t=j[e])||void 0===t?void 0:t.key)),ta(e)},[j.map(function(t){return t.key}).join("_"),U,tn]);var to=(0,b.A)(null,{value:n}),tc=(0,v.A)(to,2),tr=tc[0],ti=tc[1];(0,a.useEffect)(function(){n||(ti("rc-tabs-".concat(Z)),Z+=1)},[]);var tl={id:tr,activeKey:U,animated:X,tabPosition:_,rtl:G,mobile:K},td=(0,u.A)((0,u.A)({},tl),{},{editable:A,locale:z,more:R,tabBarGutter:S,onTabClick:function(t,e){null==M||M(t,e);var n=t!==U;$(t),n&&(null==I||I(t))},onTabScroll:L,extra:C,style:E,panes:null,getPopupContainer:O,popupClassName:D,indicator:B});return a.createElement(h.Provider,{value:{tabs:j,prefixCls:c}},a.createElement("div",(0,d.A)({ref:e,id:n,className:l()(c,"".concat(c,"-").concat(_),(0,s.A)((0,s.A)((0,s.A)({},"".concat(c,"-mobile"),K),"".concat(c,"-editable"),A),"".concat(c,"-rtl"),G),r)},N),a.createElement(V,(0,d.A)({},td,{renderTabBar:T})),a.createElement(Q,(0,d.A)({destroyInactiveTabPane:P},tl,{animated:X}))))}),tt=n(31049),te=n(7926),tn=n(27651),ta=n(19635);let to={motionAppear:!1,motionEnter:!0,motionLeave:!0};var tc=n(63588),tr=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n},ti=n(5144),tl=n(70695),td=n(1086),ts=n(56204),tu=n(46777);let tv=t=>{let{componentCls:e,motionDurationSlow:n}=t;return[{[e]:{["".concat(e,"-switch")]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:"opacity ".concat(n)}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:"opacity ".concat(n)}}}}},[(0,tu._j)(t,"slide-up"),(0,tu._j)(t,"slide-down")]]},tf=t=>{let{componentCls:e,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:c,itemSelectedColor:r}=t;return{["".concat(e,"-card")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:0,padding:n,background:a,border:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(c),transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut)},["".concat(e,"-tab-active")]:{color:r,background:t.colorBgContainer},["".concat(e,"-tab-focus")]:Object.assign({},(0,tl.jk)(t,-3)),["".concat(e,"-ink-bar")]:{visibility:"hidden"},["& ".concat(e,"-tab").concat(e,"-tab-focus ").concat(e,"-tab-btn")]:{outline:"none"}},["&".concat(e,"-top, &").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginLeft:{_skip_check_:!0,value:(0,ti.zA)(o)}}}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0 0")},["".concat(e,"-tab-active")]:{borderBottomColor:t.colorBgContainer}}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG))},["".concat(e,"-tab-active")]:{borderTopColor:t.colorBgContainer}}},["&".concat(e,"-left, &").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginTop:(0,ti.zA)(o)}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.borderRadiusLG)," 0 0 ").concat((0,ti.zA)(t.borderRadiusLG))}},["".concat(e,"-tab-active")]:{borderRightColor:{_skip_check_:!0,value:t.colorBgContainer}}}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0")}},["".concat(e,"-tab-active")]:{borderLeftColor:{_skip_check_:!0,value:t.colorBgContainer}}}}}}},tp=t=>{let{componentCls:e,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=t;return{["".concat(e,"-dropdown")]:Object.assign(Object.assign({},(0,tl.dF)(t)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:t.zIndexPopup,display:"block","&-hidden":{display:"none"},["".concat(e,"-dropdown-menu")]:{maxHeight:t.tabsDropdownHeight,margin:0,padding:"".concat((0,ti.zA)(a)," 0"),overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:t.colorBgContainer,backgroundClip:"padding-box",borderRadius:t.borderRadiusLG,outline:"none",boxShadow:t.boxShadowSecondary,"&-item":Object.assign(Object.assign({},tl.L9),{display:"flex",alignItems:"center",minWidth:t.tabsDropdownWidth,margin:0,padding:"".concat((0,ti.zA)(t.paddingXXS)," ").concat((0,ti.zA)(t.paddingSM)),color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:t.marginSM},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:t.controlItemBgHover},"&-disabled":{"&, &:hover":{color:t.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},tb=t=>{let{componentCls:e,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:c,verticalItemMargin:r,calc:i}=t;return{["".concat(e,"-top, ").concat(e,"-bottom")]:{flexDirection:"column",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(a),content:"''"},["".concat(e,"-ink-bar")]:{height:t.lineWidthBold,"&-animated":{transition:"width ".concat(t.motionDurationSlow,", left ").concat(t.motionDurationSlow,",\n            right ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-wrap")]:{"&::before, &::after":{top:0,bottom:0,width:t.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowRight},["&".concat(e,"-nav-wrap-ping-left::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-right::after")]:{opacity:1}}}},["".concat(e,"-top")]:{["> ".concat(e,"-nav,\n        > div > ").concat(e,"-nav")]:{"&::before":{bottom:0},["".concat(e,"-ink-bar")]:{bottom:0}}},["".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},["".concat(e,"-ink-bar")]:{top:0}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0}},["".concat(e,"-left, ").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{flexDirection:"column",minWidth:i(t.controlHeight).mul(1.25).equal(),["".concat(e,"-tab")]:{padding:c,textAlign:"center"},["".concat(e,"-tab + ").concat(e,"-tab")]:{margin:r},["".concat(e,"-nav-wrap")]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:t.controlHeight},"&::before":{top:0,boxShadow:t.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:t.boxShadowTabsOverflowBottom},["&".concat(e,"-nav-wrap-ping-top::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-bottom::after")]:{opacity:1}},["".concat(e,"-ink-bar")]:{width:t.lineWidthBold,"&-animated":{transition:"height ".concat(t.motionDurationSlow,", top ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-list, ").concat(e,"-nav-operations")]:{flex:"1 0 auto",flexDirection:"column"}}},["".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-ink-bar")]:{right:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{marginLeft:{_skip_check_:!0,value:(0,ti.zA)(i(t.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingLeft:{_skip_check_:!0,value:t.paddingLG}}}},["".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,["".concat(e,"-ink-bar")]:{left:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0,marginRight:{_skip_check_:!0,value:i(t.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingRight:{_skip_check_:!0,value:t.paddingLG}}}}}},tm=t=>{let{componentCls:e,cardPaddingSM:n,cardPaddingLG:a,horizontalItemPaddingSM:o,horizontalItemPaddingLG:c}=t;return{[e]:{"&-small":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:o,fontSize:t.titleFontSizeSM}}},"&-large":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:c,fontSize:t.titleFontSizeLG}}}},["".concat(e,"-card")]:{["&".concat(e,"-small")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:n}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius))}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius)," 0 0")}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius)," 0")}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.borderRadius)," 0 0 ").concat((0,ti.zA)(t.borderRadius))}}}},["&".concat(e,"-large")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:a}}}}}},th=t=>{let{componentCls:e,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:c,horizontalItemPadding:r,itemSelectedColor:i,itemColor:l}=t,d="".concat(e,"-tab");return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:r,fontSize:t.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:"all ".concat(t.motionDurationSlow),["".concat(d,"-icon:not(:last-child)")]:{marginInlineEnd:t.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:t.calc(t.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:t.marginXS},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"&:hover":{color:t.colorTextHeading}},(0,tl.K8)(t)),"&:hover":{color:a},["&".concat(d,"-active ").concat(d,"-btn")]:{color:i,textShadow:t.tabsActiveTextShadow},["&".concat(d,"-focus ").concat(d,"-btn")]:Object.assign({},(0,tl.jk)(t)),["&".concat(d,"-disabled")]:{color:t.colorTextDisabled,cursor:"not-allowed"},["&".concat(d,"-disabled ").concat(d,"-btn, &").concat(d,"-disabled ").concat(e,"-remove")]:{"&:focus, &:active":{color:t.colorTextDisabled}},["& ".concat(d,"-remove ").concat(o)]:{margin:0},["".concat(o,":not(:last-child)")]:{marginRight:{_skip_check_:!0,value:t.marginSM}}},["".concat(d," + ").concat(d)]:{margin:{_skip_check_:!0,value:c}}}},tg=t=>{let{componentCls:e,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:c}=t;return{["".concat(e,"-rtl")]:{direction:"rtl",["".concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:{_skip_check_:!0,value:n},["".concat(e,"-tab:last-of-type")]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,ti.zA)(t.marginSM)}},["".concat(e,"-tab-remove")]:{marginRight:{_skip_check_:!0,value:(0,ti.zA)(t.marginXS)},marginLeft:{_skip_check_:!0,value:(0,ti.zA)(c(t.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav")]:{order:1},["> ".concat(e,"-content-holder")]:{order:0}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav")]:{order:0},["> ".concat(e,"-content-holder")]:{order:1}},["&".concat(e,"-card").concat(e,"-top, &").concat(e,"-card").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},["".concat(e,"-dropdown-rtl")]:{direction:"rtl"},["".concat(e,"-menu-item")]:{["".concat(e,"-dropdown-rtl")]:{textAlign:{_skip_check_:!0,value:"right"}}}}},tk=t=>{let{componentCls:e,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:c,itemActiveColor:r,colorBorderSecondary:i}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,tl.dF)(t)),{display:"flex",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{position:"relative",display:"flex",flex:"none",alignItems:"center",["".concat(e,"-nav-wrap")]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:"opacity ".concat(t.motionDurationSlow),content:"''",pointerEvents:"none"}},["".concat(e,"-nav-list")]:{position:"relative",display:"flex",transition:"opacity ".concat(t.motionDurationSlow)},["".concat(e,"-nav-operations")]:{display:"flex",alignSelf:"stretch"},["".concat(e,"-nav-operations-hidden")]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},["".concat(e,"-nav-more")]:{position:"relative",padding:n,background:"transparent",border:0,color:t.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:t.calc(t.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},["".concat(e,"-nav-add")]:Object.assign({minWidth:a,marginLeft:{_skip_check_:!0,value:o},padding:(0,ti.zA)(t.paddingXS),background:"transparent",border:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(i),borderRadius:"".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0 0"),outline:"none",cursor:"pointer",color:t.colorText,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut),"&:hover":{color:c},"&:active, &:focus:not(:focus-visible)":{color:r}},(0,tl.K8)(t,-3))},["".concat(e,"-extra-content")]:{flex:"none"},["".concat(e,"-ink-bar")]:{position:"absolute",background:t.inkBarColor,pointerEvents:"none"}}),th(t)),{["".concat(e,"-content")]:{position:"relative",width:"100%"},["".concat(e,"-content-holder")]:{flex:"auto",minWidth:0,minHeight:0},["".concat(e,"-tabpane")]:Object.assign(Object.assign({},(0,tl.K8)(t)),{"&-hidden":{display:"none"}})}),["".concat(e,"-centered")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-nav-wrap")]:{["&:not([class*='".concat(e,"-nav-wrap-ping']) > ").concat(e,"-nav-list")]:{margin:"auto"}}}}}},ty=(0,td.OF)("Tabs",t=>{let e=(0,ts.oX)(t,{tabsCardPadding:t.cardPadding,dropdownEdgeChildVerticalPadding:t.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:"0 0 0 ".concat((0,ti.zA)(t.horizontalItemGutter)),tabsHorizontalItemMarginRTL:"0 0 0 ".concat((0,ti.zA)(t.horizontalItemGutter))});return[tm(e),tg(e),tb(e),tp(e),tf(e),tk(e),tv(e)]},t=>{let e=t.controlHeightLG;return{zIndexPopup:t.zIndexPopupBase+50,cardBg:t.colorFillAlter,cardHeight:e,cardPadding:"".concat((e-Math.round(t.fontSize*t.lineHeight))/2-t.lineWidth,"px ").concat(t.padding,"px"),cardPaddingSM:"".concat(1.5*t.paddingXXS,"px ").concat(t.padding,"px"),cardPaddingLG:"".concat(t.paddingXS,"px ").concat(t.padding,"px ").concat(1.5*t.paddingXXS,"px"),titleFontSize:t.fontSize,titleFontSizeLG:t.fontSizeLG,titleFontSizeSM:t.fontSize,inkBarColor:t.colorPrimary,horizontalMargin:"0 0 ".concat(t.margin,"px 0"),horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:"".concat(t.paddingSM,"px 0"),horizontalItemPaddingSM:"".concat(t.paddingXS,"px 0"),horizontalItemPaddingLG:"".concat(t.padding,"px 0"),verticalItemPadding:"".concat(t.paddingXS,"px ").concat(t.paddingLG,"px"),verticalItemMargin:"".concat(t.margin,"px 0 0 0"),itemColor:t.colorText,itemSelectedColor:t.colorPrimary,itemHoverColor:t.colorPrimaryHover,itemActiveColor:t.colorPrimaryActive,cardGutter:t.marginXXS/2}});var tA=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let tw=t=>{var e,n,i,d,s,u,v,f,p,b,m;let h;let{type:g,className:k,rootClassName:y,size:A,onEdit:w,hideAdd:x,centered:_,addIcon:S,removeIcon:E,moreIcon:C,more:z,popupClassName:R,children:P,items:T,animated:I,style:M,indicatorSize:L,indicator:O}=t,D=tA(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:B}=D,{direction:N,tabs:j,getPrefixCls:G,getPopupContainer:X}=a.useContext(tt.QO),W=G("tabs",B),H=(0,te.A)(W),[K,F,q]=ty(W,H);"editable-card"===g&&(h={onEdit:(t,e)=>{let{key:n,event:a}=e;null==w||w("add"===t?a:n,t)},removeIcon:null!==(e=null!=E?E:null==j?void 0:j.removeIcon)&&void 0!==e?e:a.createElement(o.A,null),addIcon:(null!=S?S:null==j?void 0:j.addIcon)||a.createElement(r.A,null),showAdd:!0!==x});let V=G(),Y=(0,tn.A)(A),U=function(t,e){return t?t:(0,tc.A)(e).map(t=>{if(a.isValidElement(t)){let{key:e,props:n}=t,a=n||{},{tab:o}=a,c=tr(a,["tab"]);return Object.assign(Object.assign({key:String(e)},c),{label:o})}return null}).filter(t=>t)}(T,P),Q=function(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return(e=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{})).tabPane&&(e.tabPaneMotion=Object.assign(Object.assign({},to),{motionName:(0,ta.b)(t,"switch")})),e}(W,I),J=Object.assign(Object.assign({},null==j?void 0:j.style),M),Z={align:null!==(n=null==O?void 0:O.align)&&void 0!==n?n:null===(i=null==j?void 0:j.indicator)||void 0===i?void 0:i.align,size:null!==(v=null!==(s=null!==(d=null==O?void 0:O.size)&&void 0!==d?d:L)&&void 0!==s?s:null===(u=null==j?void 0:j.indicator)||void 0===u?void 0:u.size)&&void 0!==v?v:null==j?void 0:j.indicatorSize};return K(a.createElement($,Object.assign({direction:N,getPopupContainer:X},D,{items:U,className:l()({["".concat(W,"-").concat(Y)]:Y,["".concat(W,"-card")]:["card","editable-card"].includes(g),["".concat(W,"-editable-card")]:"editable-card"===g,["".concat(W,"-centered")]:_},null==j?void 0:j.className,k,y,F,q,H),popupClassName:l()(R,F,q,H),style:J,editable:h,more:Object.assign({icon:null!==(m=null!==(b=null!==(p=null===(f=null==j?void 0:j.more)||void 0===f?void 0:f.icon)&&void 0!==p?p:null==j?void 0:j.moreIcon)&&void 0!==b?b:C)&&void 0!==m?m:a.createElement(c.A,null),transitionName:"".concat(V,"-slide-up")},z),prefixCls:W,animated:Q,indicator:Z})))};tw.TabPane=()=>null;let tx=tw}}]);
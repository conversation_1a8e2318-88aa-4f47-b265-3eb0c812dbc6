(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1126,3889],{3301:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var o=s(95155),r=s(11432),n=s(79005);function i(e){let{children:t,...s}=e;return(0,o.jsx)(r.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847",defaultBg:"none",defaultHoverBg:"none",colorLink:"#171F2D",colorLinkHover:"#303847"}}},children:(0,o.jsx)(n.Ay,{...s,children:t})})}},6521:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var o=s(95155),r=s(72093),n=s(22810),i=s(2796),a=s(53096),l=s(46742),d=s(12115),c=s(3301);function u(e){let{renderItem:t,pageSize:s=12,getList:u,params:h,refreshDeps:k,gutter:p=[24,24],maxHeight:x}=e,[j,C]=(0,d.useState)(!1),[g,L]=(0,d.useState)(1),[A,H]=(0,d.useState)(!0),[v,m]=(0,d.useState)([]),w=async e=>{if(!u)return;C(!0);let t=await u({page:e,pageSize:s,...h});t.success&&(m(1===e?t.data.records:[...v,...t.data.records]),H(e*s<t.data.total)),C(!1)};return(0,d.useEffect)(()=>{(g>1||!k)&&w(g)},[g]),(0,d.useEffect)(()=>{(null==k?void 0:k.length)>0&&(H(!0),L(1),w(1))},[null==k?void 0:k.join(",")]),(0,o.jsxs)(r.A,{spinning:j,children:[(0,o.jsx)(n.A,{gutter:p,style:{marginTop:"24px",maxHeight:x,overflow:"auto"},children:v.length?v.map(e=>t(e)):(0,o.jsx)(i.A,{span:24,children:(0,o.jsx)(a.A,{image:a.A.PRESENTED_IMAGE_SIMPLE})})}),v.length>0&&(0,o.jsx)(n.A,{justify:"center",style:{marginTop:24},children:(0,o.jsx)(i.A,{children:A?(0,o.jsx)(c.A,{loading:j,onClick:()=>L(g+1),type:"link",children:"加载更多"}):(0,o.jsx)(l.A.Text,{type:"secondary",children:"已经到底了"})})})]})}},18123:(e,t,s)=>{"use strict";s.d(t,{AK:()=>l,K:()=>a,Mj:()=>d,Mp:()=>h,RI:()=>n,Ru:()=>j,V8:()=>c,Yn:()=>k,ZY:()=>i,c9:()=>C,rp:()=>u,w4:()=>x});var o=s(69653),r=s(79471);let n=()=>(0,o.A)(r.A.getTaskList,{manual:!0}),i=()=>(0,o.A)(r.A.createTask,{manual:!0}),a=()=>(0,o.A)(r.A.updateTask,{manual:!0}),l=()=>(0,o.A)(r.A.deleteTask,{manual:!0}),d=()=>(0,o.A)(r.A.getTaskDetail,{manual:!0}),c=()=>(0,o.A)(r.A.startTask,{manual:!0}),u=()=>(0,o.A)(r.A.stopTask,{manual:!0}),h=()=>(0,o.A)(r.A.notice,{manual:!0}),k=()=>(0,o.A)(r.A.rerunReport,{manual:!0}),p={busy:"0",failed:"0",initial:"0",notexist:"0",processing:"0",success:"0",total:"0"},x=e=>{var t;let s=(0,o.A)(()=>r.A.getTaskStatusCount({robotId:e}));return{...s,data:(null==s?void 0:null===(t=s.data)||void 0===t?void 0:t.success)?s.data.data:p}},j=()=>{var e;let t=(0,o.A)(r.A.getTaskStatusCount,{manual:!0});return{...t,data:(null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.success)?t.data.data:{}}},C=()=>(0,o.A)(r.A.getContactList,{manual:!0})},18576:e=>{e.exports={svg:"svg_svg__xVdcM"}},24947:(e,t,s)=>{"use strict";s.d(t,{A:()=>k});var o=s(95155),r=s(46742),n=s(22810),i=s(2796),a=s(3387),l=s(45100),d=s(95458),c=s.n(d);let{Title:u,Text:h}=r.A;function k(e){let{title:t,badge:s,popProps:r,desc:d}=e;return(0,o.jsxs)("div",{className:c().container,children:[(0,o.jsxs)(n.A,{gutter:8,style:{flexWrap:"nowrap",paddingRight:"44px"},children:[(0,o.jsx)(i.A,{children:(0,o.jsx)(a.A,{...r,children:(0,o.jsx)(l.A,{className:c().tag,color:s.color,children:s.text})})}),(0,o.jsx)(i.A,{children:(0,o.jsx)(u,{level:5,className:c().name,children:t})})]}),(0,o.jsx)(h,{type:"secondary",className:c().description,children:d})]})}},27192:e=>{e.exports={tag:"SmallTag_tag__Ws2n0"}},32853:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var o=s(95155),r=s(79005),n=s(12115),i=s(47956);let a=e=>{let{title:t,btnProps:s,btnExtraEvent:a,onOk:l,btnText:d,description:c,children:u,modalProps:h}=e,[k,p]=(0,n.useState)(!1);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.Ay,{loading:null==s?void 0:s.loading,onClick:()=>{p(!0),null==a||a()},...s,children:d}),k&&(0,o.jsx)(i.A,{title:t,open:k,onCancel:()=>p(!1),onOk:async()=>{await (null==l?void 0:l())&&p(!1)},description:c,...h,children:u})]})}},35577:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var o=s(95155),r=s(45100),n=s(27192),i=s.n(n);function a(e){let{children:t,color:s,style:n}=e;return(0,o.jsx)(r.A,{className:i().tag,color:s,style:n,children:t})}},35587:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var o=s(95155),r=s(11432),n=s(71349);function i(e){let{children:t,...s}=e;return(0,o.jsx)(r.Ay,{theme:{components:{Card:{headerBg:"#171F2D",colorBgContainer:"#f8f8fa",colorTextHeading:"#fff"}}},children:(0,o.jsx)(n.A,{...s,children:t})})}},35594:(e,t,s)=>{"use strict";s.d(t,{Jt:()=>i,bE:()=>a,yH:()=>d,yJ:()=>l});var o=s(43932),r=s.n(o);function n(e,t){return Promise.resolve(r().ajax({method:e,url:t.path||t.url,data:"GET"===e?t.data:JSON.stringify(t.data),contentType:"application/json;charset=UTF-8"}))}let i=e=>n("GET",e),a=e=>n("POST",e),l=e=>n("PUT",e),d=e=>n("DELETE",e)},42592:(e,t,s)=>{"use strict";s.d(t,{_O:()=>i,m2:()=>a,Lt:()=>d,HB:()=>c,Fj:()=>l});var o=s(69653),r=s(35594);let n={getAssistantList:e=>(0,r.Jt)({url:"/api/evalapp",data:e}),createAssistant:e=>(0,r.bE)({url:"/api/evalapp",data:e}),updateAssistant:(e,t)=>(0,r.yJ)({url:"/api/evalapp/".concat(t),data:e}),getAssistantDetail:e=>(0,r.Jt)({url:"/api/evalapp/".concat(e)}),deleteAssistant:e=>(0,r.yH)({url:"/api/evalapp/".concat(e)})},i=()=>(0,o.A)(n.getAssistantList,{manual:!0}),a=()=>(0,o.A)(n.createAssistant,{manual:!0}),l=()=>(0,o.A)(n.updateAssistant,{manual:!0}),d=()=>(0,o.A)(n.deleteAssistant,{manual:!0}),c=()=>(0,o.A)(n.getAssistantDetail,{manual:!0})},42628:(e,t,s)=>{"use strict";s.d(t,{A:()=>f,B:()=>w});var o=s(95155),r=s(81488),n=s(71126),i=s(59575),a=s(55750),l=s(71349),d=s(22810),c=s(2796),u=s(78444),h=s(6457),k=s(42426),p=s(28041),x=s(92895),j=s(68773),C=s(79005),g=s(76046),L=s(12115),A=s(85845),H=s(24947),v=s(81909),m=s.n(v);let w=e=>e.processingTaskCount>0?{status:"service",tooltip:"服务中不支持修改",color:"#23E4A6",label:"服务"}:e.disabled?{status:"offline",tooltip:"点击发布",color:"#BEBEBE",label:"离线"}:{status:"idle",tooltip:"点击取消发布",color:"#FBBC05",label:"空闲"},f=e=>{let{robot:t,cardProps:s,readonly:v=!1,selectable:f,selected:V,onSelect:y}=e,M=(0,g.useRouter)(),[b,Z]=(0,L.useState)(t),{avatar:W,name:_,desc:B,disabled:T,id:E,todayCompletedTaskCount:N}=b,{runAsync:R,loading:S}=(0,i.lJ)(),{status:F,label:D,color:I,tooltip:O}=w(t);return(0,o.jsxs)(l.A,{variant:"borderless",className:"".concat(m().RobotInfoCard," ").concat(V?m().selected:""),...s,children:[(0,o.jsxs)(d.A,{justify:"space-between",align:"middle",style:{marginBottom:32},children:[(0,o.jsx)(c.A,{children:(0,o.jsx)(u.A,{size:64,src:W,alt:_,icon:(0,o.jsx)(a.A,{})})}),!v&&(0,o.jsx)(c.A,{children:(0,o.jsx)(h.A,{title:O,children:(0,o.jsx)(k.A,{loading:S,checked:!T,disabled:"service"===F,onChange:async e=>{(await R({disabled:!e},E)).success?Z({...b,disabled:!e}):p.Ay.error("操作失败")}})})}),f&&(0,o.jsx)(c.A,{children:(0,o.jsx)(x.A,{checked:V,onChange:e=>y(e.target.checked),className:m().checkbox})})]}),(0,o.jsx)(H.A,{title:_,badge:{color:I,text:D},desc:B}),(0,o.jsx)("div",{className:m().processed_container,children:(0,o.jsxs)(d.A,{justify:"space-between",children:[(0,o.jsxs)(c.A,{className:m().processed,children:["今日处理量: ",N,"通"]}),!v&&(0,o.jsx)(c.A,{children:(0,o.jsxs)(j.A,{className:m().button_group,children:[(0,o.jsx)(C.Ay,{type:"link",onClick:()=>{M.push((0,r.I)({url:n.Nv.RobotCopy,params:{id:t.id,platform:t.platform||A.W.Volcano}}))},children:"复制"}),(0,o.jsx)(C.Ay,{type:"link",onClick:()=>{M.push((0,r.I)({url:n.Nv.RobotEdit,params:{id:t.id,platform:t.platform||A.W.Volcano}}))},children:"详情"})]})})]})})]})}},47956:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var o=s(95155),r=s(46742),n=s(21382),i=s(22810),a=s(11432),l=s(68773),d=s(79005);let{Text:c}=r.A,u=e=>{let{open:t,title:s,onOk:r,onCancel:u,description:h,children:k,cancelText:p="取消",okText:x="确定",...j}=e;return(0,o.jsxs)(n.A,{title:s,open:t,onCancel:u,onOk:r,footer:(0,o.jsx)(i.A,{justify:"end",children:(0,o.jsx)(a.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:(0,o.jsxs)(l.A,{children:[p&&(0,o.jsx)(d.Ay,{onClick:u,style:{border:"none"},children:p}),x&&(0,o.jsx)(d.Ay,{type:"primary",onClick:r,children:x})]})})}),...j,children:[h&&(0,o.jsx)(c,{type:"secondary",children:h}),k]})}},48921:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,Q:()=>d});var o=s(95155),r=s(11432),n=s(71349),i=s(97838),a=s(89801);function l(e){let{children:t,badge:s,cardBgColor:a="#171F2D",...l}=e;return(0,o.jsx)(r.Ay,{theme:{components:{Card:{colorBgContainer:a,colorText:"#fff",bodyPadding:"24px 24px 0 24px"},Badge:{colorText:"#fff"}}},children:(0,o.jsxs)(n.A,{...l,children:[s&&(0,o.jsx)(i.A,{...s}),t]})})}let d=e=>{let{value:t,showPercent:s=!1,style:r}=e;return(0,o.jsx)("div",{children:(0,o.jsx)(a.A,{value:t,groupSeparator:",",valueStyle:{fontSize:"48px",fontWeight:600,fontFamily:"DIN Condensed",color:"#fff",...r},formatter:e=>s?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{children:e}),(0,o.jsx)("span",{style:{fontSize:"24px",fontWeight:400,fontFamily:"PingFang SC"},children:"%"})]}):e})})}},59575:(e,t,s)=>{"use strict";s.d(t,{uw:()=>l,dk:()=>c,hU:()=>u,oD:()=>a,lJ:()=>d});var o=s(69653),r=s(35594),n=s(90603);let i={getRobotList:e=>(0,r.Jt)({url:"/api/robots",data:e?{...n.u,...e}:n.u}),createRobot:e=>(0,r.bE)({url:"/api/robots",data:e}),updateRobot:(e,t)=>(0,r.yJ)({url:"/api/robots/".concat(t),data:e}),getRobotDetail:e=>(0,r.Jt)({url:"/api/robots/".concat(e)}),deleteRobot:e=>(0,r.yH)({url:"/api/robots/".concat(e)})},a=()=>(0,o.A)(i.getRobotList,{manual:!0}),l=()=>(0,o.A)(i.createRobot,{manual:!0}),d=()=>(0,o.A)(i.updateRobot,{manual:!0}),c=()=>(0,o.A)(i.deleteRobot,{manual:!0}),u=()=>(0,o.A)(i.getRobotDetail,{manual:!0})},63056:(e,t,s)=>{"use strict";s.d(t,{A:()=>w});var o=s(95155),r=s(71126),n=s(42592),i=s(55750),a=s(71349),l=s(22810),d=s(2796),c=s(78444),u=s(6457),h=s(42426),k=s(28041),p=s(92895),x=s(68773),j=s(79005),C=s(76046),g=s(12115),L=s(24947),A=s(81909),H=s.n(A),v=s(81488);let m=e=>{switch(e){case"online":return{color:"#23E4A6",text:"在线",tooltip:"工作中不支持修改"};case"offline":return{color:"#BEBEBE",text:"离线"};case"free":return{color:"#FBBC05",text:"空闲"};default:return{color:"#BEBEBE",text:"未知"}}},w=e=>{let{assistant:t,cardProps:s,onDetail:A,readonly:w=!1,selectable:f,selected:V,onSelect:y}=e,{avatar:M,name:b,desc:Z,todayCompletedcount:W=0,id:_}=t,[B,T]=(0,g.useState)(t),{runAsync:E,loading:N}=(0,n.Fj)(),R=B.disabled,S=B.status,F=(0,C.useRouter)();return(0,o.jsxs)(a.A,{variant:"borderless",className:H().RobotInfoCard,...s,children:[(0,o.jsxs)(l.A,{justify:"space-between",align:"middle",style:{marginBottom:32},children:[(0,o.jsx)(d.A,{children:(0,o.jsx)(c.A,{size:64,src:M,alt:b,icon:(0,o.jsx)(i.A,{})})}),!w&&(0,o.jsx)(d.A,{children:(0,o.jsx)(u.A,{title:m(t.status).tooltip,children:(0,o.jsx)(h.A,{loading:N,checked:!R,disabled:"online"===S,onChange:async e=>{(await E({disabled:!e},_)).success?T({...B,disabled:!e,status:e?"free":"offline"}):k.Ay.error("操作失败")}})})}),f&&(0,o.jsx)(d.A,{children:(0,o.jsx)(p.A,{checked:V,onChange:e=>y(e.target.checked),className:H().checkbox})})]}),(0,o.jsx)(L.A,{title:b,badge:m(t.status),desc:Z||""}),(0,o.jsx)("div",{className:H().processed_container,children:(0,o.jsxs)(l.A,{justify:"space-between",children:[(0,o.jsxs)(d.A,{className:H().processed,children:["今日分析量: ",W]}),!w&&(0,o.jsx)(d.A,{children:(0,o.jsxs)(x.A,{size:"small",className:H().button_group,children:[(0,o.jsx)(j.Ay,{onClick:()=>{F.push((0,v.I)({url:r.Nv.AnalysisAssistantCopy,params:{id:_}}))},type:"link",children:"复制"}),(0,o.jsx)(j.Ay,{type:"link",onClick:()=>{A?A():F.push((0,v.I)({url:r.Nv.AnalysisAssistantEdit,params:{id:_}}))},children:"详情"})]})})]})})]})}},71126:(e,t,s)=>{"use strict";s.d(t,{GT:()=>a,Nv:()=>i,u4:()=>l});var o=s(95155),r=s(18576),n=s.n(r),i=function(e){return e.Login="/login",e.RobotList="/ai_virtual_seat",e.RobotEdit="/ai_virtual_seat/edit",e.AnalysisAssistantList="/analysis_assistant",e.AnalysisAssistantEdit="/analysis_assistant/edit",e.AnalysisAssistantCopy="/analysis_assistant/copy",e.RobotCopy="/ai_virtual_seat/copy",e.TaskList="/task",e.TaskEdit="/task/edit",e.TaskBoard="/task_board",e.TaskBoardDetail="/task_board/detail",e.TaskBoardEdit="/task_board/edit",e.Report="/report",e.EmployeeTaskReport="/report/employee_task_report",e}({}),a=function(e){return e.Task="task",e.AIVirtualSeat="ai_virtual_seat",e.Login="login",e.UserPermission="user_permission",e.EmployeeInfo="employee_info",e.AnalysisAssistant="analysis_assistant",e.TaskBoard="task_board",e.Report="report",e.ModelManage="model_manage",e}({});let l={task:{label:"任务管理[正在改造]",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{className:n().svg,xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,o.jsx)("path",{d:"M17.0837 2.5H2.91699C2.22664 2.5 1.66699 3.05964 1.66699 3.75V16.25C1.66699 16.9404 2.22664 17.5 2.91699 17.5H17.0837C17.774 17.5 18.3337 16.9404 18.3337 16.25V3.75C18.3337 3.05964 17.774 2.5 17.0837 2.5Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M3.66699 5.83334H16.3337",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8.33301 10H14.9997",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8.33301 13.3333H14.9997",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5 10H5.83333",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5 13.3333H5.83333",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{className:n().svg,xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,o.jsx)("path",{d:"M17.0834 2.5H2.91675C2.22639 2.5 1.66675 3.05964 1.66675 3.75V16.25C1.66675 16.9404 2.22639 17.5 2.91675 17.5H17.0834C17.7738 17.5 18.3334 16.9404 18.3334 16.25V3.75C18.3334 3.05964 17.7738 2.5 17.0834 2.5Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M3.66675 5.83334H16.3334",stroke:"#1A2230",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8.33325 10H14.9999",stroke:"#1A2230",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8.33325 13.3333H14.9999",stroke:"#1A2230",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5 10H5.83333",stroke:"#1A2230",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5 13.3333H5.83333",stroke:"#1A2230",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]})},header:{title:"任务管理",description:"创建和维护所有AI虚拟角色任务"}},user_permission:{label:"用户权限管理",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M7.91667 8.33333C9.5275 8.33333 10.8333 7.0275 10.8333 5.41667C10.8333 3.80584 9.5275 2.5 7.91667 2.5C6.30583 2.5 5 3.80584 5 5.41667C5 7.0275 6.30583 8.33333 7.91667 8.33333Z",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M13.75 3.33325C13.75 3.33325 14.6875 5.20825 13.75 7.49992",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M16.666 1.66675C16.666 1.66675 18.541 5.04175 16.666 9.16675",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.66602 17.0001V17.5001H14.166V17.0001C14.166 15.1332 14.166 14.1998 13.8027 13.4868C13.4831 12.8596 12.9732 12.3496 12.346 12.03C11.6329 11.6667 10.6995 11.6667 8.83268 11.6667H6.99935C5.13252 11.6667 4.1991 11.6667 3.48605 12.03C2.85884 12.3496 2.3489 12.8596 2.02933 13.4868C1.66602 14.1998 1.66602 15.1332 1.66602 17.0001Z",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M7.91667 8.33333C9.5275 8.33333 10.8333 7.0275 10.8333 5.41667C10.8333 3.80584 9.5275 2.5 7.91667 2.5C6.30583 2.5 5 3.80584 5 5.41667C5 7.0275 6.30583 8.33333 7.91667 8.33333Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M13.75 3.33325C13.75 3.33325 14.6875 5.20825 13.75 7.49992",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M16.666 1.66675C16.666 1.66675 18.541 5.04175 16.666 9.16675",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.66602 17.0001V17.5001H14.166V17.0001C14.166 15.1332 14.166 14.1998 13.8027 13.4868C13.4831 12.8596 12.9732 12.3496 12.346 12.03C11.6329 11.6667 10.6995 11.6667 8.83268 11.6667H6.99935C5.13252 11.6667 4.1991 11.6667 3.48605 12.03C2.85884 12.3496 2.3489 12.8596 2.02933 13.4868C1.66602 14.1998 1.66602 15.1332 1.66602 17.0001Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]})},header:{title:"用户权限管理",description:"管理所有用户权限"}},employee_info:{label:"员工信息管理",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M1.66992 10.03H5.07992",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.66992 6.57007H5.07992",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.66992 13.48H5.07992",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5301 2.5H4.13008C3.68825 2.5 3.33008 2.85817 3.33008 3.3V16.7C3.33008 17.1418 3.68825 17.5 4.13008 17.5H17.5301C17.9719 17.5 18.3301 17.1418 18.3301 16.7V3.3C18.3301 2.85817 17.9719 2.5 17.5301 2.5Z",fill:"none",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M14.7708 12.1001H8.10078C7.65895 12.1001 7.30078 12.4583 7.30078 12.9001V14.1801C7.30078 14.6219 7.65895 14.9801 8.10078 14.9801H14.7708C15.2126 14.9801 15.5708 14.6219 15.5708 14.1801V12.9001C15.5708 12.4583 15.2126 12.1001 14.7708 12.1001Z",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M11.4394 10.2C12.7262 10.2 13.7694 9.15686 13.7694 7.87004C13.7694 6.58322 12.7262 5.54004 11.4394 5.54004C10.1526 5.54004 9.10938 6.58322 9.10938 7.87004C9.10938 9.15686 10.1526 10.2 11.4394 10.2Z",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M1.66992 10.03H5.07992",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.66992 6.57007H5.07992",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.66992 13.48H5.07992",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5301 2.5H4.13008C3.68825 2.5 3.33008 2.85817 3.33008 3.3V16.7C3.33008 17.1418 3.68825 17.5 4.13008 17.5H17.5301C17.9719 17.5 18.3301 17.1418 18.3301 16.7V3.3C18.3301 2.85817 17.9719 2.5 17.5301 2.5Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M14.7708 12.1001H8.10078C7.65895 12.1001 7.30078 12.4583 7.30078 12.9001V14.1801C7.30078 14.6219 7.65895 14.9801 8.10078 14.9801H14.7708C15.2126 14.9801 15.5708 14.6219 15.5708 14.1801V12.9001C15.5708 12.4583 15.2126 12.1001 14.7708 12.1001Z",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M11.4394 10.2C12.7262 10.2 13.7694 9.15686 13.7694 7.87004C13.7694 6.58322 12.7262 5.54004 11.4394 5.54004C10.1526 5.54004 9.10938 6.58322 9.10938 7.87004C9.10938 9.15686 10.1526 10.2 11.4394 10.2Z",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]})},header:{title:"员工信息管理",description:"管理所有员工信息"}},ai_virtual_seat:{label:"访谈助手管理",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M10 5.31006V7.53006",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5399 7.53003H2.46992C2.02809 7.53003 1.66992 7.8882 1.66992 8.33003V16.7C1.66992 17.1419 2.02809 17.5 2.46992 17.5H17.5399C17.9818 17.5 18.3399 17.1419 18.3399 16.7V8.33003C18.3399 7.8882 17.9818 7.53003 17.5399 7.53003Z",fill:"none",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M6.25 11.4302V14.5002",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M13.75 14.4999V11.4299",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10.0009 5.08C10.7134 5.08 11.2909 4.50245 11.2909 3.79C11.2909 3.07755 10.7134 2.5 10.0009 2.5C9.28849 2.5 8.71094 3.07755 8.71094 3.79C8.71094 4.50245 9.28849 5.08 10.0009 5.08Z",fill:"none",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M10 5.31006V7.53006",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5399 7.53003H2.46992C2.02809 7.53003 1.66992 7.8882 1.66992 8.33003V16.7C1.66992 17.1419 2.02809 17.5 2.46992 17.5H17.5399C17.9818 17.5 18.3399 17.1419 18.3399 16.7V8.33003C18.3399 7.8882 17.9818 7.53003 17.5399 7.53003Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M6.25 11.4302V14.5002",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M13.75 14.4999V11.4299",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10.0009 5.08C10.7134 5.08 11.2909 4.50245 11.2909 3.79C11.2909 3.07755 10.7134 2.5 10.0009 2.5C9.28849 2.5 8.71094 3.07755 8.71094 3.79C8.71094 4.50245 9.28849 5.08 10.0009 5.08Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]})},header:{title:"访谈助手管理",description:"创建和维护所有访谈助手"}},analysis_assistant:{label:"分析助手管理",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:n().svg,children:[(0,o.jsx)("path",{d:"M10 5.31006V7.53006",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5399 7.53003H2.46992C2.02809 7.53003 1.66992 7.8882 1.66992 8.33003V16.7C1.66992 17.1419 2.02809 17.5 2.46992 17.5H17.5399C17.9818 17.5 18.3399 17.1419 18.3399 16.7V8.33003C18.3399 7.8882 17.9818 7.53003 17.5399 7.53003Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10.0009 5.08C10.7134 5.08 11.2909 4.50245 11.2909 3.79C11.2909 3.07755 10.7134 2.5 10.0009 2.5C9.28849 2.5 8.71094 3.07755 8.71094 3.79C8.71094 4.50245 9.28849 5.08 10.0009 5.08Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10 11.7V17.5H15.8C15.8 14.29 13.2 11.7 10 11.7Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M9.99922 11.7C6.78922 11.7 4.19922 14.3 4.19922 17.5H9.99922V11.7Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M10 5.31006V7.53006",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5399 7.53003H2.46992C2.02809 7.53003 1.66992 7.8882 1.66992 8.33003V16.7C1.66992 17.1419 2.02809 17.5 2.46992 17.5H17.5399C17.9818 17.5 18.3399 17.1419 18.3399 16.7V8.33003C18.3399 7.8882 17.9818 7.53003 17.5399 7.53003Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10.0009 5.08C10.7134 5.08 11.2909 4.50245 11.2909 3.79C11.2909 3.07755 10.7134 2.5 10.0009 2.5C9.28849 2.5 8.71094 3.07755 8.71094 3.79C8.71094 4.50245 9.28849 5.08 10.0009 5.08Z",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10 11.7V17.5H15.8C15.8 14.29 13.2 11.7 10 11.7Z",fill:"#171F2D",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M9.99922 11.7C6.78922 11.7 4.19922 14.3 4.19922 17.5H9.99922V11.7Z",fill:"#171F2D",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]})},header:{title:"分析助手管理",description:"创建和维护所有分析助手"}},task_board:{label:"任务执行监控",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M1.66992 2.5H18.3399",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10 15.5701V17.5001",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5399 5.03003H2.46992C2.02809 5.03003 1.66992 5.3882 1.66992 5.83003V14.77C1.66992 15.2119 2.02809 15.57 2.46992 15.57H17.5399C17.9818 15.57 18.3399 15.2119 18.3399 14.77V5.83003C18.3399 5.3882 17.9818 5.03003 17.5399 5.03003Z",fill:"none",stroke:"#323232",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M4.59961 12.2901L8.61961 9.35007L11.0096 11.8801L15.3996 8.32007",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M1.66992 2.5H18.3399",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M10 15.5701V17.5001",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M17.5399 5.03003H2.46992C2.02809 5.03003 1.66992 5.3882 1.66992 5.83003V14.77C1.66992 15.2119 2.02809 15.57 2.46992 15.57H17.5399C17.9818 15.57 18.3399 15.2119 18.3399 14.77V5.83003C18.3399 5.3882 17.9818 5.03003 17.5399 5.03003Z",fill:"white",stroke:"white",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M4.59961 12.2901L8.61961 9.35007L11.0096 11.8801L15.3996 8.32007",stroke:"#171F2D",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]})},header:{title:"任务执行监控",description:"实时多维度查看与监控访谈任务"}},report:{label:"访谈分析报告",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:n().svg,children:[(0,o.jsx)("path",{d:"M9.37 4.21997C5.58 4.21997 2.5 7.29997 2.5 11.09C2.5 14.88 5.58 17.96 9.37 17.96C13.16 17.96 16.24 14.88 16.24 11.09C16.24 11.09 16.24 11.09 16.24 11.08H9.37V4.21997Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M11.5508 2.04004V8.90004H18.4208C18.4208 5.11004 15.3408 2.04004 11.5508 2.04004Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:n().svg,children:[(0,o.jsx)("path",{d:"M9.37 4.21997C5.58 4.21997 2.5 7.29997 2.5 11.09C2.5 14.88 5.58 17.96 9.37 17.96C13.16 17.96 16.24 14.88 16.24 11.09C16.24 11.09 16.24 11.09 16.24 11.08H9.37V4.21997Z",fill:"white"}),(0,o.jsx)("path",{d:"M11.5508 2.04004V8.90004H18.4208C18.4208 5.11004 15.3408 2.04004 11.5508 2.04004Z",fill:"white"})]})},header:{title:"访谈分析报告",description:"查看员工/岗位下所有任务统计数据"}},model_manage:{label:"模型管理",icon:e=>{let{theme:t}=e;return"light"===t?(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M7.36992 2.5H2.91992C2.22992 2.5 1.66992 3.06 1.66992 3.75V9.03C1.66992 9.72 2.22992 10.28 2.91992 10.28H7.36992C8.05992 10.28 8.61992 9.72 8.61992 9.03V3.75C8.61992 3.06 8.05992 2.5 7.36992 2.5Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M7.36992 12.98H2.91992C2.22992 12.98 1.66992 13.54 1.66992 14.23V16.25C1.66992 16.94 2.22992 17.5 2.91992 17.5H7.36992C8.05992 17.5 8.61992 16.94 8.61992 16.25V14.23C8.61992 13.54 8.05992 12.98 7.36992 12.98Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M12.6309 17.5002H17.0809C17.7709 17.5002 18.3309 16.9402 18.3309 16.2502V10.9702C18.3309 10.2802 17.7709 9.72021 17.0809 9.72021H12.6309C11.9409 9.72021 11.3809 10.2802 11.3809 10.9702V16.2502C11.3809 16.9402 11.9409 17.5002 12.6309 17.5002Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M12.6309 7.02H17.0809C17.7709 7.02 18.3309 6.46 18.3309 5.77V3.75C18.3309 3.06 17.7709 2.5 17.0809 2.5H12.6309C11.9409 2.5 11.3809 3.06 11.3809 3.75V5.77C11.3809 6.46 11.9409 7.02 12.6309 7.02Z",stroke:"black",strokeOpacity:"0.8",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})]}):(0,o.jsxs)("svg",{className:n().svg,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M7.37012 12.9805C8.06003 12.9806 8.62012 13.5405 8.62012 14.2305V16.25C8.62011 16.9399 8.06002 17.4999 7.37012 17.5H2.91992C2.22993 17.5 1.66993 16.94 1.66992 16.25V14.2305C1.66992 13.5405 2.22992 12.9805 2.91992 12.9805H7.37012ZM17.0811 9.7207C17.771 9.72081 18.3311 10.2808 18.3311 10.9707V16.25C18.3311 16.9399 17.771 17.4999 17.0811 17.5H12.6309C11.9409 17.5 11.3809 16.94 11.3809 16.25V10.9707C11.3809 10.2807 11.9409 9.7207 12.6309 9.7207H17.0811ZM7.37012 2.5C8.06003 2.50011 8.62012 3.06007 8.62012 3.75V9.03027C8.61997 9.72008 8.05994 10.2802 7.37012 10.2803H2.91992C2.23001 10.2803 1.67007 9.72015 1.66992 9.03027V3.75C1.66992 3.06 2.22992 2.5 2.91992 2.5H7.37012ZM17.0811 2.5C17.771 2.50011 18.3311 3.06007 18.3311 3.75V5.76953C18.3311 6.45947 17.771 7.01943 17.0811 7.01953H12.6309C11.9409 7.01953 11.3809 6.45953 11.3809 5.76953V3.75C11.3809 3.06 11.9409 2.5 12.6309 2.5H17.0811Z",fill:"white"}),(0,o.jsx)("path",{d:"M7.37012 12.9805L7.37022 12.2805H7.37012V12.9805ZM8.62012 16.25L9.32012 16.25V16.25H8.62012ZM7.37012 17.5V18.2H7.37022L7.37012 17.5ZM2.91992 17.5L2.91992 18.2H2.91992V17.5ZM1.66992 16.25H0.969922V16.25L1.66992 16.25ZM2.91992 12.9805V12.2805H2.91992L2.91992 12.9805ZM17.0811 9.7207L17.0812 9.0207H17.0811V9.7207ZM17.0811 17.5V18.2H17.0812L17.0811 17.5ZM7.37012 2.5L7.37022 1.8H7.37012V2.5ZM8.62012 9.03027L9.32012 9.03042V9.03027H8.62012ZM7.37012 10.2803V10.9803H7.37022L7.37012 10.2803ZM2.91992 10.2803L2.91992 10.9803H2.91992V10.2803ZM1.66992 9.03027H0.969922V9.03042L1.66992 9.03027ZM2.91992 2.5V1.8H2.91992L2.91992 2.5ZM17.0811 2.5L17.0812 1.8H17.0811V2.5ZM17.0811 7.01953V7.71953H17.0812L17.0811 7.01953ZM7.37012 12.9805L7.37001 13.6805C7.67351 13.6805 7.92012 13.9272 7.92012 14.2305H8.62012H9.32012C9.32012 13.1538 8.44654 12.2806 7.37022 12.2805L7.37012 12.9805ZM8.62012 14.2305H7.92012V16.25H8.62012H9.32012V14.2305H8.62012ZM8.62012 16.25L7.92012 16.25C7.92011 16.5532 7.67349 16.8 7.37001 16.8L7.37012 17.5L7.37022 18.2C8.44655 18.1998 9.3201 17.3266 9.32012 16.25L8.62012 16.25ZM7.37012 17.5V16.8H2.91992V17.5V18.2H7.37012V17.5ZM2.91992 17.5L2.91992 16.8C2.61654 16.8 2.36993 16.5534 2.36992 16.25L1.66992 16.25L0.969922 16.25C0.969938 17.3266 1.84332 18.2 2.91992 18.2L2.91992 17.5ZM1.66992 16.25H2.36992V14.2305H1.66992H0.969922V16.25H1.66992ZM1.66992 14.2305H2.36992C2.36992 13.9271 2.61652 13.6805 2.91992 13.6805L2.91992 12.9805L2.91992 12.2805C1.84332 12.2805 0.969922 13.1539 0.969922 14.2305H1.66992ZM2.91992 12.9805V13.6805H7.37012V12.9805V12.2805H2.91992V12.9805ZM17.0811 9.7207L17.0809 10.4207C17.3844 10.4207 17.6311 10.6675 17.6311 10.9707H18.3311H19.0311C19.0311 9.89406 18.1575 9.02087 17.0812 9.0207L17.0811 9.7207ZM18.3311 10.9707H17.6311V16.25H18.3311H19.0311V10.9707H18.3311ZM18.3311 16.25H17.6311C17.6311 16.5532 17.3844 16.8 17.0809 16.8L17.0811 17.5L17.0812 18.2C18.1575 18.1998 19.0311 17.3266 19.0311 16.25H18.3311ZM17.0811 17.5V16.8H12.6309V17.5V18.2H17.0811V17.5ZM12.6309 17.5V16.8C12.3275 16.8 12.0809 16.5534 12.0809 16.25H11.3809H10.6809C10.6809 17.3266 11.5543 18.2 12.6309 18.2V17.5ZM11.3809 16.25H12.0809V10.9707H11.3809H10.6809V16.25H11.3809ZM11.3809 10.9707H12.0809C12.0809 10.6673 12.3275 10.4207 12.6309 10.4207V9.7207V9.0207C11.5543 9.0207 10.6809 9.8941 10.6809 10.9707H11.3809ZM12.6309 9.7207V10.4207H17.0811V9.7207V9.0207H12.6309V9.7207ZM7.37012 2.5L7.37001 3.2C7.67351 3.20005 7.92012 3.44677 7.92012 3.75H8.62012H9.32012C9.32012 2.67336 8.44654 1.80016 7.37022 1.8L7.37012 2.5ZM8.62012 3.75H7.92012V9.03027H8.62012H9.32012V3.75H8.62012ZM8.62012 9.03027L7.92012 9.03012C7.92005 9.33349 7.67327 9.58023 7.37001 9.58027L7.37012 10.2803L7.37022 10.9803C8.4466 10.9801 9.31989 10.1067 9.32012 9.03042L8.62012 9.03027ZM7.37012 10.2803V9.58027H2.91992V10.2803V10.9803H7.37012V10.2803ZM2.91992 10.2803L2.91992 9.58027C2.61676 9.58027 2.36999 9.33366 2.36992 9.03012L1.66992 9.03027L0.969922 9.03042C0.970153 10.1066 1.84326 10.9803 2.91992 10.9803L2.91992 10.2803ZM1.66992 9.03027H2.36992V3.75H1.66992H0.969922V9.03027H1.66992ZM1.66992 3.75H2.36992C2.36992 3.4466 2.61652 3.2 2.91992 3.2L2.91992 2.5L2.91992 1.8C1.84332 1.8 0.969922 2.6734 0.969922 3.75H1.66992ZM2.91992 2.5V3.2H7.37012V2.5V1.8H2.91992V2.5ZM17.0811 2.5L17.0809 3.2C17.3844 3.20005 17.6311 3.44677 17.6311 3.75H18.3311H19.0311C19.0311 2.67336 18.1575 1.80016 17.0812 1.8L17.0811 2.5ZM18.3311 3.75H17.6311V5.76953H18.3311H19.0311V3.75H18.3311ZM18.3311 5.76953H17.6311C17.6311 6.07276 17.3844 6.31948 17.0809 6.31953L17.0811 7.01953L17.0812 7.71953C18.1575 7.71937 19.0311 6.84617 19.0311 5.76953H18.3311ZM17.0811 7.01953V6.31953H12.6309V7.01953V7.71953H17.0811V7.01953ZM12.6309 7.01953V6.31953C12.3275 6.31953 12.0809 6.07293 12.0809 5.76953H11.3809H10.6809C10.6809 6.84613 11.5543 7.71953 12.6309 7.71953V7.01953ZM11.3809 5.76953H12.0809V3.75H11.3809H10.6809V5.76953H11.3809ZM11.3809 3.75H12.0809C12.0809 3.4466 12.3275 3.2 12.6309 3.2V2.5V1.8C11.5543 1.8 10.6809 2.6734 10.6809 3.75H11.3809ZM12.6309 2.5V3.2H17.0811V2.5V1.8H12.6309V2.5Z",fill:"white"})]})},header:{title:"模型管理",description:"管理所有AI模型接入点"}}}},77135:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var o=s(95155),r=s(97172),n=s(20148),i=s(68773);function a(e){let{message:t}=e;return(0,o.jsx)(n.A,{style:{border:"2px dashed #FBBC05",backgroundColor:"rgba(251, 188, 5, 0.25)",textAlign:"center",paddingTop:"35px",paddingBottom:"35px",marginBottom:"24px"},type:"warning",message:(0,o.jsxs)(i.A,{children:[(0,o.jsx)(r.A,{style:{color:"#975923"}}),(0,o.jsx)("span",{style:{color:"#975923"},children:t})]})})}},79471:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var o=s(35594),r=s(90603);let n={getTaskList:e=>{let{contactStatus:t,employeeId:s,...n}=e||{},i={...r.u,...n};return s&&(i.employeeId=s,t&&(i.contactStatus=t)),(null==e?void 0:e.postNumber)&&(i.postNumber=e.postNumber),(0,o.Jt)({url:"/api/tasks",data:i})},createTask:e=>(0,o.bE)({url:"/api/tasks",data:e}),updateTask:(e,t)=>(0,o.yJ)({url:"/api/tasks/".concat(t),data:e}),getTaskDetail:e=>(0,o.Jt)({url:"/api/tasks/".concat(e)}),deleteTask:e=>(0,o.yH)({url:"/api/tasks/".concat(e)}),startTask:e=>(0,o.bE)({url:"/api/tasks/".concat(e,"/start")}),stopTask:e=>(0,o.bE)({url:"/api/tasks/".concat(e,"/stop")}),notice:e=>(0,o.bE)({url:"/api/tasks/".concat(e,"/notice")}),getTaskStatusCount:e=>(0,o.Jt)({url:"/api/contacts/status/count",data:e}),getContactList:e=>(0,o.Jt)({url:"/api/contacts",data:e}),addContact:(e,t)=>(0,o.bE)({url:"/api/contacts/".concat(e,"/save"),data:t}),removeContact:(e,t)=>(0,o.yH)({url:"/api/contacts/".concat(e,"/remove"),data:t}),importContacts:(e,t)=>{let s=new FormData;return s.append("file",t),fetch("/api/contacts/".concat(e,"/import"),{method:"POST",body:s}).then(e=>e.json())},rerunReport:e=>(0,o.bE)({url:"/api/evalapp/repeat/run",data:e})}},81488:(e,t,s)=>{"use strict";s.d(t,{I:()=>o,O:()=>r});let o=e=>{let{url:t,params:s,isPage:o=!0}=e,r=t;return r="/ui".concat(r),o&&(r="".concat(r,".html")),s&&(r="".concat(r,"?").concat(new URLSearchParams(s).toString())),r},r=(e,t)=>t?(e/t*100).toFixed(2):"-"},81909:e=>{e.exports={RobotInfoCard:"RobotInfoCard_RobotInfoCard__AJN0j",processed:"RobotInfoCard_processed__yfChI",checkbox:"RobotInfoCard_checkbox__h5IC8",selected:"RobotInfoCard_selected__8ZE6H",button_group:"RobotInfoCard_button_group__QJivR"}},83761:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var o=s(95155);let r=e=>{let{className:t}=e;return(0,o.jsxs)("svg",{className:t,xmlns:"http://www.w3.org/2000/svg",width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",children:[(0,o.jsx)("path",{d:"M9.89587 5.72925H19.2709V15.1042",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M19.2709 5.72927L6.01259 18.9875",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}},85845:(e,t,s)=>{"use strict";s.d(t,{W:()=>o});var o=function(e){return e.Volcano="volcano",e.Aliyun="aliyun",e}({})},90603:(e,t,s)=>{"use strict";s.d(t,{u:()=>o}),s(35594);let o={order:"desc",sort:"updateTime"}},91199:(e,t,s)=>{"use strict";s.d(t,{A:()=>k});var o=s(95155),r=s(42592),n=s(68773),i=s(21614),a=s(82133),l=s(22810),d=s(79005),c=s(2796),u=s(12115),h=s(63056);let k=e=>{let{value:t,onChange:s,multiple:k=!1,drawerTitle:p="绑定分析助手",readonly:x=!1}=e,[j,C]=(0,u.useState)(!1),[g,L]=(0,u.useState)([]),{data:A,runAsync:H}=(0,r._O)();(0,u.useEffect)(()=>{"string"==typeof t&&L(t?t.split(","):[])},[t]),(0,u.useEffect)(()=>{H()},[]);let v=(0,u.useMemo)(()=>(null==A?void 0:A.success)?A.data.records:[],[A]),m=e=>{k?L(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e]):L([e])},w=v.filter(e=>g.includes(e.id));return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.A,{children:(0,o.jsx)(i.A,{style:{width:240},placeholder:"请选择分析助手",value:w.map(e=>e.id),open:!1,onClick:()=>C(!0),mode:k?"multiple":void 0,options:v.map(e=>({label:e.name,value:e.id})),disabled:x})}),(0,o.jsx)(a.A,{open:j,onClose:()=>C(!1),title:p,width:700,footer:x?null:(0,o.jsx)(l.A,{justify:"end",children:(0,o.jsxs)(n.A,{children:[(0,o.jsx)(d.Ay,{type:"primary",onClick:()=>{s&&s(g.join(",")),C(!1)},disabled:!g.length,children:"确定"}),(0,o.jsx)(d.Ay,{onClick:()=>C(!1),children:"取消"})]})}),children:(0,o.jsx)(l.A,{gutter:[16,16],children:v.map(e=>(0,o.jsx)(c.A,{span:8,children:(0,o.jsx)(h.A,{assistant:e,selectable:!x,selected:g.includes(e.id),onSelect:()=>m(e.id),readonly:!0,cardProps:{style:{borderColor:g.includes(e.id)?"#1677ff":void 0,boxShadow:g.includes(e.id)?"0 0 0 2px #1677ff22":void 0}}})},e.id))})})]})}},95458:e=>{e.exports={container:"BadgeTitle_container__3vD4X",name:"BadgeTitle_name__dkR9K",description:"BadgeTitle_description__Qy0lS",tag:"BadgeTitle_tag__zKgzy"}},95950:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>u,HI:()=>c,nW:()=>d});var o=s(95155),r=s(48921),n=s(72093),i=s(22810),a=s(2796);let l="linear-gradient(107deg, #2175F2 51.75%, #DAF0FE 240.63%)";var d=function(e){return e.PROCESSING="processing",e.FAILED="failed",e.INITIAL="initial",e.SUCCESS="success",e}({});let c={processing:"processing",failed:"failed,notexist,busy",initial:"initial",success:"success"};function u(e){var t,s,d;let{loading:c,data:u,processingProps:h,selectConfig:k}=e,{processing:p,notexist:x,busy:j,failed:C,initial:g,success:L}=null!=u?u:{busy:"0",failed:"0",initial:"0",notexist:"0",processing:"0",success:"0",total:"0"},{enable:A,onSelect:H,selectedKey:v="processing"}=null!=k?k:{};return(0,o.jsx)(n.A,{spinning:c,children:(0,o.jsxs)(i.A,{gutter:[16,16],style:{marginBottom:"24px"},children:[(0,o.jsx)(a.A,{sm:12,xxl:6,span:6,children:(0,o.jsx)(r.A,{badge:{color:"#23E4A6",text:"实时外呼中",style:{color:(null==h?void 0:null===(t=h.badgeProps)||void 0===t?void 0:t.textColor)||"#fff"}},cardBgColor:A&&"processing"===v?l:null==h?void 0:null===(s=h.cardProps)||void 0===s?void 0:s.bgColor,onClick:()=>{null==H||H("processing")},children:(0,o.jsx)(r.Q,{value:p,style:{color:(null==h?void 0:null===(d=h.numberProps)||void 0===d?void 0:d.textColor)||"#fff"}})})}),(0,o.jsx)(a.A,{sm:12,xxl:6,span:6,children:(0,o.jsx)(r.A,{badge:{status:"error",text:"实时未接通(空号/盲线/故障)"},cardBgColor:A&&"failed"===v?l:void 0,onClick:()=>{null==H||H("failed")},children:(0,o.jsx)(r.Q,{value:"".concat(+x+ +j+ +C)})})}),(0,o.jsx)(a.A,{sm:12,xxl:6,span:6,children:(0,o.jsx)(r.A,{badge:{status:"warning",text:"待外呼"},cardBgColor:A&&"initial"===v?l:void 0,onClick:()=>{null==H||H("initial")},children:(0,o.jsx)(r.Q,{value:g})})}),(0,o.jsx)(a.A,{sm:12,xxl:6,span:6,children:(0,o.jsx)(r.A,{badge:{text:"已完成",color:"#A8A8A8"},cardBgColor:A&&"success"===v?l:void 0,onClick:()=>{null==H||H("success")},children:(0,o.jsx)(r.Q,{value:L})})})]})})}},95988:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var o=s(95155),r=s(11432),n=s(99907);function i(e){let{children:t,...s}=e;return(0,o.jsx)(r.Ay,{theme:{components:{Tabs:{inkBarColor:"#171F2D",itemHoverColor:"#000",itemSelectedColor:"#000"}}},children:(0,o.jsx)(n.A,{...s,children:t})})}},96926:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var o=s(95155);let r=e=>{let{className:t}=e;return(0,o.jsxs)("svg",{className:t,xmlns:"http://www.w3.org/2000/svg",width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",children:[(0,o.jsx)("path",{d:"M15.1042 19.2708H5.72925V9.89575",stroke:"#010101",strokeOpacity:"0.8",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5.72925 19.2707L18.9875 6.01245",stroke:"#010101",strokeOpacity:"0.8",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}}}]);
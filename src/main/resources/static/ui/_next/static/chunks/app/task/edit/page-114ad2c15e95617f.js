(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9875],{10170:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var n=t(95155),r=t(41657);function l(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,n.jsx)("path",{d:"M8.75033 15.8334C12.6623 15.8334 15.8337 12.6621 15.8337 8.75008C15.8337 4.83808 12.6623 1.66675 8.75033 1.66675C4.83833 1.66675 1.66699 4.83808 1.66699 8.75008C1.66699 12.6621 4.83833 15.8334 8.75033 15.8334Z",stroke:"black",strokeWidth:"1.25",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M11.1066 5.97629C10.5034 5.37308 9.67008 5 8.74958 5C7.82912 5 6.99579 5.37308 6.39258 5.97629",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M13.8428 13.8423L17.3783 17.3778",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}function i(e){return(0,n.jsx)(r.A,{placeholder:"搜索",prefix:(0,n.jsx)(l,{}),style:{width:"300px"},...e})}},30213:e=>{e.exports={container:"page_container__7Igr4",section:"page_section___aRLb",callerNumberContent:"page_callerNumberContent__7Ct2J",callIn:"page_callIn__36n3g",callOut:"page_callOut__61oL6",bgImg:"page_bgImg__oFnWB"}},32383:()=>{},39244:(e,s,t)=>{"use strict";t.d(s,{A:()=>h});var n=t(95155),r=t(59575),l=t(68773),i=t(21614),a=t(82133),o=t(22810),c=t(79005),d=t(2796),u=t(12115),x=t(42628);let h=e=>{let{value:s,onChange:t,drawerTitle:h="绑定访谈助手",readonly:j=!1}=e,[p,A]=(0,u.useState)(!1),[g,m]=(0,u.useState)(s),{data:f,runAsync:v}=(0,r.oD)();(0,u.useEffect)(()=>{m(s)},[s]),(0,u.useEffect)(()=>{v()},[]);let y=(0,u.useMemo)(()=>(null==f?void 0:f.success)?f.data.records:[],[f]),C=e=>{m(e)},b=y.find(e=>e.id===g);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.A,{children:(0,n.jsx)(i.A,{style:{width:240},placeholder:"请选择访谈助手",value:b?b.id:void 0,open:!1,onClick:()=>A(!0),options:y.map(e=>({label:e.name,value:e.id})),disabled:j})}),(0,n.jsx)(a.A,{open:p,onClose:()=>A(!1),title:h,width:700,footer:j?null:(0,n.jsx)(o.A,{justify:"end",children:(0,n.jsxs)(l.A,{children:[(0,n.jsx)(c.Ay,{type:"primary",onClick:()=>{t&&g&&t(g),A(!1)},disabled:!g,children:"确定"}),(0,n.jsx)(c.Ay,{onClick:()=>A(!1),children:"取消"})]})}),children:(0,n.jsx)(o.A,{gutter:[16,16],children:y.map(e=>(0,n.jsx)(d.A,{span:8,children:(0,n.jsx)(x.A,{robot:e,selectable:!j,selected:g===e.id,onSelect:()=>C(e.id),readonly:!0,cardProps:{style:{borderColor:g===e.id?"#1677ff":void 0,boxShadow:g===e.id?"0 0 0 2px #1677ff22":void 0}}})},e.id))})})]})}},48362:(e,s,t)=>{"use strict";t.d(s,{dz:()=>i,jX:()=>a,t3:()=>r,vV:()=>l});var n=t(35594);function r(e){return(0,n.Jt)({url:"/api/folder",data:e})}function l(e){return(0,n.bE)({url:"/api/folder",data:e})}function i(e){return(0,n.Jt)({url:"/api/folder/".concat(e)})}function a(e){return(0,n.bE)({url:"/api/folder/addToFolder",data:e})}},49086:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M12 22C14.7614 22 17.2614 20.8807 19.0711 19.0711C20.8807 17.2614 22 14.7614 22 12C22 9.2386 20.8807 6.7386 19.0711 4.92893C17.2614 3.11929 14.7614 2 12 2C9.2386 2 6.7386 3.11929 4.92893 4.92893C3.11929 6.7386 2 9.2386 2 12C2 14.7614 3.11929 17.2614 4.92893 19.0711C6.7386 20.8807 9.2386 22 12 22Z",fill:"#23E4A6",stroke:"#23E4A6",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M9.95719 7.2417C10.1691 7.2417 10.3644 7.35662 10.4672 7.5419L11.1808 8.82722C11.2742 8.99551 11.2786 9.19907 11.1925 9.37124L10.5051 10.7461C10.5051 10.7461 10.7043 11.7702 11.538 12.6039C12.3717 13.4376 13.3924 13.6334 13.3924 13.6334L14.767 12.9461C14.9393 12.8599 15.143 12.8644 15.3114 12.958L16.6003 13.6746C16.7854 13.7776 16.9002 13.9727 16.9002 14.1845V15.6642C16.9002 16.4178 16.2003 16.9621 15.4862 16.7211C14.0198 16.2263 11.7434 15.2842 10.3006 13.8413C8.85773 12.3985 7.91559 10.1222 7.42076 8.65569C7.17984 7.94166 7.72412 7.2417 8.47769 7.2417H9.95719Z",fill:"white",stroke:"white",strokeLinejoin:"round"})]})}},50968:(e,s,t)=>{"use strict";t.d(s,{A:()=>u});var n=t(95155),r=t(48362),l=t(96030),i=t(28041),a=t(46742),o=t(41657),c=t(12115),d=t(32853);let u=e=>{let{onCreated:s,btnType:t="link",btnText:u="创建新任务组"}=e,x=(0,c.useRef)(null);return(0,n.jsxs)(d.A,{btnText:u,title:"创建新任务组",btnProps:{type:t,icon:(0,n.jsx)(l.A,{})},onOk:async()=>{var e,t,n,l;let a=null===(n=x.current)||void 0===n?void 0:null===(t=n.input)||void 0===t?void 0:null===(e=t.value)||void 0===e?void 0:e.trim();if(!a)return i.Ay.error("请输入新任务组名称"),!1;let o=await (0,r.vV)({name:a});return o.success?(i.Ay.success("创建成功"),null==s||s({id:o.data.id,name:o.data.name}),!0):"error"in o?(i.Ay.error((null==o?void 0:null===(l=o.error)||void 0===l?void 0:l.message)||"创建失败"),!1):void 0},children:[(0,n.jsx)(a.A.Text,{children:"确定添加新的任务组别"}),(0,n.jsx)(o.A,{style:{margin:"18px 0"},ref:x,placeholder:"请输入新任务组名称",maxLength:20})]})}},57394:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M12 22C14.7614 22 17.2614 20.8807 19.0711 19.0711C20.8807 17.2614 22 14.7614 22 12C22 9.2386 20.8807 6.7386 19.0711 4.92893C17.2614 3.11929 14.7614 2 12 2C9.2386 2 6.7386 3.11929 4.92893 4.92893C3.11929 6.7386 2 9.2386 2 12C2 14.7614 3.11929 17.2614 4.92893 19.0711C6.7386 20.8807 9.2386 22 12 22Z",fill:"#BEBEBE"}),(0,n.jsx)("path",{d:"M8 12L11 15L17 9",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}},61303:(e,s,t)=>{"use strict";t.d(s,{A:()=>p});var n=t(95155),r=t(10170),l=t(48362),i=t(41657),a=t(82133),o=t(22810),c=t(68773),d=t(79005),u=t(72093),x=t(89351),h=t(12115),j=t(50968);let p=e=>{var s;let{value:t,onChange:p}=e,[A,g]=(0,h.useState)(!1),[m,f]=(0,h.useState)(!1),[v,y]=(0,h.useState)([]),[C,b]=(0,h.useState)(""),[k,w]=(0,h.useState)(t),S=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";f(!0);try{let s=await (0,l.t3)({keyword:e,page:1,pageSize:100});s.success&&y(s.data.records||[])}finally{f(!1)}};(0,h.useEffect)(()=>{A&&S()},[A]);let I=e=>{w(e),null==p||p(e),g(!1)},L=(0,h.useMemo)(()=>[{label:"不加入任务组",value:""},...v.map(e=>({label:e.name||"-",value:e.id}))],[v]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.A,{style:{width:240,cursor:"pointer"},placeholder:"请选择任务分组",value:(null===(s=L.find(e=>e.value===k))||void 0===s?void 0:s.label)||"",readOnly:!0,onClick:()=>g(!0)}),(0,n.jsxs)(a.A,{open:A,onClose:()=>g(!1),title:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,n.jsx)("span",{children:"任务组选择"}),(0,n.jsx)(j.A,{btnType:"default",btnText:"创建新任务组",onCreated:()=>S(C)})]}),width:520,footer:(0,n.jsx)(o.A,{justify:"end",children:(0,n.jsxs)(c.A,{children:[(0,n.jsx)(d.Ay,{type:"primary",onClick:()=>{void 0!==k&&I(k)},children:"保存"}),(0,n.jsx)(d.Ay,{onClick:()=>g(!1),children:"取消"})]})}),children:[(0,n.jsx)(r.A,{value:C,onChange:e=>{b(e.target.value),S(e.target.value)},style:{width:"100%"}}),(0,n.jsx)(u.A,{spinning:m,children:(0,n.jsx)(x.Ay.Group,{style:{width:"100%"},value:k,onChange:e=>w(e.target.value),children:(0,n.jsx)("div",{style:{maxHeight:400,overflowY:"auto"},children:L.map(e=>(0,n.jsx)("div",{style:{display:"flex",alignItems:"center",height:48,borderBottom:"1px solid #f0f0f0",padding:"0 8px"},children:(0,n.jsx)(x.Ay,{value:e.value,children:e.label})},e.value))})})})]})]})}},61632:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M12 22C17.5229 22 22 17.5229 22 12C22 6.47715 17.5229 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5229 6.47715 22 12 22Z",fill:"#FF004D",stroke:"#FF004D",strokeWidth:"1.5",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M14.8284 9.17139L9.1715 14.8282",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M9.17165 9.17139L14.8285 14.8282",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}},64165:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var n=t(95155);function r(){return(0,n.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M12 22C14.7614 22 17.2614 20.8807 19.0711 19.0711C20.8807 17.2614 22 14.7614 22 12C22 9.2386 20.8807 6.7386 19.0711 4.92893C17.2614 3.11929 14.7614 2 12 2C9.2386 2 6.7386 3.11929 4.92893 4.92893C3.11929 6.7386 2 9.2386 2 12C2 14.7614 3.11929 17.2614 4.92893 19.0711C6.7386 20.8807 9.2386 22 12 22Z",fill:"#FBBC05",stroke:"#FBBC05",strokeLinejoin:"round"}),(0,n.jsxs)("g",{clipPath:"url(#clip0_180_29384)",children:[(0,n.jsx)("path",{d:"M12 18.6668C15.6819 18.6668 18.6667 15.6821 18.6667 12.0002C18.6667 8.31826 15.6819 5.3335 12 5.3335C8.3181 5.3335 5.33334 8.31826 5.33334 12.0002C5.33334 15.6821 8.3181 18.6668 12 18.6668Z",stroke:"white",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M12.0028 8L12.0024 12.0029L14.8289 14.8294",stroke:"white",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"clip0_180_29384",children:(0,n.jsx)("rect",{width:"16",height:"16",fill:"white",transform:"translate(4 4)"})})})]})}},72565:(e,s,t)=>{Promise.resolve().then(t.bind(t,75789))},75789:(e,s,t)=>{"use strict";t.r(s),t.d(s,{CallInWithLabel:()=>eO,CallOutWithLabel:()=>eH,default:()=>eN});var n=t(95155),r=t(35587),l=t(95988),i=t(32853),a=t(77135),o=t(35577),c=t(81488),d=t(71126),u=t(96926),x=t(83761),h=t(18123),j=t(85407),p=t(12115);let A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};var g=t(84021),m=p.forwardRef(function(e,s){return p.createElement(g.A,(0,j.A)({},e,{ref:s,icon:A}))}),f=t(97139),v=t(96030),y=t(59276),C=t(41657),b=t(46742),k=t(64787),w=t(32392),S=t(21382),I=t(72093),L=t(22810),T=t(2796),E=t(68773),_=t(79005),F=t(11432),B=t(92895),N=t(18198),W=t(48904),M=t(6457),O=t(7974),H=t(55474),D=t(99189),Y=t(21455),P=t.n(Y),R=t(73474),z=t.n(R),V=t(49113),U=t.n(V),Z=t(91199),J=t(39244),G=t(61303),q=t(76046),K=t(3301),X=t(6521),Q=t(79471),$=t(41379),ee=t(27656),es=t(69653),et=t(28041),en=t(54857),er=t(9365);let el=e=>{let{taskId:s}=e,[t,r]=(0,p.useState)(""),[l,i]=(0,p.useState)(""),[a,o]=(0,p.useState)(0),{runAsync:d}=(0,h.c9)(),{run:u,loading:x}=(0,es.A)(e=>Q.A.addContact(s,e),{manual:!0,onSuccess:e=>{if(e.success)et.Ay.success("添加成功"),r(""),i(""),o(e=>e+1);else{var s;et.Ay.error((null===(s=e.error)||void 0===s?void 0:s.message)||"添加失败")}},onError:()=>et.Ay.error("添加失败")}),{run:j,loading:A}=(0,es.A)(e=>Q.A.removeContact(s,U().pick(e,["externalId","name"])),{manual:!0,onSuccess:e=>{if(e.success)et.Ay.success("删除成功"),o(e=>e+1);else{var s;et.Ay.error((null===(s=e.error)||void 0===s?void 0:s.message)||"删除失败")}},onError:()=>et.Ay.error("删除失败")}),{run:g,loading:m}=(0,es.A)(e=>Q.A.importContacts(s,e),{manual:!0,onSuccess:e=>{e.success?(et.Ay.success("导入成功"),o(e=>e+1)):et.Ay.error(e.message||"导入失败")},onError:()=>et.Ay.error("导入失败")}),y=async()=>{if(!t.trim()||!l.trim()){et.Ay.warning("请填写完整信息");return}u({name:t,externalId:l})},b=async e=>{j(e)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(E.A,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,n.jsxs)(E.A,{children:[(0,n.jsx)(C.A,{placeholder:"工号",value:l,onChange:e=>i(e.target.value),style:{width:120}}),(0,n.jsx)(C.A,{placeholder:"姓名",value:t,onChange:e=>r(e.target.value),style:{width:120}}),(0,n.jsx)(K.A,{type:"primary",icon:(0,n.jsx)(v.A,{}),onClick:y,loading:x,children:"添加"})]}),(0,n.jsxs)(E.A,{children:[(0,n.jsx)(W.A,{name:"file",accept:".xlsx,.xls",customRequest:async e=>{g(e.file),e.onSuccess&&e.onSuccess({},e.file)},showUploadList:!1,children:(0,n.jsx)(_.Ay,{icon:(0,n.jsx)($.A,{}),loading:m,children:"Excel导入"})}),(0,n.jsx)(K.A,{icon:(0,n.jsx)(f.A,{}),href:(0,c.I)({url:"/contact_template.xlsx",isPage:!1}),style:{border:"none"},children:"下载模版"})]})]}),(0,n.jsx)(X.A,{getList:d,renderItem:e=>(0,n.jsxs)("div",{style:{width:"100%",padding:"0 10px"},children:[(0,n.jsxs)(E.A,{style:{width:"100%",display:"flex",justifyContent:"space-between"},size:"large",children:[(0,n.jsxs)(E.A,{size:"large",children:[(0,n.jsx)("span",{style:{color:"#888"},children:e.externalId}),(0,n.jsx)("span",{style:{fontWeight:500},children:e.name})]}),(0,n.jsx)(en.A,{title:"确定删除该联系人？",onConfirm:()=>b(e),okText:"删除",cancelText:"取消",okButtonProps:{loading:A},children:(0,n.jsx)(ee.A,{})},"delete-confirm")]}),(0,n.jsx)(er.A,{style:{margin:"10px 0"}})]},e.externalId),params:{taskId:s},refreshDeps:[a,s],gutter:0,maxHeight:300})]})};var ei=t(30213),ea=t.n(ei),eo=t(95950),ec=t(59575),ed=t(78444),eu=t(13691),ex=t.n(eu),eh=t(57394),ej=t(61632),ep=t(64165),eA=t(49086),eg=t(85605),em=t(10819),ef=t(55750),ev=t(16419),ey=t(71349),eC=t(82133),eb=t(53096),ek=t(7660);let{Title:ew,Text:eS}=b.A;function eI(e){let{selectedStatus:s,contact:t,task:r,robot:l}=e,{name:i,status:a,phone:c,durationText:d,externalId:u}=t,{runAsync:x}=(0,eg.H)(),[h,j]=(0,p.useState)(!1),[A,g]=(0,p.useState)([]),[m,v]=(0,p.useState)(!1),[y,C]=(0,p.useState)(!1),w=(0,p.useRef)(null),S=(0,p.useRef)(null),{message:I}=k.A.useApp();(0,p.useEffect)(()=>{if(h&&u&&"processing"===a){let{protocol:e,host:s}=window.location,t=new WebSocket("".concat("https:"===e?"wss:":"ws:","//").concat(s,"/api/ws/").concat(u));return t.onopen=()=>{console.log("WebSocket连接已建立"),v(!0)},t.onmessage=e=>{try{var s,t;let n=JSON.parse(e.data),r={id:n.id,content:null!==(s=n.content)&&void 0!==s?s:"",userId:n.userId,userType:null!==(t=n.userType)&&void 0!==t?t:"robot",createTime:n.createTime};g(e=>[...e,r])}catch(e){console.error("解析WebSocket消息失败:",e)}},t.onerror=e=>{console.error("WebSocket错误:",e),v(!1)},t.onclose=()=>{console.log("WebSocket连接已关闭"),v(!1)},w.current=t,()=>{(t.readyState===WebSocket.OPEN||t.readyState===WebSocket.CONNECTING)&&t.close()}}},[h,u]),(0,p.useEffect)(()=>{!h&&(g([]),w.current&&(w.current.readyState===WebSocket.OPEN||w.current.readyState===WebSocket.CONNECTING)&&(w.current.close(),v(!1)))},[h]);let F=()=>{S.current&&S.current.scrollIntoView({behavior:"smooth"})};(0,p.useEffect)(()=>{h&&A.length>0&&F()},[A]);let{icon:B,color:N,desc:W}=(()=>{let e=null,t="",r="";switch(s){case"success":t="#BEBEBE",r="通话已完成",e=(0,n.jsx)(eh.A,{});break;case"failed":e=(0,n.jsx)(ej.A,{}),t="#FF004D",r="notexist"===a?"空号":"busy"===a?"占线":"失败";break;case"processing":e=(0,n.jsx)(eA.A,{}),t="#23E4A6",r="正在通话中";break;case"initial":e=(0,n.jsx)(ep.A,{}),t="#FBBC05",r="通话待进行"}return{icon:e,color:t,desc:r}})(),O=e=>{let s="robot"===e.userType,t=ex()(e.createTime).format("YYYY-MM-DD HH:mm:ss");return(0,n.jsx)(L.A,{justify:s?"start":"end",style:{marginBottom:16,width:"100%"},children:(0,n.jsxs)(E.A,{align:"start",style:{maxWidth:"80%"},children:[s&&(0,n.jsx)(ed.A,{icon:(0,n.jsx)(em.A,{}),style:{backgroundColor:"#1677ff"}}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{background:s?"#f0f2f5":"#1677ff",color:s?"#000":"#fff",padding:"8px 12px",borderRadius:"8px",borderTopLeftRadius:s?0:"8px",borderTopRightRadius:s?"8px":0,wordBreak:"break-word"},children:e.content}),(0,n.jsx)(eS,{type:"secondary",style:{fontSize:12},children:t})]}),!s&&(0,n.jsx)(ed.A,{icon:(0,n.jsx)(ef.A,{}),style:{backgroundColor:"#87d068"}})]})},e.id)},H=s===eo.nW.PROCESSING||s===eo.nW.SUCCESS,D=async()=>{try{C(!0);let e=1,s=0,n=[];for(;;){let l=await x({contactId:t.id,taskId:r.id,page:e,pageSize:100});if(s=l.data.total,!l.data.records||0===l.data.records.length||(n=[...n,...l.data.records],100*e>=s))break;e++}let a=n.map(e=>({时间:ex()(e.createTime).format("YYYY-MM-DD HH:mm:ss"),发送者:"robot"===e.userType?"AI助手":"用户",内容:e.content,访谈助手名称:l.name,访谈助手id:l.id,任务名称:r.name,任务id:r.id,联系人名称:i,号码:c})),o=ek.Wp.book_new(),d=ek.Wp.json_to_sheet(a);ek.Wp.book_append_sheet(o,d,"聊天记录"),ek._h(o,"".concat(r.name,"_").concat(i,"_").concat(c,".xlsx")),I.success("下载成功")}catch(e){console.error("下载失败:",e),I.error("下载失败，请重试")}finally{C(!1)}};return(0,n.jsxs)(k.A,{children:[(0,n.jsxs)(ey.A,{children:[(0,n.jsxs)(L.A,{justify:"space-between",children:[(0,n.jsx)(T.A,{children:(0,n.jsxs)(E.A,{size:"small",children:[B,(0,n.jsx)(M.A,{title:"客户名称/设备名称",children:(0,n.jsx)(ew,{level:5,children:i})}),(0,n.jsx)(ew,{level:5,children:"/"}),(0,n.jsx)(M.A,{title:"号码",children:(0,n.jsx)(ew,{level:5,ellipsis:!0,children:c})})]})}),(0,n.jsx)(T.A,{children:(0,n.jsx)(o.A,{color:N,children:W})})]}),(0,n.jsx)(eS,{type:"secondary",style:{marginLeft:"32px"},children:s===eo.nW.INITIAL?"暂无数据":ex()(t.createTime).format("MM/DD HH:mm")}),d&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(er.A,{type:"vertical"}),(0,n.jsx)(eS,{type:"secondary",children:d})]}),H&&(0,n.jsx)(L.A,{justify:"end",children:(0,n.jsx)(_.Ay,{onClick:()=>j(!0),children:"日志"})})]}),(0,n.jsxs)(eC.A,{open:h,onClose:()=>j(!1),title:"日志详情",width:400,children:[(0,n.jsxs)(E.A,{size:"small",style:{display:"flex",alignItems:"baseline"},children:[(0,n.jsx)(b.A.Title,{level:5,children:"历史聊天记录"}),y?(0,n.jsx)(ev.A,{}):(0,n.jsx)(M.A,{title:"下载历史聊天记录",children:(0,n.jsx)(f.A,{onClick:D})})]}),h&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(X.A,{renderItem:O,getList:x,params:{userId:u,taskId:r.id}}),"processing"===a&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(er.A,{}),(0,n.jsxs)("div",{children:[(0,n.jsxs)(b.A.Title,{level:5,children:["实时聊天记录",m&&(0,n.jsx)(o.A,{color:"#23E4A6",style:{marginLeft:8},children:"已连接"}),!m&&(0,n.jsx)(o.A,{color:"#FF004D",style:{marginLeft:8},children:"未连接"})]}),A.length>0?(0,n.jsxs)("div",{children:[A.map(O),(0,n.jsx)("div",{ref:S})]}):(0,n.jsx)(eb.A,{description:"暂无实时对话记录"})]})]})]})]})]})}function eL(e){let{task:s,statusLabel:t}=e,{data:r,run:l,loading:a}=(0,ec.hU)(),{runAsync:o,loading:c}=(0,h.V8)(),{runAsync:d}=(0,h.c9)(),[u,x]=(0,p.useState)(eo.nW.PROCESSING),{message:j}=k.A.useApp(),{robotId:A,type:g,status:m,beginTime:f}=s,v=(null==r?void 0:r.success)?r.data:null,{data:y,loading:C,runAsync:b}=(0,h.Ru)();(0,p.useEffect)(()=>{A&&l(A)},[A,l]),(0,p.useEffect)(()=>{s.id&&b({taskId:s.id,robotId:A})},[s.id,A,b]);let w="initial"===m||"completed"===m;return(0,n.jsx)("div",{children:(0,n.jsxs)(k.A,{children:[(0,n.jsxs)(L.A,{align:"middle",justify:w?"space-between":"start",children:[(0,n.jsxs)(T.A,{span:12,children:[a?(0,n.jsx)(I.A,{}):(0,n.jsxs)(E.A,{children:[(0,n.jsx)(ed.A,{src:null==v?void 0:v.avatar}),null==v?void 0:v.name]}),(0,n.jsx)(er.A,{type:"vertical"}),"inbound"===g?(0,n.jsx)(eO,{}):(0,n.jsx)(eH,{}),(0,n.jsx)(er.A,{type:"vertical"}),"initial"===m?t:"".concat(ex()(f).format("YYYY-MM-DD HH:mm:ss")," 启动")]}),w&&(0,n.jsx)(T.A,{children:(0,n.jsxs)(E.A,{children:[(0,n.jsx)(i.A,{btnText:"删除任务",title:"确定删除该任务？",description:"任务一旦删除，无法恢复",onOk:async()=>(console.log("删除任务"),!0)}),"initial"===m&&(0,n.jsx)(i.A,{btnText:"启动任务",title:"确定启动该任务",btnProps:{loading:c,type:"primary"},description:"任务一旦启动，无法终止",onOk:async()=>{let e=await o(s.id);return e.success?(j.success("启动成功"),!0):(j.error(e.error.message),!1)}})]})})]}),(0,n.jsx)(L.A,{style:{marginTop:"24px"},children:(0,n.jsx)(T.A,{span:24,children:(0,n.jsx)(eo.Ay,{loading:C,data:y[s.id],processingProps:{cardProps:"initial"===m?{bgColor:"linear-gradient(111deg, #41D189 52.04%, rgba(50, 255, 50, 0.10) 179.69%)"}:{}},selectConfig:{enable:!0,onSelect:e=>{x(e)},selectedKey:u}})})}),(0,n.jsx)(X.A,{refreshDeps:[u],getList:d,params:{status:eo.HI[u],robotId:A,taskId:s.id},gutter:[16,16],renderItem:e=>(0,n.jsx)(T.A,{span:8,children:(0,n.jsx)(eI,{selectedStatus:u,contact:e,task:s,robot:v})},e.id)})]})})}let{Content:eT}=y.A,{TextArea:eE}=C.A,{Title:e_,Text:eF}=b.A;var eB=function(e){return e.CONFIG="config",e.STATISTIC="statistic",e}(eB||{});function eN(){return(0,n.jsx)(p.Suspense,{fallback:(0,n.jsx)("div",{children:"Loading..."}),children:(0,n.jsx)(eM,{})})}let eW=e=>{switch(e){case"initial":return{color:"#FBBC05",label:"待启动"};case"processing":return{color:"#23E4A6",label:"进行中"};case"completed":return{color:"#BEBEBE",label:"已结束"}}};function eM(){var e,s,t;let{message:u}=k.A.useApp(),x=(0,q.useSearchParams)(),j=x.get("id"),A=x.get("robotId"),[g,y]=(0,p.useState)("edit"),[b,Y]=(0,p.useState)(""),[R,V]=(0,p.useState)(""),[K,X]=(0,p.useState)(60),[Q,$]=(0,p.useState)(!1),[ee,es]=(0,p.useState)("config"),[et,en]=(0,p.useState)(!0),er=(0,q.useRouter)(),{runAsync:ei,loading:eo}=(0,h.ZY)(),{runAsync:ec,loading:ed}=(0,h.K)(),{runAsync:eu,loading:ex}=(0,h.AK)(),{runAsync:eh}=(0,h.Mp)(),{runAsync:ej,data:ep,loading:eA}=(0,h.Mj)(),eg=(null==ep?void 0:ep.success)?ep.data:{},em=null==eg?void 0:eg.id,[ef]=w.A.useForm(),ev=null!==(e=(0,D.FH)("callerNumber",ef))&&void 0!==e?e:"",ey=(null!==(s=null==ev?void 0:ev.split(","))&&void 0!==s?s:[]).filter(Boolean),eC=(0,D.FH)("bgUrl",ef),eb=eW(null!==(t=null==eg?void 0:eg.status)&&void 0!==t?t:"initial"),ek=async e=>{var s;let t={name:b,desc:R,inboundConfig:JSON.stringify({holdTalkTimeout:K,showAudioWave:Q}),...e,callerNumber:ey.filter(e=>"on"!==e).join(","),contacts:(null!==(s=ef.getFieldValue("contacts"))&&void 0!==s?s:[]).map(e=>({externalId:e.externalId,name:e.name})),endTime:e.endTime?P()(e.endTime).utc().format():void 0},n=em?await ec(t,em):await ei(t);n.success?(y("view"),u.success("保存成功"),er.push((0,c.I)({url:d.Nv.TaskList}))):u.error(n.error.message)};return(0,p.useEffect)(()=>{P().extend(z()),j?ej(j).then(e=>{if(e.success){let{name:s,desc:t,inboundConfig:n}=e.data;Y(s),V(t),y("view"),en(!1),ef.setFieldsValue({...e.data,endTime:e.data.endTime?P()(e.data.endTime):void 0});let r=JSON.parse(n||"{}");X(r.holdTalkTimeout||60),$(r.showAudioWave||!1)}}):A&&ef.setFieldValue("robotId",A)},[j,A]),(0,n.jsx)(eT,{children:(0,n.jsx)(I.A,{spinning:j&&et&&(!ep||eA),children:(0,n.jsx)(k.A,{children:(0,n.jsxs)(L.A,{gutter:12,children:[(0,n.jsx)(T.A,{style:{paddingTop:"8px"},children:(0,n.jsx)(m,{onClick:()=>{er.back()}})}),(0,n.jsxs)(T.A,{span:23,children:[(0,n.jsx)(L.A,{style:{marginBottom:"60px"},gutter:12,children:"view"===g?(0,n.jsxs)(T.A,{span:23,children:[(0,n.jsxs)(L.A,{justify:"space-between",children:[(0,n.jsx)(T.A,{children:(0,n.jsx)(e_,{level:4,children:b})}),(0,n.jsx)(T.A,{children:(0,n.jsxs)(E.A,{children:[(0,n.jsx)(_.Ay,{onClick:()=>y("edit"),loading:ed,children:"编辑"}),(0,n.jsx)(_.Ay,{type:"primary",onClick:()=>{S.A.confirm({title:"发送钉钉任务提醒",content:"确定要通过钉钉向所有未完成访谈任务的员工发送任务提醒吗？",onOk:async()=>{console.log(await eh(em))}})},children:"发送钉钉通知"}),"statistic"===ee&&(0,n.jsx)(_.Ay,{icon:(0,n.jsx)(f.A,{}),type:"link",href:"/api/tasks/".concat(j,"/conversation/export"),children:"批量导出"})]})})]}),(0,n.jsx)(eF,{type:"secondary",children:R})]}):(0,n.jsxs)(T.A,{span:23,children:[(0,n.jsxs)(L.A,{justify:"space-between",children:[(0,n.jsx)(T.A,{children:(0,n.jsx)(F.Ay,{theme:{components:{Input:{colorTextPlaceholder:"#000"}}},children:(0,n.jsx)(C.A,{placeholder:"请输入访谈任务名称",variant:"borderless",style:{paddingLeft:0,fontSize:"20px",fontWeight:"500"},value:b,onChange:e=>{Y(e.target.value)}})})}),(0,n.jsx)(_.Ay,{type:"primary",loading:eo,onClick:async()=>{if(em&&U().isEqual(b,null==eg?void 0:eg.name)&&U().isEqual(R,null==eg?void 0:eg.desc)){y("view");return}let e={name:b,desc:R},s=em?await ec(e,em):await ei(e);s.success?(y("view"),u.success("保存成功"),ej(null==s?void 0:s.data.id)):u.error(s.message)},children:"完成"})]}),(0,n.jsx)(eE,{style:{marginTop:"20px"},placeholder:"请输入访谈任务简介",autoSize:{minRows:2,maxRows:6},value:R,onChange:e=>{V(e.target.value)}})]})}),j&&(0,n.jsx)(l.A,{items:[{key:"config",label:"任务配置"},{key:"statistic",label:(0,n.jsxs)(E.A,{children:["任务追踪",(0,n.jsx)(o.A,{color:eb.color,children:eb.label})]})}],onChange:e=>{es(e)}}),"config"===ee&&(0,n.jsxs)(n.Fragment,{children:["processing"===eg.status&&(0,n.jsx)(a.A,{message:"该任务正在进行中，暂不支持编辑配置"}),"completed"===eg.status&&(0,n.jsx)(a.A,{message:"该任务已结束，暂不支持编辑配置"}),(0,n.jsx)(w.A,{layout:"vertical",form:ef,onFinish:ek,initialValues:{type:"inbound",...eg},children:(0,n.jsxs)(L.A,{gutter:[24,24],align:"stretch",children:[(0,n.jsx)(T.A,{span:24,children:(0,n.jsx)(r.A,{title:"任务分组设置",children:(0,n.jsx)(w.A.Item,{label:"任务组别选择",name:"folderId",children:(0,n.jsx)(G.A,{})})})}),(0,n.jsx)(T.A,{span:24,children:(0,n.jsx)(r.A,{title:"任务设定",children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(w.A.Item,{label:"",children:(0,n.jsx)(B.A,{checked:Q,onChange:e=>$(e.target.checked),children:"是否显示声音波纹"})}),(0,n.jsx)(w.A.Item,{label:"超时未对话，则结束通话",children:(0,n.jsx)(N.A,{placeholder:"请输入",addonAfter:"秒",min:10,value:K,onChange:e=>X(e)})}),(0,n.jsx)(w.A.Item,{label:(0,n.jsxs)("span",{children:["背景图片",(0,n.jsx)("span",{style:{fontSize:"13px",color:"#999"},children:"（ 建议尺寸大小 750px \xd7 1334px ）"})]}),name:"bgUrl",children:(0,n.jsx)(W.A,{showUploadList:!1,maxCount:1,action:"/api/image/upload",accept:"image/*",className:ea().bgImg,beforeUpload:e=>{let s=e.size/1024/1024<5;return s||u.error("图片大小不超过5MB"),s},onChange:async e=>{"uploading"!==e.file.status&&("done"===e.file.status?e.file.response.success?ef.setFieldValue("bgUrl",e.file.response.data.url):u.error("".concat(e.file.name," 图片上传失败")):"error"===e.file.status&&u.error("".concat(e.file.name," 图片上传失败")))},listType:"picture-card",children:eC?(0,n.jsx)(M.A,{title:"点击更换背景图片",children:(0,n.jsx)("div",{style:{height:"100%"},children:(0,n.jsx)(O.A,{src:eC,alt:"背景图片",preview:!1,style:{height:"100%"}})})}):(0,n.jsx)(M.A,{title:"点击更换背景图片",children:(0,n.jsx)(v.A,{})})})}),(0,n.jsx)(w.A.Item,{label:"预计完成时间",name:"endTime",children:(0,n.jsx)(H.A,{showTime:!0,format:"YYYY-MM-DD HH:mm:ss"})})]})})}),em&&(0,n.jsx)(T.A,{span:12,children:(0,n.jsx)(r.A,{title:"员工配置",className:ea().section,children:(0,n.jsx)(el,{taskId:j})})}),(0,n.jsx)(T.A,{span:em?12:24,children:(0,n.jsxs)(r.A,{title:"任务指派",className:ea().section,children:[(0,n.jsx)(w.A.Item,{name:"robotId",label:"绑定访谈助手",children:(0,n.jsx)(J.A,{value:ef.getFieldValue("robotId"),onChange:e=>ef.setFieldValue("robotId",e)})}),(0,n.jsx)(w.A.Item,{name:"evalAppIds",label:"绑定分析助手",children:(0,n.jsx)(Z.A,{value:ef.getFieldValue("evalAppIds"),onChange:e=>ef.setFieldValue("evalAppIds",e),multiple:!0})})]})})]})}),(0,n.jsxs)(L.A,{justify:(null==eg?void 0:eg.status)==="initial"?"space-between":"start",style:{marginTop:"20px"},children:[(0,n.jsxs)(E.A,{size:16,children:[(0,n.jsx)(_.Ay,{type:"primary",onClick:ef.submit,loading:ed||eo,children:"保存"}),(0,n.jsx)(_.Ay,{onClick:()=>{er.push((0,c.I)({url:d.Nv.TaskList}))},children:"取消"})]}),(null==eg?void 0:eg.status)==="initial"&&(0,n.jsx)(i.A,{btnText:"删除任务",title:"确定删除该任务？",onOk:async()=>{let e=await eu(em);return e.success?(u.success("删除成功"),er.push((0,c.I)({url:d.Nv.TaskList})),!0):(u.error(e.error.message),!1)},btnProps:{loading:ex},description:"任务一旦删除，无法恢复"})]})]}),"statistic"===ee&&(0,n.jsx)(eL,{task:eg,statusLabel:eb.label})]})]})})})})}function eO(){return(0,n.jsxs)(E.A,{children:[(0,n.jsx)("div",{className:ea().callIn,children:(0,n.jsx)(u.A,{})}),(0,n.jsx)("span",{children:"网络呼入"})]})}function eH(){return(0,n.jsxs)(E.A,{children:[(0,n.jsx)("div",{className:ea().callOut,children:(0,n.jsx)(x.A,{})}),(0,n.jsx)("span",{children:"网络呼出"})]})}},83686:()=>{},85605:(e,s,t)=>{"use strict";t.d(s,{H:()=>i});var n=t(69653),r=t(35594);let l={getConversationList:e=>(0,r.Jt)({url:"/api/conversations",data:e})},i=()=>(0,n.A)(l.getConversationList,{manual:!0})}},e=>{var s=s=>e(e.s=s);e.O(0,[6772,838,402,3740,4935,586,3524,2211,6222,2602,3520,9907,3288,1509,1349,9786,4439,5585,4787,3840,2392,2342,4156,4338,5799,8198,6613,3864,3889,8441,6587,7358],()=>s(72565)),_N_E=e.O()}]);
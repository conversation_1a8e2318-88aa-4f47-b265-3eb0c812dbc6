(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9816],{9950:(e,t,n)=>{Promise.resolve().then(n.bind(n,82951))},10170:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var a=n(95155),i=n(41657);function d(){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,a.jsx)("path",{d:"M8.75033 15.8334C12.6623 15.8334 15.8337 12.6621 15.8337 8.75008C15.8337 4.83808 12.6623 1.66675 8.75033 1.66675C4.83833 1.66675 1.66699 4.83808 1.66699 8.75008C1.66699 12.6621 4.83833 15.8334 8.75033 15.8334Z",stroke:"black",strokeWidth:"1.25",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M11.1066 5.97629C10.5034 5.37308 9.67008 5 8.74958 5C7.82912 5 6.99579 5.37308 6.39258 5.97629",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M13.8428 13.8423L17.3783 17.3778",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}function r(e){return(0,a.jsx)(i.A,{placeholder:"搜索",prefix:(0,a.jsx)(d,{}),style:{width:"300px"},...e})}},35594:(e,t,n)=>{"use strict";n.d(t,{Jt:()=>r,bE:()=>o,yH:()=>l,yJ:()=>s});var a=n(43932),i=n.n(a);function d(e,t){return Promise.resolve(i().ajax({method:e,url:t.path||t.url,data:"GET"===e?t.data:JSON.stringify(t.data),contentType:"application/json;charset=UTF-8"}))}let r=e=>d("GET",e),o=e=>d("POST",e),s=e=>d("PUT",e),l=e=>d("DELETE",e)},36564:(e,t,n)=>{"use strict";n.d(t,{A:()=>x});var a=n(95155),i=n(96030),d=n(46742),r=n(22810),o=n(2796),s=n(11432),l=n(79005),p=n(68773),u=n(12115),c=n(10170);let{Title:h,Text:y}=d.A;function x(e){let{title:t,description:n,btn:d,onSearch:x,restSearch:m,customBtn:k,extraFilter:j,middleContent:w}=e,[g,A]=(0,u.useState)(""),{text:I,onClick:b,loading:f}=null!=d?d:{};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(r.A,{justify:"space-between",style:{marginBottom:"24px"},align:"middle",children:[(0,a.jsxs)(o.A,{children:[(0,a.jsx)(h,{level:2,children:t}),(0,a.jsx)(y,{type:"secondary",children:n})]}),(0,a.jsxs)(s.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:[k,d&&(0,a.jsx)(l.Ay,{type:"primary",icon:(0,a.jsx)(i.A,{}),onClick:b,loading:f,children:I})]})]}),w,(0,a.jsxs)(p.A,{size:"middle",children:[x&&(0,a.jsx)(c.A,{onChange:e=>{A(e.target.value)},onPressEnter:()=>{x(g)},...m}),j]})]})}},50759:(e,t,n)=>{"use strict";n.d(t,{dA:()=>s,YJ:()=>r,FO:()=>o});var a=n(69653),i=n(35594);let d={getEmployeeList:e=>(0,i.Jt)({url:"/api/employee",data:e}),getPositionReportList:e=>(0,i.Jt)({path:"/api/employee/postNumber",data:e}),getEmployeeDetail:e=>(0,i.Jt)({url:"/api/employee/".concat(e)})},r=()=>(0,a.A)(d.getEmployeeList,{manual:!0}),o=()=>(0,a.A)(d.getPositionReportList,{manual:!0}),s=()=>(0,a.A)(d.getEmployeeDetail,{manual:!0})},82951:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>h});var a=n(95155),i=n(36564),d=n(71126),r=n(50759),o=n(59276),s=n(28041),l=n(68874),p=n(12115);let{Content:u}=o.A,c=[{title:"姓名",dataIndex:"name",key:"name",width:100},{title:"员工工号",dataIndex:"jobNumber",key:"jobNumber",width:120},{title:"一级部门",dataIndex:"dept1",key:"dept1",width:120},{title:"二级部门",dataIndex:"dept2",key:"dept2",width:120},{title:"三级部门",dataIndex:"dept3",key:"dept3",width:120},{title:"四级部门",dataIndex:"dept4",key:"dept4",width:120},{title:"五级部门",dataIndex:"dept5",key:"dept5",width:120},{title:"六级部门",dataIndex:"dept6",key:"dept6",width:120},{title:"所属条线",dataIndex:"lineName",key:"lineName",width:120},{title:"个人岗位",dataIndex:"personalPost",key:"personalPost",width:120},{title:"标准岗位",dataIndex:"standardPost",key:"standardPost",width:120},{title:"个人职级",dataIndex:"personalRank",key:"personalRank",width:120},{title:"岗位号码",dataIndex:"postNumber",key:"postNumber",width:120},{title:"岗位职责内容",dataIndex:"jobContent",key:"jobContent",width:200},{title:"钉钉Unionid",dataIndex:"unionId",key:"unionId",width:200}];function h(){let[e,t]=(0,p.useState)({current:1,pageSize:10}),[n,o]=(0,p.useState)(""),{data:h,loading:y,error:x,run:m}=(0,r.YJ)();return(0,p.useEffect)(()=>{m({page:e.current,pageSize:e.pageSize,keyword:n})},[e.current,e.pageSize,n]),(0,p.useEffect)(()=>{x&&s.Ay.error("员工信息加载失败")},[x]),(0,a.jsxs)(u,{children:[(0,a.jsx)(i.A,{title:d.u4[d.GT.EmployeeInfo].header.title,description:d.u4[d.GT.EmployeeInfo].header.description,onSearch:e=>{o(e),t(e=>({...e,current:1}))}}),(0,a.jsx)(l.A,{size:"small",rowKey:"id",columns:c,dataSource:(null==h?void 0:h.data.records)||[],loading:y,pagination:{...e,total:(null==h?void 0:h.data.total)||0,showTotal:e=>"共 ".concat(e," 条"),showQuickJumper:!0,showSizeChanger:!1},scroll:{x:1800},onChange:e=>{t(t=>({...t,...e}))},bordered:!0,style:{marginTop:24}})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[838,3740,2211,6222,2602,3520,9786,4439,6933,7663,3840,8874,1126,8441,6587,7358],()=>t(9950)),_N_E=e.O()}]);
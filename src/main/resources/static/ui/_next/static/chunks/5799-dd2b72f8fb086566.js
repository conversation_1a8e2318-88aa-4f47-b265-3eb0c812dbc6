"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5799],{45100:(e,t,o)=>{o.d(t,{A:()=>z});var n=o(12115),a=o(4617),r=o.n(a),c=o(70527),l=o(28673),i=o(64766),s=o(58292),u=o(71054),d=o(31049),m=o(5144),b=o(10815),p=o(70695),g=o(56204),f=o(1086);let v=e=>{let{paddingXXS:t,lineWidth:o,tagPaddingHorizontal:n,componentCls:a,calc:r}=e,c=r(n).sub(o).equal(),l=r(t).sub(o).equal();return{[a]:Object.assign(Object.assign({},(0,p.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,m.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(a,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(a,"-close-icon")]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(a,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(a,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:c}}),["".concat(a,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:o,calc:n}=e,a=e.fontSizeSM;return(0,g.oX)(e,{tagFontSize:a,tagLineHeight:(0,m.zA)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(o).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},O=e=>({defaultBg:new b.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),h=(0,f.OF)("Tag",e=>v(y(e)),O);var C=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]]);return o};let S=n.forwardRef((e,t)=>{let{prefixCls:o,style:a,className:c,checked:l,onChange:i,onClick:s}=e,u=C(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:b}=n.useContext(d.QO),p=m("tag",o),[g,f,v]=h(p),y=r()(p,"".concat(p,"-checkable"),{["".concat(p,"-checkable-checked")]:l},null==b?void 0:b.className,c,f,v);return g(n.createElement("span",Object.assign({},u,{ref:t,style:Object.assign(Object.assign({},a),null==b?void 0:b.style),className:y,onClick:e=>{null==i||i(!l),null==s||s(e)}})))});var x=o(46258);let j=e=>(0,x.A)(e,(t,o)=>{let{textColor:n,lightBorderColor:a,lightColor:r,darkColor:c}=o;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:n,background:r,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}}),k=(0,f.bf)(["Tag","preset"],e=>j(y(e)),O),w=(e,t,o)=>{let n=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(o);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(o)],background:e["color".concat(n,"Bg")],borderColor:e["color".concat(n,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}},E=(0,f.bf)(["Tag","status"],e=>{let t=y(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]},O);var N=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]]);return o};let I=n.forwardRef((e,t)=>{let{prefixCls:o,className:a,rootClassName:m,style:b,children:p,icon:g,color:f,onClose:v,bordered:y=!0,visible:O}=e,C=N(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:S,direction:x,tag:j}=n.useContext(d.QO),[w,I]=n.useState(!0),z=(0,c.A)(C,["closeIcon","closable"]);n.useEffect(()=>{void 0!==O&&I(O)},[O]);let P=(0,l.nP)(f),T=(0,l.ZZ)(f),A=P||T,B=Object.assign(Object.assign({backgroundColor:f&&!A?f:void 0},null==j?void 0:j.style),b),R=S("tag",o),[F,M,D]=h(R),H=r()(R,null==j?void 0:j.className,{["".concat(R,"-").concat(f)]:A,["".concat(R,"-has-color")]:f&&!A,["".concat(R,"-hidden")]:!w,["".concat(R,"-rtl")]:"rtl"===x,["".concat(R,"-borderless")]:!y},a,m,M,D),W=e=>{e.stopPropagation(),null==v||v(e),!e.defaultPrevented&&I(!1)},[,Z]=(0,i.A)((0,i.d)(e),(0,i.d)(j),{closable:!1,closeIconRender:e=>{let t=n.createElement("span",{className:"".concat(R,"-close-icon"),onClick:W},e);return(0,s.fx)(e,t,e=>({onClick:t=>{var o;null===(o=null==e?void 0:e.onClick)||void 0===o||o.call(e,t),W(t)},className:r()(null==e?void 0:e.className,"".concat(R,"-close-icon"))}))}}),q="function"==typeof C.onClick||p&&"a"===p.type,L=g||null,Q=L?n.createElement(n.Fragment,null,L,p&&n.createElement("span",null,p)):p,X=n.createElement("span",Object.assign({},z,{ref:t,className:H,style:B}),Q,Z,P&&n.createElement(k,{key:"preset",prefixCls:R}),T&&n.createElement(E,{key:"status",prefixCls:R}));return F(q?n.createElement(u.A,{component:"Tag"},X):X)});I.CheckableTag=S;let z=I},89801:(e,t,o)=>{o.d(t,{A:()=>x});var n=o(12115),a=o(25795),r=o(58292),c=o(4617),l=o.n(c),i=o(97181),s=o(31049),u=o(43288);let d=e=>{let t;let{value:o,formatter:a,precision:r,decimalSeparator:c,groupSeparator:l="",prefixCls:i}=e;if("function"==typeof a)t=a(o);else{let e=String(o),a=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(a&&"-"!==e){let e=a[1],o=a[2]||"0",s=a[4]||"";o=o.replace(/\B(?=(\d{3})+(?!\d))/g,l),"number"==typeof r&&(s=s.padEnd(r,"0").slice(0,r>0?r:0)),s&&(s="".concat(c).concat(s)),t=[n.createElement("span",{key:"int",className:"".concat(i,"-content-value-int")},e,o),s&&n.createElement("span",{key:"decimal",className:"".concat(i,"-content-value-decimal")},s)]}else t=e}return n.createElement("span",{className:"".concat(i,"-content-value")},t)};var m=o(70695),b=o(1086),p=o(56204);let g=e=>{let{componentCls:t,marginXXS:o,padding:n,colorTextDescription:a,titleFontSize:r,colorTextHeading:c,contentFontSize:l,fontFamily:i}=e;return{[t]:Object.assign(Object.assign({},(0,m.dF)(e)),{["".concat(t,"-title")]:{marginBottom:o,color:a,fontSize:r},["".concat(t,"-skeleton")]:{paddingTop:n},["".concat(t,"-content")]:{color:c,fontSize:l,fontFamily:i,["".concat(t,"-content-value")]:{display:"inline-block",direction:"ltr"},["".concat(t,"-content-prefix, ").concat(t,"-content-suffix")]:{display:"inline-block"},["".concat(t,"-content-prefix")]:{marginInlineEnd:o},["".concat(t,"-content-suffix")]:{marginInlineStart:o}}})}},f=(0,b.OF)("Statistic",e=>[g((0,p.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:o}=e;return{titleFontSize:o,contentFontSize:t}});var v=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]]);return o};let y=e=>{let{prefixCls:t,className:o,rootClassName:a,style:r,valueStyle:c,value:m=0,title:b,valueRender:p,prefix:g,suffix:y,loading:O=!1,formatter:h,precision:C,decimalSeparator:S=".",groupSeparator:x=",",onMouseEnter:j,onMouseLeave:k}=e,w=v(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:E,direction:N,className:I,style:z}=(0,s.TP)("statistic"),P=E("statistic",t),[T,A,B]=f(P),R=n.createElement(d,{decimalSeparator:S,groupSeparator:x,prefixCls:P,formatter:h,precision:C,value:m}),F=l()(P,{["".concat(P,"-rtl")]:"rtl"===N},I,o,a,A,B),M=(0,i.A)(w,{aria:!0,data:!0});return T(n.createElement("div",Object.assign({},M,{className:F,style:Object.assign(Object.assign({},z),r),onMouseEnter:j,onMouseLeave:k}),b&&n.createElement("div",{className:"".concat(P,"-title")},b),n.createElement(u.A,{paragraph:!1,loading:O,className:"".concat(P,"-skeleton")},n.createElement("div",{style:c,className:"".concat(P,"-content")},g&&n.createElement("span",{className:"".concat(P,"-content-prefix")},g),p?p(R):R,y&&n.createElement("span",{className:"".concat(P,"-content-suffix")},y)))))},O=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var h=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]]);return o};let C=1e3/30,S=n.memo(e=>{let{value:t,format:o="HH:mm:ss",onChange:c,onFinish:l}=e,i=h(e,["value","format","onChange","onFinish"]),s=(0,a.A)(),u=n.useRef(null),d=()=>{null==l||l(),u.current&&(clearInterval(u.current),u.current=null)},m=()=>{let e=new Date(t).getTime();e>=Date.now()&&(u.current=setInterval(()=>{s(),null==c||c(e-Date.now()),e<Date.now()&&d()},C))};return n.useEffect(()=>(m(),()=>{u.current&&(clearInterval(u.current),u.current=null)}),[t]),n.createElement(y,Object.assign({},i,{value:t,valueRender:e=>(0,r.Ob)(e,{title:void 0}),formatter:(e,t)=>(function(e,t){let{format:o=""}=t;return function(e,t){let o=e,n=/\[[^\]]*]/g,a=(t.match(n)||[]).map(e=>e.slice(1,-1)),r=t.replace(n,"[]"),c=O.reduce((e,t)=>{let[n,a]=t;if(e.includes(n)){let t=Math.floor(o/a);return o-=t*a,e.replace(RegExp("".concat(n,"+"),"g"),e=>{let o=e.length;return t.toString().padStart(o,"0")})}return e},r),l=0;return c.replace(n,()=>{let e=a[l];return l+=1,e})}(Math.max(new Date(e).getTime()-Date.now(),0),o)})(e,Object.assign(Object.assign({},t),{format:o}))}))});y.Countdown=S;let x=y},97838:(e,t,o)=>{o.d(t,{A:()=>A});var n=o(12115),a=o(4617),r=o.n(a),c=o(72261),l=o(28673),i=o(58292),s=o(31049),u=o(5144),d=o(70695),m=o(46258),b=o(56204),p=o(1086);let g=new u.Mo("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),f=new u.Mo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),v=new u.Mo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),y=new u.Mo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),O=new u.Mo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),h=new u.Mo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),C=e=>{let{componentCls:t,iconCls:o,antCls:n,badgeShadowSize:a,textFontSize:r,textFontSizeSM:c,statusSize:l,dotSize:i,textFontWeight:s,indicatorHeight:b,indicatorHeightSM:p,marginXS:C,calc:S}=e,x="".concat(n,"-scroll-number"),j=(0,m.A)(e,(e,o)=>{let{darkColor:n}=o;return{["&".concat(t," ").concat(t,"-color-").concat(e)]:{background:n,["&:not(".concat(t,"-count)")]:{color:n},"a:hover &":{background:n}}}});return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,d.dF)(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,["".concat(t,"-count")]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:b,height:b,color:e.badgeTextColor,fontWeight:s,fontSize:r,lineHeight:(0,u.zA)(b),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:S(b).div(2).equal(),boxShadow:"0 0 0 ".concat((0,u.zA)(a)," ").concat(e.badgeShadowColor),transition:"background ".concat(e.motionDurationMid),a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},["".concat(t,"-count-sm")]:{minWidth:p,height:p,fontSize:c,lineHeight:(0,u.zA)(p),borderRadius:S(p).div(2).equal()},["".concat(t,"-multiple-words")]:{padding:"0 ".concat((0,u.zA)(e.paddingXS)),bdi:{unicodeBidi:"plaintext"}},["".concat(t,"-dot")]:{zIndex:e.indicatorZIndex,width:i,minWidth:i,height:i,background:e.badgeColor,borderRadius:"100%",boxShadow:"0 0 0 ".concat((0,u.zA)(a)," ").concat(e.badgeShadowColor)},["".concat(t,"-count, ").concat(t,"-dot, ").concat(x,"-custom-component")]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",["&".concat(o,"-spin")]:{animationName:h,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},["&".concat(t,"-status")]:{lineHeight:"inherit",verticalAlign:"baseline",["".concat(t,"-status-dot")]:{position:"relative",top:-1,display:"inline-block",width:l,height:l,verticalAlign:"middle",borderRadius:"50%"},["".concat(t,"-status-success")]:{backgroundColor:e.colorSuccess},["".concat(t,"-status-processing")]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:a,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:g,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},["".concat(t,"-status-default")]:{backgroundColor:e.colorTextPlaceholder},["".concat(t,"-status-error")]:{backgroundColor:e.colorError},["".concat(t,"-status-warning")]:{backgroundColor:e.colorWarning},["".concat(t,"-status-text")]:{marginInlineStart:C,color:e.colorText,fontSize:e.fontSize}}}),j),{["".concat(t,"-zoom-appear, ").concat(t,"-zoom-enter")]:{animationName:f,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},["".concat(t,"-zoom-leave")]:{animationName:v,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},["&".concat(t,"-not-a-wrapper")]:{["".concat(t,"-zoom-appear, ").concat(t,"-zoom-enter")]:{animationName:y,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},["".concat(t,"-zoom-leave")]:{animationName:O,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},["&:not(".concat(t,"-status)")]:{verticalAlign:"middle"},["".concat(x,"-custom-component, ").concat(t,"-count")]:{transform:"none"},["".concat(x,"-custom-component, ").concat(x)]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[x]:{overflow:"hidden",transition:"all ".concat(e.motionDurationMid," ").concat(e.motionEaseOutBack),["".concat(x,"-only")]:{position:"relative",display:"inline-block",height:b,transition:"all ".concat(e.motionDurationSlow," ").concat(e.motionEaseOutBack),WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",["> p".concat(x,"-only-unit")]:{height:b,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},["".concat(x,"-symbol")]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",["".concat(t,"-count, ").concat(t,"-dot, ").concat(x,"-custom-component")]:{transform:"translate(-50%, -50%)"}}})}},S=e=>{let{fontHeight:t,lineWidth:o,marginXS:n,colorBorderBg:a}=e,r=e.colorTextLightSolid,c=e.colorError,l=e.colorErrorHover;return(0,b.oX)(e,{badgeFontHeight:t,badgeShadowSize:o,badgeTextColor:r,badgeColor:c,badgeColorHover:l,badgeShadowColor:a,badgeProcessingDuration:"1.2s",badgeRibbonOffset:n,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},x=e=>{let{fontSize:t,lineHeight:o,fontSizeSM:n,lineWidth:a}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*o)-2*a,indicatorHeightSM:t,dotSize:n/2,textFontSize:n,textFontSizeSM:n,textFontWeight:"normal",statusSize:n/2}},j=(0,p.OF)("Badge",e=>C(S(e)),x),k=e=>{let{antCls:t,badgeFontHeight:o,marginXS:n,badgeRibbonOffset:a,calc:r}=e,c="".concat(t,"-ribbon"),l=(0,m.A)(e,(e,t)=>{let{darkColor:o}=t;return{["&".concat(c,"-color-").concat(e)]:{background:o,color:o}}});return{["".concat(t,"-ribbon-wrapper")]:{position:"relative"},[c]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,d.dF)(e)),{position:"absolute",top:n,padding:"0 ".concat((0,u.zA)(e.paddingXS)),color:e.colorPrimary,lineHeight:(0,u.zA)(o),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,["".concat(c,"-text")]:{color:e.badgeTextColor},["".concat(c,"-corner")]:{position:"absolute",top:"100%",width:a,height:a,color:"currentcolor",border:"".concat((0,u.zA)(r(a).div(2).equal())," solid"),transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),l),{["&".concat(c,"-placement-end")]:{insetInlineEnd:r(a).mul(-1).equal(),borderEndEndRadius:0,["".concat(c,"-corner")]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},["&".concat(c,"-placement-start")]:{insetInlineStart:r(a).mul(-1).equal(),borderEndStartRadius:0,["".concat(c,"-corner")]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},w=(0,p.OF)(["Badge","Ribbon"],e=>k(S(e)),x),E=e=>{let t;let{prefixCls:o,value:a,current:c,offset:l=0}=e;return l&&(t={position:"absolute",top:"".concat(l,"00%"),left:0}),n.createElement("span",{style:t,className:r()("".concat(o,"-only-unit"),{current:c})},a)},N=e=>{let t,o;let{prefixCls:a,count:r,value:c}=e,l=Number(c),i=Math.abs(r),[s,u]=n.useState(l),[d,m]=n.useState(i),b=()=>{u(l),m(i)};if(n.useEffect(()=>{let e=setTimeout(b,1e3);return()=>clearTimeout(e)},[l]),s===l||Number.isNaN(l)||Number.isNaN(s))t=[n.createElement(E,Object.assign({},e,{key:l,current:!0}))],o={transition:"none"};else{t=[];let a=l+10,r=[];for(let e=l;e<=a;e+=1)r.push(e);let c=d<i?1:-1,u=r.findIndex(e=>e%10===s);t=(c<0?r.slice(0,u+1):r.slice(u)).map((t,o)=>n.createElement(E,Object.assign({},e,{key:t,value:t%10,offset:c<0?o-u:o,current:o===u}))),o={transform:"translateY(".concat(-function(e,t,o){let n=e,a=0;for(;(n+10)%10!==t;)n+=o,a+=o;return a}(s,l,c),"00%)")}}return n.createElement("span",{className:"".concat(a,"-only"),style:o,onTransitionEnd:b},t)};var I=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]]);return o};let z=n.forwardRef((e,t)=>{let{prefixCls:o,count:a,className:c,motionClassName:l,style:u,title:d,show:m,component:b="sup",children:p}=e,g=I(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=n.useContext(s.QO),v=f("scroll-number",o),y=Object.assign(Object.assign({},g),{"data-show":m,style:u,className:r()(v,c,l),title:d}),O=a;if(a&&Number(a)%1==0){let e=String(a).split("");O=n.createElement("bdi",null,e.map((t,o)=>n.createElement(N,{prefixCls:v,count:Number(a),value:t,key:e.length-o})))}return((null==u?void 0:u.borderColor)&&(y.style=Object.assign(Object.assign({},u),{boxShadow:"0 0 0 1px ".concat(u.borderColor," inset")})),p)?(0,i.Ob)(p,e=>({className:r()("".concat(v,"-custom-component"),null==e?void 0:e.className,l)})):n.createElement(b,Object.assign({},y,{ref:t}),O)});var P=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]]);return o};let T=n.forwardRef((e,t)=>{var o,a,u,d,m;let{prefixCls:b,scrollNumberPrefixCls:p,children:g,status:f,text:v,color:y,count:O=null,overflowCount:h=99,dot:C=!1,size:S="default",title:x,offset:k,style:w,className:E,rootClassName:N,classNames:I,styles:T,showZero:A=!1}=e,B=P(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:R,direction:F,badge:M}=n.useContext(s.QO),D=R("badge",b),[H,W,Z]=j(D),q=O>h?"".concat(h,"+"):O,L="0"===q||0===q,Q=(null!=f||null!=y)&&(null===O||L&&!A),X=C&&!L,Y=X?"":q,_=(0,n.useMemo)(()=>(null==Y||""===Y||L&&!A)&&!X,[Y,L,A,X]),V=(0,n.useRef)(O);_||(V.current=O);let U=V.current,$=(0,n.useRef)(Y);_||($.current=Y);let G=$.current,J=(0,n.useRef)(X);_||(J.current=X);let K=(0,n.useMemo)(()=>{if(!k)return Object.assign(Object.assign({},null==M?void 0:M.style),w);let e={marginTop:k[1]};return"rtl"===F?e.left=parseInt(k[0],10):e.right=-parseInt(k[0],10),Object.assign(Object.assign(Object.assign({},e),null==M?void 0:M.style),w)},[F,k,w,null==M?void 0:M.style]),ee=null!=x?x:"string"==typeof U||"number"==typeof U?U:void 0,et=_||!v?null:n.createElement("span",{className:"".concat(D,"-status-text")},v),eo=U&&"object"==typeof U?(0,i.Ob)(U,e=>({style:Object.assign(Object.assign({},K),e.style)})):void 0,en=(0,l.nP)(y,!1),ea=r()(null==I?void 0:I.indicator,null===(o=null==M?void 0:M.classNames)||void 0===o?void 0:o.indicator,{["".concat(D,"-status-dot")]:Q,["".concat(D,"-status-").concat(f)]:!!f,["".concat(D,"-color-").concat(y)]:en}),er={};y&&!en&&(er.color=y,er.background=y);let ec=r()(D,{["".concat(D,"-status")]:Q,["".concat(D,"-not-a-wrapper")]:!g,["".concat(D,"-rtl")]:"rtl"===F},E,N,null==M?void 0:M.className,null===(a=null==M?void 0:M.classNames)||void 0===a?void 0:a.root,null==I?void 0:I.root,W,Z);if(!g&&Q){let e=K.color;return H(n.createElement("span",Object.assign({},B,{className:ec,style:Object.assign(Object.assign(Object.assign({},null==T?void 0:T.root),null===(u=null==M?void 0:M.styles)||void 0===u?void 0:u.root),K)}),n.createElement("span",{className:ea,style:Object.assign(Object.assign(Object.assign({},null==T?void 0:T.indicator),null===(d=null==M?void 0:M.styles)||void 0===d?void 0:d.indicator),er)}),v&&n.createElement("span",{style:{color:e},className:"".concat(D,"-status-text")},v)))}return H(n.createElement("span",Object.assign({ref:t},B,{className:ec,style:Object.assign(Object.assign({},null===(m=null==M?void 0:M.styles)||void 0===m?void 0:m.root),null==T?void 0:T.root)}),g,n.createElement(c.Ay,{visible:!_,motionName:"".concat(D,"-zoom"),motionAppear:!1,motionDeadline:1e3},e=>{var t,o;let{className:a}=e,c=R("scroll-number",p),l=J.current,i=r()(null==I?void 0:I.indicator,null===(t=null==M?void 0:M.classNames)||void 0===t?void 0:t.indicator,{["".concat(D,"-dot")]:l,["".concat(D,"-count")]:!l,["".concat(D,"-count-sm")]:"small"===S,["".concat(D,"-multiple-words")]:!l&&G&&G.toString().length>1,["".concat(D,"-status-").concat(f)]:!!f,["".concat(D,"-color-").concat(y)]:en}),s=Object.assign(Object.assign(Object.assign({},null==T?void 0:T.indicator),null===(o=null==M?void 0:M.styles)||void 0===o?void 0:o.indicator),K);return y&&!en&&((s=s||{}).background=y),n.createElement(z,{prefixCls:c,show:!_,motionClassName:a,className:i,count:G,title:ee,style:s,key:"scrollNumber"},eo)}),et))});T.Ribbon=e=>{let{className:t,prefixCls:o,style:a,color:c,children:i,text:u,placement:d="end",rootClassName:m}=e,{getPrefixCls:b,direction:p}=n.useContext(s.QO),g=b("ribbon",o),f="".concat(g,"-wrapper"),[v,y,O]=w(g,f),h=(0,l.nP)(c,!1),C=r()(g,"".concat(g,"-placement-").concat(d),{["".concat(g,"-rtl")]:"rtl"===p,["".concat(g,"-color-").concat(c)]:h},t),S={},x={};return c&&!h&&(S.background=c,x.color=c),v(n.createElement("div",{className:r()(f,m,y,O)},i,n.createElement("div",{className:r()(C,y),style:Object.assign(Object.assign({},S),a)},n.createElement("span",{className:"".concat(g,"-text")},u),n.createElement("div",{className:"".concat(g,"-corner"),style:x}))))};let A=T}}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2482],{3301:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(95155),s=r(11432),a=r(79005);function o(t){let{children:e,...r}=t;return(0,n.jsx)(s.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847",defaultBg:"none",defaultHoverBg:"none",colorLink:"#171F2D",colorLinkHover:"#303847"}}},children:(0,n.jsx)(a.Ay,{...r,children:e})})}},6521:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(95155),s=r(72093),a=r(22810),o=r(2796),i=r(53096),c=r(46742),l=r(12115),d=r(3301);function u(t){let{renderItem:e,pageSize:r=12,getList:u,params:h,refreshDeps:p,gutter:x=[24,24],maxHeight:g}=t,[m,j]=(0,l.useState)(!1),[A,f]=(0,l.useState)(1),[b,v]=(0,l.useState)(!0),[k,y]=(0,l.useState)([]),w=async t=>{if(!u)return;j(!0);let e=await u({page:t,pageSize:r,...h});e.success&&(y(1===t?e.data.records:[...k,...e.data.records]),v(t*r<e.data.total)),j(!1)};return(0,l.useEffect)(()=>{(A>1||!p)&&w(A)},[A]),(0,l.useEffect)(()=>{(null==p?void 0:p.length)>0&&(v(!0),f(1),w(1))},[null==p?void 0:p.join(",")]),(0,n.jsxs)(s.A,{spinning:m,children:[(0,n.jsx)(a.A,{gutter:x,style:{marginTop:"24px",maxHeight:g,overflow:"auto"},children:k.length?k.map(t=>e(t)):(0,n.jsx)(o.A,{span:24,children:(0,n.jsx)(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE})})}),k.length>0&&(0,n.jsx)(a.A,{justify:"center",style:{marginTop:24},children:(0,n.jsx)(o.A,{children:b?(0,n.jsx)(d.A,{loading:m,onClick:()=>f(A+1),type:"link",children:"加载更多"}):(0,n.jsx)(c.A.Text,{type:"secondary",children:"已经到底了"})})})]})}},9365:(t,e,r)=>{"use strict";r.d(e,{A:()=>x});var n=r(12115),s=r(4617),a=r.n(s),o=r(31049),i=r(5144),c=r(70695),l=r(1086),d=r(56204);let u=t=>{let{componentCls:e,sizePaddingEdgeHorizontal:r,colorSplit:n,lineWidth:s,textPaddingInline:a,orientationMargin:o,verticalMarginInline:l}=t;return{[e]:Object.assign(Object.assign({},(0,c.dF)(t)),{borderBlockStart:"".concat((0,i.zA)(s)," solid ").concat(n),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,i.zA)(s)," solid ").concat(n)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,i.zA)(t.dividerHorizontalGutterMargin)," 0")},["&-horizontal".concat(e,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,i.zA)(t.dividerHorizontalWithTextGutterMargin)," 0"),color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(n),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,i.zA)(s)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(e,"-with-text-start")]:{"&::before":{width:"calc(".concat(o," * 100%)")},"&::after":{width:"calc(100% - ".concat(o," * 100%)")}},["&-horizontal".concat(e,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(o," * 100%)")},"&::after":{width:"calc(".concat(o," * 100%)")}},["".concat(e,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:a},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:"".concat((0,i.zA)(s)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(e,"-dashed")]:{borderInlineStartWidth:s,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:"".concat((0,i.zA)(s)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(e,"-dotted")]:{borderInlineStartWidth:s,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(e,"-with-text")]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},["&-horizontal".concat(e,"-with-text-start").concat(e,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(e,"-inner-text")]:{paddingInlineStart:r}},["&-horizontal".concat(e,"-with-text-end").concat(e,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(e,"-inner-text")]:{paddingInlineEnd:r}}})}},h=(0,l.OF)("Divider",t=>[u((0,d.oX)(t,{dividerHorizontalWithTextGutterMargin:t.margin,dividerHorizontalGutterMargin:t.marginLG,sizePaddingEdgeHorizontal:0}))],t=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS}),{unitless:{orientationMargin:!0}});var p=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,n=Object.getOwnPropertySymbols(t);s<n.length;s++)0>e.indexOf(n[s])&&Object.prototype.propertyIsEnumerable.call(t,n[s])&&(r[n[s]]=t[n[s]]);return r};let x=t=>{let{getPrefixCls:e,direction:r,className:s,style:i}=(0,o.TP)("divider"),{prefixCls:c,type:l="horizontal",orientation:d="center",orientationMargin:u,className:x,rootClassName:g,children:m,dashed:j,variant:A="solid",plain:f,style:b}=t,v=p(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),k=e("divider",c),[y,w,T]=h(k),S=!!m,C=n.useMemo(()=>"left"===d?"rtl"===r?"end":"start":"right"===d?"rtl"===r?"start":"end":d,[r,d]),I="start"===C&&null!=u,E="end"===C&&null!=u,O=a()(k,s,w,T,"".concat(k,"-").concat(l),{["".concat(k,"-with-text")]:S,["".concat(k,"-with-text-").concat(C)]:S,["".concat(k,"-dashed")]:!!j,["".concat(k,"-").concat(A)]:"solid"!==A,["".concat(k,"-plain")]:!!f,["".concat(k,"-rtl")]:"rtl"===r,["".concat(k,"-no-default-orientation-margin-start")]:I,["".concat(k,"-no-default-orientation-margin-end")]:E},x,g),B=n.useMemo(()=>"number"==typeof u?u:/^\d+$/.test(u)?Number(u):u,[u]);return y(n.createElement("div",Object.assign({className:O,style:Object.assign(Object.assign({},i),b)},v,{role:"separator"}),m&&"vertical"!==l&&n.createElement("span",{className:"".concat(k,"-inner-text"),style:{marginInlineStart:I?B:void 0,marginInlineEnd:E?B:void 0}},m)))}},10170:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(95155),s=r(41657);function a(){return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[(0,n.jsx)("path",{d:"M8.75033 15.8334C12.6623 15.8334 15.8337 12.6621 15.8337 8.75008C15.8337 4.83808 12.6623 1.66675 8.75033 1.66675C4.83833 1.66675 1.66699 4.83808 1.66699 8.75008C1.66699 12.6621 4.83833 15.8334 8.75033 15.8334Z",stroke:"black",strokeWidth:"1.25",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M11.1066 5.97629C10.5034 5.37308 9.67008 5 8.74958 5C7.82912 5 6.99579 5.37308 6.39258 5.97629",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M13.8428 13.8423L17.3783 17.3778",stroke:"black",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}function o(t){return(0,n.jsx)(s.A,{placeholder:"搜索",prefix:(0,n.jsx)(a,{}),style:{width:"300px"},...t})}},12537:(t,e,r)=>{"use strict";r.d(e,{T:()=>s,Y:()=>n});let n={initial:"未开始",processing:"进行中",completed:"已结束"},s={initial:"未开始",processing:"进行中",notexist:"空号",busy:"占线",success:"成功",failed:"失败"}},17289:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(95155),s=r(95988),a=r(6521),o=r(35039),i=r(59575),c=r(18123),l=r(2796),d=r(12115);function u(t){let{keyword:e,status:r,onStatusChange:u,extra:h,robotId:p}=t,[x,g]=(0,d.useState)(r),{runAsync:m}=(0,c.RI)(),{loading:j,runAsync:A}=(0,c.Ru)(),{runAsync:f,loading:b}=(0,i.oD)(),v=async t=>{let{keyword:e,status:r,robotId:n,page:s,pageSize:a}=t,o=await m({keyword:e,status:r,robotId:n,page:s,pageSize:a});if(o.success){var i;let t=((null===(i=o.data)||void 0===i?void 0:i.records)||[]).map(t=>({...t,taskStatusCount:{},robot:null}));try{let e={},r={};t.forEach(t=>{e[t.id]||(e[t.id]=t.id),r[t.robotId]||(r[t.robotId]=t.robotId)});let n=await A({taskId:Object.keys(e).join(",")}),s=await f({ids:Object.keys(r).join(",")});return t.forEach(t=>{t.taskStatusCount=n.success?n.data[t.id]:{},t.robot=s.success?s.data.records.find(e=>e.id===t.robotId):null}),{success:!0,data:{records:t,total:o.data.total}}}catch(t){return console.error("Error fetching related data:",t),{success:!1,error:{message:"获取相关数据失败"}}}}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.A,{items:[{key:"processing",label:"进行中"},{key:"initial",label:"待启动"},{key:"completed",label:"已结束"}],onChange:t=>{g(t),null==u||u(t)}}),h,(0,n.jsx)(a.A,{getList:v,params:{keyword:e,status:x,robotId:p},refreshDeps:[x,e,p],renderItem:t=>(0,n.jsx)(l.A,{span:8,children:(0,n.jsx)(o.A,{task:t,onStart:()=>{g("processing")},statusCount:t.taskStatusCount,loading:{countLoading:j,robotLoading:b}})},t.id)})]})}},18123:(t,e,r)=>{"use strict";r.d(e,{AK:()=>c,K:()=>i,Mj:()=>l,Mp:()=>h,RI:()=>a,Ru:()=>m,V8:()=>d,Yn:()=>p,ZY:()=>o,c9:()=>j,rp:()=>u,w4:()=>g});var n=r(69653),s=r(79471);let a=()=>(0,n.A)(s.A.getTaskList,{manual:!0}),o=()=>(0,n.A)(s.A.createTask,{manual:!0}),i=()=>(0,n.A)(s.A.updateTask,{manual:!0}),c=()=>(0,n.A)(s.A.deleteTask,{manual:!0}),l=()=>(0,n.A)(s.A.getTaskDetail,{manual:!0}),d=()=>(0,n.A)(s.A.startTask,{manual:!0}),u=()=>(0,n.A)(s.A.stopTask,{manual:!0}),h=()=>(0,n.A)(s.A.notice,{manual:!0}),p=()=>(0,n.A)(s.A.rerunReport,{manual:!0}),x={busy:"0",failed:"0",initial:"0",notexist:"0",processing:"0",success:"0",total:"0"},g=t=>{var e;let r=(0,n.A)(()=>s.A.getTaskStatusCount({robotId:t}));return{...r,data:(null==r?void 0:null===(e=r.data)||void 0===e?void 0:e.success)?r.data.data:x}},m=()=>{var t;let e=(0,n.A)(s.A.getTaskStatusCount,{manual:!0});return{...e,data:(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.success)?e.data.data:{}}},j=()=>(0,n.A)(s.A.getContactList,{manual:!0})},24947:(t,e,r)=>{"use strict";r.d(e,{A:()=>p});var n=r(95155),s=r(46742),a=r(22810),o=r(2796),i=r(3387),c=r(45100),l=r(95458),d=r.n(l);let{Title:u,Text:h}=s.A;function p(t){let{title:e,badge:r,popProps:s,desc:l}=t;return(0,n.jsxs)("div",{className:d().container,children:[(0,n.jsxs)(a.A,{gutter:8,style:{flexWrap:"nowrap",paddingRight:"44px"},children:[(0,n.jsx)(o.A,{children:(0,n.jsx)(i.A,{...s,children:(0,n.jsx)(c.A,{className:d().tag,color:r.color,children:r.text})})}),(0,n.jsx)(o.A,{children:(0,n.jsx)(u,{level:5,className:d().name,children:e})})]}),(0,n.jsx)(h,{type:"secondary",className:d().description,children:l})]})}},26036:(t,e,r)=>{Promise.resolve().then(r.bind(r,49199))},32853:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(95155),s=r(79005),a=r(12115),o=r(47956);let i=t=>{let{title:e,btnProps:r,btnExtraEvent:i,onOk:c,btnText:l,description:d,children:u,modalProps:h}=t,[p,x]=(0,a.useState)(!1);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.Ay,{loading:null==r?void 0:r.loading,onClick:()=>{x(!0),null==i||i()},...r,children:l}),p&&(0,n.jsx)(o.A,{title:e,open:p,onCancel:()=>x(!1),onOk:async()=>{await (null==c?void 0:c())&&x(!1)},description:d,...h,children:u})]})}},35039:(t,e,r)=>{"use strict";r.d(e,{A:()=>E,q:()=>I});var n=r(95155),s=r(71126),a=r(18123),o=r(12537),i=r(55750),c=r(46742),l=r(64787),d=r(11432),u=r(71349),h=r(22810),p=r(2796),x=r(72093),g=r(6457),m=r(78444),j=r(68773),A=r(79005),f=r(76046),b=r(96926),v=r(83761),k=r(24947),y=r(32853),w=r(51480),T=r.n(w),S=r(81488);let{Text:C}=c.A,I=t=>{switch(t){case"initial":return"#FBBC05";case"processing":return"#23E4A6";default:return"#BEBEBE"}};function E(t){var e;let{task:r,onStart:c,statusCount:w,loading:E}=t,{runAsync:O,loading:B}=(0,a.V8)(),{runAsync:_,loading:z}=(0,a.rp)(),{name:L,desc:N,status:P,type:F}=r,M=(0,f.useRouter)(),{message:R}=l.A.useApp(),{total:D,processing:H,success:W,initial:J,failed:G,busy:Y,notexist:Q}=null!=w?w:{};return(0,n.jsx)(d.Ay,{theme:{components:{Card:{colorBgContainer:"#fff"}}},children:(0,n.jsxs)(u.A,{variant:"borderless",className:T().taskInfo,children:[(0,n.jsxs)(h.A,{gutter:[16,16],children:[(0,n.jsx)(p.A,{span:24,children:"outbound"===F?(0,n.jsx)("div",{className:T().callOut,children:(0,n.jsx)(v.A,{})}):(0,n.jsx)("div",{className:T().callIn,children:(0,n.jsx)(b.A,{})})}),(0,n.jsx)(p.A,{span:24,children:(0,n.jsx)(k.A,{title:L,desc:N,badge:{color:I(P),text:o.Y[P]}})}),(0,n.jsx)(p.A,{span:24,children:(0,n.jsx)(x.A,{spinning:null==E?void 0:E.countLoading,children:("processing"===P||"completed"===P)&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(C,{className:T().progressTitle,children:"outbound"===F?"外呼进度统计":"呼入进度统计"}),("outbound"===F||"out"===F)&&(0,n.jsxs)("div",{className:T().progress,style:{width:"100%"},children:[(0,n.jsx)(g.A,{title:"拨打中：".concat(H),children:(0,n.jsx)("div",{className:T().progressItem,style:{width:"".concat((0,S.O)(+H,+D),"%"),backgroundColor:"#41D189",borderRadius:"2px"}})}),(0,n.jsx)(g.A,{title:"未接通：".concat(+G+ +Y+ +Q),children:(0,n.jsx)("div",{className:T().progressItem,style:{width:"".concat((0,S.O)(+G+ +Y+ +Q,+D),"%"),backgroundColor:"#FF004D",right:"2px"}})}),(0,n.jsx)(g.A,{title:"待拨打：".concat(J),children:(0,n.jsx)("div",{className:T().progressItem,style:{width:"".concat((0,S.O)(+J,+D),"%"),backgroundColor:"#FFCE52",right:"4px"}})}),(0,n.jsx)(g.A,{title:"已完成：".concat(W),children:(0,n.jsx)("div",{className:T().progressItem,style:{width:"".concat((0,S.O)(+W,+D),"%")}})})]}),("inbound"===F||"in"===F)&&(0,n.jsxs)("div",{className:T().progress,style:{width:"100%"},children:[(0,n.jsx)(g.A,{title:"拨打中：".concat(H),children:(0,n.jsx)("div",{className:T().progressItem,style:{width:"".concat((0,S.O)(+H,+D),"%"),backgroundColor:"#FBBC05",borderRadius:"2px"}})}),(0,n.jsx)(g.A,{title:"故障：".concat(+G+ +Y+ +Q),children:(0,n.jsx)("div",{className:T().progressItem,style:{width:"".concat((0,S.O)(+G+ +Y+ +Q,+D),"%"),backgroundColor:"#FF004D",borderRadius:"2px"}})}),(0,n.jsx)(g.A,{title:"已呼入：".concat(W),children:(0,n.jsx)("div",{className:T().progressItem,style:{width:"".concat((0,S.O)(+W,+D),"%"),backgroundColor:"#41D189"}})})]})]})})}),(0,n.jsx)(p.A,{span:24,children:(0,n.jsx)(x.A,{spinning:null==E?void 0:E.robotLoading,children:(0,n.jsxs)("div",{className:T().progressInfo,children:[(0,n.jsx)("div",{children:"执行AI虚拟角色"}),(0,n.jsx)(m.A,{size:24,src:null==r?void 0:null===(e=r.robot)||void 0===e?void 0:e.avatar,icon:(0,n.jsx)(i.A,{})})]})})})]}),(0,n.jsx)(h.A,{justify:"end",children:(0,n.jsxs)(j.A,{children:["initial"===P&&(0,n.jsx)(y.A,{btnText:"启动任务",title:"确定启动该任务",btnProps:{loading:B,type:"primary"},onOk:async()=>{let t=await O(r.id);return t.success?(R.success("启动成功"),c(),!0):(R.error(t.error.message),!1)}}),"processing"===P&&(0,n.jsx)(y.A,{btnText:"停止任务",title:"确定停止该任务",btnProps:{loading:z,type:"link"},onOk:async()=>{let t=await _(r.id);return t.success?(R.success("已停止"),!0):(R.error(t.error.message),!1)}}),(0,n.jsx)(A.Ay,{onClick:()=>{M.push((0,S.I)({url:s.Nv.TaskEdit,params:{id:r.id}}))},children:"详情"})]})})]})})}},35594:(t,e,r)=>{"use strict";r.d(e,{Jt:()=>o,bE:()=>i,yH:()=>l,yJ:()=>c});var n=r(43932),s=r.n(n);function a(t,e){return Promise.resolve(s().ajax({method:t,url:e.path||e.url,data:"GET"===t?e.data:JSON.stringify(e.data),contentType:"application/json;charset=UTF-8"}))}let o=t=>a("GET",t),i=t=>a("POST",t),c=t=>a("PUT",t),l=t=>a("DELETE",t)},36564:(t,e,r)=>{"use strict";r.d(e,{A:()=>g});var n=r(95155),s=r(96030),a=r(46742),o=r(22810),i=r(2796),c=r(11432),l=r(79005),d=r(68773),u=r(12115),h=r(10170);let{Title:p,Text:x}=a.A;function g(t){let{title:e,description:r,btn:a,onSearch:g,restSearch:m,customBtn:j,extraFilter:A,middleContent:f}=t,[b,v]=(0,u.useState)(""),{text:k,onClick:y,loading:w}=null!=a?a:{};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(o.A,{justify:"space-between",style:{marginBottom:"24px"},align:"middle",children:[(0,n.jsxs)(i.A,{children:[(0,n.jsx)(p,{level:2,children:e}),(0,n.jsx)(x,{type:"secondary",children:r})]}),(0,n.jsxs)(c.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:[j,a&&(0,n.jsx)(l.Ay,{type:"primary",icon:(0,n.jsx)(s.A,{}),onClick:y,loading:w,children:k})]})]}),f,(0,n.jsxs)(d.A,{size:"middle",children:[g&&(0,n.jsx)(h.A,{onChange:t=>{v(t.target.value)},onPressEnter:()=>{g(b)},...m}),A]})]})}},47956:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(95155),s=r(46742),a=r(21382),o=r(22810),i=r(11432),c=r(68773),l=r(79005);let{Text:d}=s.A,u=t=>{let{open:e,title:r,onOk:s,onCancel:u,description:h,children:p,cancelText:x="取消",okText:g="确定",...m}=t;return(0,n.jsxs)(a.A,{title:r,open:e,onCancel:u,onOk:s,footer:(0,n.jsx)(o.A,{justify:"end",children:(0,n.jsx)(i.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:(0,n.jsxs)(c.A,{children:[x&&(0,n.jsx)(l.Ay,{onClick:u,style:{border:"none"},children:x}),g&&(0,n.jsx)(l.Ay,{type:"primary",onClick:s,children:g})]})})}),...m,children:[h&&(0,n.jsx)(d,{type:"secondary",children:h}),p]})}},48921:(t,e,r)=>{"use strict";r.d(e,{A:()=>c,Q:()=>l});var n=r(95155),s=r(11432),a=r(71349),o=r(97838),i=r(89801);function c(t){let{children:e,badge:r,cardBgColor:i="#171F2D",...c}=t;return(0,n.jsx)(s.Ay,{theme:{components:{Card:{colorBgContainer:i,colorText:"#fff",bodyPadding:"24px 24px 0 24px"},Badge:{colorText:"#fff"}}},children:(0,n.jsxs)(a.A,{...c,children:[r&&(0,n.jsx)(o.A,{...r}),e]})})}let l=t=>{let{value:e,showPercent:r=!1,style:s}=t;return(0,n.jsx)("div",{children:(0,n.jsx)(i.A,{value:e,groupSeparator:",",valueStyle:{fontSize:"48px",fontWeight:600,fontFamily:"DIN Condensed",color:"#fff",...s},formatter:t=>r?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{children:t}),(0,n.jsx)("span",{style:{fontSize:"24px",fontWeight:400,fontFamily:"PingFang SC"},children:"%"})]}):t})})}},49199:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>y});var n=r(95155),s=r(48921),a=r(36564),o=r(81488),i=r(71126),c=r(18123),l=r(86935),d=r(59276),u=r(72093),h=r(22810),p=r(11432),x=r(2796),g=r(68773),m=r(6457),j=r(9365),A=r(21703),f=r(76046),b=r(12115),v=r(17289);let{Content:k}=d.A;function y(){let t=(0,f.useRouter)(),[e,r]=(0,b.useState)("processing"),[d,A]=(0,b.useState)(""),{data:y,loading:T}=(0,c.w4)(),S=+y.total-+y.notexist-+y.busy-+y.processing-+y.failed;return(0,n.jsxs)(k,{children:[(0,n.jsx)(a.A,{title:i.u4[i.GT.Task].header.title,description:i.u4[i.GT.Task].header.description,btn:{text:"创建新任务",onClick:()=>{t.push((0,o.I)({url:i.Nv.TaskEdit}))}},onSearch:A}),(0,n.jsx)(v.A,{keyword:d,status:e,onStatusChange:t=>{r(t)},extra:"processing"===e&&(0,n.jsx)(u.A,{spinning:T,children:(0,n.jsx)(h.A,{style:{marginBottom:"24px"},gutter:[24,16],wrap:!0,children:(0,n.jsxs)(p.Ay,{theme:{components:{Divider:{colorSplit:"#979797"},Badge:{colorText:"#fff"}}},children:[(0,n.jsx)(x.A,{sm:12,xxl:5,span:5,children:(0,n.jsx)(s.A,{badge:{status:"success",text:"实时总呼叫量(外呼+呼入)"},children:(0,n.jsx)(s.Q,{value:y.total})})}),(0,n.jsx)(x.A,{sm:12,xxl:7,span:7,children:(0,n.jsx)(s.A,{children:(0,n.jsxs)(h.A,{align:"middle",justify:"space-between",children:[(0,n.jsxs)(x.A,{children:[(0,n.jsxs)(g.A,{children:[(0,n.jsx)("span",{children:"任务成功率"}),(0,n.jsx)(m.A,{title:"已完成数/（总呼叫量-空号-盲线-呼入呼叫失败）",children:(0,n.jsx)(l.A,{})})]}),(0,n.jsx)(s.Q,{value:S?(0,o.O)(+y.success,S):"-",showPercent:!0})]}),(0,n.jsx)(j.A,{type:"vertical"}),(0,n.jsxs)(x.A,{children:[(0,n.jsxs)(g.A,{children:[(0,n.jsx)("span",{children:"接通率"}),(0,n.jsx)(m.A,{title:"已完成/总呼叫量",children:(0,n.jsx)(l.A,{})})]}),(0,n.jsx)(s.Q,{value:+y.total?(0,o.O)(+y.success,+y.total):"-",showPercent:!0})]})]})})}),(0,n.jsx)(x.A,{sm:24,xxl:12,span:12,children:(0,n.jsx)(s.A,{badge:{status:"success",text:"外呼状态统计"},children:(0,n.jsx)(w,{statusCount:y,validTaskCount:S})})})]})})})})]})}let w=t=>{let{statusCount:e,validTaskCount:r}=t,s=[{label:"空号",percent:r?+(0,o.O)(+e.notexist,r):"0"},{label:"进行中",percent:r?+(0,o.O)(+e.processing,r):"0"},{label:"盲线",percent:r?+(0,o.O)(+e.busy,r):"0"},{label:"已完成",percent:r?+(0,o.O)(+e.success,r):"0"}];return(0,n.jsx)("div",{style:{marginTop:"16px",marginBottom:"16px"},children:(0,n.jsx)(h.A,{gutter:24,justify:"space-between",children:s.map(t=>(0,n.jsx)(x.A,{span:12,children:(0,n.jsx)(T,{...t},t.label)},t.label))})})},T=t=>{let{label:e,percent:r}=t;return(0,n.jsxs)(g.A,{children:[(0,n.jsx)("span",{style:{color:"#fff",width:"46px",display:"inline-block"},children:e}),(0,n.jsx)("span",{children:(0,n.jsx)(A.A,{percent:r,strokeColor:"#4B96FF",trailColor:"rgba(255, 255, 255, 0.1)",format:t=>"".concat(t,"%"),showInfo:!1,style:{width:"150px"}})}),(0,n.jsx)("span",{children:(0,n.jsxs)("span",{style:{color:"#fff"},children:[r,"%"]})})]},e)}},51480:t=>{t.exports={taskInfo:"TaskInfo_taskInfo__12TZK",callIn:"TaskInfo_callIn__gQyaY",callOut:"TaskInfo_callOut__AxJgT",progressTitle:"TaskInfo_progressTitle__MztEI",progressInfo:"TaskInfo_progressInfo__UOctk",progress:"TaskInfo_progress__LYWIv",progressItem:"TaskInfo_progressItem__zFADJ"}},55750:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(85407),s=r(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var o=r(84021);let i=s.forwardRef(function(t,e){return s.createElement(o.A,(0,n.A)({},t,{ref:e,icon:a}))})},59575:(t,e,r)=>{"use strict";r.d(e,{uw:()=>c,dk:()=>d,hU:()=>u,oD:()=>i,lJ:()=>l});var n=r(69653),s=r(35594),a=r(90603);let o={getRobotList:t=>(0,s.Jt)({url:"/api/robots",data:t?{...a.u,...t}:a.u}),createRobot:t=>(0,s.bE)({url:"/api/robots",data:t}),updateRobot:(t,e)=>(0,s.yJ)({url:"/api/robots/".concat(e),data:t}),getRobotDetail:t=>(0,s.Jt)({url:"/api/robots/".concat(t)}),deleteRobot:t=>(0,s.yH)({url:"/api/robots/".concat(t)})},i=()=>(0,n.A)(o.getRobotList,{manual:!0}),c=()=>(0,n.A)(o.createRobot,{manual:!0}),l=()=>(0,n.A)(o.updateRobot,{manual:!0}),d=()=>(0,n.A)(o.deleteRobot,{manual:!0}),u=()=>(0,n.A)(o.getRobotDetail,{manual:!0})},76046:(t,e,r)=>{"use strict";var n=r(66658);r.o(n,"usePathname")&&r.d(e,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(e,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(e,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useServerInsertedHTML")&&r.d(e,{useServerInsertedHTML:function(){return n.useServerInsertedHTML}})},79471:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(35594),s=r(90603);let a={getTaskList:t=>{let{contactStatus:e,employeeId:r,...a}=t||{},o={...s.u,...a};return r&&(o.employeeId=r,e&&(o.contactStatus=e)),(null==t?void 0:t.postNumber)&&(o.postNumber=t.postNumber),(0,n.Jt)({url:"/api/tasks",data:o})},createTask:t=>(0,n.bE)({url:"/api/tasks",data:t}),updateTask:(t,e)=>(0,n.yJ)({url:"/api/tasks/".concat(e),data:t}),getTaskDetail:t=>(0,n.Jt)({url:"/api/tasks/".concat(t)}),deleteTask:t=>(0,n.yH)({url:"/api/tasks/".concat(t)}),startTask:t=>(0,n.bE)({url:"/api/tasks/".concat(t,"/start")}),stopTask:t=>(0,n.bE)({url:"/api/tasks/".concat(t,"/stop")}),notice:t=>(0,n.bE)({url:"/api/tasks/".concat(t,"/notice")}),getTaskStatusCount:t=>(0,n.Jt)({url:"/api/contacts/status/count",data:t}),getContactList:t=>(0,n.Jt)({url:"/api/contacts",data:t}),addContact:(t,e)=>(0,n.bE)({url:"/api/contacts/".concat(t,"/save"),data:e}),removeContact:(t,e)=>(0,n.yH)({url:"/api/contacts/".concat(t,"/remove"),data:e}),importContacts:(t,e)=>{let r=new FormData;return r.append("file",e),fetch("/api/contacts/".concat(t,"/import"),{method:"POST",body:r}).then(t=>t.json())},rerunReport:t=>(0,n.bE)({url:"/api/evalapp/repeat/run",data:t})}},81488:(t,e,r)=>{"use strict";r.d(e,{I:()=>n,O:()=>s});let n=t=>{let{url:e,params:r,isPage:n=!0}=t,s=e;return s="/ui".concat(s),n&&(s="".concat(s,".html")),r&&(s="".concat(s,"?").concat(new URLSearchParams(r).toString())),s},s=(t,e)=>e?(t/e*100).toFixed(2):"-"},83761:(t,e,r)=>{"use strict";r.d(e,{A:()=>s});var n=r(95155);let s=t=>{let{className:e}=t;return(0,n.jsxs)("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",children:[(0,n.jsx)("path",{d:"M9.89587 5.72925H19.2709V15.1042",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M19.2709 5.72927L6.01259 18.9875",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}},86935:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(85407),s=r(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"};var o=r(84021);let i=s.forwardRef(function(t,e){return s.createElement(o.A,(0,n.A)({},t,{ref:e,icon:a}))})},90603:(t,e,r)=>{"use strict";r.d(e,{u:()=>n}),r(35594);let n={order:"desc",sort:"updateTime"}},95458:t=>{t.exports={container:"BadgeTitle_container__3vD4X",name:"BadgeTitle_name__dkR9K",description:"BadgeTitle_description__Qy0lS",tag:"BadgeTitle_tag__zKgzy"}},95988:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(95155),s=r(11432),a=r(99907);function o(t){let{children:e,...r}=t;return(0,n.jsx)(s.Ay,{theme:{components:{Tabs:{inkBarColor:"#171F2D",itemHoverColor:"#000",itemSelectedColor:"#000"}}},children:(0,n.jsx)(a.A,{...r,children:e})})}},96926:(t,e,r)=>{"use strict";r.d(e,{A:()=>s});var n=r(95155);let s=t=>{let{className:e}=t;return(0,n.jsxs)("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",children:[(0,n.jsx)("path",{d:"M15.1042 19.2708H5.72925V9.89575",stroke:"#010101",strokeOpacity:"0.8",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M5.72925 19.2707L18.9875 6.01245",stroke:"#010101",strokeOpacity:"0.8",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}}},t=>{var e=e=>t(t.s=e);t.O(0,[838,6772,3740,2211,6222,2602,3520,9907,3288,1509,1349,5585,4787,2342,5799,1126,8441,6587,7358],()=>e(26036)),_N_E=t.O()}]);
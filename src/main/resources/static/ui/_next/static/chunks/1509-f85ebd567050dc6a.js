"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1509],{3387:(e,t,n)=>{n.d(t,{A:()=>v});var o=n(12115),r=n(4617),a=n.n(r),l=n(35015),c=n(23672),i=n(52491),s=n(19635),d=n(58292),u=n(6457),p=n(73967),g=n(31049),m=n(33101),b=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let f=o.forwardRef((e,t)=>{var n,r;let{prefixCls:f,title:v,content:y,overlayClassName:O,placement:h="top",trigger:j="hover",children:x,mouseEnterDelay:E=.1,mouseLeaveDelay:w=.1,onOpenChange:C,overlayStyle:S={},styles:k,classNames:A}=e,N=b(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:z,className:P,style:I,classNames:M,styles:W}=(0,g.TP)("popover"),B=z("popover",f),[F,R,_]=(0,m.A)(B),D=z(),V=a()(O,R,_,P,M.root,null==A?void 0:A.root),T=a()(M.body,null==A?void 0:A.body),[K,L]=(0,l.A)(!1,{value:null!==(n=e.open)&&void 0!==n?n:e.visible,defaultValue:null!==(r=e.defaultOpen)&&void 0!==r?r:e.defaultVisible}),G=(e,t)=>{L(e,!0),null==C||C(e,t)},Q=e=>{e.keyCode===c.A.ESC&&G(!1,e)},J=(0,i.b)(v),X=(0,i.b)(y);return F(o.createElement(u.A,Object.assign({placement:h,trigger:j,mouseEnterDelay:E,mouseLeaveDelay:w},N,{prefixCls:B,classNames:{root:V,body:T},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},W.root),I),S),null==k?void 0:k.root),body:Object.assign(Object.assign({},W.body),null==k?void 0:k.body)},ref:t,open:K,onOpenChange:e=>{G(e)},overlay:J||X?o.createElement(p.hJ,{prefixCls:B,title:J,content:X}):null,transitionName:(0,s.b)(D,"zoom-big",N.transitionName),"data-popover-inject":!0}),(0,d.Ob)(x,{onKeyDown:e=>{var t,n;o.isValidElement(x)&&(null===(n=null==x?void 0:(t=x.props).onKeyDown)||void 0===n||n.call(t,e)),Q(e)}})))});f._InternalPanelDoNotUseOrYouWillBeFired=p.Ay;let v=f},33101:(e,t,n)=>{n.d(t,{A:()=>p});var o=n(70695),r=n(9023),a=n(29449),l=n(50887),c=n(57554),i=n(1086),s=n(56204);let d=e=>{let{componentCls:t,popoverColor:n,titleMinWidth:r,fontWeightStrong:l,innerPadding:c,boxShadowSecondary:i,colorTextHeading:s,borderRadiusLG:d,zIndexPopup:u,titleMarginBottom:p,colorBgElevated:g,popoverBg:m,titleBorderBottom:b,innerContentPadding:f,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},(0,o.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:u,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":g,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},["".concat(t,"-content")]:{position:"relative"},["".concat(t,"-inner")]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:d,boxShadow:i,padding:c},["".concat(t,"-title")]:{minWidth:r,marginBottom:p,color:s,fontWeight:l,borderBottom:b,padding:v},["".concat(t,"-inner-content")]:{color:n,padding:f}})},(0,a.Ay)(e,"var(--antd-arrow-background-color)"),{["".concat(t,"-pure")]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",["".concat(t,"-content")]:{display:"inline-block"}}}]},u=e=>{let{componentCls:t}=e;return{[t]:c.s.map(n=>{let o=e["".concat(n,"6")];return{["&".concat(t,"-").concat(n)]:{"--antd-arrow-background-color":o,["".concat(t,"-inner")]:{backgroundColor:o},["".concat(t,"-arrow")]:{background:"transparent"}}}})}},p=(0,i.OF)("Popover",e=>{let{colorBgElevated:t,colorText:n}=e,o=(0,s.oX)(e,{popoverBg:t,popoverColor:n});return[d(o),u(o),(0,r.aB)(o,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:n,fontHeight:o,padding:r,wireframe:c,zIndexPopupBase:i,borderRadiusLG:s,marginXS:d,lineType:u,colorSplit:p,paddingSM:g}=e,m=n-o;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:i+30},(0,l.n)(e)),(0,a.Ke)({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:12*!c,titleMarginBottom:c?0:d,titlePadding:c?"".concat(m/2,"px ").concat(r,"px ").concat(m/2-t,"px"):0,titleBorderBottom:c?"".concat(t,"px ").concat(u," ").concat(p):"none",innerContentPadding:c?"".concat(g,"px ").concat(r,"px"):0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},52491:(e,t,n)=>{n.d(t,{b:()=>o});let o=e=>e?"function"==typeof e?e():e:null},64766:(e,t,n)=>{n.d(t,{A:()=>d,d:()=>l});var o=n(12115),r=n(79624),a=n(97181);function l(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function c(e){let{closable:t,closeIcon:n}=e||{};return o.useMemo(()=>{if(!t&&(!1===t||!1===n||null===n))return!1;if(void 0===t&&void 0===n)return null;let e={closeIcon:"boolean"!=typeof n&&null!==n?n:void 0};return t&&"object"==typeof t&&(e=Object.assign(Object.assign({},e),t)),e},[t,n])}function i(){let e={};for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return n.forEach(t=>{t&&Object.keys(t).forEach(n=>{void 0!==t[n]&&(e[n]=t[n])})}),e}let s={};function d(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,l=c(e),d=c(t),u="boolean"!=typeof l&&!!(null==l?void 0:l.disabled),p=o.useMemo(()=>Object.assign({closeIcon:o.createElement(r.A,null)},n),[n]),g=o.useMemo(()=>!1!==l&&(l?i(p,d,l):!1!==d&&(d?i(p,d):!!p.closable&&p)),[l,d,p]);return o.useMemo(()=>{if(!1===g)return[!1,null,u];let{closeIconRender:e}=p,{closeIcon:t}=g,n=t;if(null!=n){e&&(n=e(t));let r=(0,a.A)(g,!0);Object.keys(r).length&&(n=o.isValidElement(n)?o.cloneElement(n,r):o.createElement("span",Object.assign({},r),n))}return[!0,n,u]},[g,p])}},73967:(e,t,n)=>{n.d(t,{Ay:()=>g,hJ:()=>u});var o=n(12115),r=n(4617),a=n.n(r),l=n(67804),c=n(52491),i=n(31049),s=n(33101),d=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let u=e=>{let{title:t,content:n,prefixCls:r}=e;return t||n?o.createElement(o.Fragment,null,t&&o.createElement("div",{className:"".concat(r,"-title")},t),n&&o.createElement("div",{className:"".concat(r,"-inner-content")},n)):null},p=e=>{let{hashId:t,prefixCls:n,className:r,style:i,placement:s="top",title:d,content:p,children:g}=e,m=(0,c.b)(d),b=(0,c.b)(p),f=a()(t,n,"".concat(n,"-pure"),"".concat(n,"-placement-").concat(s),r);return o.createElement("div",{className:f,style:i},o.createElement("div",{className:"".concat(n,"-arrow")}),o.createElement(l.z,Object.assign({},e,{className:t,prefixCls:n}),g||o.createElement(u,{prefixCls:n,title:m,content:b})))},g=e=>{let{prefixCls:t,className:n}=e,r=d(e,["prefixCls","className"]),{getPrefixCls:l}=o.useContext(i.QO),c=l("popover",t),[u,g,m]=(0,s.A)(c);return u(o.createElement(p,Object.assign({},r,{prefixCls:c,hashId:g,className:a()(n,m)})))}},78444:(e,t,n)=>{n.d(t,{A:()=>k});var o=n(12115),r=n(4617),a=n.n(r),l=n(30377),c=n(15231),i=n(45049),s=n(31049),d=n(7926),u=n(27651),p=n(7703);let g=o.createContext({});var m=n(5144),b=n(70695),f=n(1086),v=n(56204);let y=e=>{let{antCls:t,componentCls:n,iconCls:o,avatarBg:r,avatarColor:a,containerSize:l,containerSizeLG:c,containerSizeSM:i,textFontSize:s,textFontSizeLG:d,textFontSizeSM:u,borderRadius:p,borderRadiusLG:g,borderRadiusSM:f,lineWidth:v,lineType:y}=e,O=(e,t,r)=>({width:e,height:e,borderRadius:"50%",["&".concat(n,"-square")]:{borderRadius:r},["&".concat(n,"-icon")]:{fontSize:t,["> ".concat(o)]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,b.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:a,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:"".concat((0,m.zA)(v)," ").concat(y," transparent"),"&-image":{background:"transparent"},["".concat(t,"-image-img")]:{display:"block"}}),O(l,s,p)),{"&-lg":Object.assign({},O(c,d,g)),"&-sm":Object.assign({},O(i,u,f)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},O=e=>{let{componentCls:t,groupBorderColor:n,groupOverlapping:o,groupSpace:r}=e;return{["".concat(t,"-group")]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:o}},["".concat(t,"-group-popover")]:{["".concat(t," + ").concat(t)]:{marginInlineStart:r}}}},h=(0,f.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:n}=e,o=(0,v.oX)(e,{avatarBg:n,avatarColor:t});return[y(o),O(o)]},e=>{let{controlHeight:t,controlHeightLG:n,controlHeightSM:o,fontSize:r,fontSizeLG:a,fontSizeXL:l,fontSizeHeading3:c,marginXS:i,marginXXS:s,colorBorderBg:d}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:o,textFontSize:Math.round((a+l)/2),textFontSizeLG:c,textFontSizeSM:r,groupSpace:s,groupOverlapping:-i,groupBorderColor:d}});var j=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let x=o.forwardRef((e,t)=>{let n;let[r,m]=o.useState(1),[b,f]=o.useState(!1),[v,y]=o.useState(!0),O=o.useRef(null),x=o.useRef(null),E=(0,c.K4)(t,O),{getPrefixCls:w,avatar:C}=o.useContext(s.QO),S=o.useContext(g),k=()=>{if(!x.current||!O.current)return;let t=x.current.offsetWidth,n=O.current.offsetWidth;if(0!==t&&0!==n){let{gap:o=4}=e;2*o<n&&m(n-2*o<t?(n-2*o)/t:1)}};o.useEffect(()=>{f(!0)},[]),o.useEffect(()=>{y(!0),m(1)},[e.src]),o.useEffect(k,[e.gap]);let{prefixCls:A,shape:N,size:z,src:P,srcSet:I,icon:M,className:W,rootClassName:B,alt:F,draggable:R,children:_,crossOrigin:D}=e,V=j(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),T=(0,u.A)(e=>{var t,n;return null!==(n=null!==(t=null!=z?z:null==S?void 0:S.size)&&void 0!==t?t:e)&&void 0!==n?n:"default"}),K=Object.keys("object"==typeof T&&T||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),L=(0,p.A)(K),G=o.useMemo(()=>{if("object"!=typeof T)return{};let e=T[i.ye.find(e=>L[e])];return e?{width:e,height:e,fontSize:e&&(M||_)?e/2:18}:{}},[L,T]),Q=w("avatar",A),J=(0,d.A)(Q),[X,q,H]=h(Q,J),U=a()({["".concat(Q,"-lg")]:"large"===T,["".concat(Q,"-sm")]:"small"===T}),Y=o.isValidElement(P),Z=N||(null==S?void 0:S.shape)||"circle",$=a()(Q,U,null==C?void 0:C.className,"".concat(Q,"-").concat(Z),{["".concat(Q,"-image")]:Y||P&&v,["".concat(Q,"-icon")]:!!M},H,J,W,B,q),ee="number"==typeof T?{width:T,height:T,fontSize:M?T/2:18}:{};if("string"==typeof P&&v)n=o.createElement("img",{src:P,draggable:R,srcSet:I,onError:()=>{let{onError:t}=e;!1!==(null==t?void 0:t())&&y(!1)},alt:F,crossOrigin:D});else if(Y)n=P;else if(M)n=M;else if(b||1!==r){let e="scale(".concat(r,")");n=o.createElement(l.A,{onResize:k},o.createElement("span",{className:"".concat(Q,"-string"),ref:x,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},_))}else n=o.createElement("span",{className:"".concat(Q,"-string"),style:{opacity:0},ref:x},_);return delete V.onError,delete V.gap,X(o.createElement("span",Object.assign({},V,{style:Object.assign(Object.assign(Object.assign(Object.assign({},ee),G),null==C?void 0:C.style),V.style),className:$,ref:E}),n))});var E=n(63588),w=n(58292),C=n(3387);let S=e=>{let{size:t,shape:n}=o.useContext(g),r=o.useMemo(()=>({size:e.size||t,shape:e.shape||n}),[e.size,e.shape,t,n]);return o.createElement(g.Provider,{value:r},e.children)};x.Group=e=>{var t,n,r,l;let{getPrefixCls:c,direction:i}=o.useContext(s.QO),{prefixCls:u,className:p,rootClassName:g,style:m,maxCount:b,maxStyle:f,size:v,shape:y,maxPopoverPlacement:O,maxPopoverTrigger:j,children:k,max:A}=e,N=c("avatar",u),z="".concat(N,"-group"),P=(0,d.A)(N),[I,M,W]=h(N,P),B=a()(z,{["".concat(z,"-rtl")]:"rtl"===i},W,P,p,g,M),F=(0,E.A)(k).map((e,t)=>(0,w.Ob)(e,{key:"avatar-key-".concat(t)})),R=(null==A?void 0:A.count)||b,_=F.length;if(R&&R<_){let e=F.slice(0,R),c=F.slice(R,_),i=(null==A?void 0:A.style)||f,s=(null===(t=null==A?void 0:A.popover)||void 0===t?void 0:t.trigger)||j||"hover",d=(null===(n=null==A?void 0:A.popover)||void 0===n?void 0:n.placement)||O||"top",u=Object.assign(Object.assign({content:c},null==A?void 0:A.popover),{classNames:{root:a()("".concat(z,"-popover"),null===(l=null===(r=null==A?void 0:A.popover)||void 0===r?void 0:r.classNames)||void 0===l?void 0:l.root)},placement:d,trigger:s});return e.push(o.createElement(C.A,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},u),o.createElement(x,{style:i},"+".concat(_-R)))),I(o.createElement(S,{shape:y,size:v},o.createElement("div",{className:B,style:m},e)))}return I(o.createElement(S,{shape:y,size:v},o.createElement("div",{className:B,style:m},F)))};let k=x}}]);
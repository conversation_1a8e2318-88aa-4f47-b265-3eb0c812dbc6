"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3524],{7660:(e,t,r)=>{let a;r.d(t,{Wp:()=>io,_h:()=>s6});var n,s,i,o,l=r(83686).<PERSON><PERSON><PERSON>,c=r(32383),f={};f.version="0.18.5";var h=1200,u=1252,d=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],p={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},m=function(e){-1!=d.indexOf(e)&&(u=p[0]=e)},g=function(e){h=e,m(e)};function v(){g(1200),m(1252)}function T(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function b(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var w=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return 255==t&&254==r?function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}(e.slice(2)):254==t&&255==r?b(e.slice(2)):65279==t?e.slice(1):e},E=function(e){return String.fromCharCode(e)},S=function(e){return String.fromCharCode(e)},_="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function A(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,l=0,c=0;c<e.length;)s=(r=e.charCodeAt(c++))>>2,i=(3&r)<<4|(a=e.charCodeAt(c++))>>4,o=(15&a)<<2|(n=e.charCodeAt(c++))>>6,l=63&n,isNaN(a)?o=l=64:isNaN(n)&&(l=64),t+=_.charAt(s)+_.charAt(i)+_.charAt(o)+_.charAt(l);return t}function y(e){var t="",r=0,a=0,n=0,s=0,i=0,o=0,l=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;)t+=String.fromCharCode((s=_.indexOf(e.charAt(c++)))<<2|(i=_.indexOf(e.charAt(c++)))>>4),a=(15&i)<<4|(o=_.indexOf(e.charAt(c++)))>>2,64!==o&&(t+=String.fromCharCode(a)),n=(3&o)<<6|(l=_.indexOf(e.charAt(c++))),64!==l&&(t+=String.fromCharCode(n));return t}var x=void 0!==l&&void 0!==c&&void 0!==c.versions&&!!c.versions.node,O=function(){if(void 0!==l){var e=!l.from;if(!e)try{l.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new l(e,t):new l(e)}:l.from.bind(l)}return function(){}}();function k(e){return x?l.alloc?l.alloc(e):new l(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}function C(e){return x?l.allocUnsafe?l.allocUnsafe(e):new l(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}var R=function(e){return x?O(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function I(e){if("undefined"==typeof ArrayBuffer)return R(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=255&e.charCodeAt(a);return t}function N(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var D=x?function(e){return l.concat(e.map(function(e){return l.isBuffer(e)?e:O(e)}))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else if("string"==typeof e[t])throw"wtf";else a.set(new Uint8Array(e[t]),r);return a}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))},P=/\u0000/g,L=/[\u0001-\u0006]/g;function F(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function M(e,t){var r=""+e;return r.length>=t?r:eW("0",t-r.length)+r}function U(e,t){var r=""+e;return r.length>=t?r:eW(" ",t-r.length)+r}function B(e,t){var r=""+e;return r.length>=t?r:r+eW(" ",t-r.length)}function H(e,t){var r,a;return e>0x100000000||e<-0x100000000?(r=""+Math.round(e)).length>=t?r:eW("0",t-r.length)+r:(a=""+Math.round(e)).length>=t?a:eW("0",t-a.length)+a}function W(e,t){return t=t||0,e.length>=7+t&&(32|e.charCodeAt(t))==103&&(32|e.charCodeAt(t+1))==101&&(32|e.charCodeAt(t+2))==110&&(32|e.charCodeAt(t+3))==101&&(32|e.charCodeAt(t+4))==114&&(32|e.charCodeAt(t+5))==97&&(32|e.charCodeAt(t+6))==108}var G=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],V=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],z={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},j={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Y={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function K(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,l=1,c=0,f=0,h=Math.floor(n);c<t&&(o=(h=Math.floor(n))*i+s,f=h*c+l,!(n-h<5e-8));)n=1/(n-h),s=i,i=o,l=c,c=f;if(f>t&&(c>t?(f=l,o=s):(f=c,o=i)),!r)return[0,a*o,f];var u=Math.floor(a*o/f);return[u,a*o-u*f,f]}function X(e,t,r){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(o.u)&&(o.u=0),t&&t.date1904&&(a+=1462),o.u>.9999&&(o.u=0,86400==++n&&(o.T=n=0,++a,++o.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var l,c,f,h=new Date(1900,0,1);h.setDate(h.getDate()+a-1),i=[h.getFullYear(),h.getMonth()+1,h.getDate()],s=h.getDay(),a<60&&(s=(s+6)%7),r&&(l=h,c=i,c[0]-=581,f=l.getDay(),l<60&&(f=(f+6)%7),s=f)}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,n=Math.floor(n/60),o.M=n%60,n=Math.floor(n/60),o.H=n,o.q=s,o}var J=new Date(1899,11,31,0,0,0),Z=J.getTime(),q=new Date(1900,2,1,0,0,0);function Q(e,t){var r=e.getTime();return t?r-=1262304e5:e>=q&&(r+=864e5),(r-(Z+(e.getTimezoneOffset()-J.getTimezoneOffset())*6e4))/864e5}function ee(e){return -1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function et(e){var t,r,a,n,s,i=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return i>=-4&&i<=-1?s=e.toPrecision(10+i):9>=Math.abs(i)?(t=e<0?12:11,s=(r=ee(e.toFixed(12))).length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)):s=10===i?e.toFixed(10).substr(0,12):(a=ee(e.toFixed(11))).length>(e<0?12:11)||"0"===a||"-0"===a?e.toPrecision(6):a,ee(-1==(n=s.toUpperCase()).indexOf("E")?n:n.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function er(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):et(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return ev(14,Q(e,t&&t.date1904),t)}throw Error("unsupported value in General format: "+e)}function ea(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var en=/%/g,es=/# (\?+)( ?)\/( ?)(\d+)/,ei=/^#*0*\.([0#]+)/,eo=/\).*[0#]/,el=/\(###\) ###\\?-####/;function ec(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function ef(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function eh(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function eu(e,t,r){return(0|r)===r?function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(eo)){var n,s=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",s,a):"("+e("n",s,-a)+")"}if(44===r.charCodeAt(r.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return eu(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(t,r,a);if(-1!==r.indexOf("%"))return o=(i=r).replace(en,""),l=i.length-o.length,eu(t,o,a*Math.pow(10,2*l))+eW("%",l);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),!(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).match(/[Ee]/)){var o=Math.floor(Math.log(r)*Math.LOG10E);-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(o-a.length+i):a+="E+"+(o-i),a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var i,o,l,c,f,h,u,d=Math.abs(a),p=a<0?"-":"";if(r.match(/^00+$/))return p+M(d,r.length);if(r.match(/^[#?]+$/))return c=""+a,0===a&&(c=""),c.length>r.length?c:ec(r.substr(0,r.length-c.length))+c;if(f=r.match(es))return p+(0===d?"":""+d)+eW(" ",(n=f)[1].length+2+n[4].length);if(r.match(/^#+0+$/))return p+M(d,r.length-r.indexOf("0"));if(f=r.match(ei))return c=(c=(""+a).replace(/^([^\.]+)$/,"$1."+ec(f[1])).replace(/\.$/,"."+ec(f[1]))).replace(/\.(\d*)$/,function(e,t){return"."+t+eW("0",ec(f[1]).length-t.length)}),-1!==r.indexOf("0.")?c:c.replace(/^0\./,".");if(f=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return p+(""+d).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,f[1].length?"0.":".");if(f=r.match(/^#{1,3},##0(\.?)$/))return p+ea(""+d);if(f=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):ea(""+a)+"."+eW("0",f[1].length);if(f=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(f=r.match(/^([0#]+)(\\?-([0#]+))+$/))return c=F(e(t,r.replace(/[\\-]/g,""),a)),h=0,F(F(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return h<c.length?c.charAt(h++):"0"===e?"0":""}));if(r.match(el))return"("+(c=e(t,"##########",a)).substr(0,3)+") "+c.substr(3,3)+"-"+c.substr(6);var m="";if(f=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return u=K(d,Math.pow(10,h=Math.min(f[4].length,7))-1,!1),c=""+p," "==(m=eu("n",f[1],u[1])).charAt(m.length-1)&&(m=m.substr(0,m.length-1)+"0"),c+=m+f[2]+"/"+f[3],(m=B(u[2],h)).length<f[4].length&&(m=ec(f[4].substr(f[4].length-m.length))+m),c+=m;if(f=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return p+((u=K(d,Math.pow(10,h=Math.min(Math.max(f[1].length,f[4].length),7))-1,!0))[0]||(u[1]?"":"0"))+" "+(u[1]?U(u[1],h)+f[2]+"/"+f[3]+B(u[2],h):eW(" ",2*h+1+f[2].length+f[3].length));if(f=r.match(/^[#0?]+$/))return(c=""+a,r.length<=c.length)?c:ec(r.substr(0,r.length-c.length))+c;if(f=r.match(/^([#0]+)\.([#0]+)$/)){h=(c=""+a.toFixed(Math.min(f[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var g=r.indexOf(".")-h,v=r.length-c.length-g;return ec(r.substr(0,g)+c+r.substr(r.length-v))}if(f=r.match(/^00,000\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):ea(""+a).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e})+"."+M(0,f[1].length);switch(r){case"###,###":case"##,###":case"#,###":var T=ea(""+d);return"0"!==T?p+T:"";default:if(r.match(/\.[0#?]*$/))return e(t,r.slice(0,r.lastIndexOf(".")),a)+ec(r.slice(r.lastIndexOf(".")))}throw Error("unsupported format |"+r+"|")}(e,t,r):function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(eo)){var n,s,i,o,l,c,f=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",f,a):"("+e("n",f,-a)+")"}if(44===r.charCodeAt(r.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return eu(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(t,r,a);if(-1!==r.indexOf("%"))return u=(h=r).replace(en,""),d=h.length-u.length,eu(t,u,a*Math.pow(10,2*d))+eW("%",d);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),-1===(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).indexOf("e")){var o=Math.floor(Math.log(r)*Math.LOG10E);for(-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(o-a.length+i):a+="E+"+(o-i);"0."===a.substr(0,2);)a=(a=a.charAt(0)+a.substr(2,s)+"."+a.substr(2+s)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var h,u,d,p,m,g,v,T=Math.abs(a),b=a<0?"-":"";if(r.match(/^00+$/))return b+H(T,r.length);if(r.match(/^[#?]+$/))return"0"===(p=H(a,0))&&(p=""),p.length>r.length?p:ec(r.substr(0,r.length-p.length))+p;if(m=r.match(es))return o=Math.floor((i=Math.round(T*(s=parseInt((n=m)[4],10))))/s),l=i-o*s,b+(0===o?"":""+o)+" "+(0===l?eW(" ",n[1].length+1+n[4].length):U(l,n[1].length)+n[2]+"/"+n[3]+M(s,n[4].length));if(r.match(/^#+0+$/))return b+H(T,r.length-r.indexOf("0"));if(m=r.match(ei))return p=ef(a,m[1].length).replace(/^([^\.]+)$/,"$1."+ec(m[1])).replace(/\.$/,"."+ec(m[1])).replace(/\.(\d*)$/,function(e,t){return"."+t+eW("0",ec(m[1]).length-t.length)}),-1!==r.indexOf("0.")?p:p.replace(/^0\./,".");if(m=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return b+ef(T,m[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,m[1].length?"0.":".");if(m=r.match(/^#{1,3},##0(\.?)$/))return b+ea(H(T,0));if(m=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):ea(""+(Math.floor(a)+ +((c=m[1].length)<(""+Math.round((a-Math.floor(a))*Math.pow(10,c))).length)))+"."+M(eh(a,m[1].length),m[1].length);if(m=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(m=r.match(/^([0#]+)(\\?-([0#]+))+$/))return p=F(e(t,r.replace(/[\\-]/g,""),a)),g=0,F(F(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return g<p.length?p.charAt(g++):"0"===e?"0":""}));if(r.match(el))return"("+(p=e(t,"##########",a)).substr(0,3)+") "+p.substr(3,3)+"-"+p.substr(6);var w="";if(m=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return v=K(T,Math.pow(10,g=Math.min(m[4].length,7))-1,!1),p=""+b," "==(w=eu("n",m[1],v[1])).charAt(w.length-1)&&(w=w.substr(0,w.length-1)+"0"),p+=w+m[2]+"/"+m[3],(w=B(v[2],g)).length<m[4].length&&(w=ec(m[4].substr(m[4].length-w.length))+w),p+=w;if(m=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return b+((v=K(T,Math.pow(10,g=Math.min(Math.max(m[1].length,m[4].length),7))-1,!0))[0]||(v[1]?"":"0"))+" "+(v[1]?U(v[1],g)+m[2]+"/"+m[3]+B(v[2],g):eW(" ",2*g+1+m[2].length+m[3].length));if(m=r.match(/^[#0?]+$/))return(p=H(a,0),r.length<=p.length)?p:ec(r.substr(0,r.length-p.length))+p;if(m=r.match(/^([#0?]+)\.([#0]+)$/)){g=(p=""+a.toFixed(Math.min(m[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var E=r.indexOf(".")-g,S=r.length-p.length-E;return ec(r.substr(0,E)+p+r.substr(r.length-S))}if(m=r.match(/^00,000\.([#0]*0)$/))return g=eh(a,m[1].length),a<0?"-"+e(t,r,-a):ea(a<0x7fffffff&&a>-0x80000000?""+(a>=0?0|a:a-1|0):""+Math.floor(a)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e})+"."+M(g,m[1].length);switch(r){case"###,##0.00":return e(t,"#,##0.00",a);case"###,###":case"##,###":case"#,###":var _=ea(H(T,0));return"0"!==_?b+_:"";case"###,###.00":return e(t,"###,##0.00",a).replace(/^0\./,".");case"#,###.00":return e(t,"#,##0.00",a).replace(/^0\./,".")}throw Error("unsupported format |"+r+"|")}(e,t,r)}var ed=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ep(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":W(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase()||"AM/PM"===e.substr(t,5).toUpperCase()||"上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(ed))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(" "==e.charAt(t)||"*"==e.charAt(t))&&++t;break;case"(":case")":case" ":default:++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);}return!1}var em=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function eg(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function ev(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:z)[e])&&(a=r.table&&r.table[j[e]]||z[j[e]]),null==a&&(a=Y[e]||"General")}if(W(a,0))return er(t,r);t instanceof Date&&(t=Q(t,r.date1904));var n=function(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(em),o=r[1].match(em);return eg(t,i)?[a,r[0]]:eg(t,o)?[a,r[1]]:[a,r[null!=i&&null!=o?2:1]]}return[a,s]}(a,t);if(W(n[1]))return er(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,o=[],l="",c=0,f="",h="t",u="H";c<e.length;)switch(f=e.charAt(c)){case"G":if(!W(e,c))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},c+=7;break;case'"':for(l="";34!==(i=e.charCodeAt(++c))&&c<e.length;)l+=String.fromCharCode(i);o[o.length]={t:"t",v:l},++c;break;case"\\":var d=e.charAt(++c),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++c;break;case"_":o[o.length]={t:"t",v:" "},c+=2;break;case"@":o[o.length]={t:"T",v:t},++c;break;case"B":case"b":if("1"===e.charAt(c+1)||"2"===e.charAt(c+1)){if(null==n&&null==(n=X(t,r,"2"===e.charAt(c+1))))return"";o[o.length]={t:"X",v:e.substr(c,2)},h=f,c+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||null==n&&null==(n=X(t,r)))return"";for(l=f;++c<e.length&&e.charAt(c).toLowerCase()===f;)l+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:l},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=X(t,r)),"A/P"===e.substr(c,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",c+=3):"AM/PM"===e.substr(c,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",c+=5,u="h"):"上午/下午"===e.substr(c,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",c+=5,u="h"):(m.t="t",++c),null==n&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":for(l=f;"]"!==e.charAt(c++)&&c<e.length;)l+=e.charAt(c);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(ed)){if(null==n&&null==(n=X(t,r)))return"";o[o.length]={t:"Z",v:l.toLowerCase()},h=l.charAt(1)}else l.indexOf("$")>-1&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",ep(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=n){for(l=f;++c<e.length&&"0"===(f=e.charAt(c));)l+=f;o[o.length]={t:"s",v:l};break}case"0":case"#":for(l=f;++c<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(c))>-1;)l+=f;o[o.length]={t:"n",v:l};break;case"?":for(l=f;e.charAt(++c)===f;)l+=f;o[o.length]={t:f,v:l},h=f;break;case"*":++c,(" "==e.charAt(c)||"*"==e.charAt(c))&&++c;break;case"(":case")":o[o.length]={t:1===a?"t":f,v:f},++c;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(l=f;c<e.length&&"0123456789".indexOf(e.charAt(++c))>-1;)l+=e.charAt(c);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:f,v:f},++c;break;case"$":o[o.length]={t:"t",v:"$"},++c;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++c}var g,v=0,T=0;for(c=o.length-1,h="t";c>=0;--c)switch(o[c].t){case"h":case"H":o[c].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[c].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[c].t;break;case"m":"s"===h&&(o[c].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[c].v.match(/[Hh]/)&&(v=1),v<2&&o[c].v.match(/[Mm]/)&&(v=2),v<3&&o[c].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var b,w="";for(c=0;c<o.length;++c)switch(o[c].t){case"t":case"T":case" ":case"D":break;case"X":o[c].v="",o[c].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[c].v=function(e,t,r,a){var n,s="",i=0,o=0,l=r.y,c=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:n=l%100,c=2;break;default:n=l%1e4,c=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,c=t.length;break;case 3:return V[r.m-1][1];case 5:return V[r.m-1][0];default:return V[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,c=t.length;break;case 3:return G[r.q][0];default:return G[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,c=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,c=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,c=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;if(0===r.u&&("s"==t||"ss"==t))return M(r.S,t.length);if((i=Math.round((o=a>=2?3===a?1e3:100:1===a?10:1)*(r.S+r.u)))>=60*o&&(i=0),"s"===t)return 0===i?"0":""+i/o;if(s=M(i,2+a),"ss"===t)return s.substr(0,2);return"."+s.substr(2,t.length-1);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=(24*r.D+r.H)*60+r.M;break;case"[s]":case"[ss]":n=((24*r.D+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}c=3===t.length?1:2;break;case 101:n=l,c=1}return c>0?M(n,c):""}(o[c].t.charCodeAt(0),o[c].v,n,T),o[c].t="t";break;case"n":case"?":for(b=c+1;null!=o[b]&&("?"===(f=o[b].t)||"D"===f||(" "===f||"t"===f)&&null!=o[b+1]&&("?"===o[b+1].t||"t"===o[b+1].t&&"/"===o[b+1].v)||"("===o[c].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[b].v||" "===o[b].v&&null!=o[b+1]&&"?"==o[b+1].t));)o[c].v+=o[b].v,o[b]={v:"",t:";"},++b;w+=o[c].v,c=b-1;break;case"G":o[c].t="t",o[c].v=er(t,r)}var E,S,_="";if(w.length>0){40==w.charCodeAt(0)?(E=t<0&&45===w.charCodeAt(0)?-t:t,S=eu("n",w,E)):(S=eu("n",w,E=t<0&&a>1?-t:t),E<0&&o[0]&&"t"==o[0].t&&(S=S.substr(1),o[0].v="-"+o[0].v)),b=S.length-1;var A=o.length;for(c=0;c<o.length;++c)if(null!=o[c]&&"t"!=o[c].t&&o[c].v.indexOf(".")>-1){A=c;break}var y=o.length;if(A===o.length&&-1===S.indexOf("E")){for(c=o.length-1;c>=0;--c)null!=o[c]&&-1!=="n?".indexOf(o[c].t)&&(b>=o[c].v.length-1?(b-=o[c].v.length,o[c].v=S.substr(b+1,o[c].v.length)):b<0?o[c].v="":(o[c].v=S.substr(0,b+1),b=-1),o[c].t="t",y=c);b>=0&&y<o.length&&(o[y].v=S.substr(0,b+1)+o[y].v)}else if(A!==o.length&&-1===S.indexOf("E")){for(b=S.indexOf(".")-1,c=A;c>=0;--c)if(null!=o[c]&&-1!=="n?".indexOf(o[c].t)){for(s=o[c].v.indexOf(".")>-1&&c===A?o[c].v.indexOf(".")-1:o[c].v.length-1,_=o[c].v.substr(s+1);s>=0;--s)b>=0&&("0"===o[c].v.charAt(s)||"#"===o[c].v.charAt(s))&&(_=S.charAt(b--)+_);o[c].v=_,o[c].t="t",y=c}for(b>=0&&y<o.length&&(o[y].v=S.substr(0,b+1)+o[y].v),b=S.indexOf(".")+1,c=A;c<o.length;++c)if(null!=o[c]&&(-1!=="n?(".indexOf(o[c].t)||c===A)){for(s=o[c].v.indexOf(".")>-1&&c===A?o[c].v.indexOf(".")+1:0,_=o[c].v.substr(0,s);s<o[c].v.length;++s)b<S.length&&(_+=S.charAt(b++));o[c].v=_,o[c].t="t",y=c}}}for(c=0;c<o.length;++c)null!=o[c]&&"n?".indexOf(o[c].t)>-1&&(E=a>1&&t<0&&c>0&&"-"===o[c-1].v?-t:t,o[c].v=eu(o[c].t,o[c].v,E),o[c].t="t");var x="";for(c=0;c!==o.length;++c)null!=o[c]&&(x+=o[c].v);return x}(n[1],t,r,n[0])}function eT(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r){if(void 0==z[r]){t<0&&(t=r);continue}if(z[r]==e){t=r;break}}t<0&&(t=391)}return z[t]=e,t}function eb(e){for(var t=0;392!=t;++t)void 0!==e[t]&&eT(e[t],t)}function ew(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',z=e}var eE=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,eS=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}(),r=function(e){var t=0,r=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],o=r[4],l=r[5],c=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[(a^e.charCodeAt(n++))&255];return~a},e.buf=function(e,r){for(var T=-1^r,b=e.length-15,w=0;w<b;)T=v[e[w++]^255&T]^g[e[w++]^T>>8&255]^m[e[w++]^T>>16&255]^p[e[w++]^T>>>24]^d[e[w++]]^u[e[w++]]^h[e[w++]]^f[e[w++]]^c[e[w++]]^l[e[w++]]^o[e[w++]]^i[e[w++]]^s[e[w++]]^n[e[w++]]^a[e[w++]]^t[e[w++]];for(b+=15;w<b;)T=T>>>8^t[(T^e[w++])&255];return~T},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,o=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[(a^i)&255]:i<2048?a=(a=a>>>8^t[(a^(192|i>>6&31))&255])>>>8^t[(a^(128|63&i))&255]:i>=55296&&i<57344?(i=(1023&i)+64,o=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[(a^(240|i>>8&7))&255])>>>8^t[(a^(128|i>>2&63))&255])>>>8^t[(a^(128|o>>6&15|(3&i)<<4))&255])>>>8^t[(a^(128|63&o))&255]):a=(a=(a=a>>>8^t[(a^(224|i>>12&15))&255])>>>8^t[(a^(128|i>>6&63))&255])>>>8^t[(a^(128|63&i))&255];return~a},e}(),e_=function(){var e,t,r={};function a(e){if("/"==e.charAt(e.length-1))return -1===e.slice(0,-1).indexOf("/")?e:a(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(0,t+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(t+1)}function s(e){t1(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};21589===a&&(1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime))),e.l=s,t[a]=i}return t}function i(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return es(e,t);if((32|e[0])==109&&(32|e[1])==105)return function(e,t){if("mime-version:"!=b(e.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var r=t&&t.root||"",a=(x&&l.isBuffer(e)?e.toString("binary"):b(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if(s=a[n],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw Error("MAD cannot find boundary");var o="--"+(i[1]||""),c={FileIndex:[],FullPaths:[]};f(c);var h,u=0;for(n=0;n<a.length;++n){var d=a[n];(d===o||d===o+"--")&&(u++&&function(e,t,r){for(var a,n="",s="",i="",o=0;o<10;++o){var l=t[o];if(!l||l.match(/^\s*$/))break;var c=l.match(/^(.*?):\s*([^\s].*)$/);if(c)switch(c[1].toLowerCase()){case"content-location":n=c[2].trim();break;case"content-type":i=c[2].trim();break;case"content-transfer-encoding":s=c[2].trim()}}switch(++o,s.toLowerCase()){case"base64":a=R(y(t.slice(o).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return R(t.join("\r\n"))}(t.slice(o));break;default:throw Error("Unsupported Content-Transfer-Encoding "+s)}var f=eo(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}(c,a.slice(h,n),r),h=n)}return c}(e,t);if(e.length<512)throw Error("CFB file size "+e.length+" < 512");var r=3,a=512,n=0,s=0,i=0,o=0,h=0,u=[],g=e.slice(0,512);t1(g,0);var v=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(m,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(g);switch(r=v[0]){case 3:a=512;break;case 4:a=4096;break;case 0:if(0==v[1])return es(e,t);default:throw Error("Major Version: Expected 3 or 4 saw "+r)}512!==a&&t1(g=e.slice(0,a),28);var T=e.slice(0,a);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw Error("Sector Shift: Expected 12 saw "+r);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(g,r);var w=g.read_shift(4,"i");if(3===r&&0!==w)throw Error("# Directory Sectors: Expected 0 saw "+w);g.l+=4,i=g.read_shift(4,"i"),g.l+=4,g.chk("00100000","Mini Stream Cutoff Size: "),o=g.read_shift(4,"i"),n=g.read_shift(4,"i"),h=g.read_shift(4,"i"),s=g.read_shift(4,"i");for(var E=-1,S=0;S<109&&!((E=g.read_shift(4,"i"))<0);++S)u[S]=E;var _=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,a);!function e(t,r,a,n,s){var i=p;if(t===p){if(0!==r)throw Error("DIFAT chain shorter than expected")}else if(-1!==t){var o=a[t],l=(n>>>2)-1;if(!o)return;for(var c=0;c<l&&(i=tY(o,4*c))!==p;++c)s.push(i);e(tY(o,n-4),r-1,a,n,s)}}(h,s,_,a,u);var A=function(e,t,r,a){var n=e.length,s=[],i=[],o=[],l=[],c=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(o=[],(u=f+t)>=n&&(u-=n),!i[u]){l=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,l.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&c))throw Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m]||p[h=tY(e[m],d)])break}s[u]={nodes:o,data:t_([l])}}return s}(_,i,u,a);A[i].name="!Directory",n>0&&o!==p&&(A[o].name="!MiniFAT"),A[u[0]].name="!FAT",A.fat_addrs=u,A.ssz=a;var O=[],k=[],C=[];(function(e,t,r,a,n,s,i,o){for(var l,f=0,h=2*!!a.length,u=t[e].data,m=0,g=0;m<u.length;m+=128){var v=u.slice(m,m+128);t1(v,64),g=v.read_shift(2),l=ty(v,0,g-h),a.push(l);var T={name:l,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.ct=c(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.mt=c(v,v.l-8)),T.start=v.read_shift(4,"i"),T.size=v.read_shift(4,"i"),T.size<0&&T.start<0&&(T.size=T.type=0,T.start=p,T.name=""),5===T.type?(f=T.start,n>0&&f!==p&&(t[f].name="!StreamData")):T.size>=4096?(T.storage="fat",void 0===t[T.start]&&(t[T.start]=function(e,t,r,a,n){var s=[],i=[];n||(n=[]);var o=a-1,l=0,c=0;for(l=t;l>=0;){n[l]=!0,s[s.length]=l,i.push(e[l]);var f=r[Math.floor(4*l/a)];if(a<4+(c=4*l&o))throw Error("FAT boundary crossed: "+l+" 4 "+a);if(!e[f])break;l=tY(e[f],c)}return{nodes:s,data:t_([i])}}(r,T.start,t.fat_addrs,t.ssz)),t[T.start].name=T.name,T.content=t[T.start].data.slice(0,T.size)):(T.storage="minifat",T.size<0?T.size=0:f!==p&&T.start!==p&&t[f]&&(T.content=function(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*d,i*d+d)),n-=d,i=tY(r,4*i);return 0===s.length?t2(0):D(s).slice(0,e.size)}(T,t[f].data,(t[o]||{}).data))),T.content&&t1(T.content,0),s[l]=T,i.push(T)}})(i,A,_,O,n,{},k,o),function(e,t,r){for(var a=0,n=0,s=0,i=0,o=0,l=r.length,c=[],f=[];a<l;++a)c[a]=f[a]=a,t[a]=r[a];for(;o<f.length;++o)n=e[a=f[o]].L,s=e[a].R,i=e[a].C,c[a]===a&&(-1!==n&&c[n]!==n&&(c[a]=c[n]),-1!==s&&c[s]!==s&&(c[a]=c[s])),-1!==i&&(c[i]=a),-1!==n&&a!=c[a]&&(c[n]=c[a],f.lastIndexOf(n)<o&&f.push(n)),-1!==s&&a!=c[a]&&(c[s]=c[a],f.lastIndexOf(s)<o&&f.push(s));for(a=1;a<l;++a)c[a]===a&&(-1!==s&&c[s]!==s?c[a]=c[s]:-1!==n&&c[n]!==n&&(c[a]=c[n]));for(a=1;a<l;++a)if(0!==e[a].type){if((o=a)!=c[o])do o=c[o],t[a]=t[o]+"/"+t[a];while(0!==o&&-1!==c[o]&&o!=c[o]);c[a]=-1}for(t[0]+="/",a=1;a<l;++a)2!==e[a].type&&(t[a]+="/")}(k,C,O),O.shift();var I={FileIndex:k,FullPaths:C};return t&&t.raw&&(I.raw={header:T,sectors:_}),I}function c(e,t){return new Date((t$(e,t+4)/1e7*0x100000000+t$(e,t)/1e7-0x2b6109100)*1e3)}function f(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(!e_.find(e,"/"+t)){var r=t2(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),h(e)}}(e)}function h(e,t){f(e);for(var r=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var o=e.FileIndex[i];switch(o.type){case 0:s?r=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(r=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(r=!0);break;default:r=!0}}if(r||t){var l=new Date(1987,1,19),c=0,h=Object.create?Object.create(null):{},u=[];for(i=0;i<e.FullPaths.length;++i)h[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&u.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<u.length;++i){var d=a(u[i][0]);(s=h[d])||(u.push([d,{name:n(d).replace("/",""),type:1,clsid:v,ct:l,mt:l,content:null}]),h[d]=!0)}for(u.sort(function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])}),e.FullPaths=[],e.FileIndex=[],i=0;i<u.length;++i)e.FullPaths[i]=u[i][0],e.FileIndex[i]=u[i][1];for(i=0;i<u.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||v,0===i)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(c=i+1;c<u.length&&a(e.FullPaths[c])!=m;++c);for(p.C=c>=u.length?-1:c,c=i+1;c<u.length&&a(e.FullPaths[c])!=a(m);++c);p.R=c>=u.length?-1:c,p.type=1}else a(e.FullPaths[i+1]||"")==a(m)&&(p.R=i+1),p.type=2}}}function u(e,r){var a=r||{};if("mad"==a.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],c=1;c<e.FullPaths.length;++c)if(i=e.FullPaths[c].slice(s.length),(o=e.FileIndex[c]).size&&o.content&&"\x01Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});for(var f=o.content,h=x&&l.isBuffer(f)?f.toString("binary"):b(f),u=0,d=Math.min(1024,h.length),p=0,m=0;m<=d;++m)(p=h.charCodeAt(m))>=32&&p<128&&++u;var g=u>=4*d/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(g?"quoted-printable":"base64")),n.push("Content-Type: "+function(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&ei[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&ei[a[1]]?ei[a[1]]:"application/octet-stream"}(o,i)),n.push(""),n.push(g?function(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)});"\n"==(t=t.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var r=[],a=t.split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0==s.length){r.push("");continue}for(var i=0;i<s.length;){var o=76,l=s.slice(i,i+o);"="==l.charAt(o-1)?o--:"="==l.charAt(o-2)?o-=2:"="==l.charAt(o-3)&&(o-=3),l=s.slice(i,i+o),(i+=o)<s.length&&(l+="="),r.push(l)}}return r.join("\r\n")}(h):function(e){for(var t=A(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}(h))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,a);if(h(e),"zip"===a.fileType)return function(e,r){var a=[],n=[],s=t2(1),i=8*!!(r||{}).compression,o=0,l=0,c=0,f=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(o=1;o<e.FullPaths.length;++o)if(u=e.FullPaths[o].slice(h.length),(d=e.FileIndex[o]).size&&d.content&&"\x01Sh33tJ5"!=u){var g,v=c,T=t2(u.length);for(l=0;l<u.length;++l)T.write_shift(1,127&u.charCodeAt(l));T=T.slice(0,T.l),p[f]=eS.buf(d.content,0);var b=d.content;8==i&&(g=b,b=t?t.deflateRawSync(g):Z(g)),(s=t2(30)).write_shift(4,0x4034b50),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),d.mt?function(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}(s,d.mt):s.write_shift(4,0),s.write_shift(-4,(0,p[f])),s.write_shift(4,(0,b.length)),s.write_shift(4,(0,d.content.length)),s.write_shift(2,T.length),s.write_shift(2,0),c+=s.length,a.push(s),c+=T.length,a.push(T),c+=b.length,a.push(b),(s=t2(46)).write_shift(4,0x2014b50),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(-4,p[f]),s.write_shift(4,b.length),s.write_shift(4,d.content.length),s.write_shift(2,T.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,v),m+=s.l,n.push(s),m+=T.length,n.push(T),++f}return(s=t2(22)).write_shift(4,0x6054b50),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,f),s.write_shift(2,f),s.write_shift(4,m),s.write_shift(4,c),s.write_shift(2,0),D([D(a),D(n),s])}(e,a);var n=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,o=t+7>>3,l=t+127>>7,c=o+r+i+l,f=c+127>>7,h=f<=109?0:Math.ceil((f-109)/127);c+f+h+127>>7>f;)h=++f<=109?0:Math.ceil((f-109)/127);var u=[1,h,f,l,i,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),s=t2(n[7]<<9),i=0,o=0;for(i=0;i<8;++i)s.write_shift(1,g[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,n[2]),s.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:p),s.write_shift(4,n[3]),s.write_shift(-4,n[1]?n[0]-1:p),s.write_shift(4,n[1]),i=0;i<109;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);if(n[1])for(o=0;o<n[1];++o){for(;i<236+127*o;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);s.write_shift(-4,o===n[1]-1?p:o+1)}var c=function(e){for(o+=e;i<o-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,p))};for(o=(i=0)+n[1];i<o;++i)s.write_shift(-4,T.DIFSECT);for(o+=n[2];i<o;++i)s.write_shift(-4,T.FATSECT);c(n[3]),c(n[4]);for(var f=0,u=0,d=e.FileIndex[0];f<e.FileIndex.length;++f)(d=e.FileIndex[f]).content&&((u=d.content.length)<4096||(d.start=o,c(u+511>>9)));for(c(n[6]+7>>3);511&s.l;)s.write_shift(-4,T.ENDOFCHAIN);for(f=0,o=i=0;f<e.FileIndex.length;++f)(d=e.FileIndex[f]).content&&(u=d.content.length)&&!(u>=4096)&&(d.start=o,c(u+63>>6));for(;511&s.l;)s.write_shift(-4,T.ENDOFCHAIN);for(i=0;i<n[4]<<2;++i){var m=e.FullPaths[i];if(!m||0===m.length){for(f=0;f<17;++f)s.write_shift(4,0);for(f=0;f<3;++f)s.write_shift(4,-1);for(f=0;f<12;++f)s.write_shift(4,0);continue}d=e.FileIndex[i],0===i&&(d.start=d.size?d.start-1:p);var v=0===i&&a.root||d.name;if(u=2*(v.length+1),s.write_shift(64,v,"utf16le"),s.write_shift(2,u),s.write_shift(1,d.type),s.write_shift(1,d.color),s.write_shift(-4,d.L),s.write_shift(-4,d.R),s.write_shift(-4,d.C),d.clsid)s.write_shift(16,d.clsid,"hex");else for(f=0;f<4;++f)s.write_shift(4,0);s.write_shift(4,d.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,d.start),s.write_shift(4,d.size),s.write_shift(4,0)}for(i=1;i<e.FileIndex.length;++i)if((d=e.FileIndex[i]).size>=4096){if(s.l=d.start+1<<9,x&&l.isBuffer(d.content))d.content.copy(s,s.l,0,d.size),s.l+=d.size+511&-512;else{for(f=0;f<d.size;++f)s.write_shift(1,d.content[f]);for(;511&f;++f)s.write_shift(1,0)}}for(i=1;i<e.FileIndex.length;++i)if((d=e.FileIndex[i]).size>0&&d.size<4096){if(x&&l.isBuffer(d.content))d.content.copy(s,s.l,0,d.size),s.l+=d.size+63&-64;else{for(f=0;f<d.size;++f)s.write_shift(1,d.content[f]);for(;63&f;++f)s.write_shift(1,0)}}if(x)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}r.version="1.2.1";var d=64,p=-2,m="d0cf11e0a1b11ae1",g=[208,207,17,224,161,177,26,225],v="00000000000000000000000000000000",T={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:m,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:v,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function b(e){for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}for(var w=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],E=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],S=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],_="undefined"!=typeof Uint8Array,I=_?new Uint8Array(256):[],N=0;N<256;++N)I[N]=function(e){var t=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(t>>16|t>>8|t)&255}(N);function F(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function M(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function U(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function B(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a?i&s:(i|=e[n+1]<<8-a,r<16-a)?i&s:(i|=e[n+2]<<16-a,r<24-a)?i&s:(i|=e[n+3]<<24-a)&s}function H(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function W(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function G(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function V(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(x){var s=C(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(_){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function z(e){for(var t=Array(e),r=0;r<e;++r)t[r]=0;return t}function j(e,t,r){var a=1,n=0,s=0,i=0,o=0,l=e.length,c=_?new Uint16Array(32):z(32);for(s=0;s<32;++s)c[s]=0;for(s=l;s<r;++s)e[s]=0;l=e.length;var f=_?new Uint16Array(l):z(l);for(s=0;s<l;++s)c[n=e[s]]++,a<n&&(a=n),f[s]=0;for(s=1,c[0]=0;s<=a;++s)c[s+16]=o=o+c[s-1]<<1;for(s=0;s<l;++s)0!=(o=e[s])&&(f[s]=c[o+16]++);var h=0;for(s=0;s<l;++s)if(0!=(h=e[s]))for(o=function(e,t){var r=I[255&e];return t<=8?r>>>8-t:(r=r<<8|I[e>>8&255],t<=16)?r>>>16-t:(r=r<<8|I[e>>16&255])>>>24-t}(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[o|i<<h]=15&h|s<<4;return a}var Y=_?new Uint16Array(512):z(512),K=_?new Uint16Array(32):z(32);if(!_){for(var X=0;X<512;++X)Y[X]=0;for(X=0;X<32;++X)K[X]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);j(e,K,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);j(r,Y,288)}();var J=function(){for(var e=_?new Uint8Array(32768):[],t=0,r=0;t<S.length-1;++t)for(;r<S[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=_?new Uint8Array(259):[];for(t=0,r=0;t<E.length-1;++t)for(;r<E[t+1];++r)a[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var a=Math.min(65535,e.length-r),n=r+a==e.length;for(t.write_shift(1,+n),t.write_shift(2,a),t.write_shift(2,65535&~a);a-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var n=0,s=0,i=_?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(n=H(r,n,+(s+o==t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];n=8*r.l;continue}n=H(r,n,+(s+o==t.length)+2);for(var l=0;o-- >0;){var c,f,h=t[s],u=-1,d=0;if((u=i[l=(l<<5^h)&32767])&&((u|=-32768&s)>s&&(u-=32768),u<s))for(;t[u+d]==t[s+d]&&d<250;)++d;if(d>2){(h=a[d])<=22?n=W(r,n,I[h+1]>>1)-1:(W(r,n,3),W(r,n+=5,I[h-23]>>5),n+=3);var p=h<8?0:h-4>>2;p>0&&(G(r,n,d-E[h]),n+=p),n=W(r,n,I[h=e[s-u]]>>3)-3;var m=h<4?0:h-2>>1;m>0&&(G(r,n,s-u-S[h]),n+=m);for(var g=0;g<d;++g)i[l]=32767&s,l=(l<<5^t[s])&32767,++s;o-=d-1}else h<=143?h+=48:(f=(1&(f=1))<<(7&(c=n)),r[c>>>3]|=f,n=c+1),n=W(r,n,I[h]),i[l]=32767&s,++s}n=W(r,n,0)-1}return r.l=(n+7)/8|0,r.l}(t,r)}}();function Z(e){var t=t2(50+Math.floor(1.1*e.length)),r=J(e,t);return t.slice(0,r)}var q=_?new Uint16Array(32768):z(32768),Q=_?new Uint16Array(32768):z(32768),ee=_?new Uint16Array(128):z(128),et=1,er=1;function ea(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[k(t),2];for(var r=0,a=0,n=C(t||262144),s=0,i=n.length>>>0,o=0,l=0;(1&a)==0;){if(a=F(e,r),r+=3,a>>>1==0){7&r&&(r+=8-(7&r));var c=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,c>0)for(!t&&i<s+c&&(i=(n=V(n,s+c)).length);c-- >0;)n[s++]=e[r>>>3],r+=8;continue}for(a>>1==1?(o=9,l=5):(r=function(e,t){var r,a,n,s=M(e,t)+257,i=M(e,t+=5)+1;t+=5;var o=(a=7&(r=t),((e[n=r>>>3]|(a<=4?0:e[n+1]<<8))>>>a&15)+4);t+=4;for(var l=0,c=_?new Uint8Array(19):z(19),f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=_?new Uint8Array(8):z(8),d=_?new Uint8Array(8):z(8),p=c.length,m=0;m<o;++m)c[w[m]]=l=F(e,t),h<l&&(h=l),u[l]++,t+=3;var g=0;for(m=1,u[0]=0;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=c[m])&&(f[m]=d[g]++);var v=0;for(m=0;m<p;++m)if(0!=(v=c[m])){g=I[f[m]]>>8-v;for(var T=(1<<7-v)-1;T>=0;--T)ee[g|T<<v]=7&v|m<<3}var b=[];for(h=1;b.length<s+i;)switch(g=ee[U(e,t)],t+=7&g,g>>>=3){case 16:for(l=3+function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}(e,t),t+=2,g=b[b.length-1];l-- >0;)b.push(g);break;case 17:for(l=3+F(e,t),t+=3;l-- >0;)b.push(0);break;case 18:for(l=11+U(e,t),t+=7;l-- >0;)b.push(0);break;default:b.push(g),h<g&&(h=g)}var E=b.slice(0,s),S=b.slice(s);for(m=s;m<286;++m)E[m]=0;for(m=i;m<30;++m)S[m]=0;return et=j(E,q,286),er=j(S,Q,30),t}(e,r),o=et,l=er);;){!t&&i<s+32767&&(i=(n=V(n,s+32767)).length);var f=B(e,r,o),h=a>>>1==1?Y[f]:q[f];if(r+=15&h,((h>>>=4)>>>8&255)==0)n[s++]=h;else if(256==h)break;else{var u=(h-=257)<8?0:h-4>>2;u>5&&(u=0);var d=s+E[h];u>0&&(d+=B(e,r,u),r+=u),f=B(e,r,l),r+=15&(h=a>>>1==1?K[f]:Q[f]);var p=(h>>>=4)<4?0:h-2>>1,m=S[h];for(p>0&&(m+=B(e,r,p),r+=p),!t&&i<d&&(i=(n=V(n,d+100)).length);s<d;)n[s]=n[s-m],++s}}}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function en(e,t){if(e)"undefined"!=typeof console&&console.error(t);else throw Error(t)}function es(e,r){t1(e,0);var a={FileIndex:[],FullPaths:[]};f(a,{root:r.root});for(var n=e.length-4;(80!=e[n]||75!=e[n+1]||5!=e[n+2]||6!=e[n+3])&&n>=0;)--n;e.l=n+4,e.l+=4;var i=e.read_shift(2);e.l+=6;var o=e.read_shift(4);for(n=0,e.l=o;n<i;++n){e.l+=20;var l=e.read_shift(4),c=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d=e.read_shift(2);e.l+=8;var p=e.read_shift(4),m=s(e.slice(e.l+h,e.l+h+u));e.l+=h+u+d;var g=e.l;e.l=p+4,function(e,r,a,n,i){e.l+=2;var o,l,c,f,h,u,d,p=e.read_shift(2),m=e.read_shift(2),g=(o=65535&e.read_shift(2),l=65535&e.read_shift(2),c=new Date,f=31&l,h=15&(l>>>=5),l>>>=4,c.setMilliseconds(0),c.setFullYear(l+1980),c.setMonth(h-1),c.setDate(f),u=31&o,d=63&(o>>>=5),o>>>=6,c.setHours(o),c.setMinutes(d),c.setSeconds(u<<1),c);if(8257&p)throw Error("Unsupported ZIP encryption");for(var v=e.read_shift(4),T=e.read_shift(4),b=e.read_shift(4),w=e.read_shift(2),E=e.read_shift(2),S="",_=0;_<w;++_)S+=String.fromCharCode(e[e.l++]);if(E){var A=s(e.slice(e.l,e.l+E));(A[21589]||{}).mt&&(g=A[21589].mt),((i||{})[21589]||{}).mt&&(g=i[21589].mt)}e.l+=E;var y=e.slice(e.l,e.l+T);switch(m){case 8:y=function(e,r){if(!t)return ea(e,r);var a=new t.InflateRaw,n=a._processChunk(e.slice(e.l),a._finishFlushFlag);return e.l+=a.bytesRead,n}(e,b);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+m)}var x=!1;8&p&&(0x8074b50==e.read_shift(4)&&(e.read_shift(4),x=!0),T=e.read_shift(4),b=e.read_shift(4)),T!=r&&en(x,"Bad compressed size: "+r+" != "+T),b!=a&&en(x,"Bad uncompressed size: "+a+" != "+b),eo(n,S,y,{unsafe:!0,mt:g})}(e,l,c,a,m),e.l=g}return a}var ei={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function eo(e,t,r,a){var s=a&&a.unsafe;s||f(e);var i=!s&&e_.find(e,t);if(!i){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),i={name:n(t),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||e_.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,a&&(a.CLSID&&(i.clsid=a.CLSID),a.mt&&(i.mt=a.mt),a.ct&&(i.ct=a.ct)),i}return r.find=function(e,t){var r=e.FullPaths.map(function(e){return e.toUpperCase()}),a=r.map(function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]}),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(L);for(s=s.replace(P,""),o&&(s=s.replace(L,"!")),i=0;i<r.length;++i)if((o?r[i].replace(L,"!"):r[i]).replace(P,"")==s||(o?a[i].replace(L,"!"):a[i]).replace(P,"")==s)return e.FileIndex[i];return null},r.read=function(t,r){var a=r&&r.type;switch(!a&&x&&l.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return i(),o(e.readFileSync(t),r);case"base64":return o(R(y(t)),r);case"binary":return o(R(t),r)}return o(t,r)},r.parse=o,r.write=function(t,r){var a=u(t,r);switch(r&&r.type||"buffer"){case"file":i(),e.writeFileSync(r.filename,a);break;case"binary":return"string"==typeof a?a:b(a);case"base64":return A("string"==typeof a?a:b(a));case"buffer":if(x)return l.isBuffer(a)?a:O(a);case"array":return"string"==typeof a?R(a):a}return a},r.writeFile=function(t,r,a){i();var n=u(t,a);e.writeFileSync(r,n)},r.utils={cfb_new:function(e){var t={};return f(t,e),t},cfb_add:eo,cfb_del:function(e,t){f(e);var r=e_.find(e,t);if(r){for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0}return!1},cfb_mov:function(e,t,r){f(e);var a=e_.find(e,t);if(a){for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==a)return e.FileIndex[s].name=n(r),e.FullPaths[s]=r,!0}return!1},cfb_gc:function(e){h(e,!0)},ReadShift:tK,CheckField:tQ,prep_blob:t1,bconcat:D,use_zlib:function(e){try{var r=new e.InflateRaw;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),r.bytesRead)t=e;else throw Error("zlib does not expose bytesRead")}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:Z,_inflateRaw:ea,consts:T},r}();function eA(e,t,r){if(void 0!==a&&a.writeFileSync)return r?a.writeFileSync(e,t,r):a.writeFileSync(e,t);if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=I(t);break;default:throw Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?to(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!=typeof Blob){var s=new Blob(["string"==typeof n?I(n):Array.isArray(n)?function(e){if("undefined"==typeof Uint8Array)throw Error("Unsupported");return new Uint8Array(e)}(n):n],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(s,e);if("undefined"!=typeof saveAs)return saveAs(s,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(s);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var o=document.createElement("a");if(null!=o.download)return o.download=e,o.href=i,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var l=File(e);return l.open("w"),l.encoding="binary",Array.isArray(t)&&(t=N(t)),l.write(t),l.close(),t}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("cannot save file "+e)}function ey(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function ex(e,t){for(var r=[],a=ey(e),n=0;n!==a.length;++n)null==r[e[a[n]][t]]&&(r[e[a[n]][t]]=a[n]);return r}function eO(e){for(var t=[],r=ey(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function ek(e){for(var t=[],r=ey(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}var eC=new Date(1899,11,30,0,0,0);function eR(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(eC.getTime()+(e.getTimezoneOffset()-eC.getTimezoneOffset())*6e4))/864e5}var eI=new Date,eN=eC.getTime()+(eI.getTimezoneOffset()-eC.getTimezoneOffset())*6e4,eD=eI.getTimezoneOffset();function eP(e){var t=new Date;return t.setTime(864e5*e+eN),t.getTimezoneOffset()!==eD&&t.setTime(t.getTime()+(t.getTimezoneOffset()-eD)*6e4),t}var eL=new Date("2017-02-19T19:06:09.000Z"),eF=isNaN(eL.getFullYear())?new Date("2/19/17"):eL,eM=2017==eF.getFullYear();function eU(e,t){var r=new Date(e);if(eM)return t>0?r.setTime(r.getTime()+6e4*r.getTimezoneOffset()):t<0&&r.setTime(r.getTime()-6e4*r.getTimezoneOffset()),r;if(e instanceof Date)return e;if(1917==eF.getFullYear()&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-6e4*s.getTimezoneOffset())),s}function eB(e,t){if(x&&l.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return to(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return to(b(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return to(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return to(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return r[e]||e})}catch(e){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function eH(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=eH(e[r]));return t}function eW(e,t){for(var r="";r.length<t;)r+=e;return r}function eG(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return isNaN(t=Number(a))&&isNaN(t=Number(a=a.replace(/[(](.*)[)]/,function(e,t){return r=-r,t})))?t:t/r}var eV=["january","february","march","april","may","june","july","august","september","october","november","december"];function ez(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==eV.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&101!=a?t:e.match(/[^-0-9:,\/\\]/)?r:t}function ej(e){return e?e.content&&e.type?eB(e.content,!0):e.data?w(e.data):e.asNodeBuffer&&x?w(e.asNodeBuffer().toString("binary")):e.asBinary?w(e.asBinary()):e._data&&e._data.getContent?w(eB(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function e$(e,t){for(var r=e.FullPaths||ey(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function eY(e,t){var r=e$(e,t);if(null==r)throw Error("Cannot find file "+t+" in zip");return r}function eK(e,t,r){if(!r){var a;return(a=eY(e,t))&&".bin"===a.name.slice(-4)?function(e){if(!e)return null;if(e.data)return T(e.data);if(e.asNodeBuffer&&x)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?T(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}(a):ej(a)}if(!t)return null;try{return eK(e,t)}catch(e){return null}}function eX(e,t,r){if(!r)return ej(eY(e,t));if(!t)return null;try{return eX(e,t)}catch(e){return null}}function eJ(e,t,r){if(e.FullPaths){if("string"==typeof r){var a;return a=x?O(r):function(e){for(var t=[],r=0,a=e.length+250,n=k(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=(1023&i)+64;var o=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|o>>6&15|(3&i)<<4,n[r++]=128|63&o}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=k(65535),a=65530)}return t.push(n.slice(0,r)),D(t)}(r),e_.utils.cfb_add(e,t,a)}e_.utils.cfb_add(e,t,r)}else e.file(t,r)}function eZ(){return e_.utils.cfb_new()}var eq='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',eQ=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,e1=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,e0=eq.match(e1)?e1:/<[^>]*>/g,e2=/<(\/?)\w+:/;function e4(e,t,r){for(var a={},n=0,s=0;n!==e.length&&32!==(s=e.charCodeAt(n))&&10!==s&&13!==s;++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(eQ),o=0,l="",c=0,f="",h="",u=1;if(i)for(c=0;c!=i.length;++c){for(s=0,h=i[c];s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(o=0,u=+(34==(n=h.charCodeAt(s+1))||39==n),l=h.slice(s+1+u,h.length-u);o!=f.length&&58!==f.charCodeAt(o);++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=l,r||(a[f.toLowerCase()]=l);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(a[d]&&"ext"==f.slice(o-3,o))continue;a[d]=l,r||(a[d.toLowerCase()]=l)}}return a}var e3=eO({"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"}),e5=/[&<>'"]/g,e6=/[\u0000-\u0008\u000b-\u001f]/g;function e8(e){return(e+"").replace(e5,function(e){return e3[e]}).replace(e6,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function e7(e){return e8(e).replace(/ /g,"_x0020_")}var e9=/[\u0000-\u001f]/g;function te(e){return(e+"").replace(e5,function(e){return e3[e]}).replace(/\n/g,"<br/>").replace(e9,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}function tt(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function tr(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0;r<e.length;){if((a=e.charCodeAt(r++))<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){t+=String.fromCharCode((31&a)<<6|63&n);continue}if(s=e.charCodeAt(r++),a<240){t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s);continue}t+=String.fromCharCode(55296+((o=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&e.charCodeAt(r++))-65536)>>>10&1023)),t+=String.fromCharCode(56320+(1023&o))}return t}function ta(e){var t,r,a,n=k(2*e.length),s=1,i=0,o=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=(31&a)*64+(63&e.charCodeAt(r+1)),s=2):a<240?(t=(15&a)*4096+(63&e.charCodeAt(r+1))*64+(63&e.charCodeAt(r+2)),s=3):(s=4,o=55296+((t=(7&a)*262144+(63&e.charCodeAt(r+1))*4096+(63&e.charCodeAt(r+2))*64+(63&e.charCodeAt(r+3))-65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(n[i++]=255&o,n[i++]=o>>>8,o=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function tn(e){return O(e,"binary").toString("utf8")}var ts="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",ti=x&&(tn(ts)==tr(ts)&&tn||ta(ts)==tr(ts)&&ta)||tr,to=x?function(e){return O(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,t.push(String.fromCharCode(240+((n=e.charCodeAt(r++)-56320+(a<<10))>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},tl=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[RegExp("&"+e[0]+";","ig"),e[1]]});return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),tc=/<\/?(?:vt:)?variant>/g,tf=/<(?:vt:)([^>]*)>([\s\S]*)</;function th(e,t){var r=e4(e),a=e.match((null)(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(e){var t=e.replace(tc,"").match(tf);t&&n.push({v:ti(t[2]),t:t[1]})}),n}var tu=/(^\s|\s$|\n)/;function td(e,t){return"<"+e+(t.match(tu)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function tp(e){return ey(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function tm(e,t,r){return"<"+e+(null!=r?tp(r):"")+(null!=t?(t.match(tu)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function tg(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}function tv(e){if(x&&l.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return ti(N(function e(t){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(t instanceof ArrayBuffer)return e(new Uint8Array(t));for(var r=Array(t.length),a=0;a<t.length;++a)r[a]=t[a];return r}(e)));throw Error("Bad input format: expected Buffer or string")}var tT=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,tb={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},tw=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],tE={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},tS=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},t_=x?function(e){return e[0].length>0&&l.isBuffer(e[0][0])?l.concat(e[0].map(function(e){return l.isBuffer(e)?e:O(e)})):tS(e)}:tS,tA=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(tz(e,n)));return a.join("").replace(P,"")},ty=x?function(e,t,r){return l.isBuffer(e)?e.toString("utf16le",t,r).replace(P,""):tA(e,t,r)}:tA,tx=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},tO=x?function(e,t,r){return l.isBuffer(e)?e.toString("hex",t,t+r):tx(e,t,r)}:tx,tk=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(tV(e,n)));return a.join("")},tC=x?function(e,t,r){return l.isBuffer(e)?e.toString("utf8",t,r):tk(e,t,r)}:tk,tR=function(e,t){var r=t$(e,t);return r>0?tC(e,t+4,t+4+r-1):""},tI=tR,tN=function(e,t){var r=t$(e,t);return r>0?tC(e,t+4,t+4+r-1):""},tD=tN,tP=function(e,t){var r=2*t$(e,t);return r>0?tC(e,t+4,t+4+r-1):""},tL=tP,tF=function(e,t){var r=t$(e,t);return r>0?ty(e,t+4,t+4+r):""},tM=tF,tU=function(e,t){var r=t$(e,t);return r>0?tC(e,t+4,t+4+r):""},tB=tU,tH=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?1/0*r:NaN:(0==a?a=-1022:(a-=1023,n+=0x10000000000000),r*Math.pow(2,a-52)*n)}(e,t)},tW=tH,tG=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};x&&(tI=function(e,t){if(!l.isBuffer(e))return tR(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tD=function(e,t){if(!l.isBuffer(e))return tN(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tL=function(e,t){if(!l.isBuffer(e))return tP(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},tM=function(e,t){if(!l.isBuffer(e))return tF(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},tB=function(e,t){if(!l.isBuffer(e))return tU(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},tW=function(e,t){return l.isBuffer(e)?e.readDoubleLE(t):tH(e,t)},tG=function(e){return l.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==n&&(ty=function(e,t,r){return n.utils.decode(1200,e.slice(t,r)).replace(P,"")},tC=function(e,t,r){return n.utils.decode(65001,e.slice(t,r))},tI=function(e,t){var r=t$(e,t);return r>0?n.utils.decode(u,e.slice(t+4,t+4+r-1)):""},tD=function(e,t){var r=t$(e,t);return r>0?n.utils.decode(h,e.slice(t+4,t+4+r-1)):""},tL=function(e,t){var r=2*t$(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},tM=function(e,t){var r=t$(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r)):""},tB=function(e,t){var r=t$(e,t);return r>0?n.utils.decode(65001,e.slice(t+4,t+4+r)):""});var tV=function(e,t){return e[t]},tz=function(e,t){return 256*e[t+1]+e[t]},tj=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-((65535-r+1)*1)},t$=function(e,t){return 0x1000000*e[t+3]+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},tY=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]};function tK(e,t){var r,a,s,i,o,c,f="",u=[];switch(t){case"dbcs":if(c=this.l,x&&l.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)f+=String.fromCharCode(tz(this,c)),c+=2;e*=2;break;case"utf8":f=tC(this,this.l,this.l+e);break;case"utf16le":e*=2,f=ty(this,this.l,this.l+e);break;case"wstr":if(void 0===n)return tK.call(this,e,"dbcs");f=n.utils.decode(h,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=tI(this,this.l),e=4+t$(this,this.l);break;case"lpstr-cp":f=tD(this,this.l),e=4+t$(this,this.l);break;case"lpwstr":f=tL(this,this.l),e=4+2*t$(this,this.l);break;case"lpp4":e=4+t$(this,this.l),f=tM(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+t$(this,this.l),f=tB(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(s=tV(this,this.l+e++));)u.push(E(s));f=u.join("");break;case"_wstr":for(e=0,f="";0!==(s=tz(this,this.l+e));)u.push(E(s)),e+=2;e+=2,f=u.join("");break;case"dbcs-cont":for(o=0,f="",c=this.l;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=tV(this,c),this.l=c+1,i=tK.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),u.join("")+i;u.push(E(tz(this,c))),c+=2}f=u.join(""),e*=2;break;case"cpstr":if(void 0!==n){f=n.utils.decode(h,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(o=0,f="",c=this.l;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=tV(this,c),this.l=c+1,i=tK.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),u.join("")+i;u.push(E(tV(this,c))),c+=1}f=u.join("");break;default:switch(e){case 1:return r=tV(this,this.l),this.l++,r;case 2:return r=("i"===t?tj:tz)(this,this.l),this.l+=2,r;case 4:case -4:if("i"===t||(128&this[this.l+3])==0)return r=(e>0?tY:function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]})(this,this.l),this.l+=4,r;return a=t$(this,this.l),this.l+=4,a;case 8:case -8:if("f"===t)return a=8==e?tW(this,this.l):tW([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:f=tO(this,this.l,e)}}return this.l+=e,f}var tX=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},tJ=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},tZ=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function tq(e,t,r){var a=0,s=0;if("dbcs"===r){for(s=0;s!=t.length;++s)tZ(this,t.charCodeAt(s),this.l+2*s);a=2*t.length}else if("sbcs"===r){if(void 0!==n&&874==u)for(s=0;s!=t.length;++s){var i=n.utils.encode(u,t.charAt(s));this[this.l+s]=i[0]}else for(s=0,t=t.replace(/[^\x00-\x7F]/g,"_");s!=t.length;++s)this[this.l+s]=255&t.charCodeAt(s);a=t.length}else if("hex"===r){for(;s<e;++s)this[this.l++]=parseInt(t.slice(2*s,2*s+2),16)||0;return this}else if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(s=0;s<Math.min(t.length,e);++s){var l=t.charCodeAt(s);this[this.l++]=255&l,this[this.l++]=l>>8}for(;this.l<o;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=255&t;break;case 2:a=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:a=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:a=4,tX(this,t,this.l);break;case 8:if(a=8,"f"===r){!function(e,t,r){var a=+(t<0||1/t==-1/0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<0x10000000000000)?n=-1022:(s-=0x10000000000000,n+=1023)):(n=2047,s=26985*!!isNaN(t));for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case -4:a=4,tJ(this,t,this.l)}return this.l+=a,this}function tQ(e,t){var r=tO(this,this.l,e.length>>1);if(r!==e)throw Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function t1(e,t){e.l=t,e.read_shift=tK,e.chk=tQ,e.write_shift=tq}function t0(e,t){e.l+=t}function t2(e){var t=k(e);return t1(t,0),t}function t4(){var e=[],t=x?256:2048,r=function(e){var t=t2(e);return t1(t,0),t},a=r(t),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),D(e)},_bufs:e}}function t3(e,t,r,a){var n,s=+t;if(!isNaN(s)){a||(a=sE[s].p||(r||[]).length||0),n=1+ +(s>=128)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,(127&s)+128),i.write_shift(1,s>>7));for(var o=0;4!=o;++o)if(a>=128)i.write_shift(1,(127&a)+128),a>>=7;else{i.write_shift(1,a);break}a>0&&tG(r)&&e.push(r)}}function t5(e,t,r){var a=eH(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function t6(e,t,r){var a=eH(e);return a.s=t5(a.s,t.s,r),a.e=t5(a.e,t.s,r),a}function t8(e,t){if(e.cRel&&e.c<0)for(e=eH(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=eH(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=rn(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=r.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),r}function t7(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?t8(e.s,t.biff)+":"+t8(e.e,t.biff):(e.s.rRel?"":"$")+re(e.s.r)+":"+(e.e.rRel?"":"$")+re(e.e.r):(e.s.cRel?"":"$")+rr(e.s.c)+":"+(e.e.cRel?"":"$")+rr(e.e.c)}function t9(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function re(e){return""+(e+1)}function rt(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function rr(e){if(e<0)throw Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function ra(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function rn(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function rs(e){var t=e.indexOf(":");return -1==t?{s:ra(e),e:ra(e)}:{s:ra(e.slice(0,t)),e:ra(e.slice(t+1))}}function ri(e,t){return void 0===t||"number"==typeof t?ri(e.s,e.e):("string"!=typeof e&&(e=rn(e)),"string"!=typeof t&&(t=rn(t)),e==t?e:e+":"+t)}function ro(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;return t.e.r=--r,t}function rl(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=ev(e.z,r?eR(t):t)}catch(e){}try{return e.w=ev((e.XF||{}).numFmtId||14*!!r,r?eR(t):t)}catch(e){return""+t}}function rc(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t)?rM[e.v]||e.v:void 0==t?rl(e,e.v):rl(e,t)}function rf(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function rh(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,o=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var l="string"==typeof a.origin?ra(a.origin):a.origin;i=l.r,o=l.c}s["!ref"]||(s["!ref"]="A1:A1")}var c={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=ro(s["!ref"]);c.s.c=f.s.c,c.s.r=f.s.r,c.e.c=Math.max(c.e.c,f.e.c),c.e.r=Math.max(c.e.r,f.e.r),-1==i&&(c.e.r=i=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=i+h,m=o+u;if(c.s.r>p&&(c.s.r=p),c.s.c>m&&(c.s.c=m),c.e.r<p&&(c.e.r=p),c.e.c<m&&(c.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date){if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v){if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||z[14],a.cellDates?(d.t="d",d.w=ev(d.z,eR(d.v))):(d.t="n",d.v=eR(d.v),d.w=ev(d.z,d.v))):d.t="s"}else d=t[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=rn({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return c.s.c<1e7&&(s["!ref"]=ri(c)),s}function ru(e,t){return rh(null,e,t)}function rd(e,t){return t||(t=t2(4)),t.write_shift(4,e),t}function rp(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function rm(e,t){var r=!1;return null==t&&(r=!0,t=t2(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rg(e,t){var r=e.l,a=e.read_shift(1),n=rp(e),s=[],i={t:n,h:n};if((1&a)!=0){for(var o=e.read_shift(4),l=0;l!=o;++l)s.push({ich:e.read_shift(2),ifnt:e.read_shift(2)});i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}function rv(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function rT(e,t){return null==t&&(t=t2(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rb(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function rw(e,t){return null==t&&(t=t2(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rE(e){var t=e.read_shift(4);return 0===t||0xffffffff===t?"":e.read_shift(t,"dbcs")}function rS(e,t){var r=!1;return null==t&&(r=!0,t=t2(127)),t.write_shift(4,e.length>0?e.length:0xffffffff),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function r_(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?tW([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):tY(t,0)>>2;return r?n/100:n}function rA(e,t){null==t&&(t=t2(4));var r=0,a=0,n=100*e;if(e==(0|e)&&e>=-0x20000000&&e<0x20000000?a=1:n==(0|n)&&n>=-0x20000000&&n<0x20000000&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw Error("unsupported RkNumber "+e)}function ry(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var rx=function(e,t){return t||(t=t2(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function rO(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function rk(e,t){return(t||t2(8)).write_shift(8,e,"f")}function rC(e,t){if(t||(t=t2(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function rR(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[e.read_shift(4)]||""}if(r>400)throw Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var rI=[80,81],rN={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rD={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rP={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},rL=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],rF=eH([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]})),rM={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rU={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},rB={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},rH={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function rW(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function rG(e,t){var r,a=function(e){for(var t=[],r=ey(e),a=0;a!==r.length;++a)null==t[e[r[a]]]&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}(rB),n=[];n[n.length]=eq,n[n.length]=tm("Types",null,{xmlns:tb.CT,"xmlns:xsd":tb.xsd,"xmlns:xsi":tb.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return tm("Default",null,{Extension:e[0],ContentType:e[1]})}));var s=function(a){e[a]&&e[a].length>0&&(r=e[a][0],n[n.length]=tm("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:rH[a][t.bookType]||rH[a].xlsx}))},i=function(r){(e[r]||[]).forEach(function(e){n[n.length]=tm("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:rH[r][t.bookType]||rH[r].xlsx})})},o=function(t){(e[t]||[]).forEach(function(e){n[n.length]=tm("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:a[t][0]})})};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var rV={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function rz(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function rj(e){var t=[eq,tm("Relationships",null,{xmlns:tb.RELS})];return ey(e["!id"]).forEach(function(r){t[t.length]=tm("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function r$(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,s?n.TargetMode=s:[rV.HLINK,rV.XPATH,rV.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}function rY(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function rK(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+f.version+"</meta:generator></office:meta></office:document-meta>"}var rX=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function rJ(e,t,r,a,n){null==n[e]&&null!=t&&""!==t&&(n[e]=t,t=e8(t),a[a.length]=r?tm(e,t,r):td(e,t))}function rZ(e,t){var r=t||{},a=[eq,tm("cp:coreProperties",null,{"xmlns:cp":tb.CORE_PROPS,"xmlns:dc":tb.dc,"xmlns:dcterms":tb.dcterms,"xmlns:dcmitype":tb.dcmitype,"xmlns:xsi":tb.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(null!=e.CreatedDate&&rJ("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:tg(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&rJ("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:tg(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=rX.length;++s){var i=rX[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&rJ(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var rq=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],rQ=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function r1(e){var t=[];return e||(e={}),e.Application="SheetJS",t[t.length]=eq,t[t.length]=tm("Properties",null,{xmlns:tb.EXT_PROPS,"xmlns:vt":tb.vt}),rq.forEach(function(r){var a;if(void 0!==e[r[1]]){switch(r[2]){case"string":a=e8(String(e[r[1]]));break;case"bool":a=e[r[1]]?"true":"false"}void 0!==a&&(t[t.length]=tm(r[0],a))}}),t[t.length]=tm("HeadingPairs",tm("vt:vector",tm("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+tm("vt:variant",tm("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=tm("TitlesOfParts",tm("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+e8(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function r0(e){var t=[eq,tm("Properties",null,{xmlns:tb.CUST_PROPS,"xmlns:vt":tb.vt})];if(!e)return t.join("");var r=1;return ey(e).forEach(function(a){++r,t[t.length]=tm("property",function(e,t){switch(typeof e){case"string":var r=tm("vt:lpwstr",e8(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return tm((0|e)==e?"vt:i4":"vt:r8",e8(String(e)));case"boolean":return tm("vt:bool",e?"true":"false")}if(e instanceof Date)return tm("vt:filetime",tg(e));throw Error("Unable to serialize "+e)}(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:e8(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var r2={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function r4(e){var t=e.read_shift(4);return new Date((e.read_shift(4)/1e7*0x100000000+t/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function r3(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function r5(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function r6(e,t,r){return 31===t?r5(e):r3(e,t,r)}function r8(e,t,r){return r6(e,t,4*(!1!==r))}function r7(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(P,"").replace(L,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function r9(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function ae(e,t,r){var a,n,s=e.read_shift(2),i=r||{};if(e.l+=2,12!==t&&s!==t&&-1===rI.indexOf(t)&&((65534&t)!=4126||(65534&s)!=4126))throw Error("Expected type "+t+" saw "+s);switch(12===t?s:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return r3(e,s,4).replace(P,"");case 31:return r5(e);case 64:return r4(e);case 65:return r9(e);case 71:return(a={}).Size=e.read_shift(4),e.l+=a.Size+3-(a.Size-1)%4,a;case 80:return r8(e,s,!i.raw).replace(P,"");case 81:return(function(e,t){if(!t)throw Error("VtUnalignedString must have positive length");return r6(e,t,0)})(e,s).replace(P,"");case 4108:return function(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(function(e){var t=e.l,r=ae(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,ae(e,3)]}(e));return r}(e);case 4126:case 4127:return 4127==s?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(P,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(P,"");return r}(e);default:throw Error("TypedPropertyValue unrecognized type "+t+" "+s)}}function at(e,t){var r,a,n,s,i,o=t2(4),l=t2(4);switch(o.write_shift(4,80==e?31:e),e){case 3:l.write_shift(-4,t);break;case 5:(l=t2(8)).write_shift(8,t,"f");break;case 11:l.write_shift(4,+!!t);break;case 64:a=(r=("string"==typeof t?new Date(Date.parse(t)):t).getTime()/1e3+0x2b6109100)%0x100000000,n=(r-a)/0x100000000*1e7,(s=(a*=1e7)/0x100000000|0)>0&&(a%=0x100000000,n+=s),(i=t2(8)).write_shift(4,a),i.write_shift(4,n),l=i;break;case 31:case 80:for((l=t2(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),l.write_shift(0,t,"dbcs");l.l!=l.length;)l.write_shift(1,0);break;default:throw Error("TypedPropertyValue unrecognized type "+e+" "+t)}return D([o,l])}function ar(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,o=0,l=-1,c={};for(i=0;i!=n;++i){var f=e.read_shift(4),h=e.read_shift(4);s[i]=[f,h+r]}s.sort(function(e,t){return e[1]-t[1]});var u={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var p=t[s[i][0]];if(u[p.n]=ae(e,p.t,{raw:!0}),"version"===p.p&&(u[p.n]=String(u[p.n]>>16)+"."+("0000"+String(65535&u[p.n])).slice(-4)),"CodePage"==p.n)switch(u[p.n]){case 0:u[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:g(o=u[p.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+u[p.n])}}else if(1===s[i][0]){if(g(o=u.CodePage=ae(e,2)),-1!==l){var m=e.l;e.l=s[l][1],c=r7(e,o),e.l=m}}else if(0===s[i][0]){if(0===o){l=i,e.l=s[i+1][1];continue}c=r7(e,o)}else{var v,T=c[s[i][0]];switch(e[e.l]){case 65:e.l+=4,v=r9(e);break;case 30:case 31:e.l+=4,v=r8(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=al(e,4);break;case 64:e.l+=4,v=eU(r4(e));break;default:throw Error("unparsed value: "+e[e.l])}u[T]=v}}return e.l=r+a,u}var aa=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function an(e,t,r){var a=t2(8),n=[],s=[],i=8,o=0,l=t2(8),c=t2(8);if(l.write_shift(4,2),l.write_shift(4,1200),c.write_shift(4,1),s.push(l),n.push(c),i+=8+l.length,!t){(c=t2(8)).write_shift(4,0),n.unshift(c);var f=[t2(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((l=t2(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),l.write_shift(4,h.length+1),l.write_shift(0,h,"dbcs");l.l!=l.length;)l.write_shift(1,0);f.push(l)}l=D(f),s.unshift(l),i+=8+l.length}for(o=0;o<e.length;++o)if(!(t&&!t[e[o][0]]||aa.indexOf(e[o][0])>-1||rQ.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}l=at(p.t,u)}else{var g=function(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return -1}(u);-1==g&&(g=31,u=String(u)),l=at(g,u)}s.push(l),(c=t2(8)).write_shift(4,t?d:2+o),n.push(c),i+=8+l.length}var v=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,v),v+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),D([a].concat(n).concat(s))}function as(e,t,r){var a,n=e.content;if(!n)return{};t1(n,0);var s,i,o,l,c=0;n.chk("feff","Byte Order: "),n.read_shift(2);var f=n.read_shift(4),h=n.read_shift(16);if(h!==e_.utils.consts.HEADER_CLSID&&h!==r)throw Error("Bad PropertySet CLSID "+h);if(1!==(s=n.read_shift(4))&&2!==s)throw Error("Unrecognized #Sets: "+s);if(i=n.read_shift(16),l=n.read_shift(4),1===s&&l!==n.l)throw Error("Length mismatch: "+l+" !== "+n.l);2===s&&(o=n.read_shift(16),c=n.read_shift(4));var u=ar(n,t),d={SystemIdentifier:f};for(var p in u)d[p]=u[p];if(d.FMTID=i,1===s)return d;if(c-n.l==2&&(n.l+=2),n.l!==c)throw Error("Length mismatch 2: "+n.l+" !== "+c);try{a=ar(n,null)}catch(e){}for(p in a)d[p]=a[p];return d.FMTID=[i,o],d}function ai(e,t,r,a,n,s){var i=t2(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,0x32363237),i.write_shift(16,e_.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var l=an(e,r,a);if(o.push(l),n){var c=an(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+l.length),o.push(c)}return D(o)}function ao(e,t){return e.read_shift(t),null}function al(e,t){return 1===e.read_shift(t)}function ac(e,t){return t||(t=t2(2)),t.write_shift(2,+!!e),t}function af(e){return e.read_shift(2,"u")}function ah(e,t){return t||(t=t2(2)),t.write_shift(2,e),t}function au(e,t){return function(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw Error("Slurp error");return a}(e,t,af)}function ad(e,t,r){return r||(r=t2(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,+("e"==t)),r}function ap(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont",s=h;r&&r.biff>=8&&(h=1200),r&&8!=r.biff?12==r.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont"),r.biff>=2&&r.biff<=5&&(n="cpstr");var i=a?e.read_shift(a,n):"";return h=s,i}function am(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function ag(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):am(e,a,r)}function av(e,t,r){if(r.biff>5)return ag(e,t,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function aT(e,t,r){return r||(r=t2(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function ab(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(P,""):""}function aw(e,t){t||(t=t2(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function aE(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function aS(e,t){var r=aE(e,t);return r[3]=0,r}function a_(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function aA(e,t,r,a){return a||(a=t2(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function ay(e){return[e.read_shift(2),r_(e)]}function ax(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function aO(e,t){return t||(t=t2(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function ak(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}function aC(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function aR(e){e.l+=2,e.l+=e.read_shift(2)}var aI={0:aR,4:aR,5:aR,6:aR,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:aR,9:aR,10:aR,11:aR,12:aR,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:aR,15:aR,16:aR,17:aR,18:aR,19:aR,20:aR,21:aC};function aN(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function aD(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;default:throw Error("unsupported BIFF version")}var s=t2(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function aP(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function aL(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),l=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c:l}}}function aF(e,t,r,a){var n=r&&5==r.biff;a||(a=t2(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function aM(e,t,r){var a,n=a_(e,6);(2==r.biff||9==t)&&++e.l;var s=(a=e.read_shift(1),1===e.read_shift(1)?a:1===a);return n.val=s,n.t=!0===s||!1===s?"b":"e",n}var aU=function(e,t,r){return 0===t?"":av(e,t,r)};function aB(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=ap(e,t,r),s=e.read_shift(2);if(s!==(a-=e.l))throw Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"==typeof a&&(s.Name=a),s}var aH=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function aW(e,t,r){var a,n,s,i,o,l,c,f=e.l+t,h=e.read_shift(2),u=e.read_shift(1),d=e.read_shift(1),p=e.read_shift(r&&2==r.biff?1:2),m=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),m=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var g=am(e,d,r);32&h&&(g=aH[g.charCodeAt(0)]);var v=f-e.l;return r&&2==r.biff&&--v,{chKey:u,Name:g,itab:m,rgce:f!=e.l&&0!==p&&v>0?(a=e,n=v,s=r,i=p,l=a.l+n,c=nX(a,i,s),l!==a.l&&(o=nK(a,l-a.l,c,s)),[c,o]):[]}}function aG(e,t,r){if(r.biff<8){var a,n,s,i;return a=e,n=t,s=r,3==a[a.l+1]&&a[a.l]++,3==(i=ap(a,n,s)).charCodeAt(0)?i.slice(1):i}for(var o=[],l=e.l+t,c=e.read_shift(r.biff>8?4:2);0!=c--;)o.push(function(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}(e,r.biff,r));if(e.l!=l)throw Error("Bad ExternSheet: "+e.l+" != "+l);return o}function aV(e,t,r){var a=ak(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(a=t-2,void(e.l+=a))];var l=nX(e,o,r);return t!==o+i&&(n=nK(e,t-o-i,l,r)),e.l=s,[l,n]}(e,t,r,a)]}var az={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function aj(e,t,r){if(!r.cellStyles)return void(e.l+=t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),l=e.read_shift(2);2==a&&(e.l+=2);var c={s:n,e:s,w:i,ixfe:o,flags:l};return(r.biff>=5||!r.biff)&&(c.level=l>>8&7),c}var a$=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=eO({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var s=ru(function(t,r){var a=[],s=k(1);switch(r.type){case"base64":s=R(y(t));break;case"binary":s=R(t);break;case"buffer":case"array":s=t}t1(s,0);var i=s.read_shift(1),o=!!(136&i),l=!1,c=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:l=!0,o=!0;break;case 140:c=!0;break;default:throw Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=r.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),c&&(s.l+=36);for(var p=[],m={},g=Math.min(s.length,2==i?521:h-10-264*!!l),v=c?32:11;s.l<g&&13!=s[s.l];)switch((m={}).name=n.utils.decode(d,s.slice(s.l,s.l+v)).replace(/[\u0000\r\n].*$/g,""),s.l+=v,m.type=String.fromCharCode(s.read_shift(1)),2==i||c||(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=c?13:14),m.type){case"B":(!l||8!=m.len)&&r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var T=0,b=0;for(b=0,a[0]=[];b!=p.length;++b)a[0][b]=p[b].name;for(;f-- >0;){if(42===s[s.l]){s.l+=u;continue}for(++s.l,a[++T]=[],b=0,b=0;b!=p.length;++b){var w=s.slice(s.l,s.l+p[b].len);s.l+=p[b].len,t1(w,0);var E=n.utils.decode(d,w);switch(p[b].type){case"C":E.trim().length&&(a[T][b]=E.replace(/\s+$/,""));break;case"D":8===E.length?a[T][b]=new Date(+E.slice(0,4),+E.slice(4,6)-1,+E.slice(6,8)):a[T][b]=E;break;case"F":a[T][b]=parseFloat(E.trim());break;case"+":case"I":a[T][b]=c?0x80000000^w.read_shift(-4,"i"):w.read_shift(4,"i");break;case"L":switch(E.trim().toUpperCase()){case"Y":case"T":a[T][b]=!0;break;case"N":case"F":a[T][b]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+E+"|")}break;case"M":if(!o)throw Error("DBF Unexpected MEMO for type "+i.toString(16));a[T][b]="##MEMO##"+(c?parseInt(E.trim(),10):w.read_shift(4));break;case"N":(E=E.replace(/\u0000/g,"").trim())&&"."!=E&&(a[T][b]=+E||0);break;case"@":a[T][b]=new Date(w.read_shift(-8,"f")-621356832e5);break;case"T":a[T][b]=new Date((w.read_shift(4)-2440588)*864e5+w.read_shift(4));break;case"Y":a[T][b]=w.read_shift(4,"i")/1e4+w.read_shift(4,"i")/1e4*0x100000000;break;case"O":a[T][b]=-w.read_shift(-8,"f");break;case"B":if(l&&8==p[b].len){a[T][b]=w.read_shift(8,"f");break}case"G":case"P":w.l+=p[b].len;break;case"0":if("_NullFlags"===p[b].name)break;default:throw Error("DBF Unsupported data type "+p[b].type)}}}if(2!=i&&s.l<s.length&&26!=s[s.l++])throw Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return r&&r.sheetRows&&(a=a.slice(0,r.sheetRows)),r.DBF=p,a}(t,a),a);return s["!cols"]=a.DBF.map(function(e){return{wch:e.len,DBF:e}}),delete a.DBF,s}var a={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return rf(r(e,t),t)}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var n=r||{};if(+n.codepage>=0&&g(+n.codepage),"string"==n.type)throw Error("Cannot write DBF to JS string");var s=t4(),i=s8(e,{header:1,raw:!0,cellDates:!0}),o=i[0],l=i.slice(1),c=e["!cols"]||[],f=0,h=0,d=0,p=1;for(f=0;f<o.length;++f){if(((c[f]||{}).DBF||{}).name){o[f]=c[f].DBF.name,++d;continue}if(null!=o[f]){if(++d,"number"==typeof o[f]&&(o[f]=o[f].toString(10)),"string"!=typeof o[f])throw Error("DBF Invalid column name "+o[f]+" |"+typeof o[f]+"|");if(o.indexOf(o[f])!==f){for(h=0;h<1024;++h)if(-1==o.indexOf(o[f]+"_"+h)){o[f]+="_"+h;break}}}}var m=ro(e["!ref"]),v=[],T=[],b=[];for(f=0;f<=m.e.c-m.s.c;++f){var w="",E="",S=0,_=[];for(h=0;h<l.length;++h)null!=l[h][f]&&_.push(l[h][f]);if(0==_.length||null==o[f]){v[f]="?";continue}for(h=0;h<_.length;++h){switch(typeof _[h]){case"number":E="B";break;case"string":default:E="C";break;case"boolean":E="L";break;case"object":E=_[h]instanceof Date?"D":"C"}S=Math.max(S,String(_[h]).length),w=w&&w!=E?"C":E}S>250&&(S=250),"C"==(E=((c[f]||{}).DBF||{}).type)&&c[f].DBF.len>S&&(S=c[f].DBF.len),"B"==w&&"N"==E&&(w="N",b[f]=c[f].DBF.dec,S=c[f].DBF.len),T[f]="C"==w||"N"==E?S:a[w]||0,p+=T[f],v[f]=w}var A=s.next(32);for(A.write_shift(4,0x13021130),A.write_shift(4,l.length),A.write_shift(2,296+32*d),A.write_shift(2,p),f=0;f<4;++f)A.write_shift(4,0);for(A.write_shift(4,0|(+t[u]||3)<<8),f=0,h=0;f<o.length;++f)if(null!=o[f]){var y=s.next(32),x=(o[f].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);y.write_shift(1,x,"sbcs"),y.write_shift(1,"?"==v[f]?"C":v[f],"sbcs"),y.write_shift(4,h),y.write_shift(1,T[f]||a[v[f]]||0),y.write_shift(1,b[f]||0),y.write_shift(1,2),y.write_shift(4,0),y.write_shift(1,0),y.write_shift(4,0),y.write_shift(4,0),h+=T[f]||a[v[f]]||0}var O=s.next(264);for(O.write_shift(4,13),f=0;f<65;++f)O.write_shift(4,0);for(f=0;f<l.length;++f){var k=s.next(p);for(k.write_shift(1,0),h=0;h<o.length;++h)if(null!=o[h])switch(v[h]){case"L":k.write_shift(1,null==l[f][h]?63:l[f][h]?84:70);break;case"B":k.write_shift(8,l[f][h]||0,"f");break;case"N":var C="0";for("number"==typeof l[f][h]&&(C=l[f][h].toFixed(b[h]||0)),d=0;d<T[h]-C.length;++d)k.write_shift(1,32);k.write_shift(1,C,"sbcs");break;case"D":l[f][h]?(k.write_shift(4,("0000"+l[f][h].getFullYear()).slice(-4),"sbcs"),k.write_shift(2,("00"+(l[f][h].getMonth()+1)).slice(-2),"sbcs"),k.write_shift(2,("00"+l[f][h].getDate()).slice(-2),"sbcs")):k.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=l[f][h]?l[f][h]:"").slice(0,T[h]);for(k.write_shift(1,R,"sbcs"),d=0;d<T[h]-R.length;++d)k.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),aY=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=RegExp("\x1bN("+ey(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var a=e[r];return"number"==typeof a?S(a):a},a=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:S(a)};function s(e,s){var i,o=e.split(/[\n\r]+/),l=-1,c=-1,f=0,h=0,u=[],d=[],p=null,m={},v=[],T=[],b=[],w=0;for(+s.codepage>=0&&g(+s.codepage);f!==o.length;++f){w=0;var E,S=o[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),_=S.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")}),A=_[0];if(S.length>0)switch(A){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==_[1].charAt(0)&&d.push(S.slice(3).replace(/;;/g,";"));break;case"C":var y=!1,x=!1,O=!1,k=!1,C=-1,R=-1;for(h=1;h<_.length;++h)switch(_[h].charAt(0)){case"A":case"G":break;case"X":c=parseInt(_[h].slice(1))-1,x=!0;break;case"Y":for(l=parseInt(_[h].slice(1))-1,x||(c=0),i=u.length;i<=l;++i)u[i]=[];break;case"K":'"'===(E=_[h].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(eG(E))?isNaN(ez(E).getDate())||(E=eU(E)):(E=eG(E),null!==p&&ep(p)&&(E=eP(E))),void 0!==n&&"string"==typeof E&&"string"!=(s||{}).type&&(s||{}).codepage&&(E=n.utils.decode(s.codepage,E)),y=!0;break;case"E":k=!0;var I=nR(_[h].slice(1),{r:l,c:c});u[l][c]=[u[l][c],I];break;case"S":O=!0,u[l][c]=[u[l][c],"S5S"];break;case"R":C=parseInt(_[h].slice(1))-1;break;case"C":R=parseInt(_[h].slice(1))-1;break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}if(y&&(u[l][c]&&2==u[l][c].length?u[l][c][0]=E:u[l][c]=E,p=null),O){if(k)throw Error("SYLK shared formula cannot have own formula");var N=C>-1&&u[C][R];if(!N||!N[1])throw Error("SYLK shared formula cannot find base");u[l][c][1]=function(e,t){return e.replace(nI,function(e,r,a,n,s,i){return r+("$"==a?a+n:rr(rt(n)+t.c))+("$"==s?s+i:re(t9(i)+t.r))})}(N[1],{r:l-C,c:c-R})}break;case"F":var D=0;for(h=1;h<_.length;++h)switch(_[h].charAt(0)){case"X":c=parseInt(_[h].slice(1))-1,++D;break;case"Y":for(l=parseInt(_[h].slice(1))-1,i=u.length;i<=l;++i)u[i]=[];break;case"M":w=parseInt(_[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":p=d[parseInt(_[h].slice(1))];break;case"W":for(i=parseInt((b=_[h].slice(1).split(" "))[0],10);i<=parseInt(b[1],10);++i)w=parseInt(b[2],10),T[i-1]=0===w?{hidden:!0}:{wch:w},no(T[i-1]);break;case"C":T[c=parseInt(_[h].slice(1))-1]||(T[c]={});break;case"R":v[l=parseInt(_[h].slice(1))-1]||(v[l]={}),w>0?(v[l].hpt=w,v[l].hpx=nc(w)):0===w&&(v[l].hidden=!0);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}D<1&&(p=null);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}}return v.length>0&&(m["!rows"]=v),T.length>0&&(m["!cols"]=T),s&&s.sheetRows&&(u=u.slice(0,s.sheetRows)),[u,m]}function i(e,t){var r=function(e,t){switch(t.type){case"base64":return s(y(e),t);case"binary":return s(e,t);case"buffer":return s(x&&l.isBuffer(e)?e.toString("binary"):N(e),t);case"array":return s(eB(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),a=r[0],n=r[1],i=ru(a,t);return ey(n).forEach(function(e){i[e]=n[e]}),i}return e["|"]=254,{to_workbook:function(e,t){return rf(i(e,t),t)},to_sheet:i,from_sheet:function(e,t){var r,a=["ID;PWXL;N;E"],n=[],s=ro(e["!ref"]),i=Array.isArray(e);a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&function(e,t){t.forEach(function(t,r){var a="F;W"+(r+1)+" "+(r+1)+" ";t.hidden?a+="0":("number"!=typeof t.width||t.wpx||(t.wpx=na(t.width)),"number"!=typeof t.wpx||t.wch||(t.wch=nn(t.wpx)),"number"==typeof t.wch&&(a+=Math.round(t.wch)))," "!=a.charAt(a.length-1)&&e.push(a)})}(a,e["!cols"]),e["!rows"]&&function(e,t){t.forEach(function(t,r){var a="F;";t.hidden?a+="M0;":t.hpt?a+="M"+20*t.hpt+";":t.hpx&&(a+="M"+20*nl(t.hpx)+";"),a.length>2&&e.push(a+"R"+(r+1))})}(a,e["!rows"]),a.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var o=s.s.r;o<=s.e.r;++o)for(var l=s.s.c;l<=s.e.c;++l){var c=rn({r:o,c:l});(r=i?(e[o]||[])[l]:e[c])&&(null!=r.v||r.f&&!r.F)&&n.push(function(e,t,r,a){var n="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+nN(e.f,{r:r,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}(r,0,o,l,t))}return a.join("\r\n")+"\r\n"+n.join("\r\n")+"\r\nE\r\n"}}}(),aK=function(){var e,t;function r(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){if("BOT"===r[s].trim()){i[++a]=[],n=0;continue}if(!(a<0)){for(var o=r[s].trim().split(","),l=o[0],c=o[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+l){case -1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(eG(c))?isNaN(ez(c).getDate())?i[a][n]=c:i[a][n]=eU(c):i[a][n]=eG(c),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function a(e,t){return ru(function(e,t){switch(t.type){case"base64":return r(y(e),t);case"binary":return r(e,t);case"buffer":return r(x&&l.isBuffer(e)?e.toString("binary"):N(e),t);case"array":return r(eB(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),t)}return{to_workbook:function(e,t){return rf(a(e,t),t)},to_sheet:a,from_sheet:(e=function(e,t,r,a,n){e.push(t),e.push(r+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},t=function(e,t,r,a){e.push(t+","+r),e.push(1==t?'"'+a.replace(/"/g,'""')+'"':a)},function(r){var a,n=[],s=ro(r["!ref"]),i=Array.isArray(r);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){t(n,-1,0,"BOT");for(var l=s.s.c;l<=s.e.c;++l){var c=rn({r:o,c:l});if(!(a=i?(r[o]||[])[l]:r[c])){t(n,1,0,"");continue}switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?!a.f||a.F?t(n,1,0,""):t(n,1,0,"="+a.f):t(n,0,f,"V");break;case"b":t(n,0,+!!a.v,a.v?"TRUE":"FALSE");break;case"s":t(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=ev(a.z||z[14],eR(eU(a.v)))),t(n,0,a.w,"V");break;default:t(n,1,0,"")}}}return t(n,-1,0,"EOD"),n.join("\r\n")})}}(),aX=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return ru(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var l=ra(o[1]);if(i.length<=l.r)for(a=i.length;a<=l.r;++a)i[a]||(i[a]=[]);switch(a=l.r,n=l.c,o[2]){case"t":i[a][n]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+o[3];break;case"vtf":var c=o[o.length-1];case"vtc":"nl"===o[3]?i[a][n]=!!+o[4]:i[a][n]=+o[4],"vtf"==o[2]&&(i[a][n]=[i[a][n],c])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(e,r){return rf(t(e,r),r)},to_sheet:t,from_sheet:function(t){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",r,"# SocialCalc Spreadsheet Control Save\npart:sheet",r,function(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=rs(t["!ref"]),o=Array.isArray(t),l=i.s.r;l<=i.e.r;++l)for(var c=i.s.c;c<=i.e.c;++c)if(s=rn({r:l,c:c}),(r=o?(t[l]||[])[c]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=eR(eU(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||ev(r.z||z[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}(t),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),aJ=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(eG(e))?isNaN(ez(e).getDate())?t[r][a]=e:t[r][a]=eU(e):t[r][a]=eG(e))}var t={44:",",9:"	",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort(function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]}),t[i.pop()[1]]||44}function s(t,r){var s,i="",o="string"==r.type?[0,0,0,0]:function(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=y(e.slice(0,12));break;case"binary":r=e;break;default:throw Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}(t,r);switch(r.type){case"base64":i=y(t);break;case"binary":case"string":i=t;break;case"buffer":i=65001==r.codepage?t.toString("utf8"):r.codepage&&void 0!==n?n.utils.decode(r.codepage,t):x&&l.isBuffer(t)?t.toString("binary"):N(t);break;case"array":i=eB(t);break;default:throw Error("Unrecognized type "+r.type)}return(239==o[0]&&187==o[1]&&191==o[2]?i=ti(i.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?i=ti(i):"binary"==r.type&&void 0!==n&&r.codepage&&(i=n.utils.decode(r.codepage,n.utils.encode(28591,i))),"socialcalc:version:"==i.slice(0,19))?aX.to_sheet("string"==r.type?i:ti(i),r):(s=i,!(r&&r.PRN)||r.FS||"sep="==s.slice(0,4)||s.indexOf("	")>=0||s.indexOf(",")>=0||s.indexOf(";")>=0?function(e,t){var r,n,s=t||{},i="",o=s.dense?[]:{},l={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(i=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(i=e.charAt(4),e=e.slice(6)):i=a(e.slice(0,1024)):i=s&&s.FS?s.FS:a(e.slice(0,1024));var c=0,f=0,h=0,u=0,d=0,p=i.charCodeAt(0),m=!1,g=0,v=e.charCodeAt(0);e=e.replace(/\r\n/mg,"\n");var T=null!=s.dateNF?RegExp("^"+("number"==typeof(r=s.dateNF)?z[r]:r).replace(eE,"(\\d+)")+"$"):null;function b(){var t=e.slice(u,d),r={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)r.t="z";else if(s.raw)r.t="s",r.v=t;else if(0===t.trim().length)r.t="s",r.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(r.t="s",r.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(r.t="n",r.f=t.slice(1)):(r.t="s",r.v=t);else if("TRUE"==t)r.t="b",r.v=!0;else if("FALSE"==t)r.t="b",r.v=!1;else if(isNaN(h=eG(t))){if(!isNaN(ez(t).getDate())||T&&t.match(T)){r.z=s.dateNF||z[14];var a,n,i,m,b,w,E,S,_,A,y=0;T&&t.match(T)&&(a=s.dateNF,n=t.match(T)||[],i=-1,m=-1,b=-1,w=-1,E=-1,S=-1,(a.match(eE)||[]).forEach(function(e,t){var r=parseInt(n[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":i=r;break;case"d":b=r;break;case"h":w=r;break;case"s":S=r;break;case"m":w>=0?E=r:m=r}}),S>=0&&-1==E&&m>=0&&(E=m,m=-1),7==(_=(""+(i>=0?i:new Date().getFullYear())).slice(-4)+"-"+("00"+(m>=1?m:1)).slice(-2)+"-"+("00"+(b>=1?b:1)).slice(-2)).length&&(_="0"+_),8==_.length&&(_="20"+_),A=("00"+(w>=0?w:0)).slice(-2)+":"+("00"+(E>=0?E:0)).slice(-2)+":"+("00"+(S>=0?S:0)).slice(-2),t=-1==w&&-1==E&&-1==S?_:-1==i&&-1==m&&-1==b?A:_+"T"+A,y=1),s.cellDates?(r.t="d",r.v=eU(t,y)):(r.t="n",r.v=eR(eU(t,y))),!1!==s.cellText&&(r.w=ev(r.z,r.v instanceof Date?eR(r.v):r.v)),s.cellNF||delete r.z}else r.t="s",r.v=t}else r.t="n",!1!==s.cellText&&(r.w=t),r.v=h;if("z"==r.t||(s.dense?(o[c]||(o[c]=[]),o[c][f]=r):o[rn({c:f,r:c})]=r),u=d+1,v=e.charCodeAt(u),l.e.c<f&&(l.e.c=f),l.e.r<c&&(l.e.r=c),g==p)++f;else if(f=0,++c,s.sheetRows&&s.sheetRows<=c)return!0}e:for(;d<e.length;++d)switch(g=e.charCodeAt(d)){case 34:34===v&&(m=!m);break;case p:case 10:case 13:if(!m&&b())break e}return d-u>0&&b(),o["!ref"]=ri(l),o}(s,r):ru(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var o=10,l=0,c=0;c<=i;++c)-1==(l=s[c].indexOf(" "))?l=s[c].length:l++,o=Math.max(o,l);for(c=0;c<=i;++c){n[c]=[];var f=0;for(e(s[c].slice(0,o).trim(),n,c,f,a),f=1;f<=(s[c].length-o)/10+1;++f)e(s[c].slice(o+(f-1)*10,o+10*f).trim(),n,c,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(s,r),r))}return{to_workbook:function(e,t){return rf(s(e,t),t)},to_sheet:s,from_sheet:function(e){for(var t,r=[],a=ro(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],o=a.s.c;o<=a.e.c;++o){var l=rn({r:s,c:o});if(!(t=n?(e[s]||[])[o]:e[l])||null==t.v){i.push("          ");continue}for(var c=(t.w||(rc(t),t.w)||"").slice(0,10);c.length<10;)c+=" ";i.push(c+(0===o?" ":""))}r.push(i.join(""))}return r.join("\n")}}}(),aZ=function(){function e(e,t,r){if(e){t1(e,e.l||0);for(var a=r.Enum||h;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),o=e.l+i,l=s.f&&s.f(e,i,r);if(e.l=o,t(l,s,n))return}}}function t(t,r){if(!t)return t;var a=r||{},n=a.dense?[]:{},s="Sheet1",i="",o=0,l={},c=[],f=[],d={s:{r:0,c:0},e:{r:0,c:0}},p=a.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw Error("Unsupported Works 3 for Mac file");if(2==t[2])a.Enum=h,e(t,function(e,t,r){switch(r){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:d=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&(112&e[2])==112&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||z[14],a.cellDates&&(e[1].t="d",e[1].v=eP(e[1].v))),a.qpro&&e[3]>o&&(n["!ref"]=ri(d),l[s]=n,c.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=i||"Sheet"+(o+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[rn(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rn(e[0])]=e[1]}},a);else if(26==t[2]||14==t[2])a.Enum=u,14==t[2]&&(a.qpro=!0,t.l=0),e(t,function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(n["!ref"]=ri(d),l[s]=n,c.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},s="Sheet"+((o=e[3])+1)),p>0&&e[0].r>=p)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rn(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==o&&(s=e[1])}},a);else throw Error("Unrecognized LOTUS BOF "+t[2]);if(n["!ref"]=ri(d),l[i||s]=n,c.push(i||s),!f.length)return{SheetNames:c,Sheets:l};for(var m={},g=[],v=0;v<f.length;++v)l[c[v]]?(g.push(f[v]||c[v]),m[f[v]]=l[f[v]]||l[c[v]]):(g.push(f[v]),m[f[v]]={"!ref":"A1"});return{SheetNames:g,Sheets:m}}function r(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,t,a){var n=e.l+t,s=r(e,t,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function n(e,t,r){var a=32768&t;return t&=-32769,t=(a?e:0)+(t>=8192?t-16384:t),(a?"":"$")+(r?rr(t):re(t))}var s={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},i=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function o(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function l(e,t){var r=o(e,t),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&0xc0000000===n?(r[1].t="e",r[1].v=15):0===a&&0xd0000000===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function c(e,t){var r=o(e,t),a=e.read_shift(8,"f");return r[1].v=a,r}function f(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}var h={0:{n:"BOF",f:af},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2)):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0)),a}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,t,a){var o=e.l+t,l=r(e,t,a);if(l[1].v=e.read_shift(8,"f"),a.qpro)e.l=o;else{var c=e.read_shift(2);(function(e,t){t1(e,0);for(var r=[],a=0,o="",l="",c="",f="";e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:l=n(t[0].c,e.read_shift(2),!0),o=n(t[0].r,e.read_shift(2),!1),r.push(l+o);break;case 2:var u=n(t[0].c,e.read_shift(2),!0),d=n(t[0].r,e.read_shift(2),!1);l=n(t[0].c,e.read_shift(2),!0),o=n(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+l+o);break;case 3:if(e.l<e.length){console.error("WK1 premature formula end");return}break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:f=r.pop(),c=r.pop(),r.push(["AND","OR"][h-20]+"("+c+","+f+")");break;default:if(h<32&&i[h])f=r.pop(),c=r.pop(),r.push(c+i[h]+f);else if(s[h]){if(69==(a=s[h][1])&&(a=e[e.l++]),a>r.length){console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");return}var m=r.slice(-a);r.length-=a,r.push(s[h][0]+"("+m.join(",")+")")}else if(h<=7)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=24)return console.error("WK1 unsupported op "+h.toString(16));else if(h<=30)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=115)return console.error("WK1 unsupported function opcode "+h.toString(16));else return console.error("WK1 unrecognized opcode "+h.toString(16))}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")})(e.slice(e.l,e.l+c),l),e.l+=c}return l}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:f},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},u={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=o(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:l},24:{n:"NUMBER18",f:function(e,t){var r=o(e,t);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=(a>>3)*5e3;break;case 1:a=(a>>3)*500;break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=l(e,14);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=o(e,t),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:c},40:{n:"FORMULA28",f:function(e,t){var r=c(e,14);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:f},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r,a,n,s,i=t||{};if(+i.codepage>=0&&g(+i.codepage),"string"==i.type)throw Error("Cannot write WK1 to JS string");var o=t4(),l=ro(e["!ref"]),c=Array.isArray(e),f=[];s_(o,0,(r=1030,(a=t2(2)).write_shift(2,1030),a)),s_(o,6,(n=l,(s=t2(8)).write_shift(2,n.s.c),s.write_shift(2,n.s.r),s.write_shift(2,n.e.c),s.write_shift(2,n.e.r),s));for(var h=Math.min(l.e.r,8191),u=l.s.r;u<=h;++u)for(var d=re(u),p=l.s.c;p<=l.e.c;++p){u===l.s.r&&(f[p]=rr(p));var m=f[p]+d,v=c?(e[u]||[])[p]:e[m];v&&"z"!=v.t&&("n"==v.t?(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?s_(o,13,function(e,t,r){var a=t2(7);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r,"i"),a}(u,p,v.v)):s_(o,14,function(e,t,r){var a=t2(13);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r,"f"),a}(u,p,v.v)):s_(o,15,function(e,t,r){var a=t2(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}(u,p,rc(v).slice(0,239))))}return s_(o,1),o.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&g(+r.codepage),"string"==r.type)throw Error("Cannot write WK3 to JS string");var a=t4();s_(a,0,function(e){var t=t2(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++n;var l=rs(o["!ref"]);r<l.e.r&&(r=l.e.r),a<l.e.c&&(a=l.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&s_(a,27,function(e,t){var r=t2(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var o=e.Sheets[e.SheetNames[n]];if(o&&o["!ref"]){for(var l=ro(o["!ref"]),c=Array.isArray(o),f=[],h=Math.min(l.e.r,8191),u=l.s.r;u<=h;++u)for(var d=re(u),p=l.s.c;p<=l.e.c;++p){u===l.s.r&&(f[p]=rr(p));var m=f[p]+d,v=c?(o[u]||[])[p]:o[m];v&&"z"!=v.t&&("n"==v.t?s_(a,23,function(e,t,r,a){var n=t2(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s=0,i=0,o=0,l=0;return a<0&&(s=1,a=-a),i=0|Math.log2(a),a/=Math.pow(2,i-31),(0x80000000&(l=a>>>0))==0&&(a/=2,++i,l=a>>>0),a-=l,l|=0x80000000,l>>>=0,a*=0x100000000,o=a>>>0,n.write_shift(4,o),n.write_shift(4,l),i+=16383+32768*!!s,n.write_shift(2,i),n}(u,p,i,v.v)):s_(a,22,function(e,t,r,a){var n=t2(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}(u,p,i,rc(v).slice(0,239))))}++i}}return s_(a,1),a.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(R(y(e)),r);case"binary":return t(R(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),aq=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,aQ=/<(?:\w+:)?r>/,a1=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g,a0=/^\s|\s$|[\t\n\r]/;function a2(e,t){if(!t.bookSST)return"";var r=[eq];r[r.length]=tm("sst",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main",count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(a0)&&(s+=' xml:space="preserve"'),s+=">"+e8(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var a4=function(e,t){var r=!1;return null==t&&(r=!0,t=t2(15+4*e.t.length)),t.write_shift(1,0),rm(e.t,t),r?t.slice(0,t.l):t};function a3(e){if(void 0!==n)return n.utils.encode(u,e);for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function a5(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function a6(e){var t,r,a=0,n=a3(e),s=n.length+1;for(r=1,(t=k(s))[0]=n.length;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=(+((16384&a)!=0)|a<<1&32767)^t[r];return 52811^a}var a8=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){var r;return((r=e^t)/2|128*r)&255},n=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(a^=r[n]),i*=2,--n;return a};return function(t){for(var r,s,i,o=a3(t),l=n(o),c=o.length,f=k(16),h=0;16!=h;++h)f[h]=0;for((1&c)==1&&(r=l>>8,f[c]=a(187,r),--c,r=255&l,s=o[o.length-1],f[c]=a(s,r));c>0;)--c,r=l>>8,f[c]=a(o[c],r),--c,r=255&l,f[c]=a(o[c],r);for(c=15,i=15-o.length;i>0;)r=l>>8,f[c]=a(e[i],r),--c,--i,r=255&l,f[c]=a(o[c],r),--c,--i;return f}}(),a7=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=a8(e)),s=0;s!=t.length;++s)i=((i=t[s]^a[r])>>5|i<<3)&255,n[s]=i,++r;return[n,r,a]},a9=function(e){var t=0,r=a8(e);return function(e){var a=a7("",e,t,r);return t=a[1],a[0]}},ne=function(){function e(e,r){switch(r.type){case"base64":return t(y(e),r);case"binary":return t(e,r);case"buffer":return t(x&&l.isBuffer(e)?e.toString("binary"):N(e),r);case"array":return t(eB(e),r)}throw Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach(function(e,t){Array.isArray(r)&&(r[t]=[]);for(var a,s=/\\\w+\b/g,i=0,o=-1;a=s.exec(e);){if("\\cell"===a[0]){var l=e.slice(i,s.lastIndex-a[0].length);if(" "==l[0]&&(l=l.slice(1)),++o,l.length){var c={v:l,t:"s"};Array.isArray(r)?r[t][o]=c:r[rn({r:t,c:o})]=c}}i=s.lastIndex}o>n.e.c&&(n.e.c=o)}),r["!ref"]=ri(n),r}return{to_workbook:function(t,r){return rf(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],a=ro(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var o=rn({r:s,c:i});(t=n?(e[s]||[])[i]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(rc(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function nt(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var nr=6;function na(e){return Math.floor((e+Math.round(128/nr)/256)*nr)}function nn(e){return Math.floor((e-5)/nr*100+.5)/100}function ns(e){return Math.round((e*nr+5)/nr*256)/256}function ni(e){return ns(nn(na(e)))}function no(e){e.width?(e.wpx=na(e.width),e.wch=nn(e.wpx),e.MDW=nr):e.wpx?(e.wch=nn(e.wpx),e.width=ns(e.wch),e.MDW=nr):"number"==typeof e.wch&&(e.width=ns(e.wch),e.wpx=na(e.width),e.MDW=nr),e.customWidth&&delete e.customWidth}function nl(e){return 96*e/96}function nc(e){return 96*e/96}var nf={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function nh(e,t){var r,a,n,s,i,o=[eq,tm("styleSheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:vt":tb.vt})];return e.SSF&&null!=(r=e.SSF,a=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=r[t]&&(a[a.length]=tm("numFmt",null,{numFmtId:t,formatCode:e8(r[t])}))}),i=1===a.length?"":(a[a.length]="</numFmts>",a[0]=tm("numFmts",null,{count:a.length-2}).replace("/>",">"),a.join("")))&&(o[o.length]=i),o[o.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',o[o.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',o[o.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',o[o.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',n=t.cellXfs,(s=[])[s.length]=tm("cellXfs",null),n.forEach(function(e){s[s.length]=tm("xf",null,e)}),s[s.length]="</cellXfs>",(i=2===s.length?"":(s[0]=tm("cellXfs",null,{count:s.length-2}).replace("/>",">"),s.join("")))&&(o[o.length]=i),o[o.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',o[o.length]='<dxfs count="0"/>',o[o.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',o.length>2&&(o[o.length]="</styleSheet>",o[1]=o[1].replace("/>",">")),o.join("")}var nu=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function nd(e,t){t||(t=t2(84)),i||(i=eO(nu));var r=i[e.patternType];null==r&&(r=40),t.write_shift(4,r);var a=0;if(40!=r)for(rC({auto:1},t),rC({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function np(e,t,r){return r||(r=t2(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function nm(e,t){return t||(t=t2(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var ng=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function nv(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(e0)||[]).forEach(function(e){var n=e4(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[ng.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw Error("Unrecognized "+n[0]+" in clrScheme")}})}function nT(){}function nb(){}var nw=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,nE=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,nS=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,n_=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function nA(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[eq];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function ny(){var e=[eq];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var nx=1024;function nO(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[tm("xml",null,{"xmlns:v":tE.v,"xmlns:o":tE.o,"xmlns:x":tE.x,"xmlns:mv":tE.mv}).replace(/\/>/,">"),tm("o:shapelayout",tm("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),tm("v:shapetype",[tm("v:stroke",null,{joinstyle:"miter"}),tm("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];nx<1e3*e;)nx+=1e3;return t.forEach(function(e){var t=ra(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var a="gradient"==r.type?tm("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=tm("v:fill",a,r);++nx,n=n.concat(["<v:shape"+tp({id:"_x0000_s"+nx,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,tm("v:shadow",null,{on:"t",obscured:"t"}),tm("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",td("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),td("x:AutoFill","False"),td("x:Row",String(t.r)),td("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function nk(e){var t=[eq,tm("comments",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main"})],r=[];return t.push("<authors>"),e.forEach(function(e){e[1].forEach(function(e){var a=e8(e.a);-1==r.indexOf(a)&&(r.push(a),t.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))})}),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?a=r.indexOf("tc="+e[1][0].ID):e[1].forEach(function(e){e.a&&(a=r.indexOf(e8(e.a))),n.push(e.t||"")}),t.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>'),n.length<=1)t.push(td("t",e8(n[0]||"")));else{for(var s="Comment:\n    "+n[0]+"\n",i=1;i<n.length;++i)s+="Reply:\n    "+n[i]+"\n";t.push(td("t",e8(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}var nC=["xlsb","xlsm","xlam","biff8","xla"],nR=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var o=a.length>0?0|parseInt(a,10):0,l=n.length>0?0|parseInt(n,10):0;return s?l+=t.c:--l,i?o+=t.r:--o,r+(s?"":"$")+rr(l)+(i?"":"$")+re(o)}return function(a,n){return t=n,a.replace(e,r)}}(),nI=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,nN=function(e,t){return e.replace(nI,function(e,r,a,n,s,i){var o=rt(n)-(a?0:t.c),l=t9(i)-(s?0:t.r);return r+"R"+(0==l?"":s?l+1:"["+l+"]")+"C"+(0==o?"":a?o+1:"["+o+"]")})};function nD(e){e.l+=1}function nP(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function nL(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return nF(e,t,r);12==r.biff&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=nP(e,2),o=nP(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function nF(e){var t=nP(e,2),r=nP(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function nM(e,t,r){if(r&&r.biff>=2&&r.biff<=5){var a,n,s;return n=nP(a=e,2),s=a.read_shift(1),{r:n[0],c:s,cRel:n[1],rRel:n[2]}}var i=e.read_shift(r&&12==r.biff?4:2),o=nP(e,2);return{r:i,c:o[0],cRel:o[1],rRel:o[2]}}function nU(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function nB(e){return[e.read_shift(1),e.read_shift(1)]}function nH(e,t,r){var a;return e.l+=2,[{r:e.read_shift(2),c:255&(a=e.read_shift(2)),fQuoted:!!(16384&a),cRel:a>>15,rRel:a>>15}]}function nW(e){return e.l+=6,[]}function nG(e){return e.l+=2,[af(e),1&e.read_shift(2)]}var nV=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],nz={1:{n:"PtgExp",f:function(e,t,r){return(e.l++,r&&12==r.biff)?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:t0},3:{n:"PtgAdd",f:nD},4:{n:"PtgSub",f:nD},5:{n:"PtgMul",f:nD},6:{n:"PtgDiv",f:nD},7:{n:"PtgPower",f:nD},8:{n:"PtgConcat",f:nD},9:{n:"PtgLt",f:nD},10:{n:"PtgLe",f:nD},11:{n:"PtgEq",f:nD},12:{n:"PtgGe",f:nD},13:{n:"PtgGt",f:nD},14:{n:"PtgNe",f:nD},15:{n:"PtgIsect",f:nD},16:{n:"PtgUnion",f:nD},17:{n:"PtgRange",f:nD},18:{n:"PtgUplus",f:nD},19:{n:"PtgUminus",f:nD},20:{n:"PtgPercent",f:nD},21:{n:"PtgParen",f:nD},22:{n:"PtgMissArg",f:nD},23:{n:"PtgStr",f:function(e,t,r){return e.l++,ap(e,t-1,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,rM[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,rO(e,8)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[n3[n],n4[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a,n=e[e.l++],s=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:[(a=e)[a.l+1]>>7,32767&a.read_shift(2)];return[s,(0===i[0]?n4:n2)[i[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,nM(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,nL(e,r.biff>=2&&r.biff<=5?6:8,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:t0},40:{n:"PtgMemNoMem",f:t0},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,function(e,t,r){var a,n,s,i,o,l=r&&r.biff?r.biff:8;if(l>=2&&l<=5){return n=(a=e).read_shift(2),s=a.read_shift(1),i=(32768&n)>>15,o=(16384&n)>>14,n&=16383,1==i&&n>=8192&&(n-=16384),1==o&&s>=128&&(s-=256),{r:n,c:s,cRel:o,rRel:i}}var c=e.read_shift(l>=12?4:2),f=e.read_shift(2),h=(16384&f)>>14,u=(32768&f)>>15;if(f&=16383,1==u)for(;c>524287;)c-=1048576;if(1==h)for(;f>8191;)f-=16384;return{r:c,c:f,cRel:h,rRel:u}}(e,0,r)]}},45:{n:"PtgAreaN",f:function(e,t,r){return[(96&e[e.l++])>>5,function(e,t,r){if(r.biff<8)return nF(e,t,r);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=nP(e,2),i=nP(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,t-1,r)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){var a,n,s,i;return 5==r.biff?(n=(a=e).read_shift(1)>>>5&3,s=a.read_shift(2,"i"),a.l+=8,i=a.read_shift(2),a.l+=12,[n,s,i]):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,nM(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return[a,n,nL(e,s,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},nj={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},n$={1:{n:"PtgElfLel",f:nG},2:{n:"PtgElfRw",f:nH},3:{n:"PtgElfCol",f:nH},6:{n:"PtgElfRwV",f:nH},7:{n:"PtgElfColV",f:nH},10:{n:"PtgElfRadical",f:nH},11:{n:"PtgElfRadicalS",f:nW},13:{n:"PtgElfColS",f:nW},15:{n:"PtgElfColSV",f:nW},16:{n:"PtgElfRadicalLel",f:nG},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=nV[r>>2&31];return{ixti:t,coltype:3&r,rt:i,idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},nY={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:nU},33:{n:"PtgAttrBaxcel",f:nU},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),nB(e,2)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),nB(e,2)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function nK(e,t,r,a){if(a.biff<8)return n=t,void(e.l+=n);for(var n,s,i=e.l+t,o=[],l=0;l!==r.length;++l)switch(r[l][0]){case"PtgArray":r[l][1]=function(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=function(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=al(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=rM[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=rO(e,8);break;case 2:r[1]=av(e,0,{biff:t>0&&t<8?2:t});break;default:throw Error("Bad SerAr: "+r[0])}return r}(e,r.biff);return i}(e,0,a),o.push(r[l][1]);break;case"PtgMemArea":r[l][2]=function(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?ry:ax)(e,8));return n}(e,r[l][1],a),o.push(r[l][2]);break;case"PtgExp":a&&12==a.biff&&(r[l][1][1]=e.read_shift(4),o.push(r[l][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[l][0]}return 0!=(t=i-e.l)&&o.push((s=t,void(e.l+=s))),o}function nX(e,t,r){for(var a,n,s,i=e.l+t,o=[];i!=e.l;)(t=i-e.l,n=nz[s=e[e.l]]||nz[nj[s]],(24===s||25===s)&&(n=(24===s?n$:nY)[e[e.l+1]]),n&&n.f)?o.push([n.n,n.f(e,t,r)]):(a=t,e.l+=a);return o}var nJ={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function nZ(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:if(null!=r.SID)return e.SheetNames[r.SID];return"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[a[0]][0][3])return"SH33TJSERR2";return n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]}}function nq(e,t,r){var a=nZ(e,t,r);return"#REF"==a?a:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,r)}function nQ(e,t,r,a,n){var s,i,o,l,c=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var b=e[0][v];switch(b[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=eW(" ",e[0][m][1][1]);break;case 1:g=eW("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+nJ[b[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=t5(b[1][1],f,n),h.push(t8(o,c));break;case"PtgRefN":o=r?t5(b[1][1],r,n):b[1][1],h.push(t8(o,c));break;case"PtgRef3d":u=b[1][1],o=t5(b[1][2],f,n),p=nq(a,u,n),h.push(p+"!"+t8(o,c));break;case"PtgFunc":case"PtgFuncVar":var w=b[1][0],E=b[1][1];w||(w=0);var S=0==(w&=127)?[]:h.slice(-w);h.length-=w,"User"===E&&(E=S.shift()),h.push(E+"("+S.join(",")+")");break;case"PtgBool":h.push(b[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(b[1]);break;case"PtgNum":h.push(String(b[1]));break;case"PtgStr":h.push('"'+b[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":l=t6(b[1][1],r?{s:r}:f,n),h.push(t7(l,n));break;case"PtgArea":l=t6(b[1][1],f,n),h.push(t7(l,n));break;case"PtgArea3d":u=b[1][1],l=b[1][2],p=nq(a,u,n),h.push(p+"!"+t7(l,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=b[1][2];var _=(a.names||[])[d-1]||(a[0]||[])[d],A=_?_.Name:"SH33TJSNAME"+String(d);A&&"_xlfn."==A.slice(0,6)&&!n.xlfn&&(A=A.slice(6)),h.push(A);break;case"PtgNameX":var y,x=b[1][1];if(d=b[1][2],n.biff<=5)x<0&&(x=-x),a[x]&&(y=a[x][d]);else{var O="";if(14849==((a[x]||[])[0]||[])[0]||(1025==((a[x]||[])[0]||[])[0]?a[x][d]&&a[x][d].itab>0&&(O=a.SheetNames[a[x][d].itab-1]+"!"):O=a.SheetNames[d-1]+"!"),a[x]&&a[x][d])O+=a[x][d].Name;else if(a[0]&&a[0][d])O+=a[0][d].Name;else{var k=(nZ(a,x,n)||"").split(";;");k[d-1]?O=k[d-1]:O+="SH33TJSERRX"}h.push(O);break}y||(y={Name:"SH33TJSERRY"}),h.push(y.Name);break;case"PtgParen":var C="(",R=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:C=eW(" ",e[0][m][1][1])+C;break;case 3:C=eW("\r",e[0][m][1][1])+C;break;case 4:R=eW(" ",e[0][m][1][1])+R;break;case 5:R=eW("\r",e[0][m][1][1])+R;break;default:if(n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(C+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:b[1][1],r:b[1][0]};var I={c:r.c,r:r.r};if(a.sharedf[rn(o)]){var N=a.sharedf[rn(o)];h.push(nQ(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if(i=a.arrayf[s],!(o.c<i[0].s.c)&&!(o.c>i[0].e.c)&&!(o.r<i[0].s.r)&&!(o.r>i[0].e.r)){h.push(nQ(i[1],f,I,a,n)),D=!0;break}D||h.push(b[1])}break;case"PtgArray":h.push("{"+function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];i?2===i[0]?n.push('"'+i[1].replace(/"/g,'""')+'"'):n.push(i[1]):n.push("")}t.push(n.join(","))}return t.join(";")}(b[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+b[1].idx+"[#"+b[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(b))}var P=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=n.biff&&m>=0&&-1==P.indexOf(e[0][v][0])){b=e[0][m];var L=!0;switch(b[1][0]){case 4:L=!1;case 0:g=eW(" ",b[1][1]);break;case 5:L=!1;case 1:g=eW("\r",b[1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+b[1][0])}h.push((L?g:"")+h.pop()+(L?"":g)),m=-1}}if(h.length>1&&n.WTF)throw Error("bad formula stack");return h[0]}function n1(e,t,r){var a=e.l+t,n=a_(e,6);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==tz(e,e.l+6))return[rO(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e,8),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(a=t-2,void(e.l+=a))];var l=nX(e,o,r);return t!==o+i&&(n=nK(e,t-o-i,l,r)),e.l=s,[l,n]}(e,a-e.l,r);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function n0(e,t,r){var a=e.read_shift(4),n=nX(e,a,r),s=e.read_shift(4),i=s>0?nK(e,s,n,r):null;return[n,i]}var n2={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},n4={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},n3={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function n5(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,t){return t.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function n6(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var n8={},n7="undefined"!=typeof Map;function n9(e,t,r){var a=0,n=e.length;if(r){if(n7?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var s=n7?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t:t},e.Count++,e.Unique++,r&&(n7?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function se(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(nr=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?a=nn(t.wpx):null!=t.wch&&(a=t.wch),a>-1?(r.width=ns(a),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function st(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function sr(e,t,r){var a=r.revssf[null!=t.z?t.z:"General"],n=60,s=e.length;if(null==a&&r.ssf){for(;n<392;++n)if(null==r.ssf[n]){eT(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}var sa=["objects","scenarios","selectLockedCells","selectUnlockedCells"],sn=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function ss(e,t,r,a){var n,s=[eq,tm("worksheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tb.r})],i=r.SheetNames[e],o=0,l="",c=r.Sheets[i];null==c&&(c={});var f=c["!ref"]||"A1",h=ro(f);if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw Error("Range "+f+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575),f=ri(h)}a||(a={}),c["!comments"]=[];var u=[];!function(e,t,r,a,n){var s=!1,i={},o=null;if("xlsx"!==a.bookType&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch(e){}s=!0,i.codeName=to(e8(l))}if(e&&e["!outline"]){var c={summaryBelow:1,summaryRight:1};e["!outline"].above&&(c.summaryBelow=0),e["!outline"].left&&(c.summaryRight=0),o=(o||"")+tm("outlinePr",null,c)}(s||o)&&(n[n.length]=tm("sheetPr",o,i))}(c,r,e,t,s),s[s.length]=tm("dimension",null,{ref:f}),s[s.length]=(d={workbookViewId:"0"},(((r||{}).Workbook||{}).Views||[])[0]&&(d.rightToLeft=r.Workbook.Views[0].RTL?"1":"0"),tm("sheetViews",tm("sheetView",null,d),{})),t.sheetFormat&&(s[s.length]=tm("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=c["!cols"]&&c["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,a=["<cols>"],n=0;n!=t.length;++n)(r=t[n])&&(a[a.length]=tm("col",null,se(n,r)));return a[a.length]="</cols>",a.join("")}(0,c["!cols"])),s[o=s.length]="<sheetData/>",c["!links"]=[],null!=c["!ref"]&&(l=function(e,t,r,a){var n,s,i=[],o=[],l=ro(e["!ref"]),c="",f="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:f},v=-1;for(d=l.s.c;d<=l.e.c;++d)h[d]=rr(d);for(u=l.s.r;u<=l.e.r;++u){for(o=[],f=re(u),d=l.s.c;d<=l.e.c;++d){n=h[d]+f;var T=m?(e[u]||[])[d]:e[n];void 0!==T&&null!=(c=function(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var n="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=rM[e.v];break;case"d":a&&a.cellDates?n=eU(e.v,-1).toISOString():((e=eH(e)).t="n",n=""+(e.v=eR(eU(e.v)))),void 0===e.z&&(e.z=z[14]);break;default:n=e.v}var o=td("v",e8(n)),l={r:t},c=sr(a.cellXfs,e,a);switch(0!==c&&(l.s=c),e.t){case"n":case"z":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){o=td("v",""+n9(a.Strings,e.v,a.revStrings)),l.t="s";break}l.t="str"}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=tm("f",e8(e.f),f)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),tm("c",o,l)}(T,n,e,t,r,a))&&o.push(c)}(o.length>0||p&&p[u])&&(g={r:f},p&&p[u]&&((s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=nl(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level)),i[i.length]=tm("row",o.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},(s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=nl(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level),i[i.length]=tm("row","",g));return i.join("")}(c,t,e,r,a)).length>0&&(s[s.length]=l),s.length>o+1&&(s[s.length]="</sheetData>",s[o]=s[o].replace("/>",">")),c["!protect"]&&(s[s.length]=(p=c["!protect"],m={sheet:1},sa.forEach(function(e){null!=p[e]&&p[e]&&(m[e]="1")}),sn.forEach(function(e){null==p[e]||p[e]||(m[e]="0")}),p.password&&(m.password=a6(p.password).toString(16).toUpperCase()),tm("sheetProtection",null,m))),null!=c["!autofilter"]&&(s[s.length]=function(e,t,r,a){var n="string"==typeof e.ref?e.ref:ri(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=rs(n);i.s.r==i.e.r&&(i.e.r=rs(t["!ref"]).e.r,n=ri(i));for(var o=0;o<s.length;++o){var l=s[o];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==a){l.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),tm("autoFilter",null,{ref:n})}(c["!autofilter"],c,r,e)),null!=c["!merges"]&&c["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+ri(e[r])+'"/>';return t+"</mergeCells>"}(c["!merges"]));var d,p,m,g,v=-1,T=-1;return c["!links"].length>0&&(s[s.length]="<hyperlinks>",c["!links"].forEach(function(e){e[1].Target&&(g={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(T=r$(a,-1,e8(e[1].Target).replace(/#.*$/,""),rV.HLINK),g["r:id"]="rId"+T),(v=e[1].Target.indexOf("#"))>-1&&(g.location=e8(e[1].Target.slice(v+1))),e[1].Tooltip&&(g.tooltip=e8(e[1].Tooltip)),s[s.length]=tm("hyperlink",null,g))}),s[s.length]="</hyperlinks>"),delete c["!links"],null!=c["!margins"]&&(s[s.length]=(st(n=c["!margins"]),tm("pageMargins",null,n))),(!t||t.ignoreEC||void 0==t.ignoreEC)&&(s[s.length]=td("ignoredErrors",tm("ignoredError",null,{numberStoredAsText:1,sqref:f}))),u.length>0&&(T=r$(a,-1,"../drawings/drawing"+(e+1)+".xml",rV.DRAW),s[s.length]=tm("drawing",null,{"r:id":"rId"+T}),c["!drawing"]=u),c["!comments"].length>0&&(T=r$(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",rV.VML),s[s.length]=tm("legacyDrawing",null,{"r:id":"rId"+T}),c["!legacy"]=T),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}var si=["left","right","top","bottom","header","footer"],so=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function sl(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=tt(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function sc(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=tt(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}var sf="][*?/\\".split("");function sh(e,t){if(e.length>31){if(t)return!1;throw Error("Sheet names cannot exceed 31 chars")}var r=!0;return sf.forEach(function(a){if(-1!=e.indexOf(a)){if(!t)throw Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function su(e){var t=[eq];t[t.length]=tm("workbook",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tb.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(so.forEach(function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(a[t[0]]=e.Workbook.WBProps[t[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=tm("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(s=0,t[t.length]="<bookViews>";s!=e.SheetNames.length&&n[s]&&n[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(s=0,t[t.length]="<sheets>";s!=e.SheetNames.length;++s){var i={name:e8(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=tm("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=tm("definedName",e8(e.Ref),r))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}var sd=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,sp=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function sm(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,o,l=e.match(sd);if(l)for(o=0;o!=l.length;++o)-1===(s=(n=l[o].match(sp))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function sg(e,t){var r,a,i,l=t||{};ew();var c=w(tv(e));("binary"==l.type||"array"==l.type||"base64"==l.type)&&(c=void 0!==n?n.utils.decode(65001,T(c)):ti(c));var f=c.slice(0,1024).toLowerCase(),h=!1;if((1023&(f=f.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&f.indexOf(","),1023&f.indexOf(";"))){var u=eH(l);return u.type="string",aJ.to_workbook(c,u)}if(-1==f.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(e){f.indexOf("<"+e)>=0&&(h=!0)}),h)return function(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||0==r.length)throw Error("Invalid HTML: could not find <table>");if(1==r.length)return rf(sx(r[0],t),t);var a=ia();return r.forEach(function(e,r){is(a,sx(e,t),"Sheet"+(r+1))}),a}(c,l);o={"General Number":"General","General Date":z[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":z[15],"Short Date":z[14],"Long Time":z[19],"Medium Time":z[18],"Short Time":z[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:z[2],Standard:z[4],Percent:z[10],Scientific:z[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var d,p,m,g=[],v={},b=[],E=l.dense?[]:{},S="",_={},A={},y=sm('<Data ss:Type="String">'),x=0,O=0,k=0,C={s:{r:2e6,c:2e6},e:{r:0,c:0}},R={},I={},N="",D=0,P=[],L={},F={},M=0,U=[],B=[],H={},W=[],G=!1,V=[],j=[],Y={},K=0,J=0,Z={Sheets:[],WBProps:{date1904:!1}},q={};tT.lastIndex=0,c=c.replace(/<!--([\s\S]*?)-->/mg,"");for(var Q="";d=tT.exec(c);)switch(d[3]=(Q=d[3]).toLowerCase()){case"data":if("data"==Q){if("/"===d[1]){if((p=g.pop())[0]!==d[3])throw Error("Bad state: "+p.join("|"))}else"/"!==d[0].charAt(d[0].length-2)&&g.push([d[3],!0]);break}if(g[g.length-1][1])break;"/"===d[1]?function(e,t,r,a,n,s,i,l,c,f){var h="General",u=a.StyleID,d={};f=f||{};var p=[],m=0;for(void 0===u&&l&&(u=l.StyleID),void 0===u&&i&&(u=i.StyleID);void 0!==s[u]&&(s[u].nf&&(h=s[u].nf),s[u].Interior&&p.push(s[u].Interior),s[u].Parent);)u=s[u].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=tt(e);break;case"String":a.t="s",a.r=(null)((null)(e)),a.v=e.indexOf("<")>-1?(null)(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(eU(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=(null)(e):a.v<60&&(a.v=a.v-1),h&&"General"!=h||(h="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=rU[e],!1!==f.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=(null)(t||e))}if(!function(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{if("e"===e.t)e.w=e.w||rM[e.v];else if("General"===t)"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=et(e.v):e.w=er(e.v);else{var a,n,s;e.w=(a=t||"General",n=e.v,s=o[a]||(null)(a),"General"===s?er(n):ev(s,n))}}catch(e){if(r.WTF)throw e}try{var i=o[t]||t||"General";if(r.cellNF&&(e.z=i),r.cellDates&&"n"==e.t&&ep(i)){var l=X(e.v);l&&(e.t="d",e.v=new Date(l.y,l.m-1,l.d,l.H,l.M,l.S,l.u))}}catch(e){if(r.WTF)throw e}}}(a,h,f),!1!==f.cellFormula){if(a.Formula){var g=(null)(a.Formula);61==g.charCodeAt(0)&&(g=g.slice(1)),a.f=nR(g,n),delete a.Formula,"RC"==a.ArrayRange?a.F=nR("RC:RC",n):a.ArrayRange&&(a.F=nR(a.ArrayRange,n),c.push([ro(a.F),a.F]))}else for(m=0;m<c.length;++m)n.r>=c[m][0].s.r&&n.r<=c[m][0].e.r&&n.c>=c[m][0].s.c&&n.c<=c[m][0].e.c&&(a.F=c[m][1])}f.cellStyles&&(p.forEach(function(e){!d.patternType&&e.patternType&&(d.patternType=e.patternType)}),a.s=d),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}(c.slice(x,d.index),N,y,"comment"==g[g.length-1][0]?H:_,{c:O,r:k},R,W[O],A,V,l):(N="",y=sm(d[0]),x=d.index+d[0].length);break;case"cell":if("/"===d[1]){if(B.length>0&&(_.c=B),(!l.sheetRows||l.sheetRows>k)&&void 0!==_.v&&(l.dense?(E[k]||(E[k]=[]),E[k][O]=_):E[rr(O)+re(k)]=_),_.HRef&&(_.l={Target:(null)(_.HRef)},_.HRefScreenTip&&(_.l.Tooltip=_.HRefScreenTip),delete _.HRef,delete _.HRefScreenTip),(_.MergeAcross||_.MergeDown)&&(K=O+(0|parseInt(_.MergeAcross,10)),J=k+(0|parseInt(_.MergeDown,10)),P.push({s:{c:O,r:k},e:{c:K,r:J}})),l.sheetStubs){if(_.MergeAcross||_.MergeDown){for(var ee=O;ee<=K;++ee)for(var ea=k;ea<=J;++ea)(ee>O||ea>k)&&(l.dense?(E[ea]||(E[ea]=[]),E[ea][ee]={t:"z"}):E[rr(ee)+re(ea)]={t:"z"});O=K+1}else++O}else _.MergeAcross?O=K+1:++O}else(_=function(e){var t=e.split(/\s+/),r={};if(1===t.length)return r;var a,n,s,i,o=e.match(sd);if(o)for(i=0;i!=o.length;++i)-1===(n=(a=o[i].match(sp))[1].indexOf(":"))?r[a[1]]=a[2].slice(1,a[2].length-1):r["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(n+1)]=a[2].slice(1,a[2].length-1);return r}(d[0])).Index&&(O=+_.Index-1),O<C.s.c&&(C.s.c=O),O>C.e.c&&(C.e.c=O),"/>"===d[0].slice(-2)&&++O,B=[];break;case"row":"/"===d[1]||"/>"===d[0].slice(-2)?(k<C.s.r&&(C.s.r=k),k>C.e.r&&(C.e.r=k),"/>"===d[0].slice(-2)&&(A=sm(d[0])).Index&&(k=+A.Index-1),O=0,++k):((A=sm(d[0])).Index&&(k=+A.Index-1),Y={},("0"==A.AutoFitHeight||A.Height)&&(Y.hpx=parseInt(A.Height,10),Y.hpt=nl(Y.hpx),j[k]=Y),"1"==A.Hidden&&(Y.hidden=!0,j[k]=Y));break;case"worksheet":if("/"===d[1]){if((p=g.pop())[0]!==d[3])throw Error("Bad state: "+p.join("|"));b.push(S),C.s.r<=C.e.r&&C.s.c<=C.e.c&&(E["!ref"]=ri(C),l.sheetRows&&l.sheetRows<=C.e.r&&(E["!fullref"]=E["!ref"],C.e.r=l.sheetRows-1,E["!ref"]=ri(C))),P.length&&(E["!merges"]=P),W.length>0&&(E["!cols"]=W),j.length>0&&(E["!rows"]=j),v[S]=E}else C={s:{r:2e6,c:2e6},e:{r:0,c:0}},k=O=0,g.push([d[3],!1]),S=(null)((p=sm(d[0])).Name),E=l.dense?[]:{},P=[],V=[],j=[],q={name:S,Hidden:0},Z.Sheets.push(q);break;case"table":if("/"===d[1]){if((p=g.pop())[0]!==d[3])throw Error("Bad state: "+p.join("|"))}else"/>"==d[0].slice(-2)||(g.push([d[3],!1]),W=[],G=!1);break;case"style":"/"===d[1]?function(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=nf[a.Pattern]||a.Pattern)}e[t.ID]=t}(R,I,l):I=sm(d[0]);break;case"numberformat":I.nf=(null)(sm(d[0]).Format||"General"),o[I.nf]&&(I.nf=o[I.nf]);for(var en=0;392!=en&&z[en]!=I.nf;++en);if(392==en){for(en=57;392!=en;++en)if(null==z[en]){eT(I.nf,en);break}}break;case"column":if("table"!==g[g.length-1][0])break;if((m=sm(d[0])).Hidden&&(m.hidden=!0,delete m.Hidden),m.Width&&(m.wpx=parseInt(m.Width,10)),!G&&m.wpx>10){G=!0,nr=6;for(var es=0;es<W.length;++es)W[es]&&no(W[es])}G&&no(m),W[m.Index-1||W.length]=m;for(var ei=0;ei<+m.Span;++ei)W[W.length]=eH(m);break;case"namedrange":if("/"===d[1])break;Z.Names||(Z.Names=[]);var eo=e4(d[0]),el={Name:eo.Name,Ref:nR(eo.RefersTo.slice(1),{r:0,c:0})};Z.Sheets.length>0&&(el.Sheet=Z.Sheets.length-1),Z.Names.push(el);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===d[0].slice(-2)||("/"===d[1]?N+=c.slice(D,d.index):D=d.index+d[0].length);break;case"interior":if(!l.cellStyles)break;I.Interior=sm(d[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===d[0].slice(-2)||("/"===d[1]?(r=Q,a=c.slice(M,d.index),s||(s=eO(r2)),L[r=s[r]||r]=a):M=d.index+d[0].length);break;case"styles":case"workbook":if("/"===d[1]){if((p=g.pop())[0]!==d[3])throw Error("Bad state: "+p.join("|"))}else g.push([d[3],!1]);break;case"comment":if("/"===d[1]){if((p=g.pop())[0]!==d[3])throw Error("Bad state: "+p.join("|"));(i=H).t=i.v||"",i.t=i.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),i.v=i.w=i.ixfe=void 0,B.push(H)}else g.push([d[3],!1]),H={a:(p=sm(d[0])).Author};break;case"autofilter":if("/"===d[1]){if((p=g.pop())[0]!==d[3])throw Error("Bad state: "+p.join("|"))}else if("/"!==d[0].charAt(d[0].length-2)){var ec=sm(d[0]);E["!autofilter"]={ref:nR(ec.Range).replace(/\$/g,"")},g.push([d[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===d[1]){if((p=g.pop())[0]!==d[3])throw Error("Bad state: "+p.join("|"))}else"/"!==d[0].charAt(d[0].length-2)&&g.push([d[3],!0]);break;default:if(0==g.length&&"document"==d[3]||0==g.length&&"uof"==d[3])return sN(c,l);var ef=!0;switch(g[g.length-1][0]){case"officedocumentsettings":switch(d[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:ef=!1}break;case"componentoptions":switch(d[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:ef=!1}break;case"excelworkbook":switch(d[3]){case"date1904":Z.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:ef=!1}break;case"workbookoptions":switch(d[3]){case"owcversion":case"height":case"width":break;default:ef=!1}break;case"worksheetoptions":switch(d[3]){case"visible":if("/>"===d[0].slice(-2));else if("/"===d[1])switch(c.slice(M,d.index)){case"SheetHidden":q.Hidden=1;break;case"SheetVeryHidden":q.Hidden=2}else M=d.index+d[0].length;break;case"header":E["!margins"]||st(E["!margins"]={},"xlml"),isNaN(+e4(d[0]).Margin)||(E["!margins"].header=+e4(d[0]).Margin);break;case"footer":E["!margins"]||st(E["!margins"]={},"xlml"),isNaN(+e4(d[0]).Margin)||(E["!margins"].footer=+e4(d[0]).Margin);break;case"pagemargins":var eh=e4(d[0]);E["!margins"]||st(E["!margins"]={},"xlml"),isNaN(+eh.Top)||(E["!margins"].top=+eh.Top),isNaN(+eh.Left)||(E["!margins"].left=+eh.Left),isNaN(+eh.Right)||(E["!margins"].right=+eh.Right),isNaN(+eh.Bottom)||(E["!margins"].bottom=+eh.Bottom);break;case"displayrighttoleft":Z.Views||(Z.Views=[]),Z.Views[0]||(Z.Views[0]={}),Z.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":E["!outline"]||(E["!outline"]={}),E["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":E["!outline"]||(E["!outline"]={}),E["!outline"].left=!0;break;default:ef=!1}break;case"pivottable":case"pivotcache":switch(d[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:ef=!1}break;case"pagebreaks":switch(d[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:ef=!1}break;case"autofilter":switch(d[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:ef=!1}break;case"querytable":switch(d[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:ef=!1}break;case"datavalidation":switch(d[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:ef=!1}break;case"sorting":case"conditionalformatting":switch(d[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:ef=!1}break;case"mapinfo":case"schema":case"data":switch(d[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:ef=!1}break;case"smarttags":break;default:ef=!1}if(ef||d[3].match(/!\[CDATA/))break;if(!g[g.length-1][1])throw"Unrecognized tag: "+d[3]+"|"+g.join("|");if("customdocumentproperties"===g[g.length-1][0]){"/>"===d[0].slice(-2)||("/"===d[1]?function(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=tt(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=eU(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+r[0])}e[(null)(t)]=n}(F,Q,U,c.slice(M,d.index)):(U=d,M=d.index+d[0].length));break}if(l.WTF)throw"Unrecognized tag: "+d[3]+"|"+g.join("|")}var eu={};return l.bookSheets||l.bookProps||(eu.Sheets=v),eu.SheetNames=b,eu.Workbook=Z,eu.SSF=eH(z),eu.Props=L,eu.Custprops=F,eu}function sv(e){return tm("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+nN(e.Ref,{r:0,c:0})})}function sT(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=z[a])}catch(e){if(t.WTF)throw e}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||rM[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=et(e.v):e.w=er(e.v):e.w=ev(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(e){if(t.WTF)throw e}if(t.cellDates&&a&&"n"==e.t&&ep(z[a]||String(a))){var n=X(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function sb(e,t,r){return{v:e,ixfe:t,t:r}}var sw={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"},sE={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[rv(e)]}},2:{f:function(e){return[rv(e),r_(e),"n"]}},3:{f:function(e){return[rv(e),e.read_shift(1),"e"]}},4:{f:function(e){return[rv(e),e.read_shift(1),"b"]}},5:{f:function(e){return[rv(e),rO(e),"n"]}},6:{f:function(e){return[rv(e),rp(e),"str"]}},7:{f:function(e){return[rv(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=rv(e);n.r=r["!row"];var s=[n,rp(e),"str"];if(r.cellFormula){e.l+=2;var i=n0(e,a-e.l,r);s[3]=nQ(i,null,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=rv(e);n.r=r["!row"];var s=[n,rO(e),"n"];if(r.cellFormula){e.l+=2;var i=n0(e,a-e.l,r);s[3]=nQ(i,null,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=rv(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=n0(e,a-e.l,r);s[3]=nQ(i,null,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=rv(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=n0(e,a-e.l,r);s[3]=nQ(i,null,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[rb(e)]}},13:{f:function(e){return[rb(e),r_(e),"n"]}},14:{f:function(e){return[rb(e),e.read_shift(1),"e"]}},15:{f:function(e){return[rb(e),e.read_shift(1),"b"]}},16:{f:function(e){return[rb(e),rO(e),"n"]}},17:{f:function(e){return[rb(e),rp(e),"str"]}},18:{f:function(e){return[rb(e),e.read_shift(4),"s"]}},19:{f:rg},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=rp(e),i=n0(e,0,r),o=rE(e);e.l=a;var l={Name:s,Ptg:i};return n<0xfffffff&&(l.Sheet=n),o&&(l.Comment=o),l}},40:{},42:{},43:{f:function(e,t,r){var a,n={};n.sz=e.read_shift(2)/20;var s=(a=e.read_shift(1),e.l++,{fBold:1&a,fItalic:2&a,fUnderline:4&a,fStrikeout:8&a,fOutline:16&a,fShadow:32&a,fCondense:64&a,fExtend:128&a});switch(s.fItalic&&(n.italic=1),s.fCondense&&(n.condense=1),s.fExtend&&(n.extend=1),s.fShadow&&(n.shadow=1),s.fOutline&&(n.outline=1),s.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(n.underline=i);var o=e.read_shift(1);o>0&&(n.family=o);var l=e.read_shift(1);switch(l>0&&(n.charset=l),e.l++,n.color=function(e){var t={},r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r>>>1){case 0:t.auto=1;break;case 1:t.index=a;var l=rF[a];l&&(t.rgb=nt(l));break;case 2:t.rgb=nt([s,i,o]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e,8),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=rp(e,t-21),n}},44:{f:function(e,t){return[e.read_shift(2),rp(e,t-2)]}},45:{f:t0},46:{f:t0},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:aj},62:{f:function(e){return[rv(e),rg(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=rn(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:t0,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=rp(e,t-19),r}},148:{f:ry,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?rp(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=rE(e,t-8),r.name=rp(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:ry},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:ry},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:rp(e,t-8)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:rE},357:{},358:{},359:{},360:{T:1},361:{},362:{f:aG},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=ry(e,16),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var o=n0(e,a-e.l,r);i[1]=o}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[ry(e,16)];if(r.cellFormula){var s=n0(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return si.forEach(function(r){t[r]=rO(e,8)}),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=ry(e,16),n=rE(e),s=rp(e),i=rp(e),o=rp(e);e.l=r;var l={rfx:a,relId:n,loc:s,display:o};return i&&(l.Tooltip=i),l}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:rE},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:rp},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=ry(e,16);return t.rfx=r.s,t.ref=rn(r.s),e.l+=16,t}},636:{T:-1},637:{f:rg},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:rp(e,t-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},sS={6:{f:n1},10:{f:ao},12:{f:af},13:{f:af},14:{f:al},15:{f:al},16:{f:rO},17:{f:al},18:{f:al},19:{f:af},20:{f:aU},21:{f:aU},23:{f:aG},24:{f:aW},25:{f:al},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=av(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},o,i,s]}}(e,0,r)}},29:{},34:{f:al},35:{f:aB},38:{f:rO},39:{f:rO},40:{f:rO},41:{f:rO},42:{f:al},43:{f:al},47:{f:function(e,t,r){var a,n,s,i,o={Type:r.biff>=8?e.read_shift(2):0};return o.Type?(a=t-2,(n=o||{}).Info=e.read_shift(2),e.l-=2,1===n.Info?n.Data=function(e){var t={},r=t.EncryptionVersionInfo=a5(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e,a):n.Data=function(e,t){var r,a,n,s,i={},o=i.EncryptionVersionInfo=a5(e,4);if(t-=4,2!=o.Minor)throw Error("unrecognized minor version code: "+o.Minor);if(o.Major>4||o.Major<2)throw Error("unrecognized major version code: "+o.Major);i.Flags=e.read_shift(4),t-=4;var l=e.read_shift(4);return t-=4,i.EncryptionHeader=function(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}(e,l),t-=l,i.EncryptionVerifier=(r=e,a=t,n={},s=r.l+a,r.l+=4,n.Salt=r.slice(r.l,r.l+16),r.l+=16,n.Verifier=r.slice(r.l,r.l+16),r.l+=16,r.read_shift(4),n.VerifierHash=r.slice(r.l,s),r.l=s,n),i}(e,a)):(r.biff,i={key:af(e),verificationBytes:af(e)},r.password&&(i.verifier=a6(r.password)),o.valid=i.verificationBytes===i.verifier,o.valid&&(o.insitu=a9(r.password))),o}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=ap(e,0,r),a}},51:{f:af},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:al},65:{f:function(){}},66:{f:af},77:{},80:{},81:{},82:{},85:{f:af},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=av(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8){var a,n,s,i,o,l,c;return a=e,n=t,s=r,a.l+=4,i=a.read_shift(2),o=a.read_shift(2),l=a.read_shift(2),a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=6,n-=36,(c=[]).push((az[i]||t0)(a,n,s)),{cmo:[o,i,l],ft:c}}var f=aC(e,22),h=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(aI[n](e,r-e.l))}catch(t){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,f[1]);return{cmo:f,ft:h}}},94:{},95:{f:al},96:{},97:{},99:{f:al},125:{f:aj},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:af},131:{f:al},132:{f:al},133:{f:function(e,t,r){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=ap(e,0,r);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=rP[t]||t,t=e.read_shift(2),r[1]=rP[t]||t,r}},141:{f:af},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aS(e,8));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:af},157:{},158:{},160:{f:au},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=rO(e,8),r.footer=rO(e,8),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(ay(e));if(e.l!==r)throw Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:ao},197:{},198:{},199:{},200:{},201:{},202:{f:al},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:af},220:{},221:{f:al},222:{},224:{f:function(e,t,r){var a,n,s,i,o,l,c={};return c.ifnt=e.read_shift(2),c.numFmtId=e.read_shift(2),c.flags=e.read_shift(2),c.fStyle=c.flags>>2&1,c.data=(c.fStyle,n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),l=e.read_shift(2),n.patternType=rL[o>>26],r.cellStyles&&(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&o,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=127&l,n.icvBack=l>>7&127,n.fsxButton=l>>14&1),n),c}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:ao},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(ax(e,t));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(function(e){var t=h;h=1200;var r,a=e.read_shift(2),n=e.read_shift(1),s=4&n,i=8&n,o=0,l={};i&&(o=e.read_shift(2)),s&&(r=e.read_shift(4));var c=0===a?"":e.read_shift(a,2==1+(1&n)?"dbcs-cont":"sbcs-cont");return i&&(e.l+=4*o),s&&(e.l+=r),l.t=c,i||(l.raw="<t>"+l.t+"</t>",l.r=l.t),h=t,l}(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var t=a_(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:au},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:al},353:{f:ao},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw Error("Unexpected SupBook type: "+s);for(var i=am(e,s),o=[];a>e.l;)o.push(ag(e));return[s,n,i,o]}},431:{f:al},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s,i,o=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(o)?e.l+=6:(s=e.read_shift(1),e.l++,i=e.read_shift(2),e.l+=2);var l=e.read_shift(2);e.read_shift(2),af(e,2);var c=e.read_shift(2);e.l+=c;for(var f=1;f<e.lens.length-1;++f){if(e.l-a!=e.lens[f])throw Error("TxO: bad continue record");var h=e[e.l],u=am(e,e.lens[f+1]-e.lens[f]-1);if((n+=u).length>=(h?l:2*l))break}if(n.length!==l&&n.length!==2*l)throw Error("cchText: "+l+" != "+n.length);return e.l=a+t,{t:n}}catch(r){return e.l=a+t,{t:n}}}},439:{f:al},440:{f:function(e,t){var r=ax(e,8);return e.l+=16,[r,function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,l,c,f,h="";16&n&&(s=ab(e,r-e.l)),128&n&&(i=ab(e,r-e.l)),(257&n)==257&&(o=ab(e,r-e.l)),(257&n)==1&&(l=function(e,t){var r,a,n,s,i=e.read_shift(16);switch(t-=16,i){case"e0c9ea79f9bace118c8200aa004ba90b":return r=e.read_shift(4),a=e.l,n=!1,r>24&&(e.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(n=!0),e.l=a),s=e.read_shift((n?r-24:r)>>1,"utf16le").replace(P,""),n&&(e.l+=24),s;case"0303000000000000c000000000000046":return function(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw Error("Bad FileMoniker");if(0===e.read_shift(4))return r+a.replace(/\\/g,"/");var n=e.read_shift(4);if(3!=e.read_shift(2))throw Error("Bad FileMoniker");return r+e.read_shift(n>>1,"utf16le").replace(P,"")}(e,t);default:throw Error("Unsupported Moniker "+i)}}(e,r-e.l)),8&n&&(h=ab(e,r-e.l)),32&n&&(c=e.read_shift(16)),64&n&&(f=r4(e)),e.l=r;var u=i||o||l||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return c&&(d.guid=c),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24)]}},441:{},442:{f:ag},443:{},444:{f:af},445:{},446:{},448:{f:ao},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:ao},512:{f:aL},513:{f:a_},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=a_(e,6),n=rO(e,8);return a.val=n,a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=e.l+t,n=a_(e,6);2==r.biff&&e.l++;var s=ag(e,a-e.l,r);return n.val=s,n}},517:{f:aM},519:{f:ag},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:aV},549:{f:aP},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=ay(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),av(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=ak(e,6);e.l++;var n=e.read_shift(1);return[function(e,t,r){var a,n,s=e.l+t,i=e.read_shift(2),o=nX(e,i,r);return 65535==i?[[],(a=t-2,void(e.l+=a))]:(t!==i+2&&(n=nK(e,s-i-2,o,r)),[o,n])}(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=ax(e,8),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(P,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:aN},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:ao},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),s=[];n-- >0;)s.push(function(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=function(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){e.l+=4}(e,4);break;case 2:t.xclrValue=aE(e,4);break;case 3:t.xclrValue=e.read_shift(4)}return e.l+=8,t}(e,r);break;case 6:a[1]=void(e.l+=r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw Error("Unrecognized ExtProp type: "+t+" "+r)}return a}(e,r-e.l));return{ixfe:a,ext:s}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:al,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2);return[am(e,a,r),am(e,n,r)]},r:12},2197:{},2198:{f:function(e,t,r){var a,n=e.l+t;if(124226!==e.read_shift(4)){if(!r.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;try{a=function(e,t){switch(t.type){case"base64":return e_.read(e,{type:"base64"});case"binary":return e_.read(e,{type:"binary"});case"buffer":case"array":return e_.read(e,{type:"buffer"})}throw Error("Unrecognized type "+t.type)}(s,{type:"array"})}catch(e){return}var i=eX(a,"theme/theme/theme1.xml",!0);if(i)return function(e,t){e&&0!==e.length||(e=nA());var r,a,n,s={};if(!(n=e.match(n_)))throw Error("themeElements not found in theme");return r=n[0],s.themeElements={},[["clrScheme",nw,nv],["fontScheme",nE,nT],["fmtScheme",nS,nb]].forEach(function(e){if(!(a=r.match(e[1])))throw Error(e[0]+" not found in themeElements");e[2](a,s,t)}),s.raw=e,s}(i,r)}},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:ao},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t,r,a=(t=e.read_shift(2),r=e.read_shift(2),e.l+=8,{type:t,flags:r});if(2211!=a.type)throw Error("Invalid Future Record "+a.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:af},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aS(e,8));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:aL},1:{},2:{f:function(e){var t=a_(e,6);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=a_(e,6);++e.l;var r=rO(e,8);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=a_(e,6);++e.l;var n=av(e,t-7,r);return a.t="str",a.val=n,a}},5:{f:aM},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:aN},11:{},22:{f:af},30:{f:av},31:{},32:{},33:{f:aV},36:{},37:{f:aP},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:af},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=a_(e,6),s=e.read_shift(2),i=am(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:n1},521:{f:aN},536:{f:aW},547:{f:aB},561:{},579:{},1030:{f:n1},1033:{f:aN},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function s_(e,t,r,a){if(!isNaN(t)){var n=a||(r||[]).length||0,s=e.next(4);s.write_shift(2,t),s.write_shift(2,n),n>0&&tG(r)&&e.push(r)}}function sA(e,t,r){return e||(e=t2(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function sy(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];a&&a["!ref"]&&rs(a["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var n=t||{};switch(n.biff||2){case 8:case 5:return function(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=eH(z)),e&&e.SSF&&(ew(),eb(e.SSF),r.revssf=ek(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,s2(r),r.cellXfs=[],sr(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=function(e,t,r){var a,n,s,i,o,l,c,f,h,u=t4(),d=r.SheetNames[e],p=r.Sheets[d]||{},m=(r||{}).Workbook||{},g=(m.Sheets||[])[e]||{},v=Array.isArray(p),T=8==t.biff,b="",w=[],E=ro(p["!ref"]||"A1"),S=T?65536:16384;if(E.e.c>255||E.e.r>=S){if(t.WTF)throw Error("Range "+(p["!ref"]||"A1")+" exceeds format limit A1:IV16384");E.e.c=Math.min(E.e.c,255),E.e.r=Math.min(E.e.c,S-1)}s_(u,2057,aD(r,16,t)),s_(u,13,ah(1)),s_(u,12,ah(100)),s_(u,15,ac(!0)),s_(u,17,ac(!1)),s_(u,16,rk(.001)),s_(u,95,ac(!0)),s_(u,42,ac(!1)),s_(u,43,ac(!1)),s_(u,130,ah(1)),s_(u,128,(a=[0,0],(n=t2(8)).write_shift(4,0),n.write_shift(2,a[0]?a[0]+1:0),n.write_shift(2,a[1]?a[1]+1:0),n)),s_(u,131,ac(!1)),s_(u,132,ac(!1)),T&&function(e,t){if(t){var r=0;t.forEach(function(t,a){if(++r<=256&&t){var n,s,i;s_(e,125,(n=se(a,t),(s=t2(12)).write_shift(2,a),s.write_shift(2,a),s.write_shift(2,256*n.width),s.write_shift(2,0),i=0,n.hidden&&(i|=1),s.write_shift(1,i),i=n.level||0,s.write_shift(1,i),s.write_shift(2,0),s))}})}}(u,p["!cols"]),s_(u,512,((i=t2(2*(s=8!=t.biff&&t.biff?2:4)+6)).write_shift(s,E.s.r),i.write_shift(s,E.e.r+1),i.write_shift(2,E.s.c),i.write_shift(2,E.e.c+1),i.write_shift(2,0),i)),T&&(p["!links"]=[]);for(var _=E.s.r;_<=E.e.r;++_){b=re(_);for(var A=E.s.c;A<=E.e.c;++A){_===E.s.r&&(w[A]=rr(A)),h=w[A]+b;var y=v?(p[_]||[])[A]:p[h];y&&(!function(e,t,r,a,n){var s=16+sr(n.cellXfs,t,n);if(null==t.v&&!t.bf){s_(e,513,aA(r,a,s));return}if(t.bf)s_(e,6,function(e,t,r,a,n){var s=aA(t,r,n),i=function(e){if(null==e){var t=t2(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return"number"==typeof e?rk(e):rk(0)}(e.v),o=t2(6);o.write_shift(2,33),o.write_shift(4,0);for(var l=t2(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];return D([s,i,o,l])}(t,r,a,0,s));else switch(t.t){case"d":case"n":var i,o="d"==t.t?eR(eU(t.v)):t.v;s_(e,515,(aA(r,a,s,i=t2(14)),rk(o,i),i));break;case"b":case"e":s_(e,517,(l=t.v,c=t.t,aA(r,a,s,f=t2(8)),ad(l,c,f),f));break;case"s":case"str":if(n.bookSST){var l,c,f,h,u,d,p,m=n9(n.Strings,t.v,n.revStrings);s_(e,253,(aA(r,a,s,p=t2(10)),p.write_shift(4,m),p))}else s_(e,516,(h=(t.v||"").slice(0,255),aA(r,a,s,d=t2(8+ +(u=!n||8==n.biff)+(1+u)*h.length)),d.write_shift(2,h.length),u&&d.write_shift(1,1),d.write_shift((1+u)*h.length,h,u?"utf16le":"sbcs"),d));break;default:s_(e,513,aA(r,a,s))}}(u,y,_,A,t),T&&y.l&&p["!links"].push([h,y.l]))}}var x=g.CodeName||g.name||d;return T&&s_(u,574,(o=(m.Views||[])[0],l=t2(18),c=1718,o&&o.RTL&&(c|=64),l.write_shift(2,c),l.write_shift(4,0),l.write_shift(4,64),l.write_shift(4,0),l.write_shift(4,0),l)),T&&(p["!merges"]||[]).length&&s_(u,229,function(e){var t=t2(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)aO(e[r],t);return t}(p["!merges"])),T&&function(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];s_(e,440,function(e){var t=t2(24),r=ra(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return D([t,function(e){var t=t2(512),r=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)aw(a=a.slice(1),t);else if(2&s){for(r=0,i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&aw(n>-1?a.slice(n+1):"",t)}else{for(r=0,i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var l=0;"../"==a.slice(3*l,3*l+3)||"..\\"==a.slice(3*l,3*l+3);)++l;for(t.write_shift(2,l),t.write_shift(4,a.length-3*l+1),r=0;r<a.length-3*l;++r)t.write_shift(1,255&a.charCodeAt(r+3*l));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}(e[1])])}(a)),a[1].Tooltip&&s_(e,2048,function(e){var t=e[1].Tooltip,r=t2(10+2*(t.length+1));r.write_shift(2,2048);var a=ra(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}(a))}delete t["!links"]}(u,p),s_(u,442,aT(x,t)),T&&((f=t2(19)).write_shift(4,2151),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,1),f.write_shift(4,0),s_(u,2151,f),(f=t2(39)).write_shift(4,2152),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,0),f.write_shift(4,0),f.write_shift(2,1),f.write_shift(4,4),f.write_shift(2,0),aO(ro(p["!ref"]||"A1"),f),f.write_shift(4,4),s_(u,2152,f)),s_(u,10),u.end()}(n,r,e);return a.unshift(function(e,t,r){var a,n,s,i,o,l,c,f=t4(),h=(e||{}).Workbook||{},u=h.Sheets||[],d=h.WBProps||{},p=8==r.biff,m=5==r.biff;s_(f,2057,aD(e,5,r)),"xla"==r.bookType&&s_(f,135),s_(f,225,p?ah(1200):null),s_(f,193,function(e,t){t||(t=t2(2));for(var r=0;r<2;++r)t.write_shift(1,0);return t}(2)),m&&s_(f,191),m&&s_(f,192),s_(f,226),s_(f,92,function(e,t){var r=!t||8==t.biff,a=t2(r?112:54);for(a.write_shift(8==t.biff?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,0x33336853),a.write_shift(4,5458548|0x20000000*!r);a.l<a.length;)a.write_shift(1,32*!r);return a}(0,r)),s_(f,66,ah(p?1200:1252)),p&&s_(f,353,ah(0)),p&&s_(f,448),s_(f,317,function(e){for(var t=t2(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),p&&e.vbaraw&&s_(f,211),p&&e.vbaraw&&s_(f,442,aT(d.CodeName||"ThisWorkbook",r)),s_(f,156,ah(17)),s_(f,25,ac(!1)),s_(f,18,ac(!1)),s_(f,19,ah(0)),p&&s_(f,431,ac(!1)),p&&s_(f,444,ah(0)),s_(f,61,((a=t2(18)).write_shift(2,0),a.write_shift(2,0),a.write_shift(2,29280),a.write_shift(2,17600),a.write_shift(2,56),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,1),a.write_shift(2,500),a)),s_(f,64,ac(!1)),s_(f,141,ah(0)),s_(f,34,ac("true"==(e.Workbook&&e.Workbook.WBProps&&tt(e.Workbook.WBProps.date1904)?"true":"false"))),s_(f,14,ac(!0)),p&&s_(f,439,ac(!1)),s_(f,218,ah(0)),s_(f,49,(s=(n={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(o=t2((i=r&&5==r.biff)?15+s.length:16+2*s.length)).write_shift(2,20*(n.sz||12)),o.write_shift(4,0),o.write_shift(2,400),o.write_shift(4,0),o.write_shift(2,0),o.write_shift(1,s.length),i||o.write_shift(1,1),o.write_shift((i?1:2)*s.length,s,i?"sbcs":"utf16le"),o)),(l=e.SSF)&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=l[t]&&s_(f,1054,function(e,t,r,a){var n=r&&5==r.biff;a||(a=t2(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}(t,l[t],r))}),function(e,t){for(var r=0;r<16;++r)s_(e,224,aF({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(r){s_(e,224,aF(r,0,t))})}(f,r),p&&s_(f,352,ac(!1));var g=f.end(),v=t4();p&&s_(v,140,(c||(c=t2(4)),c.write_shift(2,1),c.write_shift(2,1),c)),p&&r.Strings&&function(e,t,r,a){var n=(r||[]).length||0;if(n<=8224)return s_(e,252,r,n);if(!isNaN(252)){for(var s=r.parts||[],i=0,o=0,l=0;l+(s[i]||8224)<=8224;)l+=s[i]||8224,i++;var c=e.next(4);for(c.write_shift(2,t),c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<n;){for((c=e.next(4)).write_shift(2,60),l=0;l+(s[i]||8224)<=8224;)l+=s[i]||8224,i++;c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}(v,252,function(e,t){var r=t2(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=function(e){var t=e.t||"",r=t2(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=t2(2*t.length);return a.write_shift(2*t.length,t,"utf16le"),D([r,a])}(e[n],t);var s=D([r].concat(a));return s.parts=[r.length].concat(a.map(function(e){return e.length})),s}(r.Strings,r)),s_(v,10);var T=v.end(),b=t4(),w=0,E=0;for(E=0;E<e.SheetNames.length;++E)w+=(p?12:11)+(p?2:1)*e.SheetNames[E].length;var S=g.length+w+T.length;for(E=0;E<e.SheetNames.length;++E)s_(b,133,function(e,t){var r=!t||t.biff>=8?2:1,a=t2(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}({pos:S,hs:(u[E]||{}).Hidden||0,dt:0,name:e.SheetNames[E]},r)),S+=t[E].length;var _=b.end();if(w!=_.length)throw Error("BS8 "+w+" != "+_.length);var A=[];return g.length&&A.push(g),_.length&&A.push(_),T.length&&A.push(T),D(A)}(e,a,r)),D(a)}(e,t);case 4:case 3:case 2:return function(e,t){for(var r=t||{},a=t4(),n=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(n=s);if(0==n&&r.sheet&&e.SheetNames[0]!=r.sheet)throw Error("Sheet not found: "+r.sheet);return s_(a,4==r.biff?1033:3==r.biff?521:9,aD(e,16,r)),!function(e,t,r,a){var n,s=Array.isArray(t),i=ro(t["!ref"]||"A1"),o="",l=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),n=ri(i)}for(var c=i.s.r;c<=i.e.r;++c){o=re(c);for(var f=i.s.c;f<=i.e.c;++f){c===i.s.r&&(l[f]=rr(f)),n=l[f]+o;var h=s?(t[c]||[])[f]:t[n];h&&function(e,t,r,a){if(null!=t.v)switch(t.t){case"d":case"n":var n,s,i,o,l,c,f,h="d"==t.t?eR(eU(t.v)):t.v;h==(0|h)&&h>=0&&h<65536?s_(e,2,(sA(c=t2(9),r,a),c.write_shift(2,h),c)):s_(e,3,(sA(f=t2(15),r,a),f.write_shift(8,h,"f"),f));return;case"b":case"e":s_(e,5,(n=t.v,s=t.t,sA(i=t2(9),r,a),ad(n,s||"b",i),i));return;case"s":case"str":s_(e,4,(sA(l=t2(8+2*(o=(t.v||"").slice(0,255)).length),r,a),l.write_shift(1,o.length),l.write_shift(o.length,o,"sbcs"),l.l<l.length?l.slice(0,l.l):l));return}s_(e,1,sA(null,r,a))}(e,h,c,f,a)}}}(a,e.Sheets[e.SheetNames[n]],0,r,e),s_(a,10),a.end()}(e,t)}throw Error("invalid type "+n.bookType+" for BIFF")}function sx(e,t){var r=t||{},a=r.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,o=s&&s.index||e.length,l=(null)(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),c=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<l.length;++i){var m=l[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"==g){if(++c,r.sheetRows&&r.sheetRows<=c){--c;break}f=0;continue}if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(o=0;o<v.length;++o){var T=v[o].trim();if(T.match(/<t[dh]/i)){for(var b=T,w=0;"<"==b.charAt(0)&&(w=b.indexOf(">"))>-1;)b=b.slice(w+1);for(var E=0;E<p.length;++E){var S=p[E];S.s.c==f&&S.s.r<c&&c<=S.e.r&&(f=S.e.c+1,E=-1)}var _=e4(T.slice(0,T.indexOf(">")));u=_.colspan?+_.colspan:1,((h=+_.rowspan)>1||u>1)&&p.push({s:{r:c,c:f},e:{r:c+(h||1)-1,c:f+u-1}});var A=_.t||_["data-t"]||"";if(!b.length||(b=tl(b),d.s.r>c&&(d.s.r=c),d.e.r<c&&(d.e.r=c),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),!b.length)){f+=u;continue}var y={t:"s",v:b};!r.raw&&b.trim().length&&"s"!=A&&("TRUE"===b?y={t:"b",v:!0}:"FALSE"===b?y={t:"b",v:!1}:isNaN(eG(b))?isNaN(ez(b).getDate())||(y={t:"d",v:eU(b)},r.cellDates||(y={t:"n",v:eR(y.v)}),y.z=r.dateNF||z[14]):y={t:"n",v:eG(b)}),r.dense?(a[c]||(a[c]=[]),a[c][f]=y):a[rn({r:c,c:f})]=y,f+=u}}}}return a["!ref"]=ri(d),p.length&&(a["!merges"]=p),a}function sO(e,t){var r,a,n,s=t||{},i=null!=s.header?s.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',o=null!=s.footer?s.footer:"</body></html>",l=[i],c=rs(e["!ref"]);s.dense=Array.isArray(e),l.push((r=0,a=0,"<table"+((n=s)&&n.id?' id="'+n.id+'"':"")+">"));for(var f=c.s.r;f<=c.e.r;++f)l.push(function(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o=0,l=0,c=0;c<n.length;++c)if(!(n[c].s.r>r)&&!(n[c].s.c>i)&&!(n[c].e.r<r)&&!(n[c].e.c<i)){if(n[c].s.r<r||n[c].s.c<i){o=-1;break}o=n[c].e.r-n[c].s.r+1,l=n[c].e.c-n[c].s.c+1;break}if(!(o<0)){var f=rn({r:r,c:i}),h=a.dense?(e[r]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||te(h.w||(rc(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),l>1&&(d.colspan=l),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(tm("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}(e,c,f,s));return l.push("</table>"+o),l.join("")}function sk(e,t,r){var a=r||{},n=0,s=0;if(null!=a.origin){if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?ra(a.origin):a.origin;n=i.r,s=i.c}}var o=t.getElementsByTagName("tr"),l=Math.min(a.sheetRows||1e7,o.length),c={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=rs(e["!ref"]);c.s.r=Math.min(c.s.r,f.s.r),c.s.c=Math.min(c.s.c,f.s.c),c.e.r=Math.max(c.e.r,f.e.r),c.e.c=Math.max(c.e.c,f.e.c),-1==n&&(c.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,T=0,b=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<l;++p){var w=o[p];if(sR(w)){if(a.display)continue;d[m]={hidden:!0}}var E=w.children;for(g=v=0;g<E.length;++g){var S=E[g];if(!(a.display&&sR(S))){var _=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):tl(S.innerHTML),A=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var y=h[u];y.s.c==v+s&&y.s.r<m+n&&m+n<=y.e.r&&(v=y.e.c+1-s,u=-1)}b=+S.getAttribute("colspan")||1,((T=+S.getAttribute("rowspan")||1)>1||b>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(T||1)-1,c:v+s+(b||1)-1}});var x={t:"s",v:_},O=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=_&&(0==_.length?x.t=O||"z":a.raw||0==_.trim().length||"s"==O||("TRUE"===_?x={t:"b",v:!0}:"FALSE"===_?x={t:"b",v:!1}:isNaN(eG(_))?isNaN(ez(_).getDate())||(x={t:"d",v:eU(_)},a.cellDates||(x={t:"n",v:eR(x.v)}),x.z=a.dateNF||z[14]):x={t:"n",v:eG(_)})),void 0===x.z&&null!=A&&(x.z=A);var k="",C=S.getElementsByTagName("A");if(C&&C.length)for(var R=0;R<C.length&&(!C[R].hasAttribute("href")||"#"==(k=C[R].getAttribute("href")).charAt(0));++R);k&&"#"!=k.charAt(0)&&(x.l={Target:k}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=x):e[rn({c:v+s,r:m+n})]=x,c.e.c<v+s&&(c.e.c=v+s),v+=b}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),c.e.r=Math.max(c.e.r,m-1+n),e["!ref"]=ri(c),m>=l&&(e["!fullref"]=ri((c.e.r=o.length-p+m-1+n,c))),e}function sC(e,t){return sk((t||{}).dense?[]:{},e,t)}function sR(e){var t,r="",a=(t=e).ownerDocument.defaultView&&"function"==typeof t.ownerDocument.defaultView.getComputedStyle?t.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null;return a&&(r=a(e).getPropertyValue("display")),r||(r=e.style&&e.style.display),"none"===r}var sI={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function sN(e,t){var r,a,n,s,i,o,l,c=t||{},f=tv(e),h=[],u={name:""},d="",p=0,m={},g=[],v=c.dense?[]:{},T={value:""},b="",w=0,E=[],S=-1,_=-1,A={s:{r:1e6,c:1e7},e:{r:0,c:0}},y=0,x={},O=[],k={},C=0,R=0,I=[],N=1,D=1,P=[],L={Names:[]},F={},M=["",""],U=[],B={},H="",W=0,G=!1,V=!1,z=0;for(tT.lastIndex=0,f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");i=tT.exec(f);)switch(i[3]=i[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===i[1]?(A.e.c>=A.s.c&&A.e.r>=A.s.r?v["!ref"]=ri(A):v["!ref"]="A1:A1",c.sheetRows>0&&c.sheetRows<=A.e.r&&(v["!fullref"]=v["!ref"],A.e.r=c.sheetRows-1,v["!ref"]=ri(A)),O.length&&(v["!merges"]=O),I.length&&(v["!rows"]=I),n.name=n["名称"]||n.name,"undefined"!=typeof JSON&&JSON.stringify(n),g.push(n.name),m[n.name]=v,V=!1):"/"!==i[0].charAt(i[0].length-2)&&(n=e4(i[0],!1),S=_=-1,A.s.r=A.s.c=1e7,A.e.r=A.e.c=0,v=c.dense?[]:{},O=[],I=[],V=!0);break;case"table-row-group":"/"===i[1]?--y:++y;break;case"table-row":case"行":if("/"===i[1]){S+=N,N=1;break}if((s=e4(i[0],!1))["行号"]?S=s["行号"]-1:-1==S&&(S=0),(N=+s["number-rows-repeated"]||1)<10)for(z=0;z<N;++z)y>0&&(I[S+z]={level:y});_=-1;break;case"covered-table-cell":"/"!==i[1]&&++_,c.sheetStubs&&(c.dense?(v[S]||(v[S]=[]),v[S][_]={t:"z"}):v[rn({r:S,c:_})]={t:"z"}),b="",E=[];break;case"table-cell":case"数据":if("/"===i[0].charAt(i[0].length-2))++_,D=parseInt((T=e4(i[0],!1))["number-columns-repeated"]||"1",10),o={t:"z",v:null},T.formula&&!1!=c.cellFormula&&(o.f=n5((null)(T.formula))),"string"==(T["数据类型"]||T["value-type"])&&(o.t="s",o.v=(null)(T["string-value"]||""),c.dense?(v[S]||(v[S]=[]),v[S][_]=o):v[rn({r:S,c:_})]=o),_+=D-1;else if("/"!==i[1]){b="",w=0,E=[],D=1;var j=N?S+N-1:S;if(++_>A.e.c&&(A.e.c=_),_<A.s.c&&(A.s.c=_),S<A.s.r&&(A.s.r=S),j>A.e.r&&(A.e.r=j),T=e4(i[0],!1),U=[],B={},o={t:T["数据类型"]||T["value-type"],v:null},c.cellFormula){if(T.formula&&(T.formula=(null)(T.formula)),T["number-matrix-columns-spanned"]&&T["number-matrix-rows-spanned"]&&(k={s:{r:S,c:_},e:{r:S+(C=parseInt(T["number-matrix-rows-spanned"],10)||0)-1,c:_+(parseInt(T["number-matrix-columns-spanned"],10)||0)-1}},o.F=ri(k),P.push([k,o.F])),T.formula)o.f=n5(T.formula);else for(z=0;z<P.length;++z)S>=P[z][0].s.r&&S<=P[z][0].e.r&&_>=P[z][0].s.c&&_<=P[z][0].e.c&&(o.F=P[z][1])}switch((T["number-columns-spanned"]||T["number-rows-spanned"])&&(k={s:{r:S,c:_},e:{r:S+(C=parseInt(T["number-rows-spanned"],10)||0)-1,c:_+(parseInt(T["number-columns-spanned"],10)||0)-1}},O.push(k)),T["number-columns-repeated"]&&(D=parseInt(T["number-columns-repeated"],10)),o.t){case"boolean":o.t="b",o.v=tt(T["boolean-value"]);break;case"float":case"percentage":case"currency":o.t="n",o.v=parseFloat(T.value);break;case"date":o.t="d",o.v=eU(T["date-value"]),c.cellDates||(o.t="n",o.v=eR(o.v)),o.z="m/d/yy";break;case"time":o.t="n",o.v=function(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[s],10)}return t}(T["time-value"])/86400,c.cellDates&&(o.t="d",o.v=eP(o.v)),o.z="HH:MM:SS";break;case"number":o.t="n",o.v=parseFloat(T["数据数值"]);break;default:if("string"!==o.t&&"text"!==o.t&&o.t)throw Error("Unsupported value type "+o.t);o.t="s",null!=T["string-value"]&&(b=(null)(T["string-value"]),E=[])}}else{if(G=!1,"s"===o.t&&(o.v=b||"",E.length&&(o.R=E),G=0==w),F.Target&&(o.l=F),U.length>0&&(o.c=U,U=[]),b&&!1!==c.cellText&&(o.w=b),G&&(o.t="z",delete o.v),(!G||c.sheetStubs)&&!(c.sheetRows&&c.sheetRows<=S))for(var Y=0;Y<N;++Y){if(D=parseInt(T["number-columns-repeated"]||"1",10),c.dense)for(v[S+Y]||(v[S+Y]=[]),v[S+Y][_]=0==Y?o:eH(o);--D>0;)v[S+Y][_+D]=eH(o);else for(v[rn({r:S+Y,c:_})]=o;--D>0;)v[rn({r:S+Y,c:_+D})]=eH(o);A.e.c<=_&&(A.e.c=_)}_+=(D=parseInt(T["number-columns-repeated"]||"1",10))-1,D=0,o={},b="",E=[]}F={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===i[1]){if((r=h.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&h.push([i[3],!0]);break;case"annotation":if("/"===i[1]){if((r=h.pop())[0]!==i[3])throw"Bad state: "+r;B.t=b,E.length&&(B.R=E),B.a=H,U.push(B)}else"/"!==i[0].charAt(i[0].length-2)&&h.push([i[3],!1]);H="",W=0,b="",w=0,E=[];break;case"creator":"/"===i[1]?H=f.slice(W,i.index):W=i.index+i[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===i[1]){if((r=h.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&h.push([i[3],!1]);b="",w=0,E=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===i[1]){if(x[u.name]=d,(r=h.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&(d="",u=e4(i[0],!1),h.push([i[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(h[h.length-1][0]){case"time-style":case"date-style":a=e4(i[0],!1),d+=sI[i[3]][+("long"===a.style)]}break;case"text":if("/>"===i[0].slice(-2));else if("/"===i[1])switch(h[h.length-1][0]){case"number-style":case"date-style":case"time-style":d+=f.slice(p,i.index)}else p=i.index+i[0].length;break;case"named-range":M=n6((a=e4(i[0],!1))["cell-range-address"]);var K={Name:a.name,Ref:M[0]+"!"+M[1]};V&&(K.Sheet=g.length),L.Names.push(K);break;case"p":case"文本串":if(["master-styles"].indexOf(h[h.length-1][0])>-1)break;if("/"!==i[1]||T&&T["string-value"])e4(i[0],!1),w=i.index+i[0].length;else{var X=[(null)(f.slice(w,i.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];b=(b.length>0?b+"\n":"")+X[0]}break;case"database-range":if("/"===i[1])break;try{m[(M=n6(e4(i[0])["target-range-address"]))[0]]["!autofilter"]={ref:M[1]}}catch(e){}break;case"a":if("/"!==i[1]){if(!(F=e4(i[0],!1)).href)break;F.Target=(null)(F.href),delete F.href,"#"==F.Target.charAt(0)&&F.Target.indexOf(".")>-1?(M=n6(F.Target.slice(1)),F.Target="#"+M[0]+"!"+M[1]):F.Target.match(/^\.\.[\\\/]/)&&(F.Target=F.Target.slice(3))}break;default:switch(i[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(c.WTF)throw Error(i)}}var J={Sheets:m,SheetNames:g,Workbook:L};return c.bookSheets&&delete J.Sheets,J}var sD=function(){var e="<office:document-styles "+tp({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+'><office:master-styles><style:master-page style:name="mp1" style:page-layout-name="mp1"><style:header/><style:header-left style:display="false"/><style:footer/><style:footer-left style:display="false"/></style:master-page></office:master-styles></office:document-styles>';return function(){return eq+e}}(),sP=function(){var e="          <table:table-cell />\n",t=function(t,r,a){var n=[];n.push('      <table:table table:name="'+e8(r.SheetNames[a])+'" table:style-name="ta1">\n');var s=0,i=0,o=rs(t["!ref"]||"A1"),l=t["!merges"]||[],c=0,f=Array.isArray(t);if(t["!cols"])for(i=0;i<=o.e.c;++i)n.push("        <table:table-column"+(t["!cols"][i]?' table:style-name="co'+t["!cols"][i].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(s=0;s<o.s.r;++s)h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+"></table:table-row>\n");for(;s<=o.e.r;++s){for(h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+">\n"),i=0;i<o.s.c;++i)n.push(e);for(;i<=o.e.c;++i){var d=!1,p={},m="";for(c=0;c!=l.length;++c)if(!(l[c].s.c>i)&&!(l[c].s.r>s)&&!(l[c].e.c<i)&&!(l[c].e.r<s)){(l[c].s.c!=i||l[c].s.r!=s)&&(d=!0),p["table:number-columns-spanned"]=l[c].e.c-l[c].s.c+1,p["table:number-rows-spanned"]=l[c].e.r-l[c].s.r+1;break}if(d){n.push("          <table:covered-table-cell/>\n");continue}var g=rn({r:s,c:i}),v=f?(t[s]||[])[i]:t[g];if(v&&v.f&&(p["table:formula"]=e8(("of:="+v.f.replace(nI,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var T=rs(v.F);p["table:number-matrix-columns-spanned"]=T.e.c-T.s.c+1,p["table:number-matrix-rows-spanned"]=T.e.r-T.s.r+1}if(!v){n.push(e);continue}switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||eU(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=eU(v.v).toISOString(),p["table:style-name"]="ce1";break;default:n.push(e);continue}var b=e8(m).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var w=v.l.Target;"#"==(w="#"==w.charAt(0)?"#"+w.slice(1).replace(/\./,"!"):w).charAt(0)||w.match(/^\w+:/)||(w="../"+w),b=tm("text:a",b,{"xlink:href":w.replace(/&/g,"&amp;")})}n.push("          "+tm("table:table-cell",tm("text:p",b,{}),p)+"\n")}n.push("        </table:table-row>\n")}return n.push("      </table:table>\n"),n.join("")},r=function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!cols"]){for(var a=0;a<t["!cols"].length;++a)if(t["!cols"][a]){var n=t["!cols"][a];if(null==n.width&&null==n.wpx&&null==n.wch)continue;no(n),n.ods=r;var s=t["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}});var a=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!rows"]){for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=a;var n=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n'),e.push("  </style:style>\n"),++a}}}),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,a){var n=[eq],s=tp({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),i=tp({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==a.bookType?(n.push("<office:document"+s+i+">\n"),n.push(rK().replace(/office:document-meta/g,"office:meta"))):n.push("<office:document-content"+s+">\n"),r(n,e),n.push("  <office:body>\n"),n.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)n.push(t(e.Sheets[e.SheetNames[o]],e,o,a));return n.push("    </office:spreadsheet>\n"),n.push("  </office:body>\n"),"fods"==a.bookType?n.push("</office:document>"):n.push("</office:document-content>"),n.join("")}}();function sL(e,t){if("fods"==t.bookType)return sP(e,t);var r=eZ(),a="",n=[],s=[];return eJ(r,a="mimetype","application/vnd.oasis.opendocument.spreadsheet"),eJ(r,a="content.xml",sP(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),eJ(r,a="styles.xml",sD(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),eJ(r,a="meta.xml",eq+rK()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),eJ(r,a="manifest.rdf",function(e){var t=[eq];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(rY(e[r][0],e[r][1])),t.push(['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join(""));return t.push(rY("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(s)),n.push([a,"application/rdf+xml"]),eJ(r,a="META-INF/manifest.xml",function(e){var t=[eq];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}function sF(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function sM(e){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(e):ti(N(e))}function sU(e){var t=new Uint8Array(e.reduce(function(e,t){return e+t.length},0)),r=0;return e.forEach(function(e){t.set(e,r),r+=e.length}),t}function sB(e){return e-=e>>1&0x55555555,((e=(0x33333333&e)+(e>>2&0x33333333))+(e>>4)&0xf0f0f0f)*0x1010101>>>24}function sH(e,t){var r=t?t[0]:0,a=127&e[r];t:if(e[r++]>=128&&(a|=(127&e[r])<<7,e[r++]<128||(a|=(127&e[r])<<14,e[r++]<128)||(a|=(127&e[r])<<21,e[r++]<128)||(a+=(127&e[r])*0x10000000,++r,e[r++]<128)||(a+=(127&e[r])*0x800000000,++r,e[r++]<128)||(a+=(127&e[r])*0x40000000000,++r,e[r++]<128)))break t;return t&&(t[0]=r),a}function sW(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;r:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=0xfffffff)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=0x7ffffffff)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=0x3ffffffffff))break r;t[r-1]|=128,t[r]=e/0x1000000>>>21&127,++r}return t.slice(0,r)}function sG(e){var t=0,r=127&e[0];t:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128||(r|=(127&e[t])<<14,e[t++]<128)||(r|=(127&e[t])<<21,e[t++]<128))break t;r|=(127&e[t])<<28}return r}function sV(e){for(var t=[],r=[0];r[0]<e.length;){var a,n=r[0],s=sH(e,r),i=7&s,o=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var l=r[0];e[r[0]++]>=128;);a=e.slice(l,r[0]);break;case 5:o=4,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=sH(e,r),a=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var c={data:a,type:i};null==t[s]?t[s]=[c]:t[s].push(c)}return t}function sz(e){var t=[];return e.forEach(function(e,r){e.forEach(function(e){e.data&&(t.push(sW(8*r+e.type)),2==e.type&&t.push(sW(e.data.length)),t.push(e.data))})}),sU(t)}function sj(e,t){return(null==e?void 0:e.map(function(e){return t(e.data)}))||[]}function s$(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=sH(e,a),s=sV(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:sG(s[1][0].data),messages:[]};s[2].forEach(function(t){var r=sV(t.data),n=sG(r[3][0].data);i.messages.push({meta:r,data:e.slice(a[0],a[0]+n)}),a[0]+=n}),(null==(t=s[3])?void 0:t[0])&&(i.merge=sG(s[3][0].data)>>>0>0),r.push(i)}return r}function sY(e){var t=[];return e.forEach(function(e){var r=[];r[1]=[{data:sW(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:sW(+!!e.merge),type:0}]);var a=[];e.messages.forEach(function(e){a.push(e.data),e.meta[3]=[{type:0,data:sW(e.data.length)}],r[2].push({data:sz(e.meta),type:2})});var n=sz(r);t.push(sW(n.length)),t.push(n),a.forEach(function(e){return t.push(e)})}),sU(t)}function sK(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(function(e,t){if(0!=e)throw Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=sH(t,r),n=[];r[0]<t.length;){var s=3&t[r[0]];if(0==s){var i=t[r[0]++]>>2;if(i<60)++i;else{var o=i-59;i=t[r[0]],o>1&&(i|=t[r[0]+1]<<8),o>2&&(i|=t[r[0]+2]<<16),o>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=o}n.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}var l=0,c=0;if(1==s?(c=(t[r[0]]>>2&7)+4,l=(224&t[r[0]++])<<3|t[r[0]++]):(c=(t[r[0]++]>>2)+1,2==s?(l=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(l=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[sU(n)],0==l)throw Error("Invalid offset 0");if(l>n[0].length)throw Error("Invalid offset beyond length");if(c>=l)for(n.push(n[0].slice(-l)),c-=l;c>=n[n.length-1].length;)n.push(n[n.length-1]),c-=n[n.length-1].length;n.push(n[0].slice(-l,-l+c))}var f=sU(n);if(f.length!=a)throw Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw Error("data is not a valid framed stream!");return sU(t)}function sX(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,0xfffffff),n=new Uint8Array(4);t.push(n);var s=sW(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=0x1000000?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=0x100000000&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return sU(t)}function sJ(e,t){var r=new Uint8Array(32),a=sF(r),n=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var a=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=255&n;e[t+15]|=r>=0?0:128}(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,+!!e.v,!0),s|=2,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r.slice(0,n)}function sZ(e,t){var r=new Uint8Array(32),a=sF(r),n=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,+!!e.v,!0),s|=32,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),r.slice(0,n)}function sq(e){return sH(sV(e)[1][0].data)}function sQ(e,t){var r=sV(t.data),a=sG(r[1][0].data),n=r[3],s=[];return(n||[]).forEach(function(t){var r=sV(t.data),n=sG(r[1][0].data)>>>0;switch(a){case 1:s[n]=sM(r[3][0].data);break;case 8:var i=sV(e[sq(r[9][0].data)][0].data),o=e[sq(i[1][0].data)][0],l=sG(o.meta[1][0].data);if(2001!=l)throw Error("2000 unexpected reference to ".concat(l));var c=sV(o.data);s[n]=c[3].map(function(e){return sM(e.data)}).join("")}}),s}function s1(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];void 0===t[a[0]]&&(t[a[0]]=a[1]),"n"===a[2]&&(t[a[0]]=Number(t[a[0]]))}}}function s0(e){s1([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function s2(e){s1([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function s4(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return eA(t.file,e_.write(e,{type:x?"buffer":""}));case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");default:throw Error("Unrecognized type "+t.type)}return e_.write(e,t)}function s3(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return A(to(a));case"binary":return to(a);case"string":return e;case"file":return eA(t.file,a,"utf8");case"buffer":if(x)return O(a,"utf8");if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(a);return s3(a,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}function s5(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return"base64"==t.type?A(r):"string"==t.type?ti(r):r;case"file":return eA(t.file,e);case"buffer":return e;default:throw Error("Unrecognized type "+t.type)}}function s6(e,t,r){var a=r||{};return a.type="file",a.file=t,function(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType=({xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"})[e.bookType]||e.bookType}}(a),function e(t,r){v(),function(e){if(!e||!e.SheetNames||!e.Sheets)throw Error("Invalid Workbook");if(!e.SheetNames.length)throw Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];!function(e,t,r){e.forEach(function(a,n){sh(a);for(var s=0;s<n;++s)if(a==e[s])throw Error("Duplicate Sheet Name: "+a);if(r){var i=t&&t[n]&&t[n].CodeName||a;if(95==i.charCodeAt(0)&&i.length>22)throw Error("Bad Code Name: Worksheet"+i)}})}(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)!function(e,t,r){if(e&&e["!ref"]){var a=ro(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw Error("Bad range ("+r+"): "+e["!ref"])}}(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}(t);var a,n,s=eH(r||{});if(s.cellStyles&&(s.cellNF=!0,s.sheetStubs=!0),"array"==s.type){s.type="binary";var i=e(t,s);return s.type="array",I(i)}var o=0;if(s.sheet&&(o="number"==typeof s.sheet?s.sheet:t.SheetNames.indexOf(s.sheet),!t.SheetNames[o]))throw Error("Sheet not found: "+s.sheet+" : "+typeof s.sheet);switch(s.bookType||"xlsb"){case"xml":case"xlml":return s3(function(e,t){t||(t={}),e.SSF||(e.SSF=eH(z)),e.SSF&&(ew(),eb(e.SSF),t.revssf=ek(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],sr(t.cellXfs,{},{revssf:{General:0}}));var r,a=[];a.push(function(e,t){var r,a,n,s,i,o,l,c=[];return e.Props&&c.push((r=e.Props,a=[],ey(r2).map(function(e){for(var t=0;t<rX.length;++t)if(rX[t][1]==e)return rX[t];for(t=0;t<rq.length;++t)if(rq[t][1]==e)return rq[t];throw e}).forEach(function(e){if(null!=r[e[1]]){var n=t&&t.Props&&null!=t.Props[e[1]]?t.Props[e[1]]:r[e[1]];"date"===e[2]&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof n?n=String(n):!0===n||!1===n?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),a.push(td(r2[e[1]]||e[1],n))}}),tm("DocumentProperties",a.join(""),{xmlns:tE.o}))),e.Custprops&&c.push((n=e.Props,s=e.Custprops,i=["Worksheets","SheetNames"],o="CustomDocumentProperties",l=[],n&&ey(n).forEach(function(e){if(Object.prototype.hasOwnProperty.call(n,e)){for(var t=0;t<rX.length;++t)if(e==rX[t][1])return;for(t=0;t<rq.length;++t)if(e==rq[t][1])return;for(t=0;t<i.length;++t)if(e==i[t])return;var r=n[e],a="string";"number"==typeof r?(a="float",r=String(r)):!0===r||!1===r?(a="boolean",r=r?"1":"0"):r=String(r),l.push(tm(e7(e),r,{"dt:dt":a}))}}),s&&ey(s).forEach(function(e){if(Object.prototype.hasOwnProperty.call(s,e)&&!(n&&Object.prototype.hasOwnProperty.call(n,e))){var t=s[e],r="string";"number"==typeof t?(r="float",t=String(t)):!0===t||!1===t?(r="boolean",t=t?"1":"0"):t instanceof Date?(r="dateTime.tz",t=t.toISOString()):t=String(t),l.push(tm(e7(e),t,{"dt:dt":r}))}}),"<"+o+' xmlns="'+tE.o+'">'+l.join("")+"</"+o+">")),c.join("")}(e,t)),a.push(""),a.push(""),a.push("");for(var n=0;n<e.SheetNames.length;++n)a.push(tm("Worksheet",function(e,t,r){var a=[],n=r.SheetNames[e],s=r.Sheets[n],i=s?function(e,t,r,a){if(!e||!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(sv(o)))}return s.join("")}(s,0,e,r):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),(i=s?function(e,t,r,a){if(!e["!ref"])return"";var n=ro(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach(function(e,t){no(e);var r=!!e.width,a=se(t,e),n={"ss:Index":t+1};r&&(n["ss:Width"]=na(a.width)),e.hidden&&(n["ss:Hidden"]="1"),o.push(tm("Column",null,n))});for(var l=Array.isArray(e),c=n.s.r;c<=n.e.r;++c){for(var f=[function(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=nc(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}(c,(e["!rows"]||[])[c])],h=n.s.c;h<=n.e.c;++h){var u=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>h)&&!(s[i].s.r>c)&&!(s[i].e.c<h)&&!(s[i].e.r<c)){(s[i].s.c!=h||s[i].s.r!=c)&&(u=!0);break}if(!u){var d={r:c,c:h},p=rn(d),m=l?(e[c]||[])[h]:e[p];f.push(function(e,t,r,a,n,s,i){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+e8(nN(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var l=ra(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(l.r==i.r?"":"["+(l.r-i.r)+"]")+"C"+(l.c==i.c?"":"["+(l.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=e8(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=e8(e.l.Tooltip))),r["!merges"])for(var c=r["!merges"],f=0;f!=c.length;++f)c[f].s.c==i.c&&c[f].s.r==i.r&&(c[f].e.c>c[f].s.c&&(o["ss:MergeAcross"]=c[f].e.c-c[f].s.c),c[f].e.r>c[f].s.r&&(o["ss:MergeDown"]=c[f].e.r-c[f].s.r));var h="",u="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=rM[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||z[14]);break;case"s":h="String",u=((e.v||"")+"").replace(e5,function(e){return e3[e]}).replace(e9,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var d=sr(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map(function(e){var t=tm("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return tm("Comment",t,{"ss:Author":e.a})}).join("")),tm("Cell",m,o)}(m,p,e,t,0,0,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}(s,t,0,0):"").length>0&&a.push("<Table>"+i+"</Table>"),a.push(function(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(tm("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(tm("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(tm("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r]){if(a.Workbook.Sheets[r].Hidden)n.push(tm("Visible",1==a.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!a.Workbook.Sheets[s]||a.Workbook.Sheets[s].Hidden);++s);s==r&&n.push("<Selected/>")}}return(((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(td("ProtectContents","True")),e["!protect"].objects&&n.push(td("ProtectObjects","True")),e["!protect"].scenarios&&n.push(td("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||n.push(td("EnableSelection","UnlockedCells")):n.push(td("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(t){e["!protect"][t[0]]&&n.push("<"+t[1]+"/>")})),0==n.length)?"":tm("WorksheetOptions",n.join(""),{xmlns:tE.x})}(s,0,e,r)),a.join("")}(n,t,e),{"ss:Name":e8(e.SheetNames[n])}));return a[2]=(r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'],t.cellXfs.forEach(function(e,t){var a=[];a.push(tm("NumberFormat",null,{"ss:Format":e8(z[e.numFmtId])})),r.push(tm("Style",a.join(""),{"ss:ID":"s"+(21+t)}))}),tm("Styles",r.join(""))),a[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];null==n.Sheet&&(n.Name.match(/^_xlfn\./)||r.push(sv(n)))}return tm("Names",r.join(""))}(e,t),eq+tm("Workbook",a.join(""),{xmlns:tE.ss,"xmlns:o":tE.o,"xmlns:x":tE.x,"xmlns:ss":tE.ss,"xmlns:dt":tE.dt,"xmlns:html":tE.html})}(t,s),s);case"slk":case"sylk":return s3(aY.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"htm":case"html":return s3(sO(t.Sheets[t.SheetNames[o]],s),s);case"txt":return function(e,t){switch(t.type){case"base64":return A(e);case"binary":case"string":return e;case"file":return eA(t.file,e,"binary");case"buffer":if(x)return O(e,"binary");return e.split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}(ie(t.Sheets[t.SheetNames[o]],s),s);case"csv":return s3(s9(t.Sheets[t.SheetNames[o]],s),s,"\uFEFF");case"dif":return s3(aK.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"dbf":return s5(a$.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"prn":return s3(aJ.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"rtf":return s3(ne.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"eth":return s3(aX.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"fods":return s3(sL(t,s),s);case"wk1":return s5(aZ.sheet_to_wk1(t.Sheets[t.SheetNames[o]],s),s);case"wk3":return s5(aZ.book_to_wk3(t,s),s);case"biff2":s.biff||(s.biff=2);case"biff3":s.biff||(s.biff=3);case"biff4":return s.biff||(s.biff=4),s5(sy(t,s),s);case"biff5":s.biff||(s.biff=5);case"biff8":case"xla":case"xls":return s.biff||(s.biff=8),s4(function(e,t){var r=t||{},a=e_.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw Error("invalid type "+r.bookType+" for XLS CFB")}return e_.utils.cfb_add(a,n,sy(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,a=[],n=[],s=[],i=0,o=ex(rN,"n"),l=ex(rD,"n");if(e.Props)for(i=0,r=ey(e.Props);i<r.length;++i)(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(l,r[i])?n:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(i=0,r=ey(e.Custprops);i<r.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},r[i])||(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(l,r[i])?n:s).push([r[i],e.Custprops[r[i]]]);var c=[];for(i=0;i<s.length;++i)!(aa.indexOf(s[i][0])>-1||rQ.indexOf(s[i][0])>-1)&&null!=s[i][1]&&c.push(s[i]);n.length&&e_.utils.cfb_add(t,"/\x05SummaryInformation",ai(n,sw.SI,l,rD)),(a.length||c.length)&&e_.utils.cfb_add(t,"/\x05DocumentSummaryInformation",ai(a,sw.DSI,o,rN,c.length?c:null,sw.UDI))}(e,a),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach(function(r,a){if(0!=a){var n=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==n.slice(-1)&&e_.utils.cfb_add(e,n,t.FileIndex[a].content)}})}(a,e_.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),a}(t,a=s||{}),a);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r={},a=x?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw Error("Unrecognized type "+t.type)}var n=e.FullPaths?e_.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof n){if("binary"==t.type||"base64"==t.type)return n;n=new Uint8Array(I(n))}return t.password&&"undefined"!=typeof encrypt_agile?s4(encrypt_agile(n,t.password),t):"file"===t.type?eA(t.file,n):"string"==t.type?ti(n):n}("ods"==(n=eH(s||{})).bookType?sL(t,n):"numbers"==n.bookType?function(e,t){if(!t||!t.numbers)throw Error("Must pass a `numbers` option -- check the README");var r,a=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=rs(a["!ref"]);n.s.r=n.s.c=0;var s=!1;n.e.c>9&&(s=!0,n.e.c=9),n.e.r>49&&(s=!0,n.e.r=49),s&&console.error("The Numbers writer is currently limited to ".concat(ri(n)));var i=s8(a,{range:n,header:1}),o=["~Sh33tJ5~"];i.forEach(function(e){return e.forEach(function(e){"string"==typeof e&&o.push(e)})});var l={},c=[],f=e_.read(t.numbers,{type:"base64"});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&s$(sK(t.content)).forEach(function(e){c.push(e.id),l[e.id]={deps:[],location:r,type:sG(e.messages[0].meta[1][0].data)}})}),c.sort(function(e,t){return e-t});var h=c.filter(function(e){return e>1}).map(function(e){return[e,sW(e)]});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&s$(sK(t.content)).forEach(function(e){e.messages.forEach(function(t){h.forEach(function(t){e.messages.some(function(e){return 11006!=sG(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}(e.data,t[1])})&&l[t[0]].deps.push(e.id)})})})});for(var u=e_.find(f,l[1].location),d=s$(sK(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(r=m)}var g=sq(sV(r.messages[0].data)[1][0].data);for(p=0,d=s$(sK((u=e_.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=sq(sV(r.messages[0].data)[2][0].data),d=s$(sK((u=e_.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=sq(sV(r.messages[0].data)[2][0].data),d=s$(sK((u=e_.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);var v=sV(r.messages[0].data);v[6][0].data=sW(n.e.r+1),v[7][0].data=sW(n.e.c+1);for(var T=sq(v[46][0].data),b=e_.find(f,l[T].location),w=s$(sK(b.content)),E=0;E<w.length&&w[E].id!=T;++E);if(w[E].id!=T)throw"Bad ColumnRowUIDMapArchive";var S=sV(w[E].messages[0].data);S[1]=[],S[2]=[],S[3]=[];for(var _=0;_<=n.e.c;++_){var A=[];A[1]=A[2]=[{type:0,data:sW(_+420690)}],S[1].push({type:2,data:sz(A)}),S[2].push({type:0,data:sW(_)}),S[3].push({type:0,data:sW(_)})}S[4]=[],S[5]=[],S[6]=[];for(var y=0;y<=n.e.r;++y)(A=[])[1]=A[2]=[{type:0,data:sW(y+726270)}],S[4].push({type:2,data:sz(A)}),S[5].push({type:0,data:sW(y)}),S[6].push({type:0,data:sW(y)});w[E].messages[0].data=sz(S),b.content=sX(sY(w)),b.size=b.content.length,delete v[46];var x=sV(v[4][0].data);x[7][0].data=sW(n.e.r+1);var O=sq(sV(x[1][0].data)[2][0].data);if((w=s$(sK((b=e_.find(f,l[O].location)).content)))[0].id!=O)throw"Bad HeaderStorageBucket";var k=sV(w[0].messages[0].data);for(y=0;y<i.length;++y){var C=sV(k[2][0].data);C[1][0].data=sW(y),C[4][0].data=sW(i[y].length),k[2][y]={type:k[2][0].type,data:sz(C)}}w[0].messages[0].data=sz(k),b.content=sX(sY(w)),b.size=b.content.length;var I=sq(x[2][0].data);if((w=s$(sK((b=e_.find(f,l[I].location)).content)))[0].id!=I)throw"Bad HeaderStorageBucket";for(_=0,k=sV(w[0].messages[0].data);_<=n.e.c;++_)(C=sV(k[2][0].data))[1][0].data=sW(_),C[4][0].data=sW(n.e.r+1),k[2][_]={type:k[2][0].type,data:sz(C)};w[0].messages[0].data=sz(k),b.content=sX(sY(w)),b.size=b.content.length;var N=sq(x[4][0].data);!function(){for(var e,t=e_.find(f,l[N].location),r=s$(sK(t.content)),a=0;a<r.length;++a){var n=r[a];n.id==N&&(e=n)}var s=sV(e.messages[0].data);s[3]=[];var i=[];o.forEach(function(e,t){i[1]=[{type:0,data:sW(t)}],i[2]=[{type:0,data:sW(1)}],i[3]=[{type:2,data:"undefined"!=typeof TextEncoder?new TextEncoder().encode(e):R(to(e))}],s[3].push({type:2,data:sz(i)})}),e.messages[0].data=sz(s);var c=sX(sY(r));t.content=c,t.size=t.content.length}();var D=sV(x[3][0].data),P=D[1][0];delete D[2];var L=sV(P.data),F=sq(L[2][0].data);(function(){for(var e,t=e_.find(f,l[F].location),r=s$(sK(t.content)),a=0;a<r.length;++a){var s=r[a];s.id==F&&(e=s)}var c=sV(e.messages[0].data);delete c[6],delete D[7];var h=new Uint8Array(c[5][0].data);c[5]=[];for(var u=0,d=0;d<=n.e.r;++d){var p=sV(h);u+=function(e,t,r){if(!(null==(a=e[6])?void 0:a[0])||!(null==(n=e[7])?void 0:n[0]))throw"Mutation only works on post-BNC storages!";if((null==(i=null==(s=e[8])?void 0:s[0])?void 0:i.data)&&sG(e[8][0].data)>0)throw"Math only works with normal offsets";for(var a,n,s,i,o,l,c=0,f=sF(e[7][0].data),h=0,u=[],d=sF(e[4][0].data),p=0,m=[],g=0;g<t.length;++g){if(null==t[g]){f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535);continue}switch(f.setUint16(2*g,h,!0),d.setUint16(2*g,p,!0),typeof t[g]){case"string":o=sJ({t:"s",v:t[g]},r),l=sZ({t:"s",v:t[g]},r);break;case"number":o=sJ({t:"n",v:t[g]},r),l=sZ({t:"n",v:t[g]},r);break;case"boolean":o=sJ({t:"b",v:t[g]},r),l=sZ({t:"b",v:t[g]},r);break;default:throw Error("Unsupported value "+t[g])}u.push(o),h+=o.length,m.push(l),p+=l.length,++c}for(e[2][0].data=sW(c);g<e[7][0].data.length/2;++g)f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535,!0);return e[6][0].data=sU(u),e[3][0].data=sU(m),c}(p,i[d],o),p[1][0].data=sW(d),c[5].push({data:sz(p),type:2})}c[1]=[{type:0,data:sW(n.e.c+1)}],c[2]=[{type:0,data:sW(n.e.r+1)}],c[3]=[{type:0,data:sW(u)}],c[4]=[{type:0,data:sW(n.e.r+1)}],e.messages[0].data=sz(c);var m=sX(sY(r));t.content=m,t.size=t.content.length})(),P.data=sz(L),x[3][0].data=sz(D),v[4][0].data=sz(x),r.messages[0].data=sz(v);var M=sX(sY(d));return u.content=M,u.size=u.content.length,f}(t,n):"xlsb"==n.bookType?function(e,t){nx=1024,e&&!e.SSF&&(e.SSF=eH(z)),e&&e.SSF&&(ew(),eb(e.SSF),t.revssf=ek(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,n7?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,a,n,s,i,o,l,c="xlsb"==t.bookType?"bin":"xml",h=nC.indexOf(t.bookType)>-1,u=rW();s2(t=t||{});var d=eZ(),p="",m=0;if(t.cellXfs=[],sr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eJ(d,p="docProps/core.xml",rZ(e.Props,t)),u.coreprops.push(p),r$(t.rels,2,p,rV.CORE_PROPS),p="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var g=[],v=0;v<e.SheetNames.length;++v)2!=(e.Workbook.Sheets[v]||{}).Hidden&&g.push(e.SheetNames[v]);e.Props.SheetNames=g}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,eJ(d,p,r1(e.Props,t)),u.extprops.push(p),r$(t.rels,3,p,rV.EXT_PROPS),e.Custprops!==e.Props&&ey(e.Custprops||{}).length>0&&(eJ(d,p="docProps/custom.xml",r0(e.Custprops,t)),u.custprops.push(p),r$(t.rels,4,p,rV.CUST_PROPS)),m=1;m<=e.SheetNames.length;++m){var T={"!id":{}},b=e.Sheets[e.SheetNames[m-1]];if((b||{})["!type"],eJ(d,p="xl/worksheets/sheet"+m+"."+c,(w=m-1,E=p,S=t,(".bin"===E.slice(-4)?function(e,t,r,a){var n,s,i,o,l,c=t4(),f=r.SheetNames[e],h=r.Sheets[f]||{},u=f;try{r&&r.Workbook&&(u=r.Workbook.Sheets[e].CodeName||u)}catch(e){}var d=ro(h["!ref"]||"A1");if(d.e.c>16383||d.e.r>1048575){if(t.WTF)throw Error("Range "+(h["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");d.e.c=Math.min(d.e.c,16383),d.e.r=Math.min(d.e.c,1048575)}return h["!links"]=[],h["!comments"]=[],t3(c,129),(r.vbaraw||h["!outline"])&&t3(c,147,function(e,t,r){null==r&&(r=t2(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return rC({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),rm(e,r),r.slice(0,r.l)}(u,h["!outline"])),t3(c,148,rx(d)),n=r.Workbook,t3(c,133),t3(c,137,(null==s&&(s=t2(30)),i=924,(((n||{}).Views||[])[0]||{}).RTL&&(i|=32),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(1,0),s.write_shift(1,0),s.write_shift(2,0),s.write_shift(2,100),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s)),t3(c,138),t3(c,134),h&&h["!cols"]&&(t3(c,390),h["!cols"].forEach(function(e,t){if(e){var r,a,n;t3(c,60,(null==r&&(r=t2(18)),a=se(t,e),r.write_shift(-4,t),r.write_shift(-4,t),r.write_shift(4,256*(a.width||10)),r.write_shift(4,0),n=0,e.hidden&&(n|=1),"number"==typeof a.width&&(n|=2),e.level&&(n|=e.level<<8),r.write_shift(2,n),r))}}),t3(c,391)),function(e,t,r,a){var n,s=ro(t["!ref"]||"A1"),i="",o=[];t3(e,145);var l=Array.isArray(t),c=s.e.r;t["!rows"]&&(c=Math.max(s.e.r,t["!rows"].length-1));for(var f=s.s.r;f<=c;++f){i=re(f),function(e,t,r,a){var n=function(e,t,r){var a=t2(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=20*nl(n.hpx):n.hpt&&(s=20*n.hpt),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,l=a.l;a.l+=4;for(var c={r:e,c:0},f=0;f<16;++f)if(!(t.s.c>f+1<<10)&&!(t.e.c<f<<10)){for(var h=-1,u=-1,d=f<<10;d<f+1<<10;++d)c.c=d,(Array.isArray(r)?(r[c.r]||[])[c.c]:r[rn(c)])&&(h<0&&(h=d),u=d);h<0||(++o,a.write_shift(4,h),a.write_shift(4,u))}var p=a.l;return a.l=l,a.write_shift(4,o),a.l=p,a.length>a.l?a.slice(0,a.l):a}(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&t3(e,0,n)}(e,t,s,f);var h=!1;if(f<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){f===s.s.r&&(o[u]=rr(u)),n=o[u]+i;var d=l?(t[f]||[])[u]:t[n];if(!d){h=!1;continue}h=function(e,t,r,a,n,s,i){if(void 0===t.v)return!1;var o,l,c,f,h,u,d,p,m,g,v,T,b,w,E,S,_,A,y,x,O,k,C,R,I="";switch(t.t){case"b":I=t.v?"1":"0";break;case"d":(t=eH(t)).z=t.z||z[14],t.v=eR(eU(t.v)),t.t="n";break;case"n":case"e":I=""+t.v;break;default:I=t.v}var N={r:r,c:a};switch(N.s=sr(n.cellXfs,t,n),t.l&&s["!links"].push([rn(N),t.l]),t.c&&s["!comments"].push([rn(N),t.c]),t.t){case"s":case"str":return n.bookSST?(I=n9(n.Strings,t.v,n.revStrings),N.t="s",N.v=I,i)?t3(e,18,(null==o&&(o=t2(8)),rw(N,o),o.write_shift(4,N.v),o)):t3(e,7,(null==l&&(l=t2(12)),rT(N,l),l.write_shift(4,N.v),l)):(N.t="str",i)?t3(e,17,(c=t,null==f&&(f=t2(8+4*c.v.length)),rw(N,f),rm(c.v,f),f.length>f.l?f.slice(0,f.l):f)):t3(e,6,(h=t,null==u&&(u=t2(12+4*h.v.length)),rT(N,u),rm(h.v,u),u.length>u.l?u.slice(0,u.l):u)),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?i?t3(e,13,(d=t,null==p&&(p=t2(8)),rw(N,p),rA(d.v,p),p)):t3(e,2,(m=t,null==g&&(g=t2(12)),rT(N,g),rA(m.v,g),g)):i?t3(e,16,(v=t,null==T&&(T=t2(12)),rw(N,T),rk(v.v,T),T)):t3(e,5,(b=t,null==w&&(w=t2(16)),rT(N,w),rk(b.v,w),w)),!0;case"b":return(N.t="b",i)?t3(e,15,(E=t,null==S&&(S=t2(5)),rw(N,S),S.write_shift(1,+!!E.v),S)):t3(e,4,(_=t,null==A&&(A=t2(9)),rT(N,A),A.write_shift(1,+!!_.v),A)),!0;case"e":return(N.t="e",i)?t3(e,14,(y=t,null==x&&(x=t2(8)),rw(N,x),x.write_shift(1,y.v),x.write_shift(2,0),x.write_shift(1,0),x)):t3(e,3,(O=t,null==k&&(k=t2(9)),rT(N,k),k.write_shift(1,O.v),k)),!0}return i?t3(e,12,(null==C&&(C=t2(4)),rw(N,C))):t3(e,1,(null==R&&(R=t2(8)),rT(N,R))),!0}(e,d,f,u,a,t,h)}}t3(e,146)}(c,h,0,t,r),function(e,t){if(t["!protect"]){var r,a;t3(e,535,(r=t["!protect"],null==a&&(a=t2(66)),a.write_shift(2,r.password?a6(r.password):0),a.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(e){e[1]?a.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):a.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)}),a))}}(c,h),function(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],s="string"==typeof n.ref?n.ref:ri(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=rs(s);o.s.r==o.e.r&&(o.e.r=rs(t["!ref"]).e.r,s=ri(o));for(var l=0;l<i.length;++l){var c=i[l];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+s;break}}l==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+s}),t3(e,161,rx(ro(s))),t3(e,162)}}(c,h,r,e),function(e,t){if(t&&t["!merges"]){var r,a;t3(e,177,(r=t["!merges"].length,null==a&&(a=t2(4)),a.write_shift(4,r),a)),t["!merges"].forEach(function(t){t3(e,176,rx(t))}),t3(e,178)}}(c,h),h["!links"].forEach(function(e){if(e[1].Target){var t,r,n=r$(a,-1,e[1].Target.replace(/#.*$/,""),rV.HLINK);t3(c,494,(t=t2(50+4*(e[1].Target.length+(e[1].Tooltip||"").length)),rx({s:ra(e[0]),e:ra(e[0])},t),rS("rId"+n,t),rm((-1==(r=e[1].Target.indexOf("#"))?"":e[1].Target.slice(r+1))||"",t),rm(e[1].Tooltip||"",t),rm("",t),t.slice(0,t.l)))}}),delete h["!links"],h["!margins"]&&t3(c,476,(o=h["!margins"],null==l&&(l=t2(48)),st(o),si.forEach(function(e){rk(o[e],l)}),l)),(!t||t.ignoreEC||void 0==t.ignoreEC)&&function(e,t){if(t&&t["!ref"]){var r,a;t3(e,648),t3(e,649,(r=ro(t["!ref"]),(a=t2(24)).write_shift(4,4),a.write_shift(4,1),rx(r,a),a)),t3(e,650)}}(c,h),function(e,t,r,a){if(t["!comments"].length>0){var n=r$(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",rV.VML);t3(e,551,rS("rId"+n)),t["!legacy"]=n}}(c,h,e,a),t3(c,130),c.end()}:ss)(w,S,e,T))),u.sheets.push(p),r$(t.wbrels,-1,"worksheets/sheet"+m+"."+c,rV.WS[0]),b){var w,E,S,_,A,y=b["!comments"],x=!1,O="";y&&y.length>0&&(eJ(d,O="xl/comments"+m+"."+c,(_=O,A=t,(".bin"===_.slice(-4)?function(e){var t=t4(),r=[];return t3(t,628),t3(t,630),e.forEach(function(e){e[1].forEach(function(e){!(r.indexOf(e.a)>-1)&&(r.push(e.a.slice(0,54)),t3(t,632,rm(e.a.slice(0,54))))})}),t3(t,631),t3(t,633),e.forEach(function(e){e[1].forEach(function(a){var n,s,i,o,l,c;a.iauthor=r.indexOf(a.a),t3(t,635,(n=[{s:ra(e[0]),e:ra(e[0])},a],null==s&&(s=t2(36)),s.write_shift(4,n[1].iauthor),rx(n[0],s),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s)),a.t&&a.t.length>0&&t3(t,637,(o=!1,null==i&&(o=!0,i=t2(23+4*a.t.length)),i.write_shift(1,1),rm(a.t,i),i.write_shift(4,1),l={ich:0,ifnt:0},(c=i)||(c=t2(4)),c.write_shift(2,l.ich||0),c.write_shift(2,l.ifnt||0),o?i.slice(0,i.l):i)),t3(t,636),delete a.iauthor})}),t3(t,634),t3(t,629),t.end()}:nk)(y,A))),u.comments.push(O),r$(T,-1,"../comments"+m+"."+c,rV.CMNT),x=!0),b["!legacy"]&&x&&eJ(d,"xl/drawings/vmlDrawing"+m+".vml",nO(m,b["!comments"])),delete b["!comments"],delete b["!legacy"]}T["!id"].rId1&&eJ(d,rz(p),rj(T))}return null!=t.Strings&&t.Strings.length>0&&(eJ(d,p="xl/sharedStrings."+c,(r=t.Strings,a=p,n=t,(".bin"===a.slice(-4)?function(e){var t,r=t4();t3(r,159,(t||(t=t2(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t));for(var a=0;a<e.length;++a)t3(r,19,a4(e[a]));return t3(r,160),r.end()}:a2)(r,n))),u.strs.push(p),r$(t.wbrels,-1,"sharedStrings."+c,rV.SST)),eJ(d,p="xl/workbook."+c,(s=p,i=t,(".bin"===s.slice(-4)?function(e,t){var r,a,n,s=t4();return t3(s,131),t3(s,128,function(e,t){t||(t=t2(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return rm("SheetJS",t),rm(f.version,t),rm(f.version,t),rm("7262",t),t.length>t.l?t.slice(0,t.l):t}()),t3(s,153,(r=e.Workbook&&e.Workbook.WBProps||null,a||(a=t2(72)),n=0,r&&r.filterPrivacy&&(n|=8),a.write_shift(4,n),a.write_shift(4,0),rm(r&&r.CodeName||"ThisWorkbook",a),a.slice(0,a.l))),function(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,a,n=t.Workbook.Sheets,s=0,i=-1,o=-1;s<n.length;++s)n[s]&&(n[s].Hidden||-1!=i)?1==n[s].Hidden&&-1==o&&(o=s):i=s;!(o>i)&&(t3(e,135),t3(e,158,(r=i,a||(a=t2(29)),a.write_shift(-4,0),a.write_shift(-4,460),a.write_shift(4,28800),a.write_shift(4,17600),a.write_shift(4,500),a.write_shift(4,r),a.write_shift(4,r),a.write_shift(1,120),a.length>a.l?a.slice(0,a.l):a)),t3(e,136))}}(s,e,t),function(e,t){t3(e,143);for(var r=0;r!=t.SheetNames.length;++r){var a={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},n=void 0;t3(e,156,(n||(n=t2(127)),n.write_shift(4,a.Hidden),n.write_shift(4,a.iTabID),rS(a.strRelID,n),rm(a.name.slice(0,31),n),n.length>n.l?n.slice(0,n.l):n))}t3(e,144)}(s,e,t),t3(s,132),s.end()}:su)(e,i))),u.workbooks.push(p),r$(t.rels,1,p,rV.WB),eJ(d,p="xl/theme/theme1.xml",nA(e.Themes,t)),u.themes.push(p),r$(t.wbrels,-1,"theme/theme1.xml",rV.THEME),eJ(d,p="xl/styles."+c,(o=p,l=t,(".bin"===o.slice(-4)?function(e,t){var r,a,n,s,i,o,l=t4();return t3(l,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var a=e[0];a<=e[1];++a)null!=t[a]&&++r}),0!=r&&(t3(e,615,rd(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var a=r[0];a<=r[1];++a)null!=t[a]&&t3(e,44,function(e,t,r){r||(r=t2(6+4*t.length)),r.write_shift(2,e),rm(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),a}(a,t[a]))}),t3(e,616))}}(l,e.SSF),function(e){var t,r,a,n,s,i;t3(e,611,rd(1)),t3(e,43,(t={sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"},r||(r=t2(153)),r.write_shift(2,20*t.sz),(a=r)||(a=t2(2)),n=2*!!t.italic|8*!!t.strike|16*!!t.outline|32*!!t.shadow|64*!!t.condense|128*!!t.extend,a.write_shift(1,n),a.write_shift(1,0),r.write_shift(2,t.bold?700:400),s=0,"superscript"==t.vertAlign?s=1:"subscript"==t.vertAlign&&(s=2),r.write_shift(2,s),r.write_shift(1,t.underline||0),r.write_shift(1,t.family||0),r.write_shift(1,t.charset||0),r.write_shift(1,0),rC(t.color,r),i=0,"major"==t.scheme&&(i=1),"minor"==t.scheme&&(i=2),r.write_shift(1,i),rm(t.name,r),r.length>r.l?r.slice(0,r.l):r)),t3(e,612)}(l,e),t3(l,603,rd(2)),t3(l,45,nd({patternType:"none"})),t3(l,45,nd({patternType:"gray125"})),t3(l,604),function(e){var t;t3(e,613,rd(1)),t3(e,46,(t||(t=t2(51)),t.write_shift(1,0),nm(null,t),nm(null,t),nm(null,t),nm(null,t),nm(null,t),t.length>t.l?t.slice(0,t.l):t)),t3(e,614)}(l,e),t3(l,626,rd(1)),t3(l,47,np({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),t3(l,627),t3(l,617,rd((r=t.cellXfs).length)),r.forEach(function(e){t3(l,47,np(e,0))}),t3(l,618),t3(l,619,rd(1)),t3(l,48,(a={xfId:0,builtinId:0,name:"Normal"},n||(n=t2(52)),n.write_shift(4,a.xfId),n.write_shift(2,1),n.write_shift(1,+a.builtinId),n.write_shift(1,0),rS(a.name||"",n),n.length>n.l?n.slice(0,n.l):n)),t3(l,620),t3(l,505,rd(0)),t3(l,506),t3(l,508,(s="TableStyleMedium9",i="PivotStyleMedium4",(o=t2(2052)).write_shift(4,0),rS(s,o),rS(i,o),o.length>o.l?o.slice(0,o.l):o)),t3(l,509),t3(l,279),l.end()}:nh)(e,l))),u.styles.push(p),r$(t.wbrels,-1,"styles."+c,rV.STY),e.vbaraw&&h&&(eJ(d,p="xl/vbaProject.bin",e.vbaraw),u.vba.push(p),r$(t.wbrels,-1,"vbaProject.bin",rV.VBA)),eJ(d,p="xl/metadata."+c,(".bin"===p.slice(-4)?function(){var e,t,r,a,n,s=t4();return t3(s,332),t3(s,334,rd(1)),t3(s,335,((t=t2(12+2*(e={name:"XLDAPR",version:12e4,flags:0xd06ac0b0}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),rm(e.name,t),t.slice(0,t.l))),t3(s,336),t3(s,339,((a=t2(8+2*(r="XLDAPR").length)).write_shift(4,1),rm(r,a),a.slice(0,a.l))),t3(s,52),t3(s,35,rd(514)),t3(s,4096,rd(0)),t3(s,4097,ah(1)),t3(s,36),t3(s,53),t3(s,340),t3(s,337,((n=t2(8)).write_shift(4,1),n.write_shift(4,1),n)),t3(s,51,function(e){var t=t2(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),t3(s,338),t3(s,333),s.end()}:ny)()),u.metadata.push(p),r$(t.wbrels,-1,"metadata."+c,rV.XLMETA),eJ(d,"[Content_Types].xml",rG(u,t)),eJ(d,"_rels/.rels",rj(t.rels)),eJ(d,"xl/_rels/workbook."+c+".rels",rj(t.wbrels)),delete t.revssf,delete t.ssf,d}(t,n):function(e,t){nx=1024,e&&!e.SSF&&(e.SSF=eH(z)),e&&e.SSF&&(ew(),eb(e.SSF),t.revssf=ek(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,n7?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,a=nC.indexOf(t.bookType)>-1,n=rW();s2(t=t||{});var s=eZ(),i="",o=0;if(t.cellXfs=[],sr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eJ(s,i="docProps/core.xml",rZ(e.Props,t)),n.coreprops.push(i),r$(t.rels,2,i,rV.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&l.push(e.SheetNames[c]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,eJ(s,i,r1(e.Props,t)),n.extprops.push(i),r$(t.rels,3,i,rV.EXT_PROPS),e.Custprops!==e.Props&&ey(e.Custprops||{}).length>0&&(eJ(s,i="docProps/custom.xml",r0(e.Custprops,t)),n.custprops.push(i),r$(t.rels,4,i,rV.CUST_PROPS));var f=["SheetJ5"];for(o=1,t.tcid=0;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];if((u||{})["!type"],eJ(s,i="xl/worksheets/sheet"+o+".xml",ss(o-1,t,e,h)),n.sheets.push(i),r$(t.wbrels,-1,"worksheets/sheet"+o+".xml",rV.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach(function(e){e[1].forEach(function(e){!0==e.T&&(g=!0)})}),g&&(eJ(s,m="xl/threadedComments/threadedComment"+o+".xml",function(e,t,r){var a=[eq,tm("ThreadedComments",null,{xmlns:tb.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(e){var n="";(e[1]||[]).forEach(function(s,i){if(!s.T){delete s.ID;return}s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==i?n=o.id:o.parentId=n,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(tm("threadedComment",td("text",s.t||""),o))})}),a.push("</ThreadedComments>"),a.join("")}(d,f,t)),n.threadedcomments.push(m),r$(h,-1,"../threadedComments/threadedComment"+o+".xml",rV.TCMNT)),eJ(s,m="xl/comments"+o+".xml",nk(d,t)),n.comments.push(m),r$(h,-1,"../comments"+o+".xml",rV.CMNT),p=!0}u["!legacy"]&&p&&eJ(s,"xl/drawings/vmlDrawing"+o+".vml",nO(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&eJ(s,rz(i),rj(h))}return null!=t.Strings&&t.Strings.length>0&&(eJ(s,i="xl/sharedStrings.xml",a2(t.Strings,t)),n.strs.push(i),r$(t.wbrels,-1,"sharedStrings.xml",rV.SST)),eJ(s,i="xl/workbook.xml",su(e,t)),n.workbooks.push(i),r$(t.rels,1,i,rV.WB),eJ(s,i="xl/theme/theme1.xml",nA(e.Themes,t)),n.themes.push(i),r$(t.wbrels,-1,"theme/theme1.xml",rV.THEME),eJ(s,i="xl/styles.xml",nh(e,t)),n.styles.push(i),r$(t.wbrels,-1,"styles.xml",rV.STY),e.vbaraw&&a&&(eJ(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),r$(t.wbrels,-1,"vbaProject.bin",rV.VBA)),eJ(s,i="xl/metadata.xml",ny()),n.metadata.push(i),r$(t.wbrels,-1,"metadata.xml",rV.XLMETA),f.length>1&&(eJ(s,i="xl/persons/person.xml",(r=[eq,tm("personList",null,{xmlns:tb.TCMNT,"xmlns:x":"http://schemas.openxmlformats.org/spreadsheetml/2006/main"}).replace(/[\/]>/,">")],f.forEach(function(e,t){r.push(tm("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))}),r.push("</personList>"),r.join(""))),n.people.push(i),r$(t.wbrels,-1,"persons/person.xml",rV.PEOPLE)),eJ(s,"[Content_Types].xml",rG(n,t)),eJ(s,"_rels/.rels",rj(t.rels)),eJ(s,"xl/_rels/workbook.xml.rels",rj(t.wbrels)),delete t.revssf,delete t.ssf,s}(t,n),n);default:throw Error("Unrecognized bookType |"+s.bookType+"|")}}(e,a)}function s8(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,o="",l={s:{r:0,c:0},e:{r:0,c:0}},c=t||{},f=null!=c.range?c.range:e["!ref"];switch(1===c.header?a=1:"A"===c.header?a=2:Array.isArray(c.header)?a=3:null==c.header&&(a=0),typeof f){case"string":l=ro(f);break;case"number":(l=ro(e["!ref"])).s.r=f;break;default:l=f}a>0&&(n=0);var h=re(l.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=l.s.r,T=0,b={};g&&!e[v]&&(e[v]=[]);var w=c.skipHidden&&e["!cols"]||[],E=c.skipHidden&&e["!rows"]||[];for(T=l.s.c;T<=l.e.c;++T)if(!(w[T]||{}).hidden)switch(u[T]=rr(T),r=g?e[v][T]:e[u[T]+h],a){case 1:s[T]=T-l.s.c;break;case 2:s[T]=u[T];break;case 3:s[T]=c.header[T-l.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=i=rc(r,null,c),m=b[i]||0){do o=i+"_"+m++;while(b[o]);b[i]=m,b[o]=1}else b[i]=1;s[T]=o}for(v=l.s.r+n;v<=l.e.r;++v)if(!(E[v]||{}).hidden){var S=function(e,t,r,a,n,s,i,o){var l=re(r),c=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===n?[]:{};if(1!==n){if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r}if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[a[d]+l];if(void 0===p||void 0===p.t){if(void 0===c)continue;null!=s[d]&&(u[s[d]]=c);continue}var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m){if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==c)u[s[d]]=c;else{if(!f||null!==m)continue;u[s[d]]=null}}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:rc(p,m,o);null!=m&&(h=!1)}}return{row:u,isempty:h}}(e,l,v,u,a,s,g,c);!1!==S.isempty&&(1===a?!1===c.blankrows:!c.blankrows)||(d[p++]=S.row)}return d.length=p,d}var s7=/"/g;function s9(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";var n=ro(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",l=o.charCodeAt(0),c=RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=rr(p));for(var m=0,g=n.s.r;g<=n.e.r;++g)if(!(d[g]||{}).hidden){if(null==(f=function(e,t,r,a,n,s,i,o){for(var l=!0,c=[],f="",h=re(r),u=t.s.c;u<=t.e.c;++u)if(a[u]){var d=o.dense?(e[r]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){l=!1,f=""+(o.rawNumbers&&"n"==d.t?d.v:rc(d,null,o));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||o.forceQuotes){f='"'+f.replace(s7,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(l=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(s7,'""')+'"'));c.push(f)}return!1===o.blankrows&&l?null:c.join(i)}(e,n,g,h,i,l,s,a)))continue;a.strip&&(f=f.replace(c,"")),(f||!1!==a.blankrows)&&r.push((m++?o:"")+f)}return delete a.dense,r.join("")}function ie(e,t){t||(t={}),t.FS="	",t.RS="\n";var r=s9(e,t);if(void 0===n||"string"==t.type)return r;var a=n.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function it(e,t,r){var a,n=r||{},s=+!n.skipHeader,i=e||{},o=0,l=0;if(i&&null!=n.origin){if("number"==typeof n.origin)o=n.origin;else{var c="string"==typeof n.origin?ra(n.origin):n.origin;o=c.r,l=c.c}}var f={s:{c:0,r:0},e:{c:l,r:o+t.length-1+s}};if(i["!ref"]){var h=ro(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+t.length-1+s)}else -1==o&&(o=0,f.e.r=t.length-1+s);var u=n.header||[],d=0;t.forEach(function(e,t){ey(e).forEach(function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var c=e[r],f="z",h="",p=rn({c:l+d,r:o+t+s});a=ir(i,p),!c||"object"!=typeof c||c instanceof Date?("number"==typeof c?f="n":"boolean"==typeof c?f="b":"string"==typeof c?f="s":c instanceof Date?(f="d",n.cellDates||(f="n",c=eR(c)),h=n.dateNF||z[14]):null===c&&n.nullError&&(f="e",c=0),a?(a.t=f,a.v=c,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:c},h&&(a.z=h)):i[p]=c})}),f.e.c=Math.max(f.e.c,l+u.length-1);var p=re(o);if(s)for(d=0;d<u.length;++d)i[rr(d+l)+p]={t:"s",v:u[d]};return i["!ref"]=ri(f),i}function ir(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var a=ra(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return"number"!=typeof t?ir(e,rn(t)):ir(e,rn({r:t,c:r||0}))}function ia(){return{SheetNames:[],Sheets:{}}}function is(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(sh(r),e.SheetNames.indexOf(r)>=0)throw Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function ii(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var io={encode_col:rr,encode_row:re,encode_cell:rn,encode_range:ri,decode_col:rt,decode_row:t9,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:ra,decode_range:rs,format_cell:rc,sheet_add_aoa:rh,sheet_add_json:it,sheet_add_dom:sk,aoa_to_sheet:ru,json_to_sheet:function(e,t){return it(null,e,t)},table_to_sheet:sC,table_to_book:function(e,t){return rf(sC(e,t),t)},sheet_to_csv:s9,sheet_to_txt:ie,sheet_to_json:s8,sheet_to_html:sO,sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=ro(e["!ref"]),i="",o=[],l=[],c=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)o[n]=rr(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=re(f),n=s.s.c;n<=s.e.c;++n)if(r=o[n]+i,t=c?(e[f]||[])[n]:e[r],a="",void 0!==t){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else if("z"==t.t)continue;else if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}l[l.length]=r+"="+a}return l},sheet_to_row_object_array:s8,sheet_get_cell:ir,book_new:ia,book_append_sheet:is,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw Error("Cannot find sheet name |"+t+"|")}throw Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:ii,cell_set_internal_link:function(e,t,r){return ii(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:ro(t),s="string"==typeof t?t:ri(t),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var l=ir(e,i,o);l.t="n",l.F=s,delete l.v,i==n.s.r&&o==n.s.c&&(l.f=r,a&&(l.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};f.version}}]);
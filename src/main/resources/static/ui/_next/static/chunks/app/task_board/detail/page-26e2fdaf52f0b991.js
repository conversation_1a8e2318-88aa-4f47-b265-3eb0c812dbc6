(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[55],{3937:(t,e,n)=>{Promise.resolve().then(n.bind(n,4573))},4573:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>M});var a=n(95155),r=n(95988),o=n(32853),s=n(7250),i=n(69814),l=n(59575),c=n(18123),d=n(55750),u=n(46742),p=n(45100),h=n(72093),x=n(22810),m=n(6457),f=n(9365),g=n(68773),A=n(78444),b=n(79005),j=n(28041),y=n(76046),v=n(12115),k=n(35140),S=n(48921),w=n(47956),C=n(10170),I=n(2796),T=n(68874);let{Title:z}=u.A;function O(t){var e;let{taskId:n}=t,[r,s]=(0,v.useState)({current:1,pageSize:10}),[i,l]=(0,v.useState)(""),[d,u]=(0,v.useState)(!1),[p,m]=(0,v.useState)(-1),{runAsync:f,loading:g}=(0,c.Mp)(),{data:A,runAsync:b}=(0,c.Ru)(),y=A[n],{runAsync:O,loading:E,data:P}=(0,c.c9)(),B=(null==P?void 0:P.success)?P.data.records:[];(0,v.useEffect)(()=>{n&&(O({taskId:n,page:r.current,pageSize:r.pageSize,keyword:i}),b({taskId:n}))},[n,r.pageSize,O,r,b,p]);let M=null!==(e=null==y?void 0:y.total)&&void 0!==e?e:"0",N=Number(null==y?void 0:y.processing)+Number(null==y?void 0:y.success)||"0",R=y&&y.total&&Number(y.total)>0?"".concat((Number(y.success)/Number(y.total)*100).toFixed(1),"%"):"0.0%",F=(0,v.useMemo)(()=>[{title:"员工工号",dataIndex:"jobNumber"},{title:"姓名",dataIndex:"name"},{title:"一级部门",dataIndex:"dept1"},{title:"标准岗位",dataIndex:"standardPost"},{title:"岗位号码",dataIndex:"postNumber"},{title:"个人岗位",dataIndex:"personalPost"},{title:"访谈历史次数",dataIndex:"interviewCount",sorter:!0,sortIcon:k.sortIcon}],[]);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.A,{gutter:[24,16],style:{marginBottom:"24px"},children:[(0,a.jsx)(I.A,{span:6,children:(0,a.jsxs)(S.A,{children:[(0,a.jsx)("span",{children:"任务覆盖总人数"}),(0,a.jsx)(S.Q,{value:M})]})}),(0,a.jsx)(I.A,{span:6,children:(0,a.jsxs)(S.A,{children:[(0,a.jsx)("span",{children:"开启访谈人数"}),(0,a.jsx)(S.Q,{value:String(N)})]})}),(0,a.jsx)(I.A,{span:6,children:(0,a.jsxs)(S.A,{children:[(0,a.jsx)("span",{children:"任务完成率"}),(0,a.jsx)(S.Q,{value:R})]})}),(0,a.jsx)(I.A,{span:6,style:{display:"flex",justifyContent:"flex-end"},children:(0,a.jsx)(o.A,{btnText:"发送钉钉任务提醒",title:"发送钉钉任务提醒",description:"确定要通过钉钉向所有未完成访谈任务的员工发送任务提醒吗?",btnProps:{type:"primary",loading:g},onOk:async()=>{let t=await f(n);return t.success?(u(!0),!0):(j.Ay.error(t.error.message),!1)}})}),d&&(0,a.jsx)(w.A,{title:"提醒发送成功",description:"已通过钉钉向所有未完成访谈任务的员工发送了任务提醒",onCancel:()=>u(!1),onOk:()=>u(!1),okText:"好的",cancelText:null})]}),(0,a.jsx)(C.A,{onChange:t=>l(t.target.value),onPressEnter:()=>m(t=>t+1)}),(0,a.jsx)(h.A,{spinning:E,children:(0,a.jsxs)("div",{style:{marginTop:"24px"},children:[(0,a.jsxs)(z,{level:5,children:["全部人员安排 (",(null==P?void 0:P.success)?P.data.total:0,")"]}),(0,a.jsx)(T.A,{size:"small",rowKey:"id",columns:F,loading:E,dataSource:B,pagination:{...r,total:(null==P?void 0:P.success)?P.data.total:0,showTotal:t=>"共 ".concat(t," 条"),showQuickJumper:!0,showSizeChanger:!1},onChange:t=>{s(e=>({...e,...t}))},bordered:!0,style:{marginTop:10},scroll:{x:1200}})]})})]})}function E(){return(0,a.jsx)("div",{style:{marginTop:24},children:(0,a.jsx)(u.A.Text,{type:"secondary",children:"敬请期待"})})}let{Title:P}=u.A,B=[{key:"assignment",label:"任务人员安排"},{key:"history",label:"访谈执行历史记录"}];function M(){let t=(0,y.useSearchParams)().get("id"),[e,n]=(0,v.useState)("assignment"),{runAsync:u,data:k,loading:S}=(0,c.Mj)(),{runAsync:w,loading:C}=(0,c.rp)(),{runAsync:I,loading:T,data:z}=(0,i.p_)(),{runAsync:M,data:N,loading:R}=(0,l.hU)(),F=(null==k?void 0:k.success)?k.data:{},W=(t=>{switch(t){case"initial":default:return{color:"#FBBC05",label:"未开始"};case"processing":return{color:"#23E4A6",label:"进行中"};case"completed":return{color:"#BEBEBE",label:"已结束"}}})(F.status);return(0,v.useEffect)(()=>{t&&u(t).then(t=>{t.success&&(t.data.folderId&&I(t.data.folderId),t.data.robotId&&M(t.data.robotId),t.data.evalAppIds)})},[M,u,I,t]),(0,a.jsxs)(s.A,{children:[(0,a.jsxs)("div",{style:{marginBottom:32,display:"flex",alignItems:"flex-start",justifyContent:"space-between"},children:[(0,a.jsx)("div",{style:{display:"flex",alignItems:"flex-start"},children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:12},children:[(0,a.jsx)(P,{level:3,style:{margin:0,fontWeight:600},children:F.name}),(0,a.jsx)(p.A,{color:W.color,children:W.label})]}),(0,a.jsx)("div",{style:{color:"#666",fontSize:16,margin:"12px 0 8px 0"},children:F.desc}),(0,a.jsx)(h.A,{spinning:T||R,children:(0,a.jsxs)(x.A,{align:"middle",children:[F.folderId&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{title:"任务组名称",children:(null==z?void 0:z.success)&&z.data.name}),(0,a.jsx)(f.A,{type:"vertical"})]}),F.robotId&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{title:"访谈助手",children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(A.A,{size:40,src:(null==N?void 0:N.success)&&N.data.avatar,icon:(0,a.jsx)(d.A,{})}),(null==N?void 0:N.success)&&N.data.name]})}),(0,a.jsx)(f.A,{type:"vertical"})]}),F.evalAppIds&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{title:"分析助手",children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(A.A,{size:40,icon:(0,a.jsx)(d.A,{})}),"xxxx"]})}),(0,a.jsx)(f.A,{type:"vertical"})]})]})})]})}),(0,a.jsxs)(g.A,{children:[(0,a.jsx)(b.Ay,{children:"复制任务"}),(null==F?void 0:F.status)==="processing"&&(0,a.jsx)(o.A,{btnText:"关闭任务",title:"确定关闭该访谈任务?",btnProps:{loading:C,type:"link"},description:"访谈任务一旦关闭, 无法重新启动",modalProps:{okButtonProps:{loading:C}},onOk:async()=>{let e=await w(t);return e.success?(j.Ay.success("已关闭"),!0):(j.Ay.error(e.error.message),!1)}})]})]}),(0,a.jsx)(r.A,{items:B,activeKey:e,onChange:n}),(0,a.jsxs)(h.A,{spinning:S,children:["assignment"===e&&(0,a.jsx)(O,{taskId:t}),"history"===e&&(0,a.jsx)(E,{})]})]})}},7250:(t,e,n)=>{"use strict";n.d(e,{A:()=>d});var a=n(95155);function r(){return(0,a.jsxs)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M20 39C30.4934 39 39 30.4934 39 20C39 9.50659 30.4934 1 20 1C9.50659 1 1 9.50659 1 20C1 30.4934 9.50659 39 20 39Z",stroke:"black",strokeOpacity:"0.1"}),(0,a.jsx)("path",{d:"M12.2461 20H28.7461",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M17.7461 25.5L12.2461 20L17.7461 14.5",stroke:"black",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}var o=n(59276),s=n(22810),i=n(2796),l=n(76046);let{Content:c}=o.A,d=t=>{let{children:e}=t,n=(0,l.useRouter)();return(0,a.jsx)(c,{children:(0,a.jsxs)(s.A,{wrap:!1,children:[(0,a.jsx)(i.A,{style:{cursor:"pointer",width:60,minWidth:60,maxWidth:60,flex:"0 0 60px"},children:(0,a.jsx)("span",{onClick:()=>{n.back()},children:(0,a.jsx)(r,{})})}),(0,a.jsx)(i.A,{style:{flex:1,minWidth:0,whiteSpace:"nowrap"},children:e})]})})}},9365:(t,e,n)=>{"use strict";n.d(e,{A:()=>x});var a=n(12115),r=n(4617),o=n.n(r),s=n(31049),i=n(5144),l=n(70695),c=n(1086),d=n(56204);let u=t=>{let{componentCls:e,sizePaddingEdgeHorizontal:n,colorSplit:a,lineWidth:r,textPaddingInline:o,orientationMargin:s,verticalMarginInline:c}=t;return{[e]:Object.assign(Object.assign({},(0,l.dF)(t)),{borderBlockStart:"".concat((0,i.zA)(r)," solid ").concat(a),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:c,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,i.zA)(r)," solid ").concat(a)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,i.zA)(t.dividerHorizontalGutterMargin)," 0")},["&-horizontal".concat(e,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,i.zA)(t.dividerHorizontalWithTextGutterMargin)," 0"),color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(a),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,i.zA)(r)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(e,"-with-text-start")]:{"&::before":{width:"calc(".concat(s," * 100%)")},"&::after":{width:"calc(100% - ".concat(s," * 100%)")}},["&-horizontal".concat(e,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(s," * 100%)")},"&::after":{width:"calc(".concat(s," * 100%)")}},["".concat(e,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:o},"&-dashed":{background:"none",borderColor:a,borderStyle:"dashed",borderWidth:"".concat((0,i.zA)(r)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(e,"-dashed")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:a,borderStyle:"dotted",borderWidth:"".concat((0,i.zA)(r)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(e,"-dotted")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(e,"-with-text")]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},["&-horizontal".concat(e,"-with-text-start").concat(e,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(e,"-inner-text")]:{paddingInlineStart:n}},["&-horizontal".concat(e,"-with-text-end").concat(e,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(e,"-inner-text")]:{paddingInlineEnd:n}}})}},p=(0,c.OF)("Divider",t=>[u((0,d.oX)(t,{dividerHorizontalWithTextGutterMargin:t.margin,dividerHorizontalGutterMargin:t.marginLG,sizePaddingEdgeHorizontal:0}))],t=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS}),{unitless:{orientationMargin:!0}});var h=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(t);r<a.length;r++)0>e.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(t,a[r])&&(n[a[r]]=t[a[r]]);return n};let x=t=>{let{getPrefixCls:e,direction:n,className:r,style:i}=(0,s.TP)("divider"),{prefixCls:l,type:c="horizontal",orientation:d="center",orientationMargin:u,className:x,rootClassName:m,children:f,dashed:g,variant:A="solid",plain:b,style:j}=t,y=h(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),v=e("divider",l),[k,S,w]=p(v),C=!!f,I=a.useMemo(()=>"left"===d?"rtl"===n?"end":"start":"right"===d?"rtl"===n?"start":"end":d,[n,d]),T="start"===I&&null!=u,z="end"===I&&null!=u,O=o()(v,r,S,w,"".concat(v,"-").concat(c),{["".concat(v,"-with-text")]:C,["".concat(v,"-with-text-").concat(I)]:C,["".concat(v,"-dashed")]:!!g,["".concat(v,"-").concat(A)]:"solid"!==A,["".concat(v,"-plain")]:!!b,["".concat(v,"-rtl")]:"rtl"===n,["".concat(v,"-no-default-orientation-margin-start")]:T,["".concat(v,"-no-default-orientation-margin-end")]:z},x,m),E=a.useMemo(()=>"number"==typeof u?u:/^\d+$/.test(u)?Number(u):u,[u]);return k(a.createElement("div",Object.assign({className:O,style:Object.assign(Object.assign({},i),j)},y,{role:"separator"}),f&&"vertical"!==c&&a.createElement("span",{className:"".concat(v,"-inner-text"),style:{marginInlineStart:T?E:void 0,marginInlineEnd:z?E:void 0}},f)))}},18123:(t,e,n)=>{"use strict";n.d(e,{AK:()=>l,K:()=>i,Mj:()=>c,Mp:()=>p,RI:()=>o,Ru:()=>f,V8:()=>d,Yn:()=>h,ZY:()=>s,c9:()=>g,rp:()=>u,w4:()=>m});var a=n(69653),r=n(79471);let o=()=>(0,a.A)(r.A.getTaskList,{manual:!0}),s=()=>(0,a.A)(r.A.createTask,{manual:!0}),i=()=>(0,a.A)(r.A.updateTask,{manual:!0}),l=()=>(0,a.A)(r.A.deleteTask,{manual:!0}),c=()=>(0,a.A)(r.A.getTaskDetail,{manual:!0}),d=()=>(0,a.A)(r.A.startTask,{manual:!0}),u=()=>(0,a.A)(r.A.stopTask,{manual:!0}),p=()=>(0,a.A)(r.A.notice,{manual:!0}),h=()=>(0,a.A)(r.A.rerunReport,{manual:!0}),x={busy:"0",failed:"0",initial:"0",notexist:"0",processing:"0",success:"0",total:"0"},m=t=>{var e;let n=(0,a.A)(()=>r.A.getTaskStatusCount({robotId:t}));return{...n,data:(null==n?void 0:null===(e=n.data)||void 0===e?void 0:e.success)?n.data.data:x}},f=()=>{var t;let e=(0,a.A)(r.A.getTaskStatusCount,{manual:!0});return{...e,data:(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.success)?e.data.data:{}}},g=()=>(0,a.A)(r.A.getContactList,{manual:!0})},21382:(t,e,n)=>{"use strict";n.d(e,{A:()=>y});var a=n(95043),r=n(25242),o=n(62195),s=n(12115),i=n(4617),l=n.n(i),c=n(51904),d=n(11679),u=n(31049),p=n(7926),h=n(5590),x=n(25561),m=n(3737),f=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(t);r<a.length;r++)0>e.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(t,a[r])&&(n[a[r]]=t[a[r]]);return n};let g=(0,d.U)(t=>{let{prefixCls:e,className:n,closeIcon:a,closable:r,type:o,title:i,children:d,footer:g}=t,A=f(t,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:b}=s.useContext(u.QO),j=b(),y=e||b("modal"),v=(0,p.A)(j),[k,S,w]=(0,m.Ay)(y,v),C="".concat(y,"-confirm"),I={};return I=o?{closable:null!=r&&r,title:"",footer:"",children:s.createElement(h.k,Object.assign({},t,{prefixCls:y,confirmPrefixCls:C,rootPrefixCls:j,content:d}))}:{closable:null==r||r,title:i,footer:null!==g&&s.createElement(x.w,Object.assign({},t)),children:d},k(s.createElement(c.Z,Object.assign({prefixCls:y,className:l()(S,"".concat(y,"-pure-panel"),o&&C,o&&"".concat(C,"-").concat(o),n,w,v)},A,{closeIcon:(0,x.O)(y,a),closable:r},I)))});var A=n(35585);function b(t){return(0,a.Ay)((0,a.fp)(t))}let j=o.A;j.useModal=A.A,j.info=function(t){return(0,a.Ay)((0,a.$D)(t))},j.success=function(t){return(0,a.Ay)((0,a.Ej)(t))},j.error=function(t){return(0,a.Ay)((0,a.jT)(t))},j.warning=b,j.warn=b,j.confirm=function(t){return(0,a.Ay)((0,a.lr)(t))},j.destroyAll=function(){for(;r.A.length;){let t=r.A.pop();t&&t()}},j.config=a.FB,j._InternalPanelDoNotUseOrYouWillBeFired=g;let y=j},32853:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var a=n(95155),r=n(79005),o=n(12115),s=n(47956);let i=t=>{let{title:e,btnProps:n,btnExtraEvent:i,onOk:l,btnText:c,description:d,children:u,modalProps:p}=t,[h,x]=(0,o.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.Ay,{loading:null==n?void 0:n.loading,onClick:()=>{x(!0),null==i||i()},...n,children:c}),h&&(0,a.jsx)(s.A,{title:e,open:h,onCancel:()=>x(!1),onOk:async()=>{await (null==l?void 0:l())&&x(!1)},description:d,...p,children:u})]})}},47956:(t,e,n)=>{"use strict";n.d(e,{A:()=>u});var a=n(95155),r=n(46742),o=n(21382),s=n(22810),i=n(11432),l=n(68773),c=n(79005);let{Text:d}=r.A,u=t=>{let{open:e,title:n,onOk:r,onCancel:u,description:p,children:h,cancelText:x="取消",okText:m="确定",...f}=t;return(0,a.jsxs)(o.A,{title:n,open:e,onCancel:u,onOk:r,footer:(0,a.jsx)(s.A,{justify:"end",children:(0,a.jsx)(i.Ay,{theme:{components:{Button:{colorPrimary:"#171F2D",colorPrimaryHover:"#303847"}}},children:(0,a.jsxs)(l.A,{children:[x&&(0,a.jsx)(c.Ay,{onClick:u,style:{border:"none"},children:x}),m&&(0,a.jsx)(c.Ay,{type:"primary",onClick:r,children:m})]})})}),...f,children:[p&&(0,a.jsx)(d,{type:"secondary",children:p}),h]})}},48362:(t,e,n)=>{"use strict";n.d(e,{dz:()=>s,jX:()=>i,t3:()=>r,vV:()=>o});var a=n(35594);function r(t){return(0,a.Jt)({url:"/api/folder",data:t})}function o(t){return(0,a.bE)({url:"/api/folder",data:t})}function s(t){return(0,a.Jt)({url:"/api/folder/".concat(t)})}function i(t){return(0,a.bE)({url:"/api/folder/addToFolder",data:t})}},48921:(t,e,n)=>{"use strict";n.d(e,{A:()=>l,Q:()=>c});var a=n(95155),r=n(11432),o=n(71349),s=n(97838),i=n(89801);function l(t){let{children:e,badge:n,cardBgColor:i="#171F2D",...l}=t;return(0,a.jsx)(r.Ay,{theme:{components:{Card:{colorBgContainer:i,colorText:"#fff",bodyPadding:"24px 24px 0 24px"},Badge:{colorText:"#fff"}}},children:(0,a.jsxs)(o.A,{...l,children:[n&&(0,a.jsx)(s.A,{...n}),e]})})}let c=t=>{let{value:e,showPercent:n=!1,style:r}=t;return(0,a.jsx)("div",{children:(0,a.jsx)(i.A,{value:e,groupSeparator:",",valueStyle:{fontSize:"48px",fontWeight:600,fontFamily:"DIN Condensed",color:"#fff",...r},formatter:t=>n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:t}),(0,a.jsx)("span",{style:{fontSize:"24px",fontWeight:400,fontFamily:"PingFang SC"},children:"%"})]}):t})})}},55750:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var a=n(85407),r=n(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var s=n(84021);let i=r.forwardRef(function(t,e){return r.createElement(s.A,(0,a.A)({},t,{ref:e,icon:o}))})},59575:(t,e,n)=>{"use strict";n.d(e,{uw:()=>l,dk:()=>d,hU:()=>u,oD:()=>i,lJ:()=>c});var a=n(69653),r=n(35594),o=n(90603);let s={getRobotList:t=>(0,r.Jt)({url:"/api/robots",data:t?{...o.u,...t}:o.u}),createRobot:t=>(0,r.bE)({url:"/api/robots",data:t}),updateRobot:(t,e)=>(0,r.yJ)({url:"/api/robots/".concat(e),data:t}),getRobotDetail:t=>(0,r.Jt)({url:"/api/robots/".concat(t)}),deleteRobot:t=>(0,r.yH)({url:"/api/robots/".concat(t)})},i=()=>(0,a.A)(s.getRobotList,{manual:!0}),l=()=>(0,a.A)(s.createRobot,{manual:!0}),c=()=>(0,a.A)(s.updateRobot,{manual:!0}),d=()=>(0,a.A)(s.deleteRobot,{manual:!0}),u=()=>(0,a.A)(s.getRobotDetail,{manual:!0})},69814:(t,e,n)=>{"use strict";n.d(e,{Ih:()=>i,p_:()=>s,wh:()=>o});var a=n(69653),r=n(48362);function o(){return(0,a.A)(r.t3,{manual:!0})}function s(){return(0,a.A)(r.dz,{manual:!0})}function i(){return(0,a.A)(r.jX,{manual:!0})}},76046:(t,e,n)=>{"use strict";var a=n(66658);n.o(a,"usePathname")&&n.d(e,{usePathname:function(){return a.usePathname}}),n.o(a,"useRouter")&&n.d(e,{useRouter:function(){return a.useRouter}}),n.o(a,"useSearchParams")&&n.d(e,{useSearchParams:function(){return a.useSearchParams}}),n.o(a,"useServerInsertedHTML")&&n.d(e,{useServerInsertedHTML:function(){return a.useServerInsertedHTML}})},79471:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});var a=n(35594),r=n(90603);let o={getTaskList:t=>{let{contactStatus:e,employeeId:n,...o}=t||{},s={...r.u,...o};return n&&(s.employeeId=n,e&&(s.contactStatus=e)),(null==t?void 0:t.postNumber)&&(s.postNumber=t.postNumber),(0,a.Jt)({url:"/api/tasks",data:s})},createTask:t=>(0,a.bE)({url:"/api/tasks",data:t}),updateTask:(t,e)=>(0,a.yJ)({url:"/api/tasks/".concat(e),data:t}),getTaskDetail:t=>(0,a.Jt)({url:"/api/tasks/".concat(t)}),deleteTask:t=>(0,a.yH)({url:"/api/tasks/".concat(t)}),startTask:t=>(0,a.bE)({url:"/api/tasks/".concat(t,"/start")}),stopTask:t=>(0,a.bE)({url:"/api/tasks/".concat(t,"/stop")}),notice:t=>(0,a.bE)({url:"/api/tasks/".concat(t,"/notice")}),getTaskStatusCount:t=>(0,a.Jt)({url:"/api/contacts/status/count",data:t}),getContactList:t=>(0,a.Jt)({url:"/api/contacts",data:t}),addContact:(t,e)=>(0,a.bE)({url:"/api/contacts/".concat(t,"/save"),data:e}),removeContact:(t,e)=>(0,a.yH)({url:"/api/contacts/".concat(t,"/remove"),data:e}),importContacts:(t,e)=>{let n=new FormData;return n.append("file",e),fetch("/api/contacts/".concat(t,"/import"),{method:"POST",body:n}).then(t=>t.json())},rerunReport:t=>(0,a.bE)({url:"/api/evalapp/repeat/run",data:t})}},90603:(t,e,n)=>{"use strict";n.d(e,{u:()=>a}),n(35594);let a={order:"desc",sort:"updateTime"}}},t=>{var e=e=>t(t.s=e);t.O(0,[838,3740,2211,6222,2602,3520,9907,3288,1509,1349,9786,4439,5585,6933,7663,3840,8874,5799,1126,5140,8441,6587,7358],()=>e(3937)),_N_E=t.O()}]);
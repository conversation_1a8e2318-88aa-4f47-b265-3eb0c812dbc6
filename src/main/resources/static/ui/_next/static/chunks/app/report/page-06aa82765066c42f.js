(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7081],{9365:(t,e,n)=>{"use strict";n.d(e,{A:()=>g});var r=n(12115),o=n(4617),a=n.n(o),i=n(31049),c=n(5144),d=n(70695),l=n(1086),s=n(56204);let h=t=>{let{componentCls:e,sizePaddingEdgeHorizontal:n,colorSplit:r,lineWidth:o,textPaddingInline:a,orientationMargin:i,verticalMarginInline:l}=t;return{[e]:Object.assign(Object.assign({},(0,d.dF)(t)),{borderBlockStart:"".concat((0,c.zA)(o)," solid ").concat(r),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,c.zA)(o)," solid ").concat(r)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,c.zA)(t.dividerHorizontalGutterMargin)," 0")},["&-horizontal".concat(e,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,c.zA)(t.dividerHorizontalWithTextGutterMargin)," 0"),color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(r),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,c.zA)(o)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(e,"-with-text-start")]:{"&::before":{width:"calc(".concat(i," * 100%)")},"&::after":{width:"calc(100% - ".concat(i," * 100%)")}},["&-horizontal".concat(e,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(i," * 100%)")},"&::after":{width:"calc(".concat(i," * 100%)")}},["".concat(e,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:a},"&-dashed":{background:"none",borderColor:r,borderStyle:"dashed",borderWidth:"".concat((0,c.zA)(o)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(e,"-dashed")]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:r,borderStyle:"dotted",borderWidth:"".concat((0,c.zA)(o)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(e,"-dotted")]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(e,"-with-text")]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},["&-horizontal".concat(e,"-with-text-start").concat(e,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(e,"-inner-text")]:{paddingInlineStart:n}},["&-horizontal".concat(e,"-with-text-end").concat(e,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(e,"-inner-text")]:{paddingInlineEnd:n}}})}},b=(0,l.OF)("Divider",t=>[h((0,s.oX)(t,{dividerHorizontalWithTextGutterMargin:t.margin,dividerHorizontalGutterMargin:t.marginLG,sizePaddingEdgeHorizontal:0}))],t=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS}),{unitless:{orientationMargin:!0}});var u=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(t);o<r.length;o++)0>e.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]]);return n};let g=t=>{let{getPrefixCls:e,direction:n,className:o,style:c}=(0,i.TP)("divider"),{prefixCls:d,type:l="horizontal",orientation:s="center",orientationMargin:h,className:g,rootClassName:f,children:m,dashed:p,variant:v="solid",plain:S,style:w}=t,x=u(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),z=e("divider",d),[y,k,I]=b(z),O=!!m,E=r.useMemo(()=>"left"===s?"rtl"===n?"end":"start":"right"===s?"rtl"===n?"start":"end":s,[n,s]),M="start"===E&&null!=h,P="end"===E&&null!=h,B=a()(z,o,k,I,"".concat(z,"-").concat(l),{["".concat(z,"-with-text")]:O,["".concat(z,"-with-text-").concat(E)]:O,["".concat(z,"-dashed")]:!!p,["".concat(z,"-").concat(v)]:"solid"!==v,["".concat(z,"-plain")]:!!S,["".concat(z,"-rtl")]:"rtl"===n,["".concat(z,"-no-default-orientation-margin-start")]:M,["".concat(z,"-no-default-orientation-margin-end")]:P},g,f),A=r.useMemo(()=>"number"==typeof h?h:/^\d+$/.test(h)?Number(h):h,[h]);return y(r.createElement("div",Object.assign({className:B,style:Object.assign(Object.assign({},c),w)},x,{role:"separator"}),m&&"vertical"!==l&&r.createElement("span",{className:"".concat(z,"-inner-text"),style:{marginInlineStart:M?A:void 0,marginInlineEnd:P?A:void 0}},m)))}},25813:(t,e,n)=>{Promise.resolve().then(n.bind(n,35140))},76046:(t,e,n)=>{"use strict";var r=n(66658);n.o(r,"usePathname")&&n.d(e,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(e,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(e,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useServerInsertedHTML")&&n.d(e,{useServerInsertedHTML:function(){return r.useServerInsertedHTML}})}},t=>{var e=e=>t(t.s=e);t.O(0,[838,3740,2211,6222,2602,3520,9907,9786,4439,6933,7663,3840,8874,1126,5140,8441,6587,7358],()=>e(25813)),_N_E=t.O()}]);
export function __vite_legacy_guard(){import.meta.url,import("_").catch((()=>1)),async function*(){}().next()}var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=function(t){return t&&t.Math===Math&&t},r=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof t&&t)||n("object"==typeof t&&t)||function(){return this}()||Function("return this")(),e={},o=function(t){try{return!!t()}catch(n){return!0}},i=!o((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),u=!o((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),c=u,a=Function.prototype.call,f=c?a.bind(a):function(){return a.apply(a,arguments)},l={},s={}.propertyIsEnumerable,p=Object.getOwnPropertyDescriptor,y=p&&!s.call({1:2},1);l.f=y?function(t){var n=p(this,t);return!!n&&n.enumerable}:s;var b,v,g=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},h=u,m=Function.prototype,d=m.call,w=h&&m.bind.bind(d,d),O=h?w:function(t){return function(){return d.apply(t,arguments)}},S=O,j=S({}.toString),P=S("".slice),E=o,T=function(t){return P(j(t),8,-1)},x=Object,M=O("".split),_=E((function(){return!x("z").propertyIsEnumerable(0)}))?function(t){return"String"===T(t)?M(t,""):x(t)}:x,C=function(t){return null==t},F=C,k=TypeError,I=function(t){if(F(t))throw new k("Can't call method on "+t);return t},L=_,z=I,D=function(t){return L(z(t))},N="object"==typeof document&&document.all,A=void 0===N&&void 0!==N?function(t){return"function"==typeof t||t===N}:function(t){return"function"==typeof t},G=A,R=function(t){return"object"==typeof t?null!==t:G(t)},W=r,B=A,U=function(t,n){return arguments.length<2?(r=W[t],B(r)?r:void 0):W[t]&&W[t][n];var r},$=O({}.isPrototypeOf),K=r.navigator,V=K&&K.userAgent,Y=r,q=V?String(V):"",H=Y.process,J=Y.Deno,Q=H&&H.versions||J&&J.version,X=Q&&Q.v8;X&&(v=(b=X.split("."))[0]>0&&b[0]<4?1:+(b[0]+b[1])),!v&&q&&(!(b=q.match(/Edge\/(\d+)/))||b[1]>=74)&&(b=q.match(/Chrome\/(\d+)/))&&(v=+b[1]);var Z=v,tt=o,nt=r.String,rt=!!Object.getOwnPropertySymbols&&!tt((function(){var t=Symbol("symbol detection");return!nt(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Z&&Z<41})),et=rt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,ot=U,it=A,ut=$,ct=Object,at=et?function(t){return"symbol"==typeof t}:function(t){var n=ot("Symbol");return it(n)&&ut(n.prototype,ct(t))},ft=String,lt=A,st=function(t){try{return ft(t)}catch(n){return"Object"}},pt=TypeError,yt=function(t){if(lt(t))return t;throw new pt(st(t)+" is not a function")},bt=C,vt=f,gt=A,ht=R,mt=TypeError,dt={exports:{}},wt=r,Ot=Object.defineProperty,St=function(t,n){try{Ot(wt,t,{value:n,configurable:!0,writable:!0})}catch(r){wt[t]=n}return n},jt=r,Pt=St,Et="__core-js_shared__",Tt=dt.exports=jt[Et]||Pt(Et,{});(Tt.versions||(Tt.versions=[])).push({version:"3.42.0",mode:"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"});var xt=dt.exports,Mt=xt,_t=function(t,n){return Mt[t]||(Mt[t]=n||{})},Ct=I,Ft=Object,kt=function(t){return Ft(Ct(t))},It=O({}.hasOwnProperty),Lt=Object.hasOwn||function(t,n){return It(kt(t),n)},zt=O,Dt=0,Nt=Math.random(),At=zt(1..toString),Gt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+At(++Dt+Nt,36)},Rt=_t,Wt=Lt,Bt=Gt,Ut=rt,$t=et,Kt=r.Symbol,Vt=Rt("wks"),Yt=$t?Kt.for||Kt:Kt&&Kt.withoutSetter||Bt,qt=f,Ht=R,Jt=at,Qt=function(t,n){var r=t[n];return bt(r)?void 0:yt(r)},Xt=function(t,n){var r,e;if("string"===n&&gt(r=t.toString)&&!ht(e=vt(r,t)))return e;if(gt(r=t.valueOf)&&!ht(e=vt(r,t)))return e;if("string"!==n&&gt(r=t.toString)&&!ht(e=vt(r,t)))return e;throw new mt("Can't convert object to primitive value")},Zt=TypeError,tn=function(t){return Wt(Vt,t)||(Vt[t]=Ut&&Wt(Kt,t)?Kt[t]:Yt("Symbol."+t)),Vt[t]}("toPrimitive"),nn=function(t,n){if(!Ht(t)||Jt(t))return t;var r,e=Qt(t,tn);if(e){if(void 0===n&&(n="default"),r=qt(e,t,n),!Ht(r)||Jt(r))return r;throw new Zt("Can't convert object to primitive value")}return void 0===n&&(n="number"),Xt(t,n)},rn=at,en=function(t){var n=nn(t,"string");return rn(n)?n:n+""},on=R,un=r.document,cn=on(un)&&on(un.createElement),an=function(t){return cn?un.createElement(t):{}},fn=!i&&!o((function(){return 7!==Object.defineProperty(an("div"),"a",{get:function(){return 7}}).a})),ln=i,sn=f,pn=l,yn=g,bn=D,vn=en,gn=Lt,hn=fn,mn=Object.getOwnPropertyDescriptor;e.f=ln?mn:function(t,n){if(t=bn(t),n=vn(n),hn)try{return mn(t,n)}catch(r){}if(gn(t,n))return yn(!sn(pn.f,t,n),t[n])};var dn={},wn=i&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),On=R,Sn=String,jn=TypeError,Pn=function(t){if(On(t))return t;throw new jn(Sn(t)+" is not an object")},En=i,Tn=fn,xn=wn,Mn=Pn,_n=en,Cn=TypeError,Fn=Object.defineProperty,kn=Object.getOwnPropertyDescriptor,In="enumerable",Ln="configurable",zn="writable";dn.f=En?xn?function(t,n,r){if(Mn(t),n=_n(n),Mn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&zn in r&&!r[zn]){var e=kn(t,n);e&&e[zn]&&(t[n]=r.value,r={configurable:Ln in r?r[Ln]:e[Ln],enumerable:In in r?r[In]:e[In],writable:!1})}return Fn(t,n,r)}:Fn:function(t,n,r){if(Mn(t),n=_n(n),Mn(r),Tn)try{return Fn(t,n,r)}catch(e){}if("get"in r||"set"in r)throw new Cn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Dn=dn,Nn=g,An=i?function(t,n,r){return Dn.f(t,n,Nn(1,r))}:function(t,n,r){return t[n]=r,t},Gn={exports:{}},Rn=i,Wn=Lt,Bn=Function.prototype,Un=Rn&&Object.getOwnPropertyDescriptor,$n={CONFIGURABLE:Wn(Bn,"name")&&(!Rn||Rn&&Un(Bn,"name").configurable)},Kn=A,Vn=xt,Yn=O(Function.toString);Kn(Vn.inspectSource)||(Vn.inspectSource=function(t){return Yn(t)});var qn,Hn,Jn,Qn=Vn.inspectSource,Xn=A,Zn=r.WeakMap,tr=Xn(Zn)&&/native code/.test(String(Zn)),nr=Gt,rr=_t("keys"),er={},or=tr,ir=r,ur=An,cr=Lt,ar=xt,fr=function(t){return rr[t]||(rr[t]=nr(t))},lr=er,sr="Object already initialized",pr=ir.TypeError,yr=ir.WeakMap;if(or||ar.state){var br=ar.state||(ar.state=new yr);br.get=br.get,br.has=br.has,br.set=br.set,qn=function(t,n){if(br.has(t))throw new pr(sr);return n.facade=t,br.set(t,n),n},Hn=function(t){return br.get(t)||{}},Jn=function(t){return br.has(t)}}else{var vr=fr("state");lr[vr]=!0,qn=function(t,n){if(cr(t,vr))throw new pr(sr);return n.facade=t,ur(t,vr,n),n},Hn=function(t){return cr(t,vr)?t[vr]:{}},Jn=function(t){return cr(t,vr)}}var gr={get:Hn,enforce:function(t){return Jn(t)?Hn(t):qn(t,{})}},hr=O,mr=o,dr=A,wr=Lt,Or=i,Sr=$n.CONFIGURABLE,jr=Qn,Pr=gr.enforce,Er=gr.get,Tr=String,xr=Object.defineProperty,Mr=hr("".slice),_r=hr("".replace),Cr=hr([].join),Fr=Or&&!mr((function(){return 8!==xr((function(){}),"length",{value:8}).length})),kr=String(String).split("String"),Ir=Gn.exports=function(t,n,r){"Symbol("===Mr(Tr(n),0,7)&&(n="["+_r(Tr(n),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!wr(t,"name")||Sr&&t.name!==n)&&(Or?xr(t,"name",{value:n,configurable:!0}):t.name=n),Fr&&r&&wr(r,"arity")&&t.length!==r.arity&&xr(t,"length",{value:r.arity});try{r&&wr(r,"constructor")&&r.constructor?Or&&xr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(o){}var e=Pr(t);return wr(e,"source")||(e.source=Cr(kr,"string"==typeof n?n:"")),t};Function.prototype.toString=Ir((function(){return dr(this)&&Er(this).source||jr(this)}),"toString");var Lr,zr=Gn.exports,Dr=A,Nr=dn,Ar=zr,Gr=St,Rr={},Wr=Math.ceil,Br=Math.floor,Ur=Math.trunc||function(t){var n=+t;return(n>0?Br:Wr)(n)},$r=function(t){var n=+t;return n!=n||0===n?0:Ur(n)},Kr=$r,Vr=Math.max,Yr=Math.min,qr=$r,Hr=Math.min,Jr=function(t){var n=qr(t);return n>0?Hr(n,9007199254740991):0},Qr=D,Xr=function(t,n){var r=Kr(t);return r<0?Vr(r+n,0):Yr(r,n)},Zr=function(t){return Jr(t.length)},te={indexOf:(Lr=!1,function(t,n,r){var e=Qr(t),o=Zr(e);if(0===o)return!Lr&&-1;var i,u=Xr(r,o);if(Lr&&n!=n){for(;o>u;)if((i=e[u++])!=i)return!0}else for(;o>u;u++)if((Lr||u in e)&&e[u]===n)return Lr||u||0;return!Lr&&-1})},ne=Lt,re=D,ee=te.indexOf,oe=er,ie=O([].push),ue=function(t,n){var r,e=re(t),o=0,i=[];for(r in e)!ne(oe,r)&&ne(e,r)&&ie(i,r);for(;n.length>o;)ne(e,r=n[o++])&&(~ee(i,r)||ie(i,r));return i},ce=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Rr.f=Object.getOwnPropertyNames||function(t){return ue(t,ce)};var ae={};ae.f=Object.getOwnPropertySymbols;var fe=U,le=Rr,se=ae,pe=Pn,ye=O([].concat),be=fe("Reflect","ownKeys")||function(t){var n=le.f(pe(t)),r=se.f;return r?ye(n,r(t)):n},ve=Lt,ge=be,he=e,me=dn,de=o,we=A,Oe=/#|\.prototype\./,Se=function(t,n){var r=Pe[je(t)];return r===Te||r!==Ee&&(we(n)?de(n):!!n)},je=Se.normalize=function(t){return String(t).replace(Oe,".").toLowerCase()},Pe=Se.data={},Ee=Se.NATIVE="N",Te=Se.POLYFILL="P",xe=Se,Me=r,_e=e.f,Ce=An,Fe=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Dr(r)&&Ar(r,i,e),e.global)o?t[n]=r:Gr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(u){}o?t[n]=r:Nr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},ke=St,Ie=function(t,n,r){for(var e=ge(n),o=me.f,i=he.f,u=0;u<e.length;u++){var c=e[u];ve(t,c)||r&&ve(r,c)||o(t,c,i(n,c))}},Le=xe,ze=r;(function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Me:f?Me[c]||ke(c,{}):Me[c]&&Me[c].prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=_e(r,e))&&u.value:r[e],!Le(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ie(i,o)}(t.sham||o&&o.sham)&&Ce(i,"sham",!0),Fe(r,e,i,t)}})({global:!0,forced:ze.globalThis!==ze},{globalThis:ze});

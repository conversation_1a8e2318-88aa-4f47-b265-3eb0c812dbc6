<!doctype html>
<html translate="no">

<head>
  <script type="module" crossorigin src="/pgw/aui/assets/polyfills-Cpuu8t9z.js"></script>

  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
  <title>RollingAI</title>
  <script>
    var pageData = {
      uuid: "[(${data.uuid})]",
      bgUrl: "[(${data.bgUrl})]",
      holdTalkTimeout: [[${data.holdTalkTimeout}]],
      showAudioWave: [[${data.showAudioWave}]],
      voiceAgentId: "[(${data.voiceAgentId})]"
    };
  </script>
  <script type="module" crossorigin src="/pgw/aui/assets/index-CZWkYSPK.js"></script>
  <link rel="stylesheet" crossorigin href="/pgw/aui/assets/index-CI2smg0j.css">
  <script type="module">import.meta.url;import("_").catch(()=>1);(async function*(){})().next();if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
  <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
</head>

<body>
  <div id="root"></div>
  <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
  <script nomodule crossorigin id="vite-legacy-polyfill" src="/pgw/aui/assets/polyfills-legacy-3kNEp4ZF.js"></script>
  <script nomodule crossorigin id="vite-legacy-entry" data-src="/pgw/aui/assets/index-legacy-G3YrxhJY.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
</body>

</html>

<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>钉钉网页应用免登录</title>
    <script src="https://unpkg.com/vconsole/dist/vconsole.min.js"></script>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.1.0/dingtalk.open.js"></script>
    <script>
        new window.VConsole();
        var pageData = {
            dingCorpId: "[(${data.dingCorpId})]",
            dingClientId: "[(${data.dingClientId})]",
        };
    </script>
    <script>
        (function () {
            console.log(window.dd);
            var platform = window.dd.env.platform;
            if (platform === "notInDingTalk") {
                alert("请在钉钉中打开");
                return;
            }
            window.dd.getStorage({
                key: "ding_user_info",
                success: (res) => {
                    const {value} = res;
                    const userInfo =  {};
                    if (userInfo.userId) {
                        console.log(userInfo);
                    } else {
                        window.dd.requestAuthCode({
                            corpId: pageData.dingCorpId,
                            clientId: pageData.dingClientId,
                            onSuccess: function (res) {
                                fetch(`/pgw/api/dingtalk/userinfo?code=${res.code}`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({}),
                                }).then(function (res) {
                                    return res.json();
                                }).then(function (res) {
                                    const {success, data} = res;
                                    console.log(data);
                                    if (success) {
                                        window.dd.setStorage({
                                            key: "ding_user_info",
                                            data: JSON.stringify(data),
                                            success: (res) => {
                                                console.log(res);
                                            }
                                        });
                                    }
                                });
                            },
                            onFail: function (err) {
                                console.error(err);
                                alert("获取授权码失败")
                            },
                        });
                    }
                }
            });
        })()
    </script>
</head>
<body>

</body>
</html>
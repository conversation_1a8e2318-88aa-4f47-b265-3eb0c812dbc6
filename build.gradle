plugins {
    id "java"
    id "org.springframework.boot" version "3.3.4"
    id "io.spring.dependency-management" version "1.1.6"
    id "org.sonarqube" version "6.2.0.5505"
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

configurations.all {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
        cacheDynamicVersionsFor 0, 'seconds'
    }
    exclude group: 'commons-logging', module: 'commons-logging'
    exclude group: 'org.slf4j', module: 'slf4j-simple'
}

group = "com.rolling"
version = "latest"

bootJar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    archiveFileName = "app-${project.version}.jar"
}

repositories {
    mavenLocal()
    maven { url "https://maven.aliyun.com/repository/public/" }
    mavenCentral()
}

dependencies {
    implementation "com.volcengine:ve-tos-java-sdk:2.8.8"
    implementation "com.volcengine:volc-sdk-java:1.0.207"
    implementation "com.alibaba:dashscope-sdk-java:2.19.1"
    implementation "com.aliyun.oss:aliyun-sdk-oss:3.17.4"
    implementation "com.aliyun:tea-openapi:0.3.8"
    implementation "xerces:xercesImpl:2.12.2"

    implementation "cn.hutool:hutool-all:5.8.20"
    implementation "com.alibaba:easyexcel:3.1.3"
    implementation "com.alibaba:transmittable-thread-local:2.14.2"
    implementation "cn.dev33:sa-token-spring-boot3-starter:1.44.0"
    implementation "cn.dev33:sa-token-redis-template:1.44.0"
    implementation "com.dtflys.forest:forest-spring-boot3-starter:1.5.36"
    implementation "com.mybatis-flex:mybatis-flex-spring-boot3-starter:1.9.7"

    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-aop"
    implementation "org.springframework.boot:spring-boot-starter-amqp"
    implementation "org.springframework.boot:spring-boot-starter-websocket"
    implementation "org.springframework.boot:spring-boot-starter-thymeleaf"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation "org.springframework.boot:spring-boot-starter-data-redis"
    implementation "com.github.ben-manes.caffeine:caffeine"
    implementation "org.liquibase:liquibase-core"
    implementation "com.zaxxer:HikariCP"
    runtimeOnly 'com.mysql:mysql-connector-j'
    runtimeOnly "net.logstash.logback:logstash-logback-encoder:7.1.1"
    compileOnly "org.projectlombok:lombok"
    annotationProcessor "org.projectlombok:lombok"
    testRuntimeOnly "org.junit.platform:junit-platform-launcher"
    testImplementation "org.springframework.boot:spring-boot-starter-test"
}

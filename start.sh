#! /bin/sh

export S_LOG_DIR=${LOG_DIR}/${SERVICE_NAME}
mkdir -p ${S_LOG_DIR}/

exec java \
  -Dspring.profiles.active=${ENV} \
  ${JAVA_OPTS} \
  ${JVM_OPTS} \
  ${JAVA_AGENT_OPTS} \
  -XX:+ExitOnOutOfMemoryError \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=${S_LOG_DIR}/ \
  -XX:ErrorFile=${S_LOG_DIR}/${SERVICE_NAME}_java_error%p.err \
  -Xlog:gc:${S_LOG_DIR}/${SERVICE_NAME}.gc \
  -jar ${JAR}
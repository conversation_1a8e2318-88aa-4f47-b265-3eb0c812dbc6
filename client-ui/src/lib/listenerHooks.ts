let VERTC: any;
if (process.env.NODE_ENV === 'production') {
  VERTC = window.VERTC; // 生产环境使用全局变量
} else {
  VERTC = require('@volcengine/rtc').default; // 开发环境使用 npm 包
}

import { useRef } from 'react';
import { useDispatch } from 'react-redux';

import {
  addAutoPlayFail,
  IUser,
  remoteUserJoin,
  remoteUserLeave,
  removeAutoPlayFail,
  setCurrentMsg,
  setHistoryMsg,
  updateAITalkState,
  updateLocalUser,
  updateNetworkQuality,
  updateRemoteUser,
} from '@/store/slices/room';
import RtcClient, { IEventListener } from './RtcClient';

import { setMicrophoneList, updateSelectedDevice } from '@/store/slices/device';
import { useMessageHandler } from '@/utils/handler';
import Utils from '@/utils/utils';

enum MediaType {
  AUDIO = 1,
  VIDEO = 2,
  AUDIO_AND_VIDEO = 3,
}

enum StreamIndex {
  STREAM_INDEX_MAIN = 0,
  STREAM_INDEX_SCREEN = 1,
}

// 错误码与用户友好提示映射表
const ERROR_CODE_MAP: Record<string, string> = {
  INVALID_TOKEN: 'Token 无效或已过期，请重新登录。',
  JOIN_ROOM_FAILED: '进房失败，请检查网络或稍后重试。',
  KICKED_OUT: '您已被移出房间。',
  ROOM_DISMISS: '房间已解散。',
  TOKEN_EXPIRED: 'Token 已过期，请重新登录。',
  REPEAT_JOIN: '重复进房操作。',
  ROOM_FORBIDDEN: '房间已被封禁，无法加入。',
  USER_FORBIDDEN: '当前用户已被封禁，无法加入房间。',
  ICE_SERVER_WRONG: 'ICE 节点分配失败，请检查网络。',
  NOT_CONNECTED_YET: '尚未与服务器建立连接，操作失败。',
  DUPLICATE_LOGIN: '您的账号已在其他设备登录，已被踢下线。',
  RECONNECT_FAILED: '与服务器重连失败，请检查网络。',
  UNEXPECTED_ERROR: '发生未知错误，请重试。',
  OPERATION_CANCEL: '操作已取消。',
  NO_PUBLISH_PERMISSION: '无发布权限，请切换至可见后再发布流。',
  PUBLISH_FAILED: '发布流失败，请重试。',
  STREAM_TYPE_NOT_MATCH: '流类型不匹配。',
  STREAM_NOT_EXIST: '目标流不存在。',
  REPEAT_PUSH: '重复发布公共流。',
  REPEAT_CAPTURE: '重复采集操作。',
  GET_AUDIO_TRACK_FAILED: '开启音频采集失败，请检查设备或是否被占用。',
  GET_VIDEO_TRACK_FAILED: '开启视频采集失败，请检查设备或是否被占用。',
  CANT_FIND_DOM: '播放容器不存在。',
  INVALID_PARAMS: '参数错误。',
  INVALID_DEVICE_ID: '设备 ID 无效。',
  NOT_SUPPORTED: '浏览器不支持该操作。',
  AUDIO_DEVICE_TEST_FAILED: '音频设备测试失败，请重试。',
  INVALID_ENGINE: 'Engine 对象无效。',
};

const useRtcListeners = (): IEventListener => {
  const dispatch = useDispatch();
  const { parser } = useMessageHandler();
  const playStatus = useRef<{ [key: string]: { audio: boolean; video: boolean } }>({});

  const debounceSetHistoryMsg = Utils.debounce((text: string, user: string) => {
    const isAudioEnable = RtcClient.getAudioBotEnabled();
    if (isAudioEnable) {
      dispatch(setHistoryMsg({ text, user }));
    }
  }, 600);

  const handleUserJoin = (e: any) => {
    const extraInfo = JSON.parse(e.userInfo.extraInfo || '{}');
    const userId = extraInfo.user_id || e.userInfo.userId;
    const username = extraInfo.user_name || e.userInfo.userId;
    dispatch(
      remoteUserJoin({
        userId,
        username,
      })
    );
  };

  const handleError = (e: { errorCode: string; message?: string }) => {
    const { errorCode, message } = e;
    const userMsg = ERROR_CODE_MAP[errorCode] || message || '发生未知错误，请重试。';
    console.log(userMsg);
    // Message.error(userMsg);
  };

  const handleUserLeave = (e: any) => {
    dispatch(remoteUserLeave(e.userInfo));
    dispatch(removeAutoPlayFail(e.userInfo));
  };

  const handleUserPublishStream = (e: { userId: string; mediaType: MediaType }) => {
    const { userId, mediaType } = e;
    const payload: IUser = { userId };
    if (mediaType === MediaType.AUDIO) {
      /** 暂不需要 */
    }
    payload.publishAudio = true;
    dispatch(updateRemoteUser(payload));
  };

  const handleUserUnpublishStream = (e: { userId: string; mediaType: MediaType; reason: any }) => {
    const { userId, mediaType } = e;

    const payload: IUser = { userId };
    if (mediaType === MediaType.AUDIO) {
      payload.publishAudio = false;
    }

    if (mediaType === MediaType.AUDIO_AND_VIDEO) {
      payload.publishAudio = false;
    }

    dispatch(updateRemoteUser(payload));
  };

  const handleRemoteStreamStats = (e: any) => {
    dispatch(
      updateRemoteUser({
        userId: e.userId,
        audioStats: e.audioStats,
      })
    );
  };

  const handleLocalStreamStats = (e: any) => {
    dispatch(
      updateLocalUser({
        audioStats: e.audioStats,
      })
    );
  };

  const handleLocalAudioPropertiesReport = (e: any[]) => {
    const localAudioInfo = e.find((audioInfo) => audioInfo.streamIndex === StreamIndex.STREAM_INDEX_MAIN);
    if (localAudioInfo) {
      dispatch(
        updateLocalUser({
          audioPropertiesInfo: localAudioInfo.audioPropertiesInfo,
        })
      );
    }
  };

  const handleRemoteAudioPropertiesReport = (e: any[]) => {
    const remoteAudioInfo = e
      .filter((audioInfo) => audioInfo.streamKey.streamIndex === StreamIndex.STREAM_INDEX_MAIN)
      .map((audioInfo) => ({
        userId: audioInfo.streamKey.userId,
        audioPropertiesInfo: audioInfo.audioPropertiesInfo,
      }));

    if (remoteAudioInfo.length) {
      dispatch(updateRemoteUser(remoteAudioInfo));
    }
  };

  const handleAudioDeviceStateChanged = async (device: any) => {
    const devices = await RtcClient.getDevices();

    if (device.mediaDeviceInfo.kind === 'audioinput') {
      let deviceId = device.mediaDeviceInfo.deviceId;
      if (device.deviceState === 'inactive') {
        deviceId = devices.audioInputs?.[0].deviceId || '';
      }
      RtcClient.switchDevice(MediaType.AUDIO, deviceId);
      dispatch(setMicrophoneList(devices.audioInputs));

      dispatch(
        updateSelectedDevice({
          selectedMicrophone: deviceId,
        })
      );
    }
  };

  const handleUserMessageReceived = (e: { userId: string; message: any }) => {
    /** debounce 记录用户输入文字 */
    if (e.message) {
      const msgObj = JSON.parse(e.message || '{}');
      if (msgObj.text) {
        const { text: msg, definite, user_id: user } = msgObj;
        if ((window as any)._debug_mode) {
          dispatch(setHistoryMsg({ msg, user }));
        } else {
          debounceSetHistoryMsg(msg, user);
        }
        dispatch(setCurrentMsg({ msg, definite, user }));
      }
    }
  };

  const handleAutoPlayFail = (event: any) => {
    const { userId, kind } = event;
    let playUser = playStatus.current?.[userId] || {};
    playUser = { ...playUser, [kind]: false };
    playStatus.current[userId] = playUser;

    dispatch(
      addAutoPlayFail({
        userId,
      })
    );
  };

  const addFailUser = (userId: string) => {
    dispatch(addAutoPlayFail({ userId }));
  };

  const playerFail = (params: { type: 'audio' | 'video'; userId: string }) => {
    const { type, userId } = params;
    let playUser = playStatus.current?.[userId] || {};

    playUser = { ...playUser, [type]: false };

    const { audio, video } = playUser;

    if (audio === false || video === false) {
      addFailUser(userId);
    }

    return playUser;
  };

  const handlePlayerEvent = (event: any) => {
    const { userId, rawEvent, type } = event;
    let playUser = playStatus.current?.[userId] || {};

    if (!playStatus.current) return;

    if (rawEvent.type === 'playing') {
      playUser = { ...playUser, [type]: true };
      const { audio, video } = playUser;
      if (audio !== false && video !== false) {
        dispatch(removeAutoPlayFail({ userId }));
      }
    } else if (rawEvent.type === 'pause') {
      playUser = playerFail({ type, userId });
    }

    playStatus.current[userId] = playUser;
  };

  const handleUserStartAudioCapture = (_: { userId: string }) => {
    dispatch(updateAITalkState({ isAITalking: true }));
  };

  const handleUserStopAudioCapture = (_: { userId: string }) => {
    dispatch(updateAITalkState({ isAITalking: false }));
  };

  const handleNetworkQuality = (uplinkNetworkQuality: any, downlinkNetworkQuality: any) => {
    dispatch(
      updateNetworkQuality({
        networkQuality: Math.floor((uplinkNetworkQuality + downlinkNetworkQuality) / 2) as any,
      })
    );
  };

  const handleRoomBinaryMessageReceived = (event: { userId: string; message: ArrayBuffer }) => {
    const { message } = event;
    parser(message);
  };

  return {
    handleError,
    handleUserJoin,
    handleUserLeave,
    handleUserPublishStream,
    handleUserUnpublishStream,
    handleRemoteStreamStats,
    handleLocalStreamStats,
    handleLocalAudioPropertiesReport,
    handleRemoteAudioPropertiesReport,
    handleAudioDeviceStateChanged,
    handleUserMessageReceived,
    handleAutoPlayFail,
    handlePlayerEvent,
    handleUserStartAudioCapture,
    handleUserStopAudioCapture,
    handleRoomBinaryMessageReceived,
    handleNetworkQuality,
  };
};

export default useRtcListeners;

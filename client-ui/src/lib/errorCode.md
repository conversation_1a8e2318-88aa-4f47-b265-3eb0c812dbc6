| 方法名称/事件         | 错误码                           | 描述                                                         | |----------------------|----------------------------------|--------------------------------------------------------------| | joinRoom             | INVALID_TOKEN                     | 调用 joinRoom 进房时使用了已过期的 Token 或 Token 无效。      | | joinRoom             | JOIN_ROOM_FAILED                  | 调用 joinRoom 进房失败，具体错误原因查看 message。           | | joinRoom             | KICKED_OUT                        | 服务端调用 OpenAPI 将当前用户踢出房间。通过 onError 回调。    | | joinRoom             | ROOM_DISMISS                      | 服务端调用 OpenAPI 解散房间，所有用户被移出房间。             | | joinRoom             | TOKEN_EXPIRED                     | 加入房间后，Token 过期。通过 onError 回调。                   | | joinRoom             | REPEAT_JOIN                       | 重复进房。已加入房间后，又再次调用 joinRoom 时触发。           | | joinRoom             | ROOM_FORBIDDEN                    | 调用 joinRoom 进房失败，原因是房间被封禁。                   | | joinRoom             | USER_FORBIDDEN                    | 调用 joinRoom 进房失败，原因是本地用户被封禁。               | | joinRoom             | ICE_SERVER_WRONG                  | 调用 joinRoom 时请求分配 ICE 节点时失败。                    | | joinRoom             | NOT_CONNECTED_YET                 | SDK 尚未与服务器建连，不能进行该操作。                        | | joinRoom             | DUPLICATE_LOGIN                   | 有相同用户 ID 的用户加入本房间，当前用户被踢出房间。           | | joinRoom             | RECONNECT_FAILED                  | SDK 与服务端重连失败，并不再自动重试。                        | | joinRoom             | UNEXPECTED_ERROR                  | 通用错误码。具体错误原因查看 message。                        | | leaveRoom            | OPERATION_CANCEL                  | 发布/订阅等操作执行到一半时离开房间，操作中断，无需处理。      | | publishStream        | NO_PUBLISH_PERMISSION             | 无发布权限。请先将自身切换至可见后再发布流。                  | | publishStream        | PUBLISH_FAILED                    | 调用 publishStream 或 publishScreen 发布流失败。              | | publishStream        | STREAM_TYPE_NOT_MATCH             | 流类型不匹配。                                                | | publishStream        | STREAM_NOT_EXIST                  | 目标流不存在。                                                | | publishStream        | REPEAT_PUSH                       | 重复发布公共流。                                              | | unpublishStream      | STREAM_NOT_EXIST                  | 目标流不存在。                                                | | startAudioCapture    | REPEAT_CAPTURE                    | 重复采集。                                                    | | startAudioCapture    | GET_AUDIO_TRACK_FAILED            | 开启音频采集失败。请确认有可用采集设备，或是否被其他 App 占用。| | startVideoCapture    | REPEAT_CAPTURE                    | 重复采集。                                                    | | startVideoCapture    | GET_VIDEO_TRACK_FAILED            | 开启视频采集失败。请确认有可用采集设备，或是否被其他 App 占用。| | setLocalVideoPlayer  | CANT_FIND_DOM                     | 传入的播放容器不存在。                                        | | setAudioProfile      | INVALID_PARAMS                    | 参数错误。                                                    | | setAudioVolume       | INVALID_PARAMS                    | 参数错误。                                                    | | switchDevice         | INVALID_DEVICE_ID                 | 输入的设备 ID 无效。                                          | | enableDevices        | NOT_SUPPORTED                     | 浏览器不支持设置音频播放设备或测试音频采集/播放设备。          | | enableDevices        | AUDIO_DEVICE_TEST_FAILED          | 开启音频设备测试失败，请重试。                                | | destroyEngine        | INVALID_ENGINE                    | 传入的参数不是合法的 engine 对象。                            |

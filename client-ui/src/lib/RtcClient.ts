import openAPIs from '@/app/api';
import Utils from '@/utils/utils';

declare global {
  interface Window {
    VERTC: any;
  }
}

enum StreamRemoveReason {
  /**
   * @brief 远端用户停止发布流。
   */

  STREAM_REMOVE_REASON_UNPUBLISH = 0,
  /**
   * @brief 远端用户发布流失败。
   */

  STREAM_REMOVE_REASON_PUBLISH_FAILED = 1,
  /**
   * @brief 保活失败。
   */

  STREAM_REMOVE_REASON_KEEP_LIVE_FAILED = 2,
  /**
   * @brief 远端用户断网。
   */

  STREAM_REMOVE_REASON_CLIENT_DISCONNECTED = 3,
  /**
   * @brief 远端用户重新发布流。
   */

  STREAM_REMOVE_REASON_REPUBLISH = 4,

  STREAM_REMOVE_REASON_OTHER = 5,

  STREAM_REMOVE_REASON_TOKEN_PRIVILEGE_EXPIRED = 6,
}

enum AudioProfileType {
  default = 0,
  fluent = 1,
  standard = 2,
  hd = 3,
  standardStereo = 4,
  hdMono = 5,
}

enum MirrorType {
  MIRROR_TYPE_NONE = 0,
  MIRROR_TYPE_RENDER = 1,
}

enum StreamIndex {
  STREAM_INDEX_MAIN = 0,
  STREAM_INDEX_SCREEN = 1,
}

enum RoomProfileType {
  communication = 0,
  chat = 5,
  chatRoom = 6,
  coHost = 9,
  meeting = 16,
  classRoom = 18,
}

enum MediaType {
  AUDIO = 1,
  VIDEO = 2,
  AUDIO_AND_VIDEO = 3,
}

enum VideoRenderMode {
  RENDER_MODE_HIDDEN = 0,
  RENDER_MODE_FIT = 1,
  RENDER_MODE_FILL = 2,
}

let VERTC: any;
if (process.env.NODE_ENV === 'production') {
  VERTC = window.VERTC; // 生产环境使用全局变量
} else {
  VERTC = require('@volcengine/rtc').default; // 开发环境使用 npm 包
}

export interface IEventListener {
  handleError: (e: { errorCode: any }) => void;
  handleUserJoin: (e: any) => void;
  handleUserLeave: (e: any) => void;
  handleUserPublishStream: (e: { userId: string; mediaType: MediaType }) => void;
  handleUserUnpublishStream: (e: { userId: string; mediaType: MediaType; reason: StreamRemoveReason }) => void;
  handleRemoteStreamStats: (e: any) => void;
  handleLocalStreamStats: (e: any) => void;
  handleLocalAudioPropertiesReport: (e: any[]) => void;
  handleRemoteAudioPropertiesReport: (e: any[]) => void;
  handleAudioDeviceStateChanged: (e: any) => void;
  handleUserMessageReceived: (e: { userId: string; message: any }) => void;
  handleAutoPlayFail: (e: any) => void;
  handlePlayerEvent: (e: any) => void;
  handleUserStartAudioCapture: (e: { userId: string }) => void;
  handleUserStopAudioCapture: (e: { userId: string }) => void;
  handleRoomBinaryMessageReceived: (e: { userId: string; message: ArrayBuffer }) => void;
  handleNetworkQuality: (uplinkNetworkQuality: any, downlinkNetworkQuality: any) => void;
}

interface EngineOptions {
  appId: string;
  uid: string;
  taskId: string;
  roomId: string;
}

export interface BasicBody {
  task_id: string;
  room_id: string;
  user_id: string;
  login_token: string | null;
}

/**
 * @brief RTC Core Client
 * @notes Refer to official website documentation to get more information about the API.
 */
export class RTCClient {
  engine!: any;

  config!: EngineOptions;

  basicInfo!: BasicBody;

  private _audioCaptureDevice?: string;

  private _videoCaptureDevice?: string;

  audioBotEnabled = false;

  audioBotStartTime = 0;

  createEngine = async (props: EngineOptions, token: string) => {
    this.config = props;
    this.basicInfo = {
      task_id: props.taskId,
      room_id: props.roomId,
      user_id: props.uid,
      login_token: token,
    };
    this.engine = VERTC.createEngine(this.config.appId);
  };

  addEventListeners = ({
    handleError,
    handleUserJoin,
    handleUserLeave,
    handleUserPublishStream,
    handleUserUnpublishStream,
    handleRemoteStreamStats,
    handleLocalStreamStats,
    handleLocalAudioPropertiesReport,
    handleRemoteAudioPropertiesReport,
    handleAudioDeviceStateChanged,
    handleUserMessageReceived,
    handleAutoPlayFail,
    handlePlayerEvent,
    handleUserStartAudioCapture,
    handleUserStopAudioCapture,
    handleRoomBinaryMessageReceived,
    handleNetworkQuality,
  }: IEventListener) => {
    this.engine.on(VERTC.events.onError, handleError);
    this.engine.on(VERTC.events.onUserJoined, handleUserJoin);
    this.engine.on(VERTC.events.onUserLeave, handleUserLeave);
    this.engine.on(VERTC.events.onUserPublishStream, handleUserPublishStream);
    this.engine.on(VERTC.events.onUserUnpublishStream, handleUserUnpublishStream);
    this.engine.on(VERTC.events.onRemoteStreamStats, handleRemoteStreamStats);
    this.engine.on(VERTC.events.onLocalStreamStats, handleLocalStreamStats);
    this.engine.on(VERTC.events.onAudioDeviceStateChanged, handleAudioDeviceStateChanged);
    this.engine.on(VERTC.events.onLocalAudioPropertiesReport, handleLocalAudioPropertiesReport);
    this.engine.on(VERTC.events.onRemoteAudioPropertiesReport, handleRemoteAudioPropertiesReport);
    this.engine.on(VERTC.events.onUserMessageReceived, handleUserMessageReceived);
    this.engine.on(VERTC.events.onAutoplayFailed, handleAutoPlayFail);
    this.engine.on(VERTC.events.onPlayerEvent, handlePlayerEvent);
    this.engine.on(VERTC.events.onUserStartAudioCapture, handleUserStartAudioCapture);
    this.engine.on(VERTC.events.onUserStopAudioCapture, handleUserStopAudioCapture);
    this.engine.on(VERTC.events.onRoomBinaryMessageReceived, handleRoomBinaryMessageReceived);
    this.engine.on(VERTC.events.onNetworkQuality, handleNetworkQuality);
  };

  joinRoom = (token: string | null, username: string): Promise<void> => {
    this.engine.enableAudioPropertiesReport({ interval: 1000 });
    return this.engine.joinRoom(
      token,
      `${this.config.roomId!}`,
      {
        userId: this.config.uid!,
        extraInfo: JSON.stringify({
          user_name: username,
          user_id: this.config.uid,
        }),
      },
      {
        isAutoPublish: true,
        isAutoSubscribeAudio: true,
        roomProfileType: RoomProfileType.chat,
      }
    );
  };

  leaveRoom = (taskId: string, userId: string, roomId: string) => {
    if (!this.engine) {
      console.log('engine not initialized');
      return;
    }
    this.stopAudioBot(taskId, userId, roomId);
    this.audioBotEnabled = false;
    this.engine.leaveRoom();
    VERTC.destroyEngine(this.engine);
    this._audioCaptureDevice = undefined;
  };

  checkPermission(): Promise<{
    video: boolean;
    audio: boolean;
  }> {
    return VERTC.enableDevices({
      video: false,
      audio: true,
    });
  }

  /**
   * @brief get the devices
   * @returns
   */
  async getDevices(props?: { video?: boolean; audio?: boolean }): Promise<{
    audioInputs: MediaDeviceInfo[];
    audioOutputs: MediaDeviceInfo[];
    videoInputs: MediaDeviceInfo[];
  }> {
    const { video, audio = true } = props || {};
    let audioInputs: MediaDeviceInfo[] = [];
    let audioOutputs: MediaDeviceInfo[] = [];
    let videoInputs: MediaDeviceInfo[] = [];
    if (audio) {
      const inputs = await VERTC.enumerateAudioCaptureDevices();
      const outputs = await VERTC.enumerateAudioPlaybackDevices();
      audioInputs = inputs.filter((i: any) => i.deviceId && i.kind === 'audioinput');
      audioOutputs = outputs.filter((i: any) => i.deviceId && i.kind === 'audiooutput');
      this._audioCaptureDevice = audioInputs.filter((i) => i.deviceId)?.[0]?.deviceId;
    }
    if (video) {
      videoInputs = await VERTC.enumerateVideoCaptureDevices();
      videoInputs = videoInputs.filter((i) => i.deviceId && i.kind === 'videoinput');
      this._videoCaptureDevice = videoInputs?.[0]?.deviceId;
    }

    return {
      audioInputs,
      audioOutputs,
      videoInputs,
    };
  }

  startVideoCapture = async (camera?: string) => {
    await this.engine.startVideoCapture(camera || this._videoCaptureDevice);
  };

  stopVideoCapture = async () => {
    this.engine.setLocalVideoMirrorType(MirrorType.MIRROR_TYPE_RENDER);
    await this.engine.stopVideoCapture();
  };

  startAudioCapture = async (mic?: string) => {
    await this.engine.startAudioCapture(mic || this._audioCaptureDevice);
    this.setAudioVolume(50);
  };

  stopAudioCapture = async () => {
    await this.engine.stopAudioCapture();
  };

  publishStream = (mediaType: MediaType) => {
    this.engine.publishStream(mediaType);
  };

  unpublishStream = (mediaType: MediaType) => {
    this.engine.unpublishStream(mediaType);
  };

  /**
   * @brief 设置业务标识参数
   * @param businessId
   */
  setBusinessId = (businessId: string) => {
    this.engine.setBusinessId(businessId);
  };

  setAudioVolume = (volume: number) => {
    this.engine.setCaptureVolume(StreamIndex.STREAM_INDEX_MAIN, volume);
    this.engine.setCaptureVolume(StreamIndex.STREAM_INDEX_SCREEN, volume);
  };

  /**
   * @brief 设置音质档位
   */
  setAudioProfile = (profile: AudioProfileType) => {
    this.engine.setAudioProfile(profile);
  };

  /**
   * @brief 切换设备
   */
  switchDevice = (deviceType: MediaType, deviceId: string) => {
    if (deviceType === MediaType.AUDIO) {
      this._audioCaptureDevice = deviceId;
      this.engine.setAudioCaptureDevice(deviceId);
    }
    if (deviceType === MediaType.VIDEO) {
      this._videoCaptureDevice = deviceId;
      this.engine.setVideoCaptureDevice(deviceId);
    }
    if (deviceType === MediaType.AUDIO_AND_VIDEO) {
      this._audioCaptureDevice = deviceId;
      this._videoCaptureDevice = deviceId;
      this.engine.setVideoCaptureDevice(deviceId);
      this.engine.setAudioCaptureDevice(deviceId);
    }
  };

  setLocalVideoMirrorType = (type: MirrorType) => {
    return this.engine.setLocalVideoMirrorType(type);
  };

  setLocalVideoPlayer = (userId: string, renderDom?: string | HTMLElement) => {
    return this.engine.setLocalVideoPlayer(StreamIndex.STREAM_INDEX_MAIN, {
      renderDom,
      userId,
      renderMode: VideoRenderMode.RENDER_MODE_HIDDEN,
    });
  };

  /**
   * @brief 启用 AIGC
   */
  startAudioBot = async (taskId: string, userId: string, roomId: string) => {
    if (this.audioBotEnabled) {
      await this.stopAudioBot(taskId, userId, roomId);
    }
    await openAPIs.StartVoiceChat({ taskId, userId, roomId });
    this.audioBotEnabled = true;
    this.audioBotStartTime = Date.now();
    Utils.setSessionInfo({ audioBotEnabled: 'enable' });
  };

  /**
   * @brief 关闭 AIGC
   */
  stopAudioBot = async (taskId: string, userId: string, roomId: string) => {
    await openAPIs.StopVoiceChat({ taskId, userId, roomId });
    this.audioBotStartTime = 0;
    this.audioBotEnabled = false;
  };

  /**
   * @brief 获取当前 AI 是否启用
   */
  getAudioBotEnabled = () => {
    return this.audioBotEnabled;
  };

  sendMessage = async (userId: string, content: string) => {
    const { task_id: taskId, room_id: roomId } = this.basicInfo;
    await openAPIs.SendMessage({ taskId, userId, roomId, content, pushTime: new Date().getTime() });
  };
}

export default new RTCClient();

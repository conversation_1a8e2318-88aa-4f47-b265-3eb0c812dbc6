import RtcClient from '@/lib/RtcClient';
import { clearCurrentMsg, clearHistoryMsg, localJoinRoom, localLeaveRoom, updateAIGCState, updateLocalUser } from '@/store/slices/room';
import Utils from '@/utils/utils';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import openAPIs, { limitCheck } from '@/app/api';
import useRtcListeners from '@/lib/listenerHooks';
import { RootState } from '@/store';

import { updateMediaInputs, updateSelectedDevice } from '@/store/slices/device';
import logger from '@/utils/logger';
import { getUserInfoFromStorage } from '@/utils/user';

enum MediaType {
  AUDIO = 1,
  VIDEO = 2,
  AUDIO_AND_VIDEO = 3,
}

export const useJoin = (): [boolean, () => Promise<void | boolean | { error: string }>] => {
  const devicePermissions = useSelector((state: RootState) => state.device.devicePermissions);
  const room = useSelector((state: RootState) => state.room);
  const dispatch = useDispatch();
  const userInfo = getUserInfoFromStorage();

  const [joining, setJoining] = useState(false);

  const listeners = useRtcListeners();

  const handleAigcModeStart = async (taskId: string, userId: string, roomId: string) => {
    dispatch(clearHistoryMsg());
    if (room.isAIGCEnable) {
      await RtcClient.stopAudioBot(taskId, userId, roomId);
      dispatch(clearCurrentMsg());
      await RtcClient.startAudioBot(taskId, userId, roomId);
    } else {
      await RtcClient.startAudioBot(taskId, userId, roomId);
    }
    dispatch(updateAIGCState({ isAIGCEnable: true }));
  };

  async function dispatchJoin(): Promise<boolean | undefined | { error: string }> {
    if (joining) {
      return;
    }
    const taskId = window.pageData.uuid;
    const userId = userInfo?.jobNumber;

    // 限流检测
    try {
      const limitRes = await limitCheck(userId);
      if (!limitRes.success) {
        return { error: 'get throttle error' };
      }
      if (limitRes.data) {
        return { error: '忙线中，请稍候再试' };
      }
    } catch (e) {
      return { error: 'get throttle error' };
    }

    const result: any = await openAPIs.GetConfig({
      taskId,
      userId,
    });

    setJoining(true);

    /** 1. Create RTC Engine */
    await RtcClient.createEngine(
      {
        appId: result.appId,
        roomId: result.roomId,
        uid: userId,
        taskId,
      } as any,
      result.token
    );

    /** 2.1 Set events callbacks */
    RtcClient.addEventListeners(listeners);

    await RtcClient.joinRoom(result.token!, userId);
    console.log(' ------ userJoinRoom\n', `roomId: ${result.roomId}\n`, `uid: ${userId}`);

    /** 3. Set users' devices info */
    const mediaDevices = await RtcClient.getDevices({
      audio: true,
      video: false,
    });
    if (devicePermissions.audio) {
      try {
        await RtcClient.startAudioCapture();
      } catch (e) {
        logger.debug('No permission for mic');
        setJoining(false);
        return { error: '无法获取麦克风权限' };
      }
    }

    dispatch(
      localJoinRoom({
        roomId: result.roomId,
        user: {
          userId,
          username: userId,
          publishAudio: true,
        },
      })
    );
    dispatch(
      updateSelectedDevice({
        selectedMicrophone: mediaDevices.audioInputs[0]?.deviceId,
        selectedCamera: mediaDevices.videoInputs[0]?.deviceId,
      })
    );
    dispatch(updateMediaInputs(mediaDevices));

    setJoining(false);

    Utils.setSessionInfo({
      username: userId,
      roomId: result.roomId,
      publishAudio: true,
    });

    await handleAigcModeStart(taskId, userId, result.roomId);
  }

  return [joining, dispatchJoin];
};

export const useLeave = () => {
  const dispatch = useDispatch();
  const room = useSelector((state: RootState) => state.room);
  const user = getUserInfoFromStorage();
  const userId = user?.jobNumber;
  const taskId = window.pageData.uuid;
  const roomId = room.roomId;
  if (roomId) {
    // 界面退出时，无法获取 state.room.roomId，所以需要保存到 localStorage
    localStorage.setItem('rtc_roomId', roomId);
  }
  return async function () {
    dispatch(localLeaveRoom());
    dispatch(updateAIGCState({ isAIGCEnable: false }));
    await Promise.all([RtcClient.stopAudioCapture]);
    RtcClient.leaveRoom(taskId, userId, localStorage.getItem('rtc_roomId') || roomId);
  };
};

export const useDeviceState = () => {
  const dispatch = useDispatch();
  const room = useSelector((state: RootState) => state.room);
  const localUser = room.localUser;
  const isAudioPublished = localUser.publishAudio;

  const queryDevices = async (type: MediaType) => {
    const mediaDevices = await RtcClient.getDevices({
      audio: type === MediaType.AUDIO,
      video: type === MediaType.VIDEO,
    });
    if (type === MediaType.AUDIO) {
      dispatch(
        updateMediaInputs({
          audioInputs: mediaDevices.audioInputs,
        })
      );
      dispatch(
        updateSelectedDevice({
          selectedMicrophone: mediaDevices.audioInputs[0]?.deviceId,
        })
      );
    } else {
      dispatch(
        updateMediaInputs({
          videoInputs: mediaDevices.videoInputs,
        })
      );
      dispatch(
        updateSelectedDevice({
          selectedCamera: mediaDevices.videoInputs[0]?.deviceId,
        })
      );
    }
    return mediaDevices;
  };

  const switchMic = async (publish = true) => {
    if (publish) {
      await (!isAudioPublished ? RtcClient.publishStream(MediaType.AUDIO) : RtcClient.unpublishStream(MediaType.AUDIO));
    }
    await queryDevices(MediaType.AUDIO);
    await (!isAudioPublished ? RtcClient.startAudioCapture() : RtcClient.stopAudioCapture());
    dispatch(
      updateLocalUser({
        publishAudio: !localUser.publishAudio,
      })
    );
  };

  return {
    isAudioPublished,
    switchMic,
  };
};

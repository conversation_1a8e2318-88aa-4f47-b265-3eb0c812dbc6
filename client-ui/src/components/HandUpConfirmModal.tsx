import { Modal, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';
import ModalFooter from './ModalFooter';

const HandUpConfirmModal = ({ open, setOpen, setOnlyHistory }: { open: boolean; setOpen: (open: boolean) => void; setOnlyHistory: (onlyHistory: boolean) => void }) => {
  const navigate = useNavigate();
  return (
    <Modal
      open={open}
      title={'访谈已完成'}
      onCancel={() => setOpen(false)}
      closable={false}
      footer={
        <ModalFooter
          onOk={async () => {
            setOpen(false);
            navigate(-1);
            return true;
          }}
          okText="知道了"
        />
      }
    >
      <Typography.Text type="secondary">点击"知道了", 你可以返回访谈任务列表</Typography.Text>
    </Modal>
  );
};
export default HandUpConfirmModal;

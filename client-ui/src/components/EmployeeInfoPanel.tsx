import { getUserInfoFromStorage } from '@/utils/user';
import { Col, Divider, Row, Space, Typography } from 'antd';
import { FC } from 'react';
import styles from './EmployeeInfoPanel.module.less';

const { Text } = Typography;

const EmployeeInfoPanel: FC<{ extra?: React.ReactNode }> = ({ extra }) => {
  const user = getUserInfoFromStorage();

  return (
    <div className={`${styles.infoCard} ${styles[window.isPCPlatform ? 'pcCard' : 'mobileCard']}`}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text style={{ color: '#fff', fontWeight: 600, fontSize: 20 }}>你好：{user?.name ?? '-'}</Text>
        <Text style={{ color: '#fff', fontWeight: 400, fontSize: 16 }}>感谢你参与本次访谈, 你的信息我已确认如下</Text>
        <Divider style={{ margin: '10px 0', borderColor: '#fff' }} />
        <LabelContent label="工号" value={user?.jobNumber ?? '-'} />
        <Row>
          <Col span={12}>
            <LabelContent label="部门" value={user?.deptName ?? '-'} />
          </Col>
          <Col span={12}>
            <LabelContent label="岗位" value={user?.title ?? '-'} />
          </Col>
        </Row>
        {extra}
      </Space>
    </div>
  );
};

function LabelContent({ label, value }: { label: string; value: string }) {
  return (
    <Space>
      <Text style={{ color: '#fff', fontWeight: 400, fontSize: 16, opacity: 0.6 }}>{label}</Text>
      <Text style={{ color: '#fff', fontWeight: 400, fontSize: 16 }}>{value}</Text>
    </Space>
  );
}

export default EmployeeInfoPanel;

import logo from '@/assets/img/logo.png';
import { ReactComponent as LogoSVG } from '@/assets/img/Logo.svg';
import { getUserInfoFromStorage } from '@/utils/user';
import { Row, Space } from 'antd';

function HeaderLogo() {
  const user = getUserInfoFromStorage();
  return (
    <Row
      justify="space-between"
      style={{
        backgroundColor: 'white',
        padding: '16px 24px',
        position: 'fixed',
        top: 0,
        width: '100vw',
        zIndex: 1,
        lineHeight: '50px',
      }}
    >
      <img src={logo} alt="logo" style={{ height: '70px' }} />
      <Space>
        <span>欢迎使用RTC</span>
        <a>{user?.name}</a>
      </Space>
    </Row>
  );
}

export default HeaderLogo;
export function LogoWithRTC() {
  return (
    <Space
      size={24}
      style={{
        position: 'absolute',
        left: 16,
      }}
    >
      <LogoSVG />
    </Space>
  );
}

.bg {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.infoCard {
  background: rgba(0, 0, 0, 0.55);
  padding: 24px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  position: absolute;
  backdrop-filter: blur(6px) saturate(180%);
  -webkit-backdrop-filter: blur(6px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.pcCard {
  bottom: 40px;
  width: calc(100% - 80px);
  border-radius: 16px;
}

.mobileCard {
  width: calc(100% - 50px);
  bottom: 0;
  border-radius: 16px 16px 0 0;
}

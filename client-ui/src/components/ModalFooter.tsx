import { Button, ConfigProvider, Row, Space } from 'antd';
import { FC } from 'react';

const ModalFooter: FC<{
  onOk?: () => Promise<boolean>;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
}> = ({ onOk, okText = '确定', cancelText = '取消', onCancel }) => {
  return (
    <Row justify={'end'}>
      <ConfigProvider
        theme={{
          components: {
            Button: {
              colorPrimary: '#171F2D',
              colorPrimaryHover: '#303847',
            },
          },
        }}
      >
        <Space>
          {onCancel && (
            <Button
              onClick={() => {
                onCancel();
              }}
              style={{
                border: 'none',
              }}
            >
              {cancelText}
            </Button>
          )}
          {onOk && (
            <Button
              type="primary"
              onClick={async () => {
                const res = await onOk();
              }}
            >
              {okText}
            </Button>
          )}
        </Space>
      </ConfigProvider>
    </Row>
  );
};

export default ModalFooter;

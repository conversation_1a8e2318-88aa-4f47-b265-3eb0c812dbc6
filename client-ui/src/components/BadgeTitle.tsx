import { Col, Popover, PopoverProps, Row, Tag, Typography } from 'antd';

import styles from './BadgeTitle.module.less';

const { Title, Text } = Typography;
export default function BadgeTitle({
  title,
  badge,
  popProps,
  desc,
}: {
  title: string;
  desc: string;
  badge: {
    color: string;
    text: string;
  };
  popProps?: PopoverProps;
}) {
  return (
    <div className={styles.container}>
      <Row
        gutter={8}
        style={{
          flexWrap: 'nowrap',
          paddingRight: '44px',
        }}
      >
        <Col>
          <Popover {...popProps}>
            <Tag className={styles.tag} color={badge.color}>
              {badge.text}
            </Tag>
          </Popover>
        </Col>
        <Col>
          <Title level={5} className={styles.name}>
            {title}
          </Title>
        </Col>
      </Row>
      <Text type="secondary" className={styles.description}>
        {desc}
      </Text>
    </div>
  );
}

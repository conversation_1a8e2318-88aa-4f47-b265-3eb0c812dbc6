import { RootState } from '@/store';
import { getUserInfoFromStorage } from '@/utils/user';
import { RobotOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar } from 'antd';
import { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import styles from './HistoryMsg.module.less';

const HistoryMsg = () => {
  const msgHistory = useSelector((state: RootState) => state.room.msgHistory);
  const userInfo = getUserInfoFromStorage();
  const userId = userInfo?.jobNumber;
  const subtitleContainer = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isRobot = (uid: string) => uid && uid.startsWith('voiceChat_');

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [JSON.stringify(msgHistory)]);

  return (
    <div className={styles.subtitleContainer} style={window.isPCPlatform ? { width: '100%' } : { width: 'calc(100vw - 20px)' }} ref={subtitleContainer}>
      {msgHistory.map(({ value, user }, index) => {
        const isUserMsg = user === userId;
        const isRobotMsg = isRobot(user);
        if (!isUserMsg && !isRobotMsg) return null;
        if (isRobotMsg) {
          return (
            <div className={styles.robotMsgRow} key={index}>
              <Avatar style={{ minWidth: '40px' }} src={pageData.taskInfo?.robot?.avatar ? <img src={pageData.taskInfo?.robot?.avatar} /> : <RobotOutlined />} size={40} />
              <div className={styles.robotMsgContent}>
                <div className={styles.robotName}>{pageData.taskInfo?.robot?.name}</div>
                <div
                  className={styles.robotBubble}
                  style={
                    window.isPCPlatform
                      ? {
                          fontSize: '22px',
                          padding: '18px',
                        }
                      : {
                          fontSize: '14px',
                          padding: '8px',
                        }
                  }
                >
                  {value}
                </div>
              </div>
            </div>
          );
        }
        return (
          <div className={styles.userMsgRow} key={index}>
            <span
              className={`${styles.subtitleMessage} ${styles.userMessage}`}
              style={
                window.isPCPlatform
                  ? {
                      fontSize: '22px',
                      padding: '18px',
                    }
                  : {
                      fontSize: '14px',
                      padding: '8px',
                    }
              }
            >
              {value}
            </span>
            <Avatar style={{ minWidth: '40px' }} src={userInfo?.avatar ? <img src={userInfo?.avatar} /> : <UserOutlined />} size={40} />
          </div>
        );
      })}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default HistoryMsg;

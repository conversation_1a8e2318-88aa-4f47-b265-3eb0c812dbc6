import errorPageBg from '@/assets/img/errorPageBg.png';
import { ReactComponent as InforCircle } from '@/assets/img/InforCircle.svg';
import React from 'react';

const ErrorContainer = ({ title, hasMask = true, showIcon = true }: { title: React.ReactNode; hasMask?: boolean; showIcon?: boolean }) => {
  return (
    <div
      style={{
        position: 'relative',
        width: '100vw',
        height: '100vh',
        overflow: 'hidden',
      }}
    >
      <img
        src={errorPageBg}
        alt="error-bg"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          top: 0,
          left: 0,
          zIndex: 1,
        }}
      />
      {hasMask && (
        <div
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            background: 'rgba(0,0,0,0.5)',
            top: 0,
            left: 0,
            zIndex: 2,
          }}
        />
      )}
      <div
        style={{
          position: 'relative',
          zIndex: 3,
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff',
        }}
      >
        {showIcon && <InforCircle />}
        <div
          style={{
            marginTop: window.isPCPlatform ? '20px' : '10px',
            fontSize: window.isPCPlatform ? '34px' : '18px',
            fontWeight: 600,
          }}
        >
          {title}
        </div>
      </div>
    </div>
  );
};

export default ErrorContainer;

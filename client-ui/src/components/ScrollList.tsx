import { But<PERSON>, Col, Empty, Row, Spin, Typography } from 'antd';
import { ReactNode, useEffect, useState } from 'react';

export type ListRequest = {
  /** 关键字搜索 */
  keyword?: string;
  /** 排序方式 */
  order?: string;
  /** 当前页码 */
  page?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 排序字段 */
  sort?: string;
};

export default function ScrollList<Data, Query = ListRequest>({
  renderItem,
  pageSize = 12,
  getList,
  /** 其他查询参数 */
  params,
  /** 刷新依赖 */
  refreshDeps,
  gutter = [24, 24],
}: {
  renderItem: (data: Data) => ReactNode;
  pageSize?: number;
  getList?: (query: Query) => Promise<{ success: boolean; data: { records: Data[]; total: number } }>;
  params?: Record<string, string | number | boolean>;
  /** 刷新依赖 */
  refreshDeps?: (string | number | boolean)[];
  gutter?: [number, number] | number;
}) {
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [dataSource, setDataSource] = useState<Data[]>([]);

  const fetchData = async (currentPage: number) => {
    if (!getList) return;
    setLoading(true);
    const res = await getList({
      page: currentPage,
      pageSize,
      ...params,
    } as Query);
    if (res.success) {
      setDataSource(currentPage === 1 ? res.data.records : [...dataSource, ...res.data.records]);
      setHasMore(currentPage * pageSize < res.data.total);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (page > 1 || !refreshDeps) {
      fetchData(page);
    }
  }, [page]);
  useEffect(() => {
    if (refreshDeps && refreshDeps?.length > 0) {
      setHasMore(true);
      setPage(1);
      fetchData(1);
    }
  }, [refreshDeps?.join(',')]);

  return (
    <Spin spinning={loading}>
      <Row
        gutter={gutter}
        style={{
          marginTop: window.isPCPlatform ? '24px' : 0,
        }}
      >
        {dataSource.length ? (
          dataSource.map((item) => renderItem(item))
        ) : (
          <Col span={24}>
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>暂无数据</span>} />
          </Col>
        )}
      </Row>
      {dataSource.length > 0 && (
        <Row justify="center" style={{ marginTop: 24 }}>
          <Col>
            {hasMore ? (
              <Button loading={loading} onClick={() => setPage(page + 1)} type="link">
                加载更多
              </Button>
            ) : (
              <Typography.Text type="secondary">已经到底了</Typography.Text>
            )}
          </Col>
        </Row>
      )}
    </Spin>
  );
}

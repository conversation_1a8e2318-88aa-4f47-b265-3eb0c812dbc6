.subtitleContainer {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.subtitleMessage {
  max-width: 70%;
  border-radius: 20px;
  opacity: 0.85;
  transition: opacity 0.3s ease;
  word-break: break-all;
}

.userMessage {
  align-self: flex-end;
  color: #fff;
  background-color: #1a73e8;
  border-radius: 18px;
  margin-right: 5px;
}

.robotMsgRow {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

.robotMsgRow .ant-avatar {
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  border-radius: 50% !important;
  object-fit: cover;
}

.robotMsgContent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.robotName {
  color: #888;
  font-size: 16px;
  margin-bottom: 4px;
}

.robotBubble {
  background: #fff;
  color: #222;
  border-radius: 28px;
  font-size: 22px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  border: 1px solid #eee;
  font-weight: 400;
  line-height: 1.6;
  border-top-left-radius: 0;
}

.userMsgRow {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

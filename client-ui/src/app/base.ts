import { AIGC_PROXY_HOST } from '@/config';
import { Message } from '@arco-design/web-react';

type Headers = Record<string, string>;

/**
 * @brief Get
 * @param apiName
 * @param headers
 */
export const requestGetMethod = (apiBasicParams: string, headers = {}) => {
  return async (params: Record<string, any> = {}) => {
    const url = `${apiBasicParams}&${Object.keys(params)
      .map((key) => `${key}=${params[key]}`)
      .join('&')}`;
    const res = await fetch(url, {
      headers: {
        ...headers,
      },
    });
    return res;
  };
};

/**
 * @brief Post
 * @param apiName
 * @param isJson
 * @param headers
 */
export const requestPostMethod = (
  apiBasicParams: string,
  isJson: boolean = true,
  headers: Headers = {}
) => {
  return async <T>(params: T) => {
    const res = await fetch(`${apiBasicParams}`, {
      method: 'post',
      headers: {
        'content-type': 'application/json',
        ...headers,
      },
      body: (isJson ? JSON.stringify(params) : params) as BodyInit,
    });
    return res;
  };
};

/**
 * @brief Handler
 * @param res
 */
export const resultHandler = (res: any) => {
  const { result, responseMetadata } = res || {};
  if (result === 'ok') {
    return result;
  }
  Message.error("系统出现异常，请联系管理人员");
  throw new Error(`[${responseMetadata?.Action}]Failed(${JSON.stringify(responseMetadata, null, 2)})`);
};

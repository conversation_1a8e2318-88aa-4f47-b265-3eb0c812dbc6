/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

import { requestGetMethod, requestPostMethod, resultHandler } from './base';
import { ACTIONS, RequestParams, RequestResponse } from './type';

const APIS_CONFIG = [
  {
    action: ACTIONS.StartVoiceChat,
    apiBasicParams: `/pgw/api/client/proxyFetch?action=${ACTIONS.StartVoiceChat}`,
    method: 'post',
  },
  {
    action: ACTIONS.UpdateVoiceChat,
    apiBasicParams: `/pgw/api/client/proxyFetch?action=${ACTIONS.UpdateVoiceChat}`,
    method: 'post',
  },
  {
    action: ACTIONS.StopVoiceChat,
    apiBasicParams: `/pgw/api/client/proxyFetch?action=${ACTIONS.StopVoiceChat}`,
    method: 'post',
  },
  {
    action: ACTIONS.GetConfig,
    apiBasicParams: `/pgw/api/client/proxyFetch?action=${ACTIONS.GetConfig}`,
    method: 'post',
  },
  {
    action: ACTIONS.SendMessage,
    apiBasicParams: `/pgw/api/message/send`,
    method: 'post',
  },
] as const;

type ApiConfig = typeof APIS_CONFIG;
type TupleToUnion<T extends readonly unknown[]> = T[number];
type ApiNames = Pick<TupleToUnion<ApiConfig>, 'action'>['action'];
type RequestFn = <T extends keyof RequestResponse>(params?: RequestParams[T]) => RequestResponse[T];
type PromiseRequestFn = <T extends keyof RequestResponse>(params?: RequestParams[T]) => Promise<RequestResponse[T]>;
type Apis = Record<ApiNames, RequestFn | PromiseRequestFn>;

const APIS = APIS_CONFIG.reduce((store, cur) => {
  const { action, apiBasicParams, method = 'get' } = cur;
  store[action] = async (params) => {
    const queryData = method === 'get' ? await requestGetMethod(apiBasicParams)(params) : await requestPostMethod(apiBasicParams)(params);
    const res = await queryData?.json();
    if (action === ACTIONS.GetConfig || ACTIONS.SendMessage) {
      return res.data;
    }
    return resultHandler(res.data);
  };
  return store;
}, {} as Apis);

/**
 * 限流检测API
 * @param userId 用户ID
 * @returns {Promise<{ success: boolean, data: boolean }>} data=true表示限流
 */
export async function limitCheck(userId: string): Promise<{ success: boolean; data: boolean }> {
  const url = `/pgw/api/throttle/limit?userId=${userId}`;
  try {
    const res = await requestGetMethod(url)();
    return await res.json();
  } catch (e) {
    // 网络或服务异常
    return { success: false, data: false };
  }
}

export default APIS;

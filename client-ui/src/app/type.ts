export enum ACTIONS {
  StartVoiceChat = 'StartVoiceChat',
  UpdateVoiceChat = 'UpdateVoiceChat',
  StopVoiceChat = 'StopVoiceChat',
  GetConfig = 'GetConfig',
  GetTaskInfo = 'GetTaskInfo',
  SendMessage = 'SendMessage',
}


export interface RequestParams {
  [ACTIONS.StartVoiceChat]: {
    RoomId: string;
    TaskId: string;
  };

  [ACTIONS.UpdateVoiceChat]: {
    RoomId: string;
    TaskId: string;
  };

  [ACTIONS.StopVoiceChat]: {
    RoomId: string;
    TaskId: string;
  };

  [ACTIONS.GetConfig]: {
    taskId?: string;
    userId?: string;
    roomId?: string;
  };

  [ACTIONS.SendMessage]: {
    taskId?: string;
    userId?: string;
    roomId?: string;
    content?: string;
    pushTime?: number;
  };
}

/**
 * @brief 返回参数类型
 */
export interface RequestResponse {
  [ACTIONS.StartVoiceChat]: string;
  [ACTIONS.UpdateVoiceChat]: string;
  [ACTIONS.StopVoiceChat]: string;
  [ACTIONS.GetConfig]: string;
  [ACTIONS.SendMessage]: string;
}

const USER_INFO_KEY = 'megniu_rtc_userInfo';
const USER_INFO_EXPIRE_KEY = 'megniu_rtc_userInfo_expire';
const EXPIRE_MS = 2 * 60 * 60 * 1000; // 2小时

export function setUserInfoToStorage(userInfo: any) {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
  localStorage.setItem(USER_INFO_EXPIRE_KEY, Date.now().toString());
}

export function getUserInfoFromStorage(): any | null {
  const userInfo = localStorage.getItem(USER_INFO_KEY);
  const expire = localStorage.getItem(USER_INFO_EXPIRE_KEY);
  if (!userInfo || !expire) return null;
  if (Date.now() - Number(expire) > EXPIRE_MS) {
    localStorage.removeItem(USER_INFO_KEY);
    localStorage.removeItem(USER_INFO_EXPIRE_KEY);
    return null;
  }
  return JSON.parse(userInfo);
}

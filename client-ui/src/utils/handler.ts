import { useDispatch } from 'react-redux';
import logger from './logger';
import Utils from '@/utils/utils';
import RtcClient from '@/lib/RtcClient';
import { setHistoryMsg } from '@/store/slices/room';


export type AnyRecord = Record<string, any>;

export enum MESSAGE_TYPE {
  BRIEF = 'conv',
  SUBTITLE = 'subv',
}

export const useMessageHandler = () => {
  const dispatch = useDispatch();

  const maps = {
    [MESSAGE_TYPE.BRIEF]: (parsed: AnyRecord) => {
      const { Stage } = parsed || {};
      const { Code, Description } = Stage || {};
      logger.debug(Code, Description);
    },
    [MESSAGE_TYPE.SUBTITLE]: async (parsed: AnyRecord) => {
      const data = parsed.data?.[0] || {};
      if (data) {
        const { text: msg, definite, userId: user, paragraph } = data;
        dispatch(setHistoryMsg({ text: msg, user, paragraph, definite }));
        const lastMsgCompleted = definite || paragraph || false;
        if (lastMsgCompleted) {
          await RtcClient.sendMessage(user, msg);
        }
      }
    },
  };

  return {
    parser: (buffer: ArrayBuffer) => {
      try {
        const { type, value } = Utils.tlv2String(buffer);
        maps[type as MESSAGE_TYPE]?.(JSON.parse(value));
      } catch (e) {
        logger.debug('parse error', e);
      }
    },
  };
};

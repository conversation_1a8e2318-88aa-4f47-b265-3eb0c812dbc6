export { };
declare global {
  interface PageData {
    voiceAgentId: string;
    uuid: string;
    bgUrl: string | null;
    holdTalkTimeout: number | null;
    showAudioWave: boolean | null;
    corpId: string;
    clientId: string;
    userId: string;
    taskInfo: any;
  }
  interface Window {
    DDPlatform?: ENV_ENUM;
    isPCPlatform?: boolean;
    VConsole?: any;
  }
  const pageData: PageData;
}

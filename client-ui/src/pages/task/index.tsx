import { TaskService } from '@/app/task';
import BadgeTitle from '@/components/BadgeTitle';
import HeaderLogo from '@/components/HeaderLogo';
import { getUserInfoFromStorage } from '@/utils/user';
import { Avatar, Button, ButtonProps, Card, Col, Divider, Layout, Row, Space, Typography } from 'antd';
import moment from 'moment';
import { useNavigate } from 'react-router-dom';
import ScrollList from '../../components/ScrollList';

type TaskStatus = 'initial' | 'processing' | 'completed';

interface Task {
  /** 任务描述 */
  desc: null | string;
  /** 关联AI员工ID */
  robotId: string;
  robot: {
    desc: string;
    name: string;
    avatar: string;
  };
  /** 错误原因 */
  failedReason: null | string;
  /** 主键ID */
  id: string;
  /** 任务名称 */
  name: string;
  /** 任务状态：initial(待启动)、processing(进行中)、completed(已结束)  */
  status: TaskStatus;
  /** 任务类型: inbound（呼入）、outbound（呼出）、all（呼入+呼出）,老数据："in" | "out" */
  type: 'inbound' | 'outbound' | 'all' | 'in' | 'out';
  /** 背景图片，用于呼入端UI使用 */
  bgUrl?: string | null;
  /** 坐席号码，多个按英文逗号分隔 */
  callerNumber?: string;
  /** 联系人名单 */
  contacts?: {
    /** 联系人号码 */
    phone: string;
    /** 联系人姓名 */
    name?: string;
  }[];
  /** 任务开始时间，示例：2025-03-10T09:33:08Z */
  beginTime: string;
  /** 任务结束时间 */
  endTime: string;
  uuid: string;
  /** 是否已沟通 */
  hasConversation: boolean;
  inboundConfig?: {
    holdTalkTimeout?: number;
    showAudioWave?: boolean;
  };
}

const { Content } = Layout;
const { Title, Text } = Typography;

const StatusMap: {
  [key in Task['status']]: {
    color: string;
    text: string;
    btn: {
      text: string | null;
      type: ButtonProps['type'];
      /** 拨打｜回顾 */
      action: 'call' | 'review';
    };
  };
} = {
  initial: {
    color: '#FBBC05',
    text: '待完成',
    btn: {
      text: '立即访谈',
      type: 'primary',
      action: 'call',
    },
  },
  processing: {
    color: '#00A6FF',
    text: '进行中',
    btn: {
      text: '继续访谈',
      type: 'primary',
      action: 'call',
    },
  },
  completed: {
    color: '#979797',
    text: '已完成',
    btn: {
      // text: '访谈回顾',
      text: null,
      type: 'default',
      action: 'review',
    },
  },
};

const getStatus = (task: Task) => {
  if (task.status === 'processing') {
    if (task.hasConversation) {
      return StatusMap.processing;
    }
    return StatusMap.initial;
  }
  return StatusMap.completed;
};

const TaskList = () => {
  const navigate = useNavigate();
  const user = getUserInfoFromStorage();
  const userId = user?.jobNumber || '';
  return (
    <Layout style={{ height: '100vh' }}>
      <HeaderLogo />
      <Content style={window.isPCPlatform ? { padding: '80px 50px 30px' } : { padding: '16px', paddingTop: '120px' }}>
        {window.isPCPlatform && (
          <>
            <Title level={2}>智能访谈</Title>
            <Text type="secondary">查看你的全部待完成及已完成访谈</Text>
          </>
        )}
        <ScrollList<Task>
          getList={(data) =>
            TaskService.get({
              page: data.page ?? 1,
              pageSize: data.pageSize ?? 10,
              userId,
            })
          }
          renderItem={(item) => {
            const { color, text, btn } = getStatus(item);
            const { text: btnTxt, type: btnType, action } = btn;

            return (
              <Col key={item.id} span={window.isPCPlatform ? 8 : 24}>
                <Card variant="borderless" styles={{ body: window.isPCPlatform ? undefined : { padding: '10px' } }}>
                  <Row>
                    <Avatar src={item.robot?.avatar} />
                  </Row>
                  <BadgeTitle title={item.name} badge={{ color, text }} desc={item.desc ?? ''} />
                  <Divider />
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Text type="secondary">访谈助手：{item.robot?.name}</Text>
                    <Row>
                      <Col span={window.isPCPlatform ? 12 : 24}>
                        <Text type="secondary">下发时间：{item.beginTime ? moment(item.beginTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Text>
                      </Col>
                      <Col span={window.isPCPlatform ? 12 : 24}>
                        <Text type="secondary">截止时间：{item.endTime ? moment(item.endTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Text>
                      </Col>
                    </Row>
                    {btnTxt && (
                      <Row justify="end">
                        <Button
                          type={btnType}
                          onClick={() => {
                            pageData.bgUrl = item.bgUrl ?? null;
                            pageData.uuid = item.uuid;
                            pageData.holdTalkTimeout = item.inboundConfig?.holdTalkTimeout ?? 60;
                            pageData.showAudioWave = item.inboundConfig?.showAudioWave ?? false;
                            pageData.taskInfo = item;
                            navigate(`/${window.isPCPlatform ? 'pc_room' : 'mobile_room'}/${item.uuid}?mode=${action}`);
                          }}
                        >
                          {btnTxt}
                        </Button>
                      </Row>
                    )}
                  </Space>
                </Card>
              </Col>
            );
          }}
        />
      </Content>
    </Layout>
  );
};

export default TaskList;

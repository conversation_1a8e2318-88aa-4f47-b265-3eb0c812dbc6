.root {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
.bg {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
}
.joinedBg {
  position: relative;
}

.joinedBg::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4); // 灰色半透明蒙版
  backdrop-filter: blur(6px) saturate(180%);
  pointer-events: none;
  z-index: 1;
}

.callIcon {
  width: 60px;
  height: 60px;
}
.callIconContainer {
  margin-bottom: 20px;
  display: flex;
  gap: 32px;
  justify-content: center;
}

.historyMsgScroll {
  flex: 1;
  height: calc(100vh - 130px);
  overflow-y: auto;
  width: 100%;
  margin-bottom: 20px;
  margin-top: 10px;
}

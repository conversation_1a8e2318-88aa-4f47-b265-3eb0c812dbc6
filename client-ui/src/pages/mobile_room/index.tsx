import { ReactComponent as HangupIcon } from '@/assets/img/handup.svg';
import { ReactComponent as MicCloseSVG } from '@/assets/img/MicClose.svg';
import { ReactComponent as MicOpenSVG } from '@/assets/img/MicOpen.svg';
import EmployeeInfoPanel from '@/components/EmployeeInfoPanel';
import HandUpConfirmModal from '@/components/HandUpConfirmModal';
import HistoryMsg from '@/components/HistoryMsg';
import { useDeviceState, useJoin, useLeave } from '@/lib/useCommon';
import { RootState } from '@/store';
import { Button, message } from 'antd';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import styles from './index.module.less';

const MobileRoom = () => {
  const { isAudioPublished, switchMic } = useDeviceState();
  const [joining, dispatchJoin] = useJoin();
  const leaveRoom = useLeave();
  const isJoined = useSelector((state: RootState) => state.room.isJoined);
  const [loading, setLoading] = useState(false);
  const searchParams = new URLSearchParams(location.search);
  const [onlyHistory, setOnlyHistory] = useState(searchParams.get('mode') === 'review');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    return () => {
      leaveRoom();
    };
  }, []);

  // 拨打
  const handleJoinRoom = async () => {
    if (joining || loading) return;
    setLoading(true);
    try {
      const res = await dispatchJoin();
      if (typeof res === 'object' && 'error' in res && res.error) {
        message.error(res.error);
      }
    } catch (e) {
      message.error('进入房间失败');
    } finally {
      setLoading(false);
    }
  };

  // 挂断
  const handleLeaveRoom = async () => {
    setLoading(true);
    try {
      await leaveRoom();
      setOpen(true);
    } catch (e) {
      message.error('挂断失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换麦克风
  const handleSwitchMic = async () => {
    try {
      await switchMic(true);
    } catch (e) {
      message.error('切换麦克风失败');
    }
  };

  return (
    <div className={styles.root}>
      <div className={`${styles.bg} ${isJoined ? styles.joinedBg : ''}`} style={pageData.bgUrl ? { backgroundImage: `url(${pageData.bgUrl})` } : {}}>
        {isJoined || open || onlyHistory ? (
          <div style={{ position: 'relative', zIndex: 2 }}>
            <div className={styles.historyMsgScroll}>
              <HistoryMsg />
            </div>
            {!onlyHistory && (
              <div className={styles.callIconContainer}>
                <span onClick={handleSwitchMic} style={{ cursor: 'pointer' }}>
                  {isAudioPublished ? <MicOpenSVG className={styles.callIcon} /> : <MicCloseSVG className={styles.callIcon} />}
                </span>
                <HangupIcon className={styles.callIcon} onClick={handleLeaveRoom} />
              </div>
            )}
          </div>
        ) : (
          <EmployeeInfoPanel
            extra={
              <Button type="primary" block loading={loading} onClick={handleJoinRoom} style={{ margin: '20px 0' }}>
                我准备好了
              </Button>
            }
          />
        )}
      </div>
      {open && <HandUpConfirmModal open={open} setOpen={setOpen} setOnlyHistory={setOnlyHistory} />}
    </div>
  );
};
export default MobileRoom;

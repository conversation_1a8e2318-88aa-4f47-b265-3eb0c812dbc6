.root {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(30, 30, 30, 0.7);
  z-index: 2;
}
.pageWrap {
  position: relative;
  z-index: 3;
  margin: 70px 40px 40px;
  overflow: hidden;
}
.headerRow {
  background-color: #fff;
  width: 100%;
  padding: 16px 24px;
}
.contentWrap {
  display: flex;
  height: calc(100vh - 190px);
}
.callIcon {
  cursor: pointer;
  width: 70px;
  height: 70px;
}
.sider {
  width: 40%;
  background: transparent;
  position: relative;
}
.siderBg {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  flex: 1;
  background: #fafafc;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-end;
  position: relative;
  overflow: hidden;
  padding: 20px;
  padding-right: 40px;
}

.interviewBox {
  text-align: center;
  left: 0;
  right: 0;
  margin-top: 16px;
}

.readyText {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 32px;
}

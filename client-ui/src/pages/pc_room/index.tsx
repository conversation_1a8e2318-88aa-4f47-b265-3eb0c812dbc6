import { ReactComponent as CallIcon } from '@/assets/img/call.svg';
import { ReactComponent as HangupIcon } from '@/assets/img/handup.svg';
import logo from '@/assets/img/logo.png';
import { ReactComponent as MicCloseSVG } from '@/assets/img/MicClose.svg';
import { ReactComponent as MicOpenSVG } from '@/assets/img/MicOpen.svg';
import EmployeeInfoPanel from '@/components/EmployeeInfoPanel';
import HandUpConfirmModal from '@/components/HandUpConfirmModal';
import HeaderLogo from '@/components/HeaderLogo';
import HistoryMsg from '@/components/HistoryMsg';
import { useDeviceState, useJoin, useLeave } from '@/lib/useCommon';
import { RootState } from '@/store';
import { Row, Spin, message } from 'antd';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import styles from './index.module.less';

const PCRoom = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const { isAudioPublished, switchMic } = useDeviceState();
  const [joining, dispatchJoin] = useJoin();
  const leaveRoom = useLeave();
  const isJoined = useSelector((state: RootState) => state.room.isJoined);
  const [loading, setLoading] = useState(false);

  const [onlyHistory, setOnlyHistory] = useState(searchParams.get('mode') === 'review');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    return () => {
      leaveRoom();
    };
  }, []);

  // 拨打
  const handleJoinRoom = async () => {
    if (joining || loading) return;
    setLoading(true);
    try {
      const res = await dispatchJoin();
      if (typeof res === 'object' && 'error' in res && res.error) {
        message.error(res.error);
      }
    } catch (e) {
      message.error('进入房间失败');
    } finally {
      setLoading(false);
    }
  };

  // 挂断
  const handleLeaveRoom = async () => {
    setLoading(true);
    try {
      await leaveRoom();
      setOpen(true);
    } catch (e) {
      message.error('挂断失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换麦克风
  const handleSwitchMic = async () => {
    try {
      await switchMic(true);
    } catch (e) {
      message.error('切换麦克风失败');
    }
  };

  return (
    <div className={styles.root}>
      <div className={styles.mask} />
      <HeaderLogo />
      <div className={styles.pageWrap}>
        <Row className={styles.headerRow} justify="center">
          <img src={logo} alt="logo" style={{ height: '70px', position: 'absolute', left: '20px' }} />
          <span style={{ fontSize: 34, fontWeight: 600, lineHeight: '70px' }}>2025 年度岗位职责采集</span>
        </Row>
        <div className={styles.contentWrap}>
          <div className={styles.sider}>
            <div className={styles.siderBg} style={pageData.bgUrl ? { backgroundImage: `url(${pageData.bgUrl})` } : {}}>
              <EmployeeInfoPanel />
            </div>
          </div>
          <div className={styles.content}>
            <HistoryMsg />
            {!onlyHistory && (
              <div className={styles.interviewBox}>
                {!isJoined && !open ? (
                  <>
                    <div className={styles.readyText}>准备好了, 立即开始访谈</div>
                    {loading ? <Spin /> : <CallIcon className={styles.callIcon} onClick={handleJoinRoom} />}
                  </>
                ) : (
                  <div style={{ display: 'flex', gap: 32, justifyContent: 'center' }}>
                    <span onClick={handleSwitchMic} style={{ cursor: 'pointer' }}>
                      {isAudioPublished ? <MicOpenSVG className={styles.callIcon} /> : <MicCloseSVG className={styles.callIcon} />}
                    </span>
                    <HangupIcon className={styles.callIcon} onClick={handleLeaveRoom} />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      {open && <HandUpConfirmModal open={open} setOpen={setOpen} setOnlyHistory={setOnlyHistory} />}
    </div>
  );
};
export default PCRoom;

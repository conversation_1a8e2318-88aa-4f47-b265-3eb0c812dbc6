.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.header {
  flex-shrink: 0;
  height: 25vh;
}

.content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 60vh;
}

.footer {
  flex-shrink: 0;
  height: 15vh;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logoIcon {
  width: 200px;
  height: 100px;
}

.audioWave {
  display: flex;
  justify-content: center;
}

.audioWave img {
  width: 250px;
}

.subtitleContainer {
  position: relative;
  padding: 0 10px;
  overflow: hidden;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}


.subtitleMessage {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 20px;
  opacity: 0.75;
  transition: opacity 0.3s ease;
}

.userMessage {
  align-self: flex-end;
  color: white;
  background-color: #1a73e8;
  border-radius: 18px;
  border-top-right-radius: 4px;
  margin-right: 5px;
}

.systemMessage {
  align-self: flex-start;
  color: white;
  background-color: rgba(70, 70, 70, 0.95);
  border-radius: 18px;
  border-top-left-radius: 4px;
  margin-left: 5px;
}

.callControls {
  display: flex;
  justify-content: center;
  padding: 20px;
  gap: 80px;
}

.controlBtn {
  width: 70px;
  height: 70px;
  border-radius: 50% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
}

.controlBtnImg {
  border-radius: 50% !important;
  width: 70px;
  height: 70px;
}

@media (max-width: 768px) {
  .controlBtn {
    -webkit-tap-highlight-color: transparent;
  }
  .controlBtn:active {
    opacity: 0.8;
  }
}

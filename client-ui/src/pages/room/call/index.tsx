import AudioWave from '@/assets/img/AudioWave.gif';
import LeaveRoomSVG from '@/assets/img/LeaveRoom.svg';
import MicCloseSVG from '@/assets/img/MicClose.svg';
import MicOpenSVG from '@/assets/img/MicOpen.svg';
import PhoneSVG from '@/assets/img/Phone.svg';
import styles from './index.module.less';

import { useDeviceState, useJoin, useLeave } from '@/lib/useCommon';
import { RootState } from '@/store';
import { getUserInfoFromStorage } from '@/utils/user';
import { Affix, Layout, Message } from '@arco-design/web-react';
import { useCallback, useEffect, useLayoutEffect, useRef } from 'react';
import { useSelector } from 'react-redux';

function Room() {
  const { isAudioPublished, switchMic } = useDeviceState();
  const [joining, dispatchJoin] = useJoin();
  const leaveRoom = useLeave();
  const room = useSelector((state: RootState) => state.room);
  const msgHistory = useSelector((state: RootState) => state.room.msgHistory);
  const isJoined = room.isJoined;
  // const userId = Config.BaseConfig.UserId;
  const user = getUserInfoFromStorage();
  const userId = user?.jobNumber;
  const subtitleContainer = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const lastTalkTimestamp = useCallback((state: RootState) => state.room.lastTalkTimestamp, []);
  const lastTalkTimestampUse = useSelector(lastTalkTimestamp);
  const lastTalkTimestampRef = useRef(lastTalkTimestampUse);
  const showAudioWave = window.pageData?.showAudioWave || false;
  const holdTalkTimeout = window.pageData?.holdTalkTimeout || 60;

  const isRobot = (uid: string) => uid.startsWith(`voiceChat_`);

  useEffect(() => {
    const container = subtitleContainer.current;
    if (!container) return;

    const handleScroll = () => {
      const messages = container.querySelectorAll(`.${styles.subtitleMessage}`);
      const scrollTop = container.scrollTop;
      const containerHeight = container.clientHeight;
      const scrollBottom = scrollTop + containerHeight;

      const fadeHeight = 66;
      const minOpacity = 0.1;
      const defaultOpacity = 0.75;

      messages.forEach((msg: Element, index: number) => {
        const msgElement = msg as HTMLElement;
        const msgOffsetTop = msgElement.offsetTop;
        const msgHeight = msgElement.offsetHeight;
        const msgOffsetBottom = msgOffsetTop + msgHeight;

        const distanceFromTop = msgOffsetTop - scrollTop;
        const distanceFromBottom = scrollBottom - msgOffsetBottom;

        let opacity = defaultOpacity;

        if (distanceFromTop < fadeHeight) {
          const topOpacity = distanceFromTop / fadeHeight;
          opacity = minOpacity + (defaultOpacity - minOpacity) * topOpacity;
        } else if (distanceFromBottom < fadeHeight) {
          const bottomOpacity = distanceFromBottom / fadeHeight;
          opacity = minOpacity + (defaultOpacity - minOpacity) * bottomOpacity;
        }
        if (index === messages.length - 1) {
          opacity = defaultOpacity;
        }

        msgElement.style.opacity = opacity.toString();
      });
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll();
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [msgHistory.length]);

  useEffect(() => {
    if (msgHistory.length > 0) {
      const lastMsg = msgHistory[msgHistory.length - 1];
      if (isRobot(lastMsg.user) && lastMsg.value?.includes('【结束通话】')) {
        setTimeout(() => {
          leaveRoom();
        }, 1500);
      }
    }
  }, [msgHistory]);

  useEffect(() => {
    lastTalkTimestampRef.current = lastTalkTimestampUse;
  }, [lastTalkTimestampUse]);

  useEffect(() => {
    if (!isJoined) return;
    const timerId = setInterval(() => {
      // console.log('定时器运行中，当前计数:', lastTalkTimestampRef.current);
      const curTimestamp = new Date().getTime();
      // 超过60秒未对话，则结束通话
      if (curTimestamp - lastTalkTimestampRef.current > holdTalkTimeout * 1000) {
        leaveRoom();
        Message.info('超时未对话，结束通话');
      }
    }, 2000);
    return () => {
      clearInterval(timerId);
      // console.log('定时器已清除');
    };
  }, [isJoined]);

  useLayoutEffect(() => {
    const container = messagesEndRef.current;
    if (container) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [msgHistory.length]);

  const handleJoinRoom = () => {
    if (!joining) {
      dispatchJoin();
    }
  };
  return (
    <Layout className={styles.container}>
      <Layout.Header>
        <div className={styles.header}>
          {/* <div className={styles.logo}> */}
          {/*  <img src={Logo} alt="Logo" className={styles.logoIcon} /> */}
          {/* </div> */}
          {isJoined && showAudioWave ? (
            <div className={styles.audioWave}>
              <img src={AudioWave} />
            </div>
          ) : null}
        </div>
      </Layout.Header>
      <Layout.Content className={styles.content}>
        <div className={styles.subtitleContainer} ref={subtitleContainer}>
          {msgHistory.map(({ value, user, isInterrupted }, index) => {
            const isUserMsg = user === userId;
            const isRobotMsg = isRobot(user);
            if (!isUserMsg && !isRobotMsg) {
              return '';
            }
            return (
              <div className={`${styles.subtitleMessage} ${styles[isUserMsg ? 'userMessage' : 'systemMessage']}`} key={index}>
                {value}
              </div>
            );
          })}
          <div ref={messagesEndRef} style={{ marginTop: '50px' }} />
        </div>
      </Layout.Content>
      <Layout.Footer className={styles.footer}>
        <Affix offsetBottom={10}>
          <div className={styles.callControls}>
            {isJoined ? (
              <>
                <button className={styles.controlBtn} onClick={() => switchMic(true)}>
                  <img src={isAudioPublished ? MicOpenSVG : MicCloseSVG} className={styles.controlBtnImg} />
                </button>
                <button className={styles.controlBtn} onClick={leaveRoom}>
                  <img src={LeaveRoomSVG} className={styles.controlBtnImg} />
                </button>
              </>
            ) : (
              <button className={`${styles.controlBtn}`} style={{ backgroundColor: '#333' }} onClick={handleJoinRoom}>
                <img src={PhoneSVG} />
              </button>
            )}
          </div>
        </Affix>
      </Layout.Footer>
    </Layout>
  );
}

export default Room;

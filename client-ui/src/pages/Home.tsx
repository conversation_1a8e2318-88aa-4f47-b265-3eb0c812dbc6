import { ReactComponent as Loading } from '@/assets/img/Loading.svg';
import ErrorContainer from '@/components/ErrorContainer';
import { setUserInfo } from '@/store/slices/dd_user';
import { getUserInfoFromStorage, setUserInfoToStorage } from '@/utils/user';
import * as dd from 'dingtalk-jsapi';
import { FC, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';

const Home: FC<any> = ({ children }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const defaultPage = location.hash.slice(1) || '/task';
    const user = getUserInfoFromStorage();
    if (pageData.userId) {
      setUserInfoToStorage({
        jobNumber: pageData.userId,
      });
      navigate(defaultPage);
    } else if (user?.jobNumber) {
      navigate(defaultPage);
    } else if (dd.env.platform === 'notInDingTalk') {
      setError('请在钉钉中打开');
    } else if (!dd.requestAuthCode) {
      setError('钉钉版本过低，请更新');
    } else {
      dd.requestAuthCode({
        corpId: pageData.corpId,
        clientId: pageData.clientId,
        onSuccess: (result: { code: string }) => {
          const code = result.code;
          if (!code) {
            setError('未获取到钉钉免登code');
            return;
          }
          setLoading(true);
          fetch(`/pgw/api/dingtalk/userinfo?code=${code}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({}),
          })
            .then((res) => res.json())
            .then((res) => {
              setLoading(false);
              if (res.success && res.data?.userId) {
                dispatch(setUserInfo(res.data));
                setUserInfoToStorage(res.data);
                navigate(defaultPage);
              } else {
                setError(res.error?.message || '获取用户信息失败');
              }
            })
            .catch(() => {
              setLoading(false);
              setError('获取用户信息失败');
            });
        },
        onFail: (err: any) => {
          setError(err?.message || err?.errorMessage || '获取授权码失败');
        },
      });
    }
  }, []);

  if (error) {
    return <ErrorContainer title={error} />;
  }
  if (!children || loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <ErrorContainer title={<Loading style={{ width: window.isPCPlatform ? '220px' : '78px' }} />} hasMask={false} showIcon={false} />
      </div>
    );
  }
  return <div>{children}</div>;
};
export default Home;

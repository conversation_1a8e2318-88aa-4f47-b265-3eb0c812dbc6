import '@arco-design/web-react/dist/css/arco.css';
import * as dd from 'dingtalk-jsapi';
import { ENV_ENUM } from 'dingtalk-jsapi/lib/env';
import { useEffect } from 'react';
import { HashRouter, Route, Routes } from 'react-router-dom';
import Home from './pages/Home';
import MobileRoom from './pages/mobile_room';
import PcRoom from './pages/pc_room';
import Room from './pages/room';
import TaskList from './pages/task';

declare global {
  interface Window {
    DDPlatform?: string;
    isPCPlatform?: boolean;
  }
}

function App() {
  useEffect(() => {
    window.DDPlatform = dd.env.platform;
    window.isPCPlatform = location.hostname === 'localhost' ? window.innerWidth > 500 : dd.env.platform === ENV_ENUM.pc;
  }, []);
  return (
    <HashRouter>
      <Routes>
        <Route path="/">
          <Route index element={<Home />} />
          <Route path="/*" element={<Home />} />
          <Route
            path="/task"
            element={
              <Home>
                <TaskList />
              </Home>
            }
          />
          <Route
            path="/mobile_room/*"
            element={
              <Home>
                <MobileRoom />
              </Home>
            }
          />
          <Route
            path="/pc_room/*"
            element={
              <Home>
                <PcRoom />
              </Home>
            }
          />
          <Route
            path="/room/*"
            element={
              <Home>
                <Room />
              </Home>
            }
          />
        </Route>
      </Routes>
    </HashRouter>
  );
}

export default App;

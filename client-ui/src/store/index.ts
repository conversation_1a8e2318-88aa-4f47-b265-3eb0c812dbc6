import { configureStore } from '@reduxjs/toolkit';
import ddUserSlice, { DDUserState } from './slices/dd_user';
import deviceSlice, { DeviceState } from './slices/device';
import roomSlice, { RoomState } from './slices/room';

export interface RootState {
  room: RoomState;
  device: DeviceState;
  ddUser: DDUserState;
}

const store = configureStore({
  reducer: {
    room: roomSlice,
    device: deviceSlice,
    ddUser: ddUserSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export default store;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface DDUserInfo {
  deptId: string | null;
  deptName: string | null;
  jobNumber: string | null;
  position: string | null;
  title: string | null;
  userId: string | null;
  name: string | null;
}

export interface DDUserState {
  userInfo: DDUserInfo;
}

const initialState: DDUserState = {
  userInfo: {
    deptId: null,
    deptName: null,
    jobNumber: null,
    position: null,
    title: null,
    userId: null,
    name: null,
  },
};

const ddUserSlice = createSlice({
  name: 'ddUser',
  initialState,
  reducers: {
    setUserInfo: (state, action: PayloadAction<DDUserInfo>) => {
      state.userInfo = action.payload;
    },
    clearUserInfo: (state) => {
      state.userInfo = initialState.userInfo;
    },
  },
});

export const { setUserInfo, clearUserInfo } = ddUserSlice.actions;
export default ddUserSlice.reducer;

const CracoLessPlugin = require('craco-less');
// const CompressionWebpackPlugin = require('compression-webpack-plugin');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const path = require('path');

module.exports = {
  devServer: {
    port: 3003,
    proxy: {
      '/pgw': {
        // target: 'http://host.docker.internal:8080',
        target: 'http://rtc-mgt-test.mengniu.cn',
        changeOrigin: true,
      },
    },
  },
  webpack: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
    configure: (webpackConfig, { env }) => {
      if (env === 'production') {
        // 禁止生成许可证文件(*.LICENSE.txt)
        const terserPlugin = webpackConfig.optimization.minimizer.find((plugin) => plugin.constructor.name === 'TerserPlugin');
        if (terserPlugin) {
          terserPlugin.options.extractComments = false;
        }
        webpackConfig.externals = {
          ...webpackConfig.externals,
          '@volcengine/rtc': 'VERTC',
        };
      }

      webpackConfig.plugins = webpackConfig.plugins.filter((plugin) => plugin.constructor.name !== 'HtmlWebpackPlugin');
      const templatePath = env === 'production' ? 'public/index.html' : 'public/index_dev.html';
      webpackConfig.plugins.push(
        new HtmlWebpackPlugin({
          template: templatePath,
          filename: 'index.html',
        })
      );

      return webpackConfig;
    },
  },
  plugins: [
    {
      plugin: CracoLessPlugin,
      options: {
        lessLoaderOptions: {
          lessOptions: {
            javascriptEnabled: true,
          },
        },
      },
    },
  ],
};

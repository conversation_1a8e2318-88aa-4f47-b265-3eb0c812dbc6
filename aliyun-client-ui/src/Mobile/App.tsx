import { getCallAgentId, getRuntimeConfig } from "@/interface.ts";
import runUserConfig from "@/runConfig.ts";
import { JSONObject } from "@/service/interface.ts";
import service from "@/service/standard";
import {
  AICallAgentType,
  AICallTemplateConfig,
  AIChatAgentType,
  AIChatTemplateConfig,
} from "aliyun-auikit-aicall";
import { Toast } from "antd-mobile";
import { ClientJS } from "clientjs";
import { useEffect, useState } from "react";
import "./App.css";
import Call from "./Call";
import { Phone } from "./Call/Icons";

Toast.config({
  position: "bottom",
});

interface AppProps {
  userId?: string;
  userToken?: string;

  shareToken?: string;
  appServer?: string;
  region?: string;

  agentType?: AICallAgentType | AIChatAgentType;
  agentId?: string;

  userData?: string | JSONObject;
  templateConfig?: AICallTemplateConfig | AIChatTemplateConfig;

  onAuthFail?: () => void;
}

function App(props: AppProps) {
  const runConfig = getRuntimeConfig(runUserConfig);
  const {
    //TODO:
    userId = new ClientJS().getFingerprint().toString(),
    userToken = "YourToken",
    shareToken,
    appServer = runConfig.appServer,
    region = runConfig.region,
    onAuthFail,
    agentId,
    userData,
    templateConfig,
  } = props;
  const [isCalling, setIsCalling] = useState(false);

  useEffect(() => {
    if (runConfig.appServer) {
      service.setAppServer(runConfig.appServer);
    }

    const preventContextMenu = function (e: Event) {
      e.preventDefault();
    };
    // 禁用右键菜单
    // disable context menu
    document.addEventListener("contextmenu", preventContextMenu);
    return () => {
      document.removeEventListener("contextmenu", preventContextMenu);
    };
  }, []);

  const bgUrl = pageData.bgUrl;

  const style = bgUrl
    ? {
        backgroundImage: `url(${bgUrl})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "100% auto",
      }
    : {
        backgroundColor: "#000",
      };

  if (!isCalling)
    return (
      <div className="app" style={style}>
        <div className="controlBtn" onClick={() => setIsCalling(true)}>
          {Phone}
        </div>
      </div>
    );

  return (
    <div className="app" style={style}>
      <Call
        rc={runConfig}
        userId={userId}
        userToken={userToken}
        agentType={AICallAgentType.VoiceAgent}
        shareToken={shareToken}
        agentId={
          agentId || getCallAgentId(runConfig, AICallAgentType.VoiceAgent)
        }
        appServer={appServer}
        region={region}
        userData={
          typeof userData === "object"
            ? JSON.stringify(userData)
            : userData || runConfig.callUserData
        }
        templateConfig={
          templateConfig instanceof AICallTemplateConfig
            ? templateConfig
            : runConfig.callTemplateConfig
        }
        onExit={() => {
          setIsCalling(false);
        }}
        onAuthFail={() => {
          onAuthFail?.();
        }}
      />
    </div>
  );
}

export default App;

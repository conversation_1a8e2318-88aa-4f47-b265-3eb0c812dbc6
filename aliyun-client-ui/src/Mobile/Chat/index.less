.chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-image: url(https://img.alicdn.com/imgextra/i1/O1CN01PJOg1K1aEXFhTKDeU_!!6000000003298-0-tps-1125-2436.jpg);
  background-size: 100% auto;
  background-position: top center;
  background-repeat: no-repeat;
  background-color: #f7f6fe;
  min-width: 320px;

  * {
    -webkit-user-select: none; /* For Safari and Chrome */
    -moz-user-select: none; /* For Firefox */
    -ms-user-select: none; /* For Internet Explorer/Edge */

    -webkit-touch-callout: none; /* Disable callout, browser's default context menu */
    user-select: none; /* For modern browsers */
  }

  input,
  input:before,
  input:after,
  textarea,
  textarea:before,
  textarea:after {
    -webkit-user-select: initial;
    -khtml-user-select: initial;
    -moz-user-select: initial;
    -ms-user-select: initial;
    user-select: initial;
  }

  .adm-safe-area-position-bottom {
    background-color: #f7f6fe;
  }
}

export const backSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='26'
    height='26'
    viewBox='0 0 26 26'
  >
    <g>
      <path
        d='M14.957071301879882,20.074462381744382L7.882631301879883,13.000002381744384L14.957071301879882,5.925562381744385L16.10594130187988,7.074432381744385L10.180381301879883,13.000002381744384L16.10594130187988,18.925562381744385L14.957071301879882,20.074462381744382Z'
        fillRule='evenodd'
        fill='#2C2C36'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const settingSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='18'
    height='18'
    viewBox='0 0 18 18'
  >
    <g>
      <path
        d='M15.187476001358032,8.999047978286743C15.187476001358032,9.145297978286743,15.176276001358032,9.285917978286744,15.164976001358033,9.426547978286743L16.250576001358034,10.382787978286743C16.649976001358034,10.737167978286744,16.751276001358033,11.322117978286743,16.481276001358033,11.789017978286743L15.153776001358032,14.089617978286743C14.951276001358032,14.444017978286743,14.574376001358033,14.652117978286743,14.180576001358032,14.652117978286743C14.062476001358032,14.652117978286743,13.938776001358033,14.629717978286743,13.820576001358033,14.590317978286743L12.459376001358033,14.129017978286743C12.223076001358033,14.286517978286744,11.969976001358033,14.427217978286743,11.711276001358032,14.550917978286742L11.429976001358032,15.968417978286743C11.323126001358032,16.497117978286745,10.861876001358032,16.874017978286744,10.327496001358032,16.874017978286744L7.672496001358033,16.874017978286744C7.1381260013580325,16.874017978286744,6.676876001358032,16.491517978286744,6.5699960013580325,15.968417978286743L6.2887460013580325,14.550917978286742C6.0356260013580325,14.427217978286743,5.788126001358032,14.280917978286743,5.551876001358032,14.123417978286744L4.184996001358032,14.584717978286744C4.0668760013580325,14.629717978286743,3.9431260013580323,14.646517978286743,3.8249960013580324,14.646517978286743C3.431246001358032,14.646517978286743,3.0599960013580323,14.438417978286743,2.851876001358032,14.084017978286743L1.5243740013580322,11.783417978286742C1.2543740013580322,11.322117978286743,1.3499989013580322,10.731537978286743,1.754999001358032,10.377167978286742L2.834996001358032,9.426547978286743C2.823746001358032,9.280287978286744,2.8181260013580323,9.139667978286743,2.8181260013580323,8.993417978286743C2.8181260013580323,8.847167978286743,2.8293760013580322,8.706537978286743,2.8406260013580322,8.565917978286743L1.754999001358032,7.609667978286743C1.3556239013580322,7.255297978286743,1.2543740013580322,6.670297978286743,1.5243740013580322,6.203417978286743L2.851876001358032,3.902787978286743C3.0543760013580323,3.5484179782867433,3.431246001358032,3.340287978286743,3.8249960013580324,3.340287978286743C3.9431260013580323,3.340287978286743,4.0668760013580325,3.362787978286743,4.184996001358032,3.402167978286743L5.546246001358032,3.8634179782867433C5.782496001358032,3.705917978286743,6.0356260013580325,3.5652879782867433,6.2943760013580325,3.441547978286743L6.5756260013580325,2.0240429782867433C6.682496001358032,1.4952929782867432,7.143746001358032,1.1184179782867432,7.678126001358033,1.1184179782867432L10.333126001358032,1.1184179782867432C10.867496001358033,1.1184179782867432,11.328746001358033,1.5009179782867432,11.435576001358033,2.0240429782867433L11.716876001358033,3.441547978286743C11.969976001358033,3.5652879782867433,12.217476001358031,3.711547978286743,12.453776001358031,3.8690379782867432L13.820576001358033,3.407797978286743C13.938776001358033,3.362787978286743,14.062476001358032,3.345917978286743,14.180576001358032,3.345917978286743C14.574376001358033,3.345917978286743,14.945576001358033,3.5540479782867433,15.153776001358032,3.908417978286743L16.481276001358033,6.209047978286743C16.751276001358033,6.670297978286743,16.655576001358032,7.260917978286743,16.250576001358034,7.615297978286743L15.170576001358032,8.565917978286743C15.181876001358033,8.712167978286743,15.187476001358032,8.852797978286743,15.187476001358032,8.999047978286743ZM12.250876001358032,12.870017978286743L14.180276001358033,13.522517978286743L15.507776001358032,11.227517978286743L13.977776001358032,9.883137978286744C14.078976001358033,9.292507978286743,14.073376001358032,8.707507978286742,13.972176001358033,8.122507978286743L15.507776001358032,6.772507978286743L14.180276001358033,4.471887978286743L12.239676001358033,5.130007978286743C11.795276001358033,4.758757978286743,11.277756001358032,4.449387978286744,10.726506001358032,4.246887978286743L10.327126001358032,2.2500079782867433L7.672126001358032,2.2500079782867433L7.272756001358032,4.246887978286743C6.721506001358033,4.443757978286744,6.1983760013580325,4.741887978286743,5.748376001358032,5.124387978286743L3.8190060013580323,4.471887978286743L2.491506001358032,6.772507978286743L4.021506001358032,8.116887978286744C3.920256001358032,8.707507978286742,3.925876001358032,9.292507978286743,4.027126001358033,9.877507978286744L2.491506001358032,11.221917978286744L3.8190060013580323,13.522517978286743L5.759626001358032,12.864417978286744C6.2040060013580325,13.235617978286744,6.721506001358033,13.545017978286744,7.272756001358032,13.747517978286742L7.672126001358032,15.744417978286743L10.327126001358032,15.744417978286743L10.726506001358032,13.747517978286742C11.277756001358032,13.550617978286743,11.800876001358032,13.252517978286743,12.250876001358032,12.870017978286743Z'
        fillRule='evenodd'
        fill='#26244C'
        fillOpacity='1'
      />
    </g>
    <g>
      <path
        d='M5.625,9C5.625,10.89,7.11,12.375,9,12.375C10.89,12.375,12.375,10.89,12.375,9C12.375,7.11,10.89,5.625,9,5.625C7.11,5.625,5.625,7.11,5.625,9ZM6.75,9C6.75,7.717499999999999,7.717499999999999,6.75,9,6.75C10.282499999999999,6.75,11.25,7.717499999999999,11.25,9C11.25,10.282499999999999,10.282499999999999,11.25,9,11.25C7.717499999999999,11.25,6.75,10.282499999999999,6.75,9Z'
        fillRule='evenodd'
        fill='#26244C'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const volumeSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='20'
    height='20'
    viewBox='0 0 20 20'
  >
    <g>
      <g>
        <path
          d='M9.35059,3.4198L9.35059,15.9694C9.07453,15.7849,8.591149999999999,15.4263,7.80344,14.7428C6.91549,13.9724,6.10833,13.1963,5.80561,12.8935C5.5492,12.6371,5.2014499999999995,12.4931,4.83887,12.4931L3.30078,12.4931C2.92385,12.4931,2.61719,12.1864,2.61719,11.8095L2.61719,7.77629C2.61719,7.39936,2.92385,7.0927,3.30078,7.0927L4.83887,7.0927C5.2014700000000005,7.0927,5.54922,6.94866,5.80561,6.69225C5.83787,6.66,5.87479,6.623,5.91595,6.58175C6.3021,6.19477,7.1056,5.38951,7.91316,4.64487C8.62861,3.98517,9.07944,3.61986,9.35059,3.4198ZM9.38477,1.875C8.64857,1.875,5.56233,5.0020500000000006,4.83887,5.72551L3.30078,5.72551C2.168169,5.72551,1.25,6.64368,1.25,7.77629L1.25,11.8095C1.25,12.9421,2.168169,13.8603,3.30078,13.8603L4.83887,13.8603C5.67058,14.692,8.64857,17.4799,9.38477,17.4799C10.12096,17.4799,10.71777,16.8831,10.71777,16.146900000000002L10.71777,3.20801C10.71777,2.471812,10.12096,1.875000166893,9.38477,1.875Z'
          fill='currentColor'
          fillOpacity='1'
        />
      </g>
      <g className='_volume-1'>
        <path
          d='M12.3509,13.8957C12.0503,13.8957,11.7748,13.6958,11.6918,13.3918C11.5925,13.0276,11.8072,12.6518,12.1714,12.5524C13.5803,12.168,14.5643,10.8781,14.5643,9.41555C14.5643,7.92648,13.558,6.63068,12.1171,6.26441C11.7512,6.17139,11.53,5.79938,11.623,5.43346C11.716,5.067550000000001,12.0882,4.8463899999999995,12.454,4.939360000000001C13.4313,5.1878,14.3149,5.76294,14.9422,6.55885C15.2563,6.95735,15.5008,7.40112,15.6691,7.87777C15.8432,8.37097,15.9315,8.888359999999999,15.9315,9.41555C15.9315,10.44909,15.5977,11.42636,14.9661,12.2417C14.3546,13.0311,13.4899,13.6099,12.5312,13.8714C12.4725,13.8875,12.4118,13.8956,12.3509,13.8957Z'
          fill='currentColor'
          fillOpacity='1'
        />
      </g>
      <g className='_volume-2'>
        <path
          d='M13.564115375,15.8285634765625C13.258378375,15.8285634765625,12.980001875,15.6219634765625,12.901798475,15.3120634765625C12.809427875,14.9459634765625,13.031288375,14.5743634765625,13.397369375,14.4819634765625C15.718359375,13.8962634765625,17.339399375,11.8129234765625,17.339399375,9.4155534765625C17.339399375,7.0181934765625,15.718379375,4.9348034765625,13.397386375,4.3491334765625C13.031322375,4.2567634765625,12.809461175,3.8851314765625,12.901831775,3.5190664765625C12.994202375,3.1530194765625,13.365735375,2.9311417765625,13.731903375,3.0234953765625C14.437489375,3.2015374765625,15.105069375,3.4942354765625,15.716049375,3.8934714765625C16.316379375,4.2857334765625,16.846319375,4.7703034765625,17.291139375,5.3337134765625C17.740499375,5.9029134765624995,18.090429375,6.5368234765625,18.331169375,7.2178534765625C18.580279375,7.9225034765625,18.706589375,8.6619134765625,18.706589375,9.415543476562501C18.706589375,10.1691834765625,18.580279375,10.9085734765625,18.331159375,11.6132434765625C18.090399375,12.2942934765625,17.740479375,12.9281834765625,17.291119375,13.4973634765625C16.846309375,14.0608634765625,16.316369375,14.5453634765625,15.716019375,14.9376634765625C15.105039375,15.3368634765625,14.437479375,15.6295634765625,13.731852375,15.8075634765625C13.677021375,15.8214634765625,13.620680375,15.8285634765625,13.564115375,15.8285634765625Z'
          fill='currentColor'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const copySVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='20'
    height='20'
    viewBox='0 0 20 20'
  >
    <g>
      <g>
        <path
          d='M16.136400000000002,14.7396L14.0909,14.7396C13.7159,14.7396,13.4091,14.4349,13.4091,14.0625C13.4091,13.6901,13.7159,13.3854,14.0909,13.3854L15.7955,13.3854C16,13.3854,16.136400000000002,13.25,16.136400000000002,13.0469L16.136400000000002,3.56771C16.136400000000002,3.36458,16,3.22917,15.7955,3.22917L8.97727,3.22917C8.77273,3.22917,8.63636,3.36458,8.63636,3.56771L8.63636,3.90625C8.63636,4.27865,8.329550000000001,4.58333,7.95455,4.58333C7.57955,4.58333,7.27273,4.27865,7.27273,3.90625L7.27273,3.22917C7.27273,2.484375,7.88636,1.875,8.63636,1.875L16.136400000000002,1.875C16.886400000000002,1.875,17.5,2.484375,17.5,3.22917L17.5,13.3854C17.5,14.1302,16.886400000000002,14.7396,16.136400000000002,14.7396ZM12.7273,6.61458L12.7273,16.7708C12.7273,17.5156,12.11364,18.125,11.36364,18.125L3.86364,18.125C3.113636,18.125,2.5000000780279,17.5156,2.5000000780279,16.7708L2.5,6.61458C2.5,5.86979,3.113636,5.26042,3.86364,5.26042L11.36364,5.26042C12.11364,5.26042,12.7273,5.86979,12.7273,6.61458ZM11.02273,6.61458L4.20455,6.61458C4,6.61458,3.86364,6.75,3.86364,6.95312L3.86364,16.432299999999998C3.86364,16.6354,4,16.7708,4.20455,16.7708L11.02273,16.7708C11.22727,16.7708,11.36364,16.6354,11.36364,16.432299999999998L11.36364,6.95312C11.36364,6.75,11.22727,6.61458,11.02273,6.61458Z'
          fill='currentColor'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const transitionSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <g>
        <path
          d='M15.00035480316162,14.500012732543945L14.021494803161621,14.500012732543945L13.29304480316162,12.642862732543946L10.17434480316162,12.642862732543946L9.44588680316162,14.500012732543945L8.467024803161621,14.500012732543945L11.19873480316162,7.535722732543945L12.26865480316162,7.535722732543945L15.00035480316162,14.500012732543945ZM11.73115480316162,8.668412732543946L10.53831480316162,11.714122732543945L12.92400480316162,11.714122732543945L11.73115480316162,8.668412732543946Z'
          fillRule='evenodd'
          fill='#878AAB'
          fillOpacity='1'
        />
      </g>
      <g>
        <path
          d='M2.86692675,10.321463734375L2.86692675,9.857177734375L1.93359375,9.857177734375L1.93359375,10.321463734375C1.93359375,12.118247734375,3.39426375,13.571467734375,5.2002637499999995,13.571467734375L6.60026375,13.571467734375L6.60026375,12.642887734375L5.2002637499999995,12.642887734375C3.91226375,12.642887734375,2.86692675,11.602887734375,2.86692675,10.321463734375Z'
          fill='#878AAB'
          fillOpacity='1'
        />
      </g>
      <g transform='matrix(-1,0,0,-1,29.06640625,12.28564453125)'>
        <path
          d='M15.466536125,6.6071082656249995L15.466536125,6.142822265625L14.533203125,6.142822265625L14.533203125,6.6071082656249995C14.533203125,8.403892265625,15.993873125,9.857112265625,17.799873125,9.857112265625L19.199873125,9.857112265625L19.199873125,8.928532265625L17.799873125,8.928532265625C16.511873125,8.928532265625,15.466536125,7.888532265625,15.466536125,6.6071082656249995Z'
          fill='#878AAB'
          fillOpacity='1'
        />
      </g>
      <g>
        <path
          d='M4.7333300000000005,1.5L3.8,1.5L3.8,2.8928599999999998L1,2.8928599999999998L1,7.07143L3.8,7.07143L3.8,8.92857L4.7333300000000005,8.92857L4.7333300000000005,7.07143L7.53333,7.07143L7.53333,2.8928599999999998L4.7333300000000005,2.8928599999999998L4.7333300000000005,1.5ZM3.8,3.82143L1.933333,3.82143L1.933333,6.14286L3.8,6.14286L3.8,3.82143ZM4.7333300000000005,6.14286L4.7333300000000005,3.82143L6.6,3.82143L6.6,6.14286L4.7333300000000005,6.14286Z'
          fillRule='evenodd'
          fill='#878AAB'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const micSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <g>
      <g>
        <path
          d='M8.2613928125,7.8246573046875L8.2613928125,13.0714373046875C8.2613928125,16.1126373046875,10.726702812500001,18.5779373046875,13.7678128125,18.5779373046875L14.2328528125,18.5779373046875C17.2739328125,18.5779373046875,19.7392328125,16.1126373046875,19.7392328125,13.0714373046875L19.739332812500002,7.8246573046875C19.739332812500002,4.783547304687501,17.2739328125,2.3182373046875,14.2328728125,2.3182373046875L13.7678128125,2.3182373046875C10.726702812500001,2.3182373046875,8.2613928125,4.783547304687501,8.2613928125,7.8246573046875ZM11.1116228125,15.7276373046875Q10.011392812499999,14.6274373046875,10.011392812499999,13.0714373046875L10.011392812499999,7.8246573046875Q10.011392812499999,6.2686973046875,11.1116228125,5.1684673046875Q12.2118528125,4.0682373046875,13.7678128125,4.0682373046875L14.2328728125,4.0682373046875Q15.7888328125,4.0682373046875,16.889032812499998,5.1684673046875Q17.989332812500002,6.2686973046875,17.989332812500002,7.8246573046875L17.9892328125,13.0714373046875Q17.9892328125,14.6274373046875,16.889032812499998,15.7276373046875Q15.7888328125,16.827937304687502,14.2328528125,16.827937304687502L13.7678128125,16.827937304687502Q12.2118528125,16.827937304687502,11.1116228125,15.7276373046875ZM5.9736328125,12.6339373046875C5.4903838125,12.6339373046875,5.0986328125,13.0257373046875,5.0986328125,13.5089373046875Q5.0986328125,16.966837304687502,7.5437328125,19.4119373046875Q9.8732928125,21.7415373046875,13.1221728125,21.8516373046875L13.1221728125,24.7765373046875C13.1221728125,25.2598373046875,13.5139228125,25.6515373046875,13.9971728125,25.6515373046875C14.4804228125,25.6515373046875,14.8721728125,25.2598373046875,14.8721728125,24.7765373046875L14.8721728125,21.8518373046875Q18.125032812500002,21.7437373046875,20.4567328125,19.4119373046875Q22.9018328125,16.966837304687502,22.9018328125,13.5089373046875C22.9018328125,13.0257373046875,22.5101328125,12.6339373046875,22.0268328125,12.6339373046875C21.5436328125,12.6339373046875,21.1518328125,13.0257373046875,21.1518328125,13.5089373046875Q21.1518328125,16.2419373046875,19.2193328125,18.174537304687497Q17.2867328125,20.1070373046875,14.5537228125,20.1070373046875L14.0006228125,20.1070373046875L13.9971728125,20.1070373046875L13.9937128125,20.1070373046875L13.4467428125,20.1070373046875Q10.713712812499999,20.1070373046875,8.7811728125,18.174537304687497Q6.8489628125,16.2423373046875,6.8486328125,13.5098373046875C6.8486328125,13.0257373046875,6.4568828125,12.6339373046875,5.9736328125,12.6339373046875Z'
          fillRule='evenodd'
          fill='#56597E'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const interruptSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <g>
      <path
        d='M13.9999259765625,2.6666259765625C20.2593259765625,2.6666259765625,25.3333259765625,7.7406259765625,25.3333259765625,13.9999259765625C25.3333259765625,20.2593259765625,20.2593259765625,25.3333259765625,13.9999259765625,25.3333259765625C7.7406259765625,25.3333259765625,2.6666259765625,20.2593259765625,2.6666259765625,13.9999259765625C2.6666259765625,7.7406259765625,7.7406259765625,2.6666259765625,13.9999259765625,2.6666259765625ZM15.6666259765625,9.6666259765625L12.3332559765625,9.6666259765625C10.4999959765625,9.6666259765625,9.6666259765625,10.4999959765625,9.6666259765625,12.3332559765625L9.6666259765625,15.6666259765625C9.6666259765625,17.500025976562497,10.4999959765625,18.333225976562503,12.3332559765625,18.333225976562503L15.6666259765625,18.333225976562503C17.500025976562497,18.333225976562503,18.333225976562503,17.500025976562497,18.333225976562503,15.6666259765625L18.333225976562503,12.3332559765625C18.333225976562503,10.4999959765625,17.500025976562497,9.6666259765625,15.6666259765625,9.6666259765625Z'
        fill='#624AFF'
      />
    </g>
  </svg>
);

export const keyboardSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='24'
    height='24'
    viewBox='0 0 24 24'
  >
    <g>
      <g>
        <path
          d='M12.0032,24C8.79447,24,5.78098,22.7521,3.51445,20.4856C1.24792,18.219,0,15.2055,0,11.9969C0,8.78817,1.24792,5.77456,3.51445,3.50803C8.1917,-1.16934,15.8147,-1.16934,20.492,3.50803C25.1693,8.1854,25.1693,15.8083,20.492,20.4856C18.2254,22.7521,15.212,24,12.0032,24L12.0032,24ZM12.0032,1.79324C9.38859,1.79324,6.77415,2.79493,4.78769,4.78127C2.86078,6.70831,1.79966,9.26344,1.79966,11.9968C1.79966,14.7303,2.86078,17.2853,4.78769,19.2123C6.71473,21.1392,9.27827,22.2087,12.0032,22.2003C14.7367,22.2003,17.2917,21.1392,19.2186,19.2123C23.1999,15.231,23.1999,8.76259,19.2186,4.78139C17.2323,2.79493,14.6177,1.79336,12.0032,1.79324L12.0032,1.79324Z'
          fill='#56597E'
        />
      </g>
      <g>
        <path
          d='M5.585001,7L7.002,7C7.34,7,7.6,7.244445,7.6,7.550001L7.6,8.88222C7.6,9.18778,7.34,9.432220000000001,7.015000000000001,9.432220000000001L5.585,9.432220000000001C5.26,9.44444,5,9.2,5,8.89444L5,7.55C5,7.244443,5.26,6.99999887636,5.585001,7ZM10.785,7L12.202,7C12.54,7,12.8,7.244445,12.8,7.550001L12.8,8.88222C12.8,9.18778,12.54,9.432220000000001,12.215,9.432220000000001L10.785,9.432220000000001C10.46,9.44444,10.2,9.2,10.2,8.89444L10.2,7.55C10.2,7.244443,10.46,6.99999887636,10.785,7ZM15.985,7L17.402,7C17.740000000000002,7,18,7.244445,18,7.550001L18,8.88222C18,9.18778,17.740000000000002,9.432220000000001,17.415,9.432220000000001L15.985,9.432220000000001C15.66,9.44444,15.4,9.2,15.4,8.89444L15.4,7.55C15.4,7.244443,15.66,6.99999887636,15.985,7ZM5.585001,10.66667L7.002,10.66667C7.327,10.66667,7.587,10.91111,7.587,11.21667L7.587,12.54889C7.587,12.85444,7.327,13.09889,7.002,13.09889L5.585001,13.09889C5.260001,13.11111,5.000000597572,12.86666,5.000000597572,12.56111L5.000000597572,11.21667C5.000000597572,10.91111,5.260001,10.66666,5.585001,10.66667ZM10.785,10.66667L12.202,10.66667C12.527000000000001,10.66667,12.786999999999999,10.91111,12.786999999999999,11.21667L12.786999999999999,12.54889C12.786999999999999,12.85444,12.527000000000001,13.09889,12.202,13.09889L10.785,13.09889C10.46,13.11111,10.2,12.86666,10.2,12.56111L10.2,11.21667C10.2,10.91111,10.46,10.66666,10.785,10.66667ZM15.985,10.66667L17.402,10.66667C17.727,10.66667,17.987000000000002,10.91111,17.987000000000002,11.21667L17.987000000000002,12.54889C17.987000000000002,12.85444,17.727,13.09889,17.402,13.09889L15.985,13.09889C15.66,13.11111,15.4,12.86666,15.4,12.56111L15.4,11.21667C15.4,10.91111,15.66,10.66666,15.985,10.66667ZM9.875,16.16667L13.125,16.16667C13.658,16.16667,14.1,16.58222,14.1,17.0833C14.1,17.584400000000002,13.658,18,13.125,18L9.875,18C9.341999999999999,18,8.9,17.584400000000002,8.9,17.0833C8.9,16.58222,9.341999999999999,16.16667,9.875,16.16667Z'
          fill='#56597E'
        />
      </g>
    </g>
  </svg>
);

export const callSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <g>
      <path
        d='M17.548000000000002,25C16.261,25.0002,14.9953,24.6712,13.8714,24.0443C9.704080000000001,21.7314,6.2688299999999995,18.2967,3.95569,14.13C2.1729570000000002,10.928550000000001,2.9421111,6.9098500000000005,5.7810500000000005,4.59276L6.99028,3.602478C8.04266,2.7362,9.58132,2.81225,10.54312,3.77808L12.5038,5.72985C13.5361,6.76377,13.5361,8.43827,12.5038,9.472190000000001C12.46249,9.51594,12.41609,9.5546,12.3656,9.587340000000001L10.450980000000001,10.86261C10.029250000000001,11.14687,9.96977,11.74441,10.32718,12.10622L15.8925,17.685200000000002C16.2544,18.0425,16.852,17.9831,17.1363,17.5614L18.4117,15.647C18.4452,15.5971,18.483800000000002,15.5508,18.526899999999998,15.5089C19.5603,14.4751,21.2364,14.4751,22.2698,15.5089L24.2218,17.4578C25.1878,18.4194,25.2638,19.9579,24.3974,21.0101L23.407,22.2192C21.9714,23.9789,19.8201,25,17.548000000000002,25ZM8.66881,4.84896C8.4836,4.84547,8.302769999999999,4.9054,8.15633,5.01881L6.9471,6.0090900000000005C4.79976,7.76298,4.2176100000000005,10.8031,5.56512,13.226C7.71415,17.0977,10.90608,20.2892,14.7783,22.4379C17.2016,23.7853,20.2421,23.2032,21.9963,21.0562L22.9867,19.8442C23.2486,19.5227,23.225,19.0553,22.932,18.7618L20.9713,16.8216C20.6722,16.534599999999998,20.2067,16.5137,19.883,16.7726L18.668,18.5948C17.7394,19.9854,15.7711,20.1798,14.5883,18.9979L9.0143,13.4132C7.8346,12.23075,8.02894,10.26541,9.41738,9.33689L11.23986,8.12207C11.50683,7.80107,11.48563,7.32966,11.19092,7.03391L9.23887,5.08502C9.089179999999999,4.93093,8.88365,4.84375,8.66881,4.84321L8.66881,4.84896Z'
        fill='#56597E'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const sendSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='18'
    height='18'
    viewBox='0 0 18 18'
  >
    <g>
      <path
        d='M14.894968153381347,2.2838352440979004C15.097468153381348,2.2107102440979003,15.333768153381348,2.2613352440979004,15.485568153381347,2.4132105440979004C15.643068153381348,2.5707105440979006,15.693768153381347,2.8013355440979004,15.614968153381348,2.9982105440979003L11.114978153381347,15.373222544097901C11.036228153381348,15.5926225440979,10.828108153381347,15.7388225440979,10.597478153381347,15.744422544097901L10.586228153381347,15.744422544097901C10.355608153381347,15.744422544097901,10.147478153381348,15.6038225440979,10.063108153381346,15.3901225440979L7.903108153381348,9.9900825440979L2.5031061533813475,7.8300825440979C2.2837311533813476,7.7457125440979,2.143105893381348,7.5375825440979005,2.148730896381348,7.3013325440979004C2.154355893381348,7.0707125440979,2.3006061533813478,6.862582544097901,2.5199811533813476,6.7838325440979L14.894968153381347,2.2838352440979004ZM9.000248153381348,9.6974125440979L10.563998153381348,13.6124225440979L14.147168153381347,3.7574125440979005L4.292118153381347,7.3405425440979L8.207118153381348,8.9042925440979L10.923998153381348,6.1874125440979L11.717118153381348,6.980542544097901L9.000248153381348,9.6974125440979Z'
        fillRule='evenodd'
        fill='#FFFFFF'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const resendSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <path
        d='M13.6178,13.6959C10.4719,16.7987,5.40731,16.763,2.30426,13.6174C-0.798791,10.4717,-0.763062,5.40511,2.38287,2.30409C5.52879,-0.798714,10.5934,-0.763008,13.6964,2.38264C16.7977,5.52651,16.7637,10.5931,13.6178,13.6959Z'
        fill='#F53F3F'
        fillOpacity='1'
      />
    </g>
    <g>
      <path
        d='M3.820952892303467,8.42757835998535C4.033539892303467,10.548478359985353,5.823552892303467,12.20520835998535,8.001232892303467,12.20520835998535C10.321822892303466,12.20520835998535,12.202942892303467,10.323528359985351,12.202942892303467,8.002678359985351C12.202942892303467,5.6800383599853514,10.321822892303466,3.800148359985352,8.001232892303467,3.800148359985352C7.350962892303467,3.800148359985352,6.738212892303467,3.9518963599853514,6.189772892303466,4.216116359985351L6.461312892303466,3.5716333599853516L5.684212892303467,3.2449283599853516L4.705242892303467,5.572928359985351L4.803496892303467,5.613988359985352L4.782059892303467,5.683608359985351L5.448402892303466,5.885348359985352L5.482342892303467,5.899628359985352L5.484132892303467,5.896058359985352L5.773532892303467,5.983538359985351L5.773532892303467,6.069228359985352C5.7931828923034665,6.046018359985352,5.816412892303466,6.024598359985351,5.8378528923034665,6.003178359985352L7.200902892303467,6.417358359985352L7.445642892303467,5.612198359985351L6.666752892303467,5.376548359985351C7.066922892303467,5.173018359985352,7.517102892303466,5.053408359985352,7.997662892303467,5.053408359985352C9.626892892303466,5.053408359985352,10.947082892303467,6.374508359985351,10.947082892303467,8.002678359985351C10.947082892303467,9.63084835998535,9.626892892303466,10.951948359985352,7.997662892303467,10.951948359985352C6.513122892303467,10.951948359985352,5.289412892303467,9.85400835998535,5.082182892303467,8.42757835998535L3.820952892303467,8.42757835998535Z'
        fill='#FFFFFF'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const deleteSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <g>
        <rect x='6' y='6' width='1' height='6' rx='0' fill='#FCFCFD' />
      </g>
      <g>
        <rect x='9' y='6' width='1' height='6' rx='0' fill='#FCFCFD' />
      </g>
      <g>
        <path
          d='M2,4L2,3L14,3L14,4L13,4L13,14C13,14.55,12.55,15,12,15L4,15C3.45,15,3,14.55,3,14L3,4L2,4ZM4,4L4,14L12,14L12,4L4,4Z'
          fillRule='evenodd'
          fill='#FCFCFD'
        />
      </g>
      <g>
        <rect x='6' y='1' width='4' height='1' rx='0' fill='#FCFCFD' />
      </g>
    </g>
  </svg>
);

export const recordingSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <g>
      <path
        d='M8.33985265625,7.89919203125L8.33985265625,13.19603203125C8.33985265625,16.26603203125,10.82864265625,18.75483203125,13.89872265625,18.75483203125L14.36818265625,18.75483203125C17.43827265625,18.75483203125,19.927072656249997,16.26603203125,19.927072656249997,13.19603203125L19.927072656249997,7.89919203125C19.927072656249997,4.82912203125,17.43827265625,2.34033203125,14.36820265625,2.34033203125L13.89871265625,2.34033203125C10.82864265625,2.34033203125,8.33985265625,4.82912203125,8.33985265625,7.89919203125ZM11.217232656250001,15.87743203125Q10.10652265625,14.76673203125,10.10652265625,13.19603203125L10.10652265625,7.89919203125Q10.10652265625,6.32841203125,11.217232656250001,5.2177120312500005Q12.32794265625,4.1070020312499995,13.89871265625,4.1070020312499995L14.36820265625,4.1070020312499995Q15.93897265625,4.1070020312499995,17.04967265625,5.2177120312500005Q18.16037265625,6.32841203125,18.16037265625,7.89919203125L18.16037265625,13.19603203125Q18.16037265625,14.76673203125,17.04967265625,15.87743203125Q15.93897265625,16.98823203125,14.36818265625,16.98823203125L13.89872265625,16.98823203125Q12.32794265625,16.98823203125,11.217232656250001,15.87743203125ZM6.03030565625,12.75433203125C5.54245465625,12.75433203125,5.14697265625,13.14973203125,5.14697265625,13.63763203125Q5.14697265625,17.12843203125,7.615362656249999,19.59683203125Q9.967102656249999,21.94863203125,13.24692265625,22.05973203125L13.24692265625,25.01253203125C13.24692265625,25.50043203125,13.64241265625,25.89593203125,14.13026265625,25.89593203125C14.61811265625,25.89593203125,15.01359265625,25.50043203125,15.01359265625,25.01253203125L15.01359265625,22.05993203125Q18.297372656249998,21.95083203125,20.65137265625,19.59683203125Q23.11977265625,17.12843203125,23.11977265625,13.63763203125C23.11977265625,13.14973203125,22.72427265625,12.75433203125,22.23637265625,12.75433203125C21.74857265625,12.75433203125,21.35307265625,13.14973203125,21.35307265625,13.63763203125Q21.35307265625,16.39663203125,19.40207265625,18.34763203125Q17.45117265625,20.29853203125,14.69211265625,20.29853203125L14.13375265625,20.29853203125L14.13026265625,20.29853203125L14.12677265625,20.29853203125L13.57459265625,20.29853203125Q10.81553265625,20.29853203125,8.86458265625,18.34763203125Q6.9139726562499995,16.39703203125,6.9136426562499995,13.63853203125C6.9136426562499995,13.14973203125,6.51816265625,12.75433203125,6.03030565625,12.75433203125Z'
        fillRule='evenodd'
        fill='#26244C'
      />
    </g>
  </svg>
);

export const cancelRecordSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <g>
      <path
        d='M8.33985265625,7.89919203125L8.33985265625,13.19603203125C8.33985265625,16.26603203125,10.82864265625,18.75483203125,13.89872265625,18.75483203125L14.36818265625,18.75483203125C17.43827265625,18.75483203125,19.927072656249997,16.26603203125,19.927072656249997,13.19603203125L19.927072656249997,7.89919203125C19.927072656249997,4.82912203125,17.43827265625,2.34033203125,14.36820265625,2.34033203125L13.89871265625,2.34033203125C10.82864265625,2.34033203125,8.33985265625,4.82912203125,8.33985265625,7.89919203125ZM11.217232656250001,15.87743203125Q10.10652265625,14.76673203125,10.10652265625,13.19603203125L10.10652265625,7.89919203125Q10.10652265625,6.32841203125,11.217232656250001,5.2177120312500005Q12.32794265625,4.1070020312499995,13.89871265625,4.1070020312499995L14.36820265625,4.1070020312499995Q15.93897265625,4.1070020312499995,17.04967265625,5.2177120312500005Q18.16037265625,6.32841203125,18.16037265625,7.89919203125L18.16037265625,13.19603203125Q18.16037265625,14.76673203125,17.04967265625,15.87743203125Q15.93897265625,16.98823203125,14.36818265625,16.98823203125L13.89872265625,16.98823203125Q12.32794265625,16.98823203125,11.217232656250001,15.87743203125ZM6.03030565625,12.75433203125C5.54245465625,12.75433203125,5.14697265625,13.14973203125,5.14697265625,13.63763203125Q5.14697265625,17.12843203125,7.615362656249999,19.59683203125Q9.967102656249999,21.94863203125,13.24692265625,22.05973203125L13.24692265625,25.01253203125C13.24692265625,25.50043203125,13.64241265625,25.89593203125,14.13026265625,25.89593203125C14.61811265625,25.89593203125,15.01359265625,25.50043203125,15.01359265625,25.01253203125L15.01359265625,22.05993203125Q18.297372656249998,21.95083203125,20.65137265625,19.59683203125Q23.11977265625,17.12843203125,23.11977265625,13.63763203125C23.11977265625,13.14973203125,22.72427265625,12.75433203125,22.23637265625,12.75433203125C21.74857265625,12.75433203125,21.35307265625,13.14973203125,21.35307265625,13.63763203125Q21.35307265625,16.39663203125,19.40207265625,18.34763203125Q17.45117265625,20.29853203125,14.69211265625,20.29853203125L14.13375265625,20.29853203125L14.13026265625,20.29853203125L14.12677265625,20.29853203125L13.57459265625,20.29853203125Q10.81553265625,20.29853203125,8.86458265625,18.34763203125Q6.9139726562499995,16.39703203125,6.9136426562499995,13.63853203125C6.9136426562499995,13.14973203125,6.51816265625,12.75433203125,6.03030565625,12.75433203125Z'
        fillRule='evenodd'
        fill='#F95353'
      />
    </g>
  </svg>
);

export const reasoningEndSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <g>
        <path
          d='M8,15.5C3.858,15.5,0.5,12.142,0.5,8C0.5,3.858,3.858,0.5,8,0.5C12.142,0.5,15.5,3.858,15.5,8C15.5,12.142,12.142,15.5,8,15.5ZM8,14.5C11.59,14.5,14.5,11.59,14.5,8C14.5,4.41,11.59,1.5,8,1.5C4.41,1.5,1.5,4.41,1.5,8C1.5,11.59,4.41,14.5,8,14.5Z'
          fill='#624AFF'
        />
      </g>
      <g>
        <path
          d='M11.58148813232422,5.375241953125C11.771108132324219,5.180311853125,12.081748132324218,5.172670253125,12.28071813232422,5.3580429531250005C12.479698132324218,5.543413953125,12.49402813232422,5.853820953125,12.312988132324218,6.056742953125L7.386488132324219,11.341246953125001C7.205098132324219,11.535716953125,6.903218132324218,11.554166953125,6.699488132324219,11.383246953125L3.6784871323242188,8.846246953125C3.4592688323242187,8.670796953125,3.427463032324219,8.349276953124999,3.6080571323242188,8.134276953125C3.7886501323242188,7.919276953124999,4.110829132324219,7.8951069531249995,4.3214871323242185,8.080746953125L6.978988132324218,10.312246953125001L11.58148813232422,5.374742953125L11.58148813232422,5.375241953125Z'
          fill='#624AFF'
        />
      </g>
    </g>
  </svg>
);

export const reasoningExpandSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='10.000000312876722'
    height='6.000000521461203'
    viewBox='0 0 10.000000312876722 6.000000521461203'
  >
    <g transform='matrix(-1,-5.2146120310681e-8,5.2146120310681e-8,-1,19.99999968712325,12.00000156438361)'>
      <path
        d='M15,12.000000521461203L10,6.838257521461204L10.81199,6.000000521461203L15,10.323480521461203L19.18801,6.000000521461203L20,6.838257521461204L15,12.000000521461203Z'
        fillRule='evenodd'
        fill='#878AAB'
      />
    </g>
  </svg>
);

export const openExtraSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <g>
      <g>
        <path
          d='M20.125,13.125L14.875,13.125L14.875,7.875C14.875,7.391751,14.48325,7,14,7C13.51675,7,13.125,7.391751,13.125,7.875L13.125,13.125L7.875,13.125C7.391751,13.125,7,13.51675,7,14C7,14.48325,7.391751,14.875,7.875,14.875L13.125,14.875L13.125,20.125C13.125,20.6082,13.51675,21,14,21C14.48325,21,14.875,20.6082,14.875,20.125L14.875,14.875L20.125,14.875C20.6082,14.875,21,14.48325,21,14C21,13.51675,20.6082,13.125,20.125,13.125Z'
          fill='currentColor'
        />
      </g>
      <g>
        <path
          d='M14,1.75C7.23451,1.749999599457,1.75,7.23451,1.75,14C1.75,20.7655,7.23451,26.25,14,26.25C20.7655,26.25,26.25,20.7655,26.25,14C26.2425,7.23763,20.7624,1.75753342,14,1.75ZM14,24.5C8.20101,24.5,3.5,19.799,3.5,14C3.5,8.20101,8.20101,3.5,14,3.5C19.799,3.5,24.5,8.20101,24.5,14C24.4925,19.7959,19.7959,24.4925,14,24.5Z'
          fill='currentColor'
        />
      </g>
    </g>
  </svg>
);

export const closeExtraSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <g transform='matrix(0.7071067690849304,0.7071067690849304,-0.7071067690849304,0.7071067690849304,4.100642776577757,-9.899663748898234)'>
      <path
        d='M32.561172750854496,8.662251539154052L25.136872750854494,8.662251539154052L25.136872750854494,1.2375315391540527C25.136872750854494,0.5541085391540528,24.58287275085449,0.00008153915405273438,23.89944275085449,0.00008153915405273438C23.21604275085449,0.00008153915405273438,22.662042750854493,0.5541085391540528,22.662042750854493,1.2375315391540527L22.662042750854493,8.662251539154052L15.237672750854493,8.662251539154052C14.554273750854492,8.662251539154052,14.000272750854492,9.216281539154053,14.000272750854492,9.899701539154053C14.000272750854492,10.583081539154053,14.554273750854492,11.137181539154053,15.237672750854493,11.137181539154053L22.662042750854493,11.137181539154053L22.662042750854493,18.561881539154054C22.662042750854493,19.245281539154053,23.21604275085449,19.79928153915405,23.89944275085449,19.79928153915405C24.58287275085449,19.79928153915405,25.136872750854494,19.245281539154053,25.136872750854494,18.561881539154054L25.136872750854494,11.137181539154053L32.561172750854496,11.137181539154053C33.24457275085449,11.137181539154053,33.79857275085449,10.583081539154053,33.79857275085449,9.899701539154053C33.79857275085449,9.216281539154053,33.24457275085449,8.662251539154052,32.561172750854496,8.662251539154052Z'
        fill='#56597E'
      />
    </g>
  </svg>
);

export const imageUploadSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='32'
    height='32'
    viewBox='0 0 32 32'
  >
    <g>
      <g>
        <path
          d='M22,11C22,12.65,20.65,14,19,14C17.35,14,16,12.65,16,11C16,9.35,17.35,8,19,8C20.65,8,22,9.35,22,11ZM20,11C20,10.45,19.55,10,19,10C18.45,10,18,10.45,18,11C18,11.55,18.45,12,19,12C19.55,12,20,11.55,20,11Z'
          fillRule='evenodd'
          fill='#56597E'
        />
      </g>
      <g>
        <path
          d='M6,4L26,4C27.1,4,28,4.9,28,6L28,26C28,27.1,27.1,28,26,28L6,28C4.9,28,4,27.1,4,26L4,6C4,4.9,4.9,4,6,4ZM25.98,6L25.98,21.15L22.41,17.58C21.63,16.8,20.36,16.8,19.58,17.58L17.990000000000002,19.17L12.39998,13.58C11.61998,12.8,10.34998,12.8,9.569980000000001,13.58L5.97998,17.17L5.97998,6L25.98,6ZM25.98,21.15L25.98,21.17L26,21.17L25.98,21.15ZM26,26L6,26L6,20L11,15L16.59,20.59C17.369999999999997,21.37,18.64,21.37,19.42,20.59L21,19L26,24L26,26Z'
          fillRule='evenodd'
          fill='#56597E'
        />
      </g>
    </g>
  </svg>
);

export const attachmentUploadFailSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='24'
    height='24'
    viewBox='0 0 24 24'
  >
    <g>
      <g>
        <path
          d='M0,12C0,5.37258,5.37258,0,12,0C15.1826,0,18.2348,1.26428,20.4853,3.51472C22.7357,5.76515,24,8.8174,24,12C24,18.6274,18.6274,24,12,24C5.37258,24,0,18.6274,0,12ZM11.1429,13.7143L11.1429,6L12.8571,6L12.8571,13.7143L11.1429,13.7143ZM12,19.2857C11.2899,19.2857,10.7143,18.7101,10.7143,18C10.7143,17.2899,11.2899,16.7143,12,16.7143C12.7101,16.7143,13.2857,17.2899,13.2857,18C13.2857,18.7101,12.7101,19.2857,12,19.2857Z'
          fillRule='evenodd'
          fill='#E00000'
        />
      </g>
    </g>
  </svg>
);

export const attachmentAddSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='20'
    height='20'
    viewBox='0 0 20 20'
  >
    <g>
      <g>
        <path
          d='M10.625,9.375L10.625,5L9.375,5L9.375,9.375L5,9.375L5,10.625L9.375,10.625L9.375,15L10.625,15L10.625,10.625L15,10.625L15,9.375L10.625,9.375Z'
          fill='#56597E'
        />
      </g>
      <g></g>
    </g>
  </svg>
);

export const attachmentRemoveSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <g>
        <path
          d='M8,1C4.134,1,1,4.134,1,8C1,11.866,4.134,15,8,15C11.866,15,15,11.866,15,8C15,4.134,11.866,1,8,1L8,1Z'
          fillRule='evenodd'
          fill='#E6E7EC'
        />
      </g>
      <g>
        <path
          d='M11.207,10.5L10.5,11.207L8.1035,8.810500000000001L5.707,11.207L5,10.5L7.3965,8.1035L5,5.707L5.707,5L8.1035,7.3965L10.5,5L11.207,5.707L8.810500000000001,8.1035L11.207,10.5Z'
          fillRule='evenodd'
          fill='#1A1A1A'
        />
      </g>
    </g>
  </svg>
);

export const toVoiceSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='32'
    height='32'
    viewBox='0 0 32 32'
  >
    <g>
      <path
        d='M14.8686,25.666015625C14.8686,26.218915625,15.3114,26.666015625,15.8572,26.666015625C16.4063,26.662915625,16.8489,26.215115625,16.8457,25.666015625L16.8457,7.666015625C16.8457,7.113158625,16.402900000000002,6.666015625,15.8572,6.666015625C15.308,6.669161775,14.8654,7.116882625,14.8686,7.666015625L14.8686,25.666015625ZM9.18145,22.380315625C9.18145,22.931715625,9.6243,23.380315625,10.170020000000001,23.380315625C10.71971,23.377915625,11.16318,22.930015625,11.16002,22.380315625L11.16002,10.951725625C11.16002,10.398875625,10.71716,9.951725625,10.170020000000001,9.951725625C9.62088,9.954875625,9.17828,10.402595625,9.18145,10.951725625L9.18145,22.380315625ZM3.988588,20.951715625C3.439455,20.948615625000002,2.99685026,20.500915624999998,3.0000163487,19.951715625L3.0000163487,13.380305625C3.0000163487,12.828875625,3.4428739999999998,12.380305625,3.988588,12.380305625C4.53573,12.380305625,4.9785900000000005,12.828875625,4.9785900000000005,13.380305625L4.9785900000000005,19.951715625C4.9785900000000005,20.504615625,4.53573,20.951715625,3.988588,20.951715625ZM27.7257,19.526015625C27.176,19.523615624999998,26.7326,19.075715625,26.7357,18.526015625L26.7357,14.811735625C26.7357,14.258875625,27.1786,13.811735625,27.7257,13.811735625C28.2714,13.811735625,28.7143,14.258875625,28.7143,14.811735625L28.7143,18.526015625C28.7143,19.077415625,28.2714,19.526015625,27.7257,19.526015625ZM21.7915,22.380315625C21.2418,22.377915625,20.7983,21.930015625,20.8015,21.380315625L20.8015,11.951735625000001C20.8015,11.398875624999999,21.2443,10.951725625,21.7915,10.951725625C22.3372,10.951725625,22.78,11.398875624999999,22.78,11.951735625000001L22.78,21.380315625C22.78,21.931715625000002,22.3372,22.380315625,21.7915,22.380315625Z'
        fill='#56597E'
      />
    </g>
  </svg>
);

export const toAvatarSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='32'
    height='32'
    viewBox='0 0 32 32'
  >
    <g>
      <path
        d='M21.366,10.356C21.366,13.832,18.462,16.656,14.858,16.71C14.7904,16.7247,14.7213,16.732100000000003,14.652,16.732C10.23204,16.732,7.06204,19.854,5.91004,23.856C5.73004,24.476,5.91004,25.04,6.33004,25.476C6.758039999999999,25.926,7.44004,26.232,8.21004,26.232L22.116,26.232C22.624,26.232,23.036,26.628,23.036,27.116C23.0251,27.6137,22.6138,28.0089,22.116,28L8.21004,28C6.94604,28,5.76404,27.5,4.97204,26.672C4.16804,25.832,3.77204,24.648,4.13604,23.386C5.12604,19.95,7.51604,16.875999999999998,11.008040000000001,15.596C9.27404,14.45,8.13804,12.53,8.13804,10.356C8.13804,6.846,11.098040000000001,4,14.752,4C18.404,4,21.366,6.846,21.366,10.356ZM14.752,14.94C17.387999999999998,14.94,19.526,12.886,19.526,10.354C19.526,7.82,17.387999999999998,5.766,14.752,5.766C12.11604,5.766,9.97804,7.822,9.97804,10.356C9.97804,12.888,12.11604,14.942,14.752,14.942L14.752,14.94ZM23.938,16.71C23.9282,16.2118,23.5162,15.816,23.018,15.826C22.5198,15.816,22.1079,16.2118,22.098,16.71L22.098,23.13C22.098,23.616,22.51,24.012,23.018,24.012C23.526,24.012,23.938,23.618,23.938,23.128L23.938,16.708L23.938,16.71ZM19.875999999999998,20.56C19.8662,20.0618,19.4542,19.666,18.956,19.676000000000002C18.4578,19.666,18.0459,20.0618,18.036,20.56L18.036,23.128C18.036,23.616,18.445999999999998,24.012,18.956,24.012C19.464,24.012,19.875999999999998,23.616,19.875999999999998,23.128L19.875999999999998,20.56ZM27.08,18.398C27.588,18.398,28,18.794,28,19.282L28,23.132C27.9913,23.631,27.579,24.028,27.08,24.018C26.5811,24.028,26.1688,23.631,26.16,23.132L26.16,19.282C26.16,18.794,26.572,18.398,27.08,18.398Z'
        fill='#56597E'
      />
    </g>
  </svg>
);

export const toVisionSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='32'
    height='32'
    viewBox='0 0 32 32'
  >
    <g>
      <g>
        <path
          d='M23.604,27.778865625Q22.8846,28.749765625,21.4812,28.749765625Q20.9848,28.749765625,20.3086,28.520365625Q12.93558,25.871365625,9.48695,22.360665625Q5.73335,18.662965625,3.437846,11.440085625Q2.85887,9.476025625,3.909122,8.034514625Q5.00003,6.384765625,7.04072,6.384765625L9.92881,6.384765625Q11.64559,6.384765625,12.44611,7.810903625L13.83445,10.354765625Q14.6561,11.637225625,13.85112,13.084005625L12.79188,15.157655625Q13.29003,16.510095624999998,14.2017,17.527365625Q15.0584,18.393365625,16.4847,18.931165625L18.5217,17.852865625Q18.5467,17.839565625,18.572699999999998,17.828265625Q19.8508,17.270665625,21.129,17.828265625Q21.1645,17.843765625,21.1983,17.862865624999998L23.7137,19.285465625Q25.1193,20.103065625,25.1193,21.740265625L25.1193,24.680365625Q25.1193,26.677065625,23.604,27.778865625ZM22.6438,26.620465625Q22.5197,26.704665625,22.4376,26.830165625Q22.1628,27.249765625,21.4812,27.249765625Q21.2323,27.249765625,20.7906,27.099865625Q13.76541,24.575665625,10.55702,21.309565624999998Q7.05047,17.854865625000002,4.867388,10.985765624999999Q4.504796,9.754785625,5.1291,8.907405624999999Q5.14137,8.890745625000001,5.15271,8.873435624999999Q5.80016,7.884765625,7.04072,7.884765625L9.92881,7.884765625Q10.767420000000001,7.884765625,11.13809,8.545125625L12.53123,11.098005624999999Q12.54565,11.124435625,12.56214,11.149615625Q12.91466,11.687905624999999,12.53556,12.363265625Q12.52836,12.376105625000001,12.52166,12.389215625L11.31053,14.760235625Q11.242370000000001,14.893665625,11.23071,15.043035625Q11.21905,15.192405625,11.26567,15.334795625Q11.86955,17.179065625,13.09805,18.543365625Q13.10894,18.555465625,13.12036,18.567065624999998Q14.3168,19.785065625,16.302500000000002,20.458865625Q16.448999999999998,20.508565625,16.6033,20.496265625Q16.7576,20.483865625,16.894399999999997,20.411465624999998L19.1965,19.192865625Q19.8464,18.919365624999998,20.4963,19.189165625L22.9753,20.591165625000002Q23.6193,20.965865625,23.6193,21.740265625L23.6193,24.680365625Q23.6193,25.958465625,22.6438,26.620465625Z'
          fillRule='evenodd'
          fill='#56597E'
        />
      </g>
      <g>
        <path
          d='M25.768972734374998,11.65186Q25.658672734375,12.05195,25.308812734375,12.35567Q24.854772734375,12.74984,24.261412734375,12.74984L17.087393734375,12.74984Q16.465311734375,12.74984,16.013691734375,12.31857Q15.529052734375,11.85577,15.529052734375,11.15775L15.529052734375,4.842088Q15.529052734375,4.224173,15.930713734375,3.767704Q16.386256734375,3.25,17.087393734375,3.25L24.261412734375,3.25Q24.883492734375,3.25,25.335112734375002,3.681269Q25.676342734374998,4.00711927,25.777312734375002,4.449579L26.669452734375,4.0582762Q27.022152734375,3.883534,27.420952734375,3.9250759Q27.904552734375002,3.9754619,28.237952734375,4.322712Q28.749952734375,4.856161,28.749952734375,5.47365L28.749952734375,10.52618Q28.749952734375,11.45484,28.043352734375,11.82292Q28.021152734375,11.83446,27.960052734374997,11.86862Q27.195152734375,12.29628,26.674052734375,12.04886L25.768972734374998,11.65186ZM24.319752734375,11.22768Q24.291462734375,11.249839999999999,24.261412734375,11.249839999999999L17.087393734375,11.249839999999999Q17.029052734375,11.249839999999999,17.029052734375,11.15775L17.029052734375,4.842088Q17.029052734375,4.75,17.087393734375,4.75L24.261412734375,4.75Q24.319752734375,4.75,24.319752734375,4.842088L24.319752734375,5.57892Q24.319752734375,5.65278,24.334162734375,5.72523Q24.348572734375,5.79768,24.376842734375,5.8659300000000005Q24.405112734375003,5.93417,24.446152734374998,5.99559Q24.487182734375,6.05701,24.539422734375,6.109249999999999Q24.591652734375,6.16148,24.653072734375,6.20252Q24.714492734375,6.24356,24.782732734375,6.27182Q24.850982734375002,6.30009,24.923432734374998,6.3145Q24.995882734375,6.32892,25.069752734375,6.32892Q25.227012734375002,6.32892,25.371022734375,6.265750000000001L27.229652734375,5.4505Q27.240852734375,5.46743,27.248052734375,5.4824L27.249952734375,5.47365L27.249952734375,10.52618Q27.249952734375,10.536349999999999,27.247252734375,10.54866Q27.237952734375,10.55382,27.228052734374998,10.55935Q27.172452734375,10.59046,27.131952734374998,10.61177L25.371012734375,9.83935Q25.234402734375,9.77943,25.085242734375,9.776340000000001Q24.936092734375002,9.77326,24.797112734375,9.827490000000001Q24.658132734375002,9.88172,24.550492734375,9.985009999999999Q24.442842734375,10.0883,24.382922734375,10.224920000000001Q24.319752734375,10.368929999999999,24.319752734375,10.52618L24.319752734375,11.22768ZM27.336052734375002,10.7031Q27.313752734375,10.691510000000001,27.290852734375,10.68144Q27.312952734375,10.691089999999999,27.336052734375002,10.7031Z'
          fillRule='evenodd'
          fill='#56597E'
        />
      </g>
    </g>
  </svg>
);

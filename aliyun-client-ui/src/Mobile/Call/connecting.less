.connecting {
  flex: 1;
  text-align: center;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  ._tip {
    font-size: 14px;
    line-height: 22px;
    color: white;
  }

  ._loading {
    list-style: none;
    padding: 0;
    margin: 10px 0 0;

    display: flex;

    li {
      margin: 0 4px;
      width: 4px;
      height: 5px;
      border-radius: 2px;
      background-color: #573dff;
      animation: connecting-loading 1s ease-in-out infinite;

      &:nth-child(1) {
        animation-delay: -0.4s;
      }
      &:nth-child(2) {
        animation-delay: -0.2s;
      }
      &:nth-child(3) {
        animation-delay: 0s;
      }
      &:nth-child(4) {
        animation-delay: 0.2s;
      }
      &:nth-child(5) {
        animation-delay: 0.4s;
      }
    }
  }
}

@keyframes connecting-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

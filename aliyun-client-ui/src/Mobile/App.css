html,
body {
  height: 100%;
  margin: 0;
}

#root {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 9;
  margin: 0 auto;
  box-sizing: border-box;
  background-color: #fff;
  max-width: 576px;

  --adm-color-primary: #624aff;
  --adm-mask-z-index: 5;

  .adm-popup {
    position: static;
  }

  .adm-mask {
    position: absolute;
  }

  .adm-center-popup {
    position: static;

    .adm-center-popup-mask {
      z-index: 5;
    }
  }

  .adm-center-popup-wrap,
  .adm-popup-body {
    position: absolute;
    z-index: 5;
  }

  .adm-toast-mask .adm-toast-wrap {
    position: absolute;
  }

  .app {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }

  .controlBtn {
    position: absolute;
    left: 50%;
    bottom: 60px;
    transform: translateX(-50%);
    background: #23BD7F;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 10;
  }
}

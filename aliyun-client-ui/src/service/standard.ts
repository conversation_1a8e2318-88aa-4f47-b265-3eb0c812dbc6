import {
  AICallAgentError,
  AICallAgentInfo,
  AICallErrorCode,
  AIChatAuthToken,
} from "aliyun-auikit-aicall";
import AUIAICallConfig from "../controller/call/AUIAICallConfig";

import { getWorkflowType, TemplateConfig, WorkflowType } from "./interface";

class StandardAppService {
  private appServer = "";
  private serverAuth = "";

  setAppServer(appServer: string) {
    this.appServer = appServer;
  }

  setServerAuth(serverAuth: string) {
    this.serverAuth = serverAuth;
  }

  /**
   * 启动智能体实例 Start a AI agent instance
   * @param userId
   * @param token token
   * @param config 智能体实例配置 agent instance config
   * @returns {Promise<AICallAgentInfo>} 智能体实例信息 agent instance info
   * @note 调用之前需要先设置用户 id 和 token
   * @note id and token are required before calling this method
   */
  generateAIAgent = async (
    userId: string,
    token: string,
    config: AUIAICallConfig
  ): Promise<AICallAgentInfo> => {
    if (!userId) {
      throw new AICallAgentError("userId is empty");
    }

    const param: {
      user_id: string;
      workflow_type?: WorkflowType;
      ai_agent_id?: string;
      template_config?: string;
      expire?: number;
      user_data?: string;
      region?: string;
      session_id?: string;
      chat_sync_config?: string;
    } = {
      user_id: userId,
      expire: 24 * 60 * 60,
      template_config: config.templateConfig.getJsonString(config.agentType),
    };

    if (config.agentId) {
      param.ai_agent_id = config.agentId;
    } else {
      param.workflow_type = getWorkflowType(config.agentType);
    }

    if (config.userData) {
      param.user_data = config.userData;
    }
    if (config.region) {
      param.region = config.region;
    }

    if (config.chatSyncConfig) {
      param.session_id = config.chatSyncConfig.sessionId;
      param.chat_sync_config = config.chatSyncConfig.getConfigString();
    }

    return fetch(`${this.appServer}/api/v2/aiagent/generateAIAgentCall`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: token || "",
      },
      body: JSON.stringify(param),
    })
      .then(async (res) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let data: any = {};
        try {
          data = await res.json();
        } catch (e) {
          console.error(e);
        }
        if (data.error_code === "Forbidden.SubscriptionRequired") {
          throw new AICallAgentError(
            "Forbidden.SubscriptionRequired",
            AICallErrorCode.AgentSubscriptionRequired
          );
        } else if (data.error_code === "AgentNotFound") {
          throw new AICallAgentError(
            "AgentNotFound",
            AICallErrorCode.AgentNotFound
          );
        }

        if (res.status === 403) {
          const error = new AICallAgentError("token is invalid");
          error.name = "ServiceAuthError";
          throw error;
        } else if (res.status !== 200) {
          throw new AICallAgentError(`response status is ${res.status}`);
        }

        return data;
      })
      .then((data) => {
        if (data.code === 200) {
          return {
            agentType: config.agentType,
            instanceId: data.ai_agent_instance_id,
            channelId: data.channel_id,
            userId: data.ai_agent_user_id,
            rtcToken: data.rtc_auth_token,
            reqId: data.request_id || "",
          };
        }
        throw new AICallAgentError(data.message || "request error");
      });
  };

  getRtcAuthToken = async (userId: string, channelId: string) => {
    const body = {
      userId,
      channelId,
    };
    return fetch(`/pgw/api/aiagent/authToken`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: this.serverAuth || "",
      },
      body: JSON.stringify(body),
    })
      .then((res) => {
        if (res.status === 403) {
          const error = new AICallAgentError("token is invalid");
          error.name = "ServiceAuthError";
          throw error;
        } else if (res.status !== 200) {
          throw new AICallAgentError(`response status is ${res.status}`);
        }

        return res.json();
      })
      .then((data) => {
        return data.data;
      });
  };

  describeAIAgent = async (
    userId: string,
    token: string,
    instanceId: string
  ): Promise<TemplateConfig> => {
    if (!userId || !instanceId) {
      throw new AICallAgentError("userId or instanceId is empty");
    }

    const param = {
      userId,
      aiAgentInstanceId: instanceId,
    };

    return fetch(`/pgw/api/aiagent/describe`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: token || "",
      },
      body: JSON.stringify(param),
    })
      .then((res) => {
        if (res.status === 403) {
          const error = new AICallAgentError("token is invalid");
          error.name = "ServiceAuthError";
          throw error;
        } else if (res.status !== 200) {
          throw new AICallAgentError(
            `describeAIAgentInstance error, response status: ${res.status}`
          );
        }
        return res.json();
      })
      .then((data) => {
        return JSON.parse(data.data.template_config);
      });
  };

  generateMessageChatToken = async (
    userId: string,
    token: string,
    agentId?: string,
    region?: string
  ): Promise<AIChatAuthToken> => {
    if (!userId) {
      throw new AICallAgentError("userId or instanceId is empty");
    }

    const param: {
      user_id: string;
      ai_agent_id?: string;
      region?: string;
    } = {
      user_id: userId,
      ai_agent_id: agentId,
    };

    if (region) {
      param.region = region;
    }

    return fetch(`${this.appServer}/api/v2/aiagent/generateMessageChatToken`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: token || "",
      },
      body: JSON.stringify(param),
    })
      .then((res) => {
        if (res.status === 403) {
          const error = new AICallAgentError("token is invalid");
          error.name = "ServiceAuthError";
          throw error;
        } else if (res.status !== 200) {
          throw new AICallAgentError(
            `generateMessageChatToken error, response status: ${res.status}`
          );
        }
        return res.json();
      })
      .then((data) => {
        if (data.code === 200) {
          return AIChatAuthToken.fromData(data);
        }
        throw new AICallAgentError(
          `generateMessageChatToken error, message: ${data.message || "request error"}`
        );
      });
  };
  saveMsg = async (param: {
    taskId?: string;
    userId?: string;
    roomId?: string;
    content?: string;
    pushTime?: number;
  }) => {
    return fetch(`/pgw/api/message/send`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Authorization: token || "",
      },
      body: JSON.stringify(param),
    });
  };
  startAgent = async (
    token: string,
    data: {
      userId: string;
      taskId: string;
    }
  ) => {
    return fetch(`/pgw/api/aiagent/start`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: token || "",
      },
      body: JSON.stringify(data),
    }).then((res) => {
      return res.json();
    });
  };
  stopAgent = async (
    token: string,
    data: {
      userId: string;
      taskId: string;
    }
  ) => {
    return fetch(`/pgw/api/aiagent/stop`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: token || "",
      },
      body: JSON.stringify(data),
    }).then((res) => {
      return res.json();
    });
  };
}

export default new StandardAppService();

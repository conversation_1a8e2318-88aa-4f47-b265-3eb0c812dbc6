{"v": "5.12.1", "fr": 30, "ip": 0, "op": 60, "w": 720, "h": 720, "nm": "机器人+眼镜", "ddd": 0, "assets": [{"id": "image_0", "w": 720, "h": 720, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "imgSeq_0", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_0.png", "e": 0}, {"id": "imgSeq_1", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_1.png", "e": 0}, {"id": "imgSeq_2", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_2.png", "e": 0}, {"id": "imgSeq_3", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_3.png", "e": 0}, {"id": "imgSeq_4", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_4.png", "e": 0}, {"id": "imgSeq_5", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_5.png", "e": 0}, {"id": "imgSeq_6", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_6.png", "e": 0}, {"id": "imgSeq_7", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_7.png", "e": 0}, {"id": "imgSeq_8", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_8.png", "e": 0}, {"id": "imgSeq_9", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_9.png", "e": 0}, {"id": "imgSeq_10", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_10.png", "e": 0}, {"id": "imgSeq_11", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_11.png", "e": 0}, {"id": "imgSeq_12", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_12.png", "e": 0}, {"id": "imgSeq_13", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_13.png", "e": 0}, {"id": "imgSeq_14", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_14.png", "e": 0}, {"id": "imgSeq_15", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_15.png", "e": 0}, {"id": "imgSeq_16", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_16.png", "e": 0}, {"id": "imgSeq_17", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_17.png", "e": 0}, {"id": "imgSeq_18", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_18.png", "e": 0}, {"id": "imgSeq_19", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_19.png", "e": 0}, {"id": "imgSeq_20", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_20.png", "e": 0}, {"id": "imgSeq_21", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_21.png", "e": 0}, {"id": "imgSeq_22", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_22.png", "e": 0}, {"id": "imgSeq_23", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_23.png", "e": 0}, {"id": "imgSeq_24", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_24.png", "e": 0}, {"id": "imgSeq_25", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_25.png", "e": 0}, {"id": "imgSeq_26", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_26.png", "e": 0}, {"id": "imgSeq_27", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_27.png", "e": 0}, {"id": "imgSeq_28", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_28.png", "e": 0}, {"id": "imgSeq_29", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_29.png", "e": 0}, {"id": "imgSeq_30", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_30.png", "e": 0}, {"id": "imgSeq_31", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_31.png", "e": 0}, {"id": "imgSeq_32", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_32.png", "e": 0}, {"id": "imgSeq_33", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_33.png", "e": 0}, {"id": "imgSeq_34", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_34.png", "e": 0}, {"id": "imgSeq_35", "w": 720, "h": 720, "t": "seq", "u": "images/", "p": "seq_0_35.png", "e": 0}, {"id": "sequence_0", "layers": [{"ty": 2, "sc": "#00ffff", "refId": "imgSeq_0", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 0, "st": 0, "op": 1, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_1", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 1, "st": 1, "op": 2, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_2", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 2, "st": 2, "op": 3, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_3", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 3, "st": 3, "op": 4, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_4", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 4, "st": 4, "op": 5, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_5", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 5, "st": 5, "op": 6, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_6", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 6, "st": 6, "op": 7, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_7", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 7, "st": 7, "op": 8, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_8", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 8, "st": 8, "op": 9, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_9", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 9, "st": 9, "op": 10, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_10", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 10, "st": 10, "op": 11, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_11", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 11, "st": 11, "op": 12, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_12", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 12, "st": 12, "op": 13, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_13", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 13, "st": 13, "op": 14, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_14", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 14, "st": 14, "op": 15, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_15", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 15, "st": 15, "op": 16, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_16", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 16, "st": 16, "op": 17, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_17", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 17, "st": 17, "op": 18, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_18", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 18, "st": 18, "op": 19, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_19", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 19, "st": 19, "op": 20, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_20", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 20, "st": 20, "op": 21, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_21", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 21, "st": 21, "op": 22, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_22", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 22, "st": 22, "op": 23, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_23", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 23, "st": 23, "op": 24, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_24", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 24, "st": 24, "op": 25, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_25", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 25, "st": 25, "op": 26, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_26", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 26, "st": 26, "op": 27, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_27", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 27, "st": 27, "op": 28, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_28", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 28, "st": 28, "op": 29, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_29", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 29, "st": 29, "op": 30, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_30", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 30, "st": 30, "op": 31, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_31", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 31, "st": 31, "op": 32, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_32", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 32, "st": 32, "op": 33, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_33", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 33, "st": 33, "op": 34, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_34", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 34, "st": 34, "op": 35, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_35", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 35, "st": 35, "op": 37, "sr": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "调整图层 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [360, 360, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [562.5, 1218, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [64, 29.557, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "亮度和对比度", "np": 5, "mn": "ADBE Brightness & Contrast 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "亮度", "mn": "ADBE Brightness & Contrast 2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 0, "nm": "对比度", "mn": "ADBE Brightness & Contrast 2-0002", "ix": 2, "v": {"a": 0, "k": 15, "ix": 2}}, {"ty": 7, "nm": "使用旧版（支持 HDR）", "mn": "ADBE Brightness & Contrast 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}, {"ty": 5, "nm": "曲线", "np": 4, "mn": "ADBE CurvesCustom", "ix": 2, "en": 1, "ef": [{"ty": 7, "nm": "通道:", "mn": "ADBE CurvesCustom-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}]}, {"ty": 5, "nm": "锐化", "np": 3, "mn": "ADBE Sharpen", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "锐化量", "mn": "ADBE Sharpen-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1}}]}], "ip": 0, "op": 100, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "01_DeMain_[0000-0035].png", "cl": "png", "refId": "sequence_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [360, 360, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [360, 360, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "简单阻塞工具", "np": 4, "mn": "ADBE Simple Choker", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "视图", "mn": "ADBE Simple Choker-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 0, "nm": "阻塞遮罩", "mn": "ADBE Simple Choker-0002", "ix": 2, "v": {"a": 0, "k": 1.3, "ix": 2}}]}], "w": 720, "h": 720, "ip": 0, "op": 36, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "01_DeMain_0035.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [360, 360, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [360, 360, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "简单阻塞工具", "np": 4, "mn": "ADBE Simple Choker", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "视图", "mn": "ADBE Simple Choker-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 0, "nm": "阻塞遮罩", "mn": "ADBE Simple Choker-0002", "ix": 2, "v": {"a": 0, "k": 1.3, "ix": 2}}]}], "ip": 36, "op": 96, "st": 36, "bm": 0}], "markers": [], "props": {}}
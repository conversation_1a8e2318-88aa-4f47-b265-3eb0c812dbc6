{"name": "amaui-web-aicall", "private": true, "version": "1.8.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build && rm -rf ../src/main/resources/templates/aliyun/index.html && cp -r dist/* ../src/main/resources/static/pgw/aui && cp dist/index.html ../src/main/resources/templates/aliyun", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@react-spring/web": "^9.7.5", "antd": "^5.20.1", "antd-mobile": "^5.38.1", "eventemitter3": "^5.0.1", "highlight.js": "^11.11.1", "lottie-web": "^5.12.2", "markdown-it": "^14.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "uuid": "^11.1.0", "zustand": "^4.5.5", "aliyun-auikit-aicall": "2.3.1", "@types/clientjs": "^0.2.2", "clientjs": "^0.2.1"}, "devDependencies": {"@arms/rum-browser": "^0.0.33", "@eslint/js": "^9.8.0", "@types/markdown-it": "^14.1.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/throttle-debounce": "^5.0.2", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.8.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "less": "^4.2.0", "prettier": "3.5.3", "terser": "^5.31.6", "typescript": "^5.5.3", "typescript-eslint": "^8.0.0", "vite": "^5.4.0", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-dts": "^4.2.2", "vite-plugin-svgr": "^4.2.0", "yargs-parser": "^21.1.1"}}
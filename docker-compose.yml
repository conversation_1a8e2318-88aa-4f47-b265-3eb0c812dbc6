services:
  mysql:
    image: mysql:8.1
    command: --default-authentication-plugin=mysql_native_password
    container_name: mysql
    restart: always
    ports:
      - 3306:3306
    environment:
      - MYSQL_USER=mysql
      - MYSQL_PASSWORD=mysql
      - MYSQL_ROOT_PASSWORD=root
    volumes:
      - /opt/data/mysql:/var/lib/mysql
  redis:
    image: redis:alpine
    container_name: redis
    restart: always
    volumes:
      - /opt/data/redis:/data
    ports:
      - 6379:6379
  nginx:
    image: nginx:1.15.12-alpine
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /opt/data/nginx/conf.d:/etc/nginx/conf.d
      - /opt/data/nginx/ssl:/etc/nginx/ssl
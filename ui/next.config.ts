/**
 * @type {import('next').NextConfig}
 */
// const baseUrl = "https://rtc.rollingdigital.cn/frp";
// const baseUrl = "https://rtc.rollingdigital.cn";
const baseUrl = "http://rtc-mgt-test.mengniu.cn";

import type { NextConfig } from "next";
const isDevelopment = process.env.NODE_ENV !== "production";
const rewritesConfig = isDevelopment
  ? [
      {
        source: "/api/:path*",
        destination: `${baseUrl}/api/:path*`,
      },
    ]
  : [];

const nextConfig: NextConfig = {
  output: isDevelopment ? "standalone" : "export",
  assetPrefix: isDevelopment ? "" : "/ui/",
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  distDir: "build",
  async rewrites() {
    return rewritesConfig;
  },
};

export default nextConfig;

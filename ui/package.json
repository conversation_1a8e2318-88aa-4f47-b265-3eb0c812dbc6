{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001 --turbopack", "build": "DISABLE_ESLINT_PLUGIN=true next build && rm -rf ../src/main/resources/static/ui/** && cp -r build/* ../src/main/resources/static/ui && rm -rf build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ant-design/x": "^1.0.5", "ahooks": "^3.8.4", "antd": "^5.24.2", "antd-style": "^3.7.1", "dayjs": "^1.11.13", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "15.2.0", "next-http-proxy-middleware": "^1.2.6", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jquery": "^3.5.32", "@types/next": "^9.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "typescript": "^5"}}
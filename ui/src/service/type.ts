export interface ErrorResponse {
  success: false;
  error: {
    code: number;
    message: string;
  };
}

export type Data<T = unknown> = {
  success: true;
  /** 数据 */
  data: T;
};

export type PageData<T = unknown> = Data<{
  records: T[];
  /** 总页数 */
  total: number;
}>;

export type PageResponse<T = unknown> = ErrorResponse | PageData<T>;
export type DataResponse<T = unknown> = ErrorResponse | Data<T>;
export type Response<T = unknown> = ErrorResponse | T;

export type ListRequest = {
  /** 关键字搜索 */
  keyword?: string;
  /** 排序方式 */
  order?: string;
  /** 当前页码 */
  page?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 排序字段 */
  sort?: string;
};

/** 通用实体元信息 */
export interface BaseInfo {
  id: string;
  createTime?: string | null;
  updateTime?: string | null;
  creatorId?: string | null;
  creatorName?: string | null;
  updaterId?: string | null;
  updaterName?: string | null;
}

/** 可扩展的带元信息实体 */
export type WithBaseInfo<T> = T & BaseInfo;

/** 通用状态映射 */
export type StatusMap<T extends string> = Record<T, string>;

import { del, get, post, put } from "./apiUtility";
import {
  Assistant,
  AssistantDetailResponse,
  AssistantListRequest,
  AssistantListResponse,
  DeleteAssistantResponse
} from "./types/assistant";

const service = {
  getAssistantList: (data?: AssistantListRequest) =>
    get<AssistantListResponse>({
      url: "/api/evalapp",
      data,
    }),
  createAssistant: (data: Partial<Assistant>) =>
    post({ url: "/api/evalapp", data }),
  updateAssistant: (data: Partial<Assistant>, id: string) =>
    put({ url: `/api/evalapp/${id}`, data }),
  getAssistantDetail: (id: string) =>
    get<AssistantDetailResponse>({ url: `/api/evalapp/${id}` }),
  deleteAssistant: (id: string) =>
    del<DeleteAssistantResponse>({ url: `/api/evalapp/${id}` }),
};

export default service;

import $ from "jquery";

type HttpMethod = "GET" | "POST" | "PUT" | "DELETE" | "OPTION";

export interface RequestOption {
  data?: any;
  method?: HttpMethod;
  path?: string;
  url?: string;
}

function doApiRequest<R = any>(method: HttpMethod, options: RequestOption) {
  return Promise.resolve<R>(
    $.ajax({
      method,
      url: options.path || options.url,
      data: method === "GET" ? options.data : JSON.stringify(options.data),
      contentType: "application/json;charset=UTF-8",
    })
  );
}

export const get = <T = any>(options: RequestOption) =>
  doApiRequest<T>("GET", options);
export const post = <T = any>(options: RequestOption) =>
  doApiRequest<T>("POST", options);
export const put = <T = any>(options: RequestOption) =>
  doApiRequest<T>("PUT", options);
export const del = <T = any>(options: RequestOption) =>
  doApiRequest<T>("DELETE", options);
export const delay = <T>(data: T, ms: number) =>
  new Promise((resolve) => setTimeout(() => resolve(data || ""), ms || 600));

/**AI员工 */
import { del, get, post, put } from "./apiUtility";
import { defaultQuery } from "./common";
import { DataResponse, ListRequest, PageData } from "./type";
import { Robot, RobotFullResponse } from "./types/robots";
const service = {
  getRobotList: (
    data?: ListRequest & { disabled?: boolean; ids?: string }
  ) =>
    get<PageData<RobotFullResponse>>({
      url: "/api/robots",
      data: data ? { ...defaultQuery, ...data } : defaultQuery,
    }),
  createRobot: (data: Partial<Robot>) =>
    post({ url: "/api/robots", data }),
  updateRobot: (data: Partial<Robot>, id: string) =>
    put({ url: `/api/robots/${id}`, data }),
  getRobotDetail: (id: string) =>
    get<DataResponse<RobotFullResponse>>({ url: `/api/robots/${id}` }),
  deleteRobot: (id: string) => del({ url: `/api/robots/${id}` }),
};

export default service;

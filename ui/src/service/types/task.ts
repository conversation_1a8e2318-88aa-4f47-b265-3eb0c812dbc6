import { DataResponse, ListRequest, WithBaseInfo } from "../type";

/**任务状态:initial(未开始)、processing(进行中)、completed(已结束)  */
export type TaskStatus = "initial" | "processing" | "completed";
export const TaskStatusMap: Record<TaskStatus, string> = {
  initial: "未开始",
  processing: "进行中",
  completed: "已结束",
};
export type TaskListRequest = ListRequest & {
  status?: TaskStatus;
  robotId?: string;
  folderId?: string;
  employeeId?: string | number;
  postNumber?: string;
  contactStatus?: ContactStatus;
};
export interface Task {
  /**任务描述 */
  desc: null | string;
  /**关联AI员工ID */
  robotId: string;
  /**错误原因 */
  failedReason: null | string;
  /**主键ID */
  id: string;
  /**任务名称 */
  name: string;
  /**任务状态：initial(待启动)、processing(进行中)、completed(已结束)  */
  status: TaskStatus;
  /**任务类型: inbound（呼入）、outbound（呼出）、all（呼入+呼出）,老数据："in" | "out" */
  type: "inbound" | "outbound" | "all" | "in" | "out";
  /**背景图片，用于呼入端UI使用 */
  bgUrl?: string | null;
  /**坐席号码，多个按英文逗号分隔 */
  callerNumber?: string;
  /**联系人名单 */
  contacts?: {
    /**联系人号码 */
    phone: string;
    /**联系人姓名 */
    name?: string;
  }[];
  /**任务开始时间，示例：2025-03-10T09:33:08Z */
  beginTime: string;
  /**任务结束时间 */
  endTime: string;
  clientUrl: string;
  /**分析助手ID，多个用英文逗号分隔 */
  evalAppIds?: string;
  folderId: string;
}
export type TaskFullResponse = WithBaseInfo<Task> & {
  /** 报告任务状态（员工重跑状态/岗位生成报告状态） initial（未生成） completed(已完成)、processing(进行中) */
  taskStatus?: "initial" | "completed" | "processing";
};

export interface TaskStatusCount {
  /**占线数量 */
  busy: string;
  /**失败数量 */
  failed: string;
  /**未开始数量 */
  initial: string;
  /**空号数量 */
  notexist: string;
  /**进行中数量 */
  processing: string;
  /**成功数量 */
  success: string;
  /**总数量 */
  total: string;
}

export interface ContactListRequest extends ListRequest {
  /**关联任务ID */
  taskId?: string;
  /**执行状态:initial(未开始)、processing(进行中)、notexist(空号)、busy(占线)、success(成功)、failed(失败), 多个按英文逗号分隔 */
  status?: string;
  /**关联AI员工ID */
  robotId?: string;
}

export interface Contact {
  /**创建时间 */
  createTime: string;
  /**关联的AI员工ID */
  robotId: string;
  /**主键ID */
  id: string;
  /**任务名称 */
  name: string;
  /**手机号码 */
  phone: string;
  /**房间号 */
  roomId: string;
  /**执行状态：initial(未开始)、processing(进行中)、notexist(空号)、busy(占线)、success(成功)、failed(失败) */
  status: string;
  /**关联的任务ID */
  taskId: string;
  /**更新时间 */
  updateTime: string;
  /**通话时长 */
  durationText: string;
  externalId: string;
}
/** 联系人执行状态: initial(未开始)、processing(进行中)、notexist(空号)、busy(占线)、success(成功)、failed(失败) */
export type ContactStatus =
  | "initial"
  | "processing"
  | "notexist"
  | "busy"
  | "success"
  | "failed";
export const ContactStatusMap: Record<ContactStatus, string> = {
  initial: "未开始",
  processing: "进行中",
  notexist: "空号",
  busy: "占线",
  success: "成功",
  failed: "失败",
};

/**
 * 重跑报告请求参数
 */
export type RerunReportRequest =
  | {
      type: "user";
      userId: string;
      taskId: string;
    }
  | {
      type: "task";
      postNumber: string;
      taskId: string;
    };

/**
 * 重跑报告响应
 */
export type RerunReportResponse = DataResponse<boolean>;

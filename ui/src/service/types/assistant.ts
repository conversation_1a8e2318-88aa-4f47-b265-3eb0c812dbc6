import { DataResponse, ListRequest, PageResponse } from "../type";

export interface Assistant {
  id: string;
  name: string;
  avatar: string;
  desc: string;
  /** 在线、离线、空闲 */
  status: "online" | "offline" | "free";
  disabled: boolean;
  createTime: string;
  updateTime: string;
  llmId: string;
  temperature: number;
  systemMessage: string;
  creatorId: string;
  creatorName: string;
  /** 今日分析量 */
  todayCompletedcount: number;
}
export type AssistantDetailResponse = DataResponse<Assistant>;
export type DeleteAssistantResponse = DataResponse<string>;

export interface AssistantListRequest extends ListRequest {
  employeeId?: number;
  keyword?: string;
  order?: string;
  page?: number;
  pageSize?: number;
  sort?: string;
  status?: string;
}

export type AssistantListResponse = PageResponse<Assistant>;

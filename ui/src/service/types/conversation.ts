import { ListRequest, PageResponse } from "../type";

export interface ConversationListRequest extends ListRequest {
  /** 联系人ID */
  contactId?: string;
  /** 任务ID */
  taskId?: string;
  /** 用户ID */
  userId?: string;
}

export interface Conversation {
  /** 关联联系人ID */
  contactId: string;
  /** 对话文本 */
  content: string;
  /** 创建时间 */
  createTime: string;
  /** 关联AI员工ID */
  robotId: string;
  /** 主键ID */
  id: string;
  /** 语言 */
  language: string;
  /** 关联任务ID */
  taskId: string;
  /** 更新时间 */
  updateTime: string;
  userId: string;
  userType: string;
}

export type ConversationListResponse = PageResponse<Conversation>;

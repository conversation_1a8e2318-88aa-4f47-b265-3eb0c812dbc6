import { DataResponse, PageResponse } from "../type";
// 分组信息
export interface FolderInfo {
  createTime: string | null;
  id: string;
  name: string | null;
  parentId: string | null;
  type: string | null;
  updateTime: string | null;
}

// 分组列表响应
export type FolderListResponse = PageResponse<FolderInfo>;

// 新建分组请求
export interface CreateFolderRequest {
  name: string;
  parentId?: string | null;
  type?: string | null;
}

// 新建分组响应
export type CreateFolderResponse = DataResponse<FolderInfo>;

// 移动内容到分组请求
export interface MoveToFolderRequest {
  contentIds: number[];
  folderId: number;
}

// 移动内容到分组响应
export type MoveToFolderResponse = DataResponse<true>;

// 移出分组请求
export interface RemoveFromFolderRequest {
  contentIds?: number[] | null;
  folderId: number;
}

// 移出分组响应
export type RemoveFromFolderResponse = DataResponse<true>;

import { WithBaseInfo } from "../type";

export enum ValidPlantform {
  Volcano = "volcano",
  Aliyun = "aliyun",
}
/** AI员工 */
export interface Robot {
  /** ASR配置 */
  asrConfig: AsrConfig;
  /** 头像 */
  avatar?: null | string;
  /** 简介 */
  desc?: null | string;
  /** 是否禁用 */
  disabled?: boolean | null;
  /** 主键ID */
  id: string;
  /** LLM配置 */
  llmId?: number;
  llmConfig: LlmConfig;
  /** 名称 */
  name: string;
  /** TTS配置 */
  ttsConfig: TtsConfig;
  /** 进行中的任务数 */
  processingTaskCount: number;
  /** 今日已完成的任务数 */
  todayCompletedTaskCount: number;
  /**关联的RTC平台 */
  platform?: ValidPlantform;
  /**分析助手ID，多用英文逗号分隔 */
  evalAppIds?: string;
}

export type RobotFullResponse = WithBaseInfo<Robot>;

/**
 * ASR配置
 * AI员工ARS配置
 */
export interface AsrConfig {
  /** 模型提供商 */
  provider: string;
}

/**
 * LLM配置
 * AI员工LLM配置
 */
export interface LlmConfig {
  /** 模型提供商 */
  provider: string;
  /** 系统提示词 */
  systemMessage: null | string;
  /** 采样温度 */
  temperature: number | null;
  /** 欢迎语 */
  welcomeMessage: null | string;
  /** 历史问题轮数 */
  historyLength: number | null;
}

/**
 * TTS配置
 * AI员工TTS配置
 */
export interface TtsConfig {
  /** 模型提供商 */
  provider: string;
  /** 语速 */
  speedRatio: number | null;
  /** 音色 */
  voiceType: null | string;
  /** 音量 */
  volumeRatio: number | null;
}

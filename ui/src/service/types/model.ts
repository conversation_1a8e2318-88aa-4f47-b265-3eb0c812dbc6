import { PageResponse } from "../type";

export interface Model {
  provider: string;
  providerName: string;
  providerParams: Record<string, unknown>;
}
export interface LlmModel extends Model {
  providerParams: {
    temperatureMin: number;
    temperatureMax: number;
    temperatureDefault: number;
  };
}
export interface TtsModel extends Model {
  providerParams: {
    voiceType: string;
    voiceTypeName: string;
    speedRatioMin: number;
    speedRatioMax: number;
    speedRatioDefault: number;
    volumeRatioMin: number;
    volumeRatioMax: number;
    volumeRatioDefault: number;
  };
}

/** LLM模型信息 */
export interface Llm {
  id: string;
  /** 模型接入点ID */
  endPointId: string;
  /** 模型名称 */
  name: string;
  /** 模型平台：ArkV3(火山方舟) */
  provider: string;
  /** 模型扩展信息 */
  providerParams?: {
    /** 第三方模型ID */
    modelName: string;
    /** 第三方模型ApiUrl */
    apiUrl: string;
    /** 第三方模型Apikey */
    apiKey: string;
  };
  /** 备注 */
  remark: string;
  /** 模型图标 */
  icon?: string;
}

export type LlmListResponse = PageResponse<Llm>;

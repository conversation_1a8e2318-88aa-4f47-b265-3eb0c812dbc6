import { ListRequest, PageResponse } from "../type";

export interface EmployeeInfo {
  id: string;
  /** 姓名 */
  name: string | null;
  /** 员工工号 */
  jobNumber: string;
  /** 一级部门 */
  dept1: string | null;
  /** 二级部门 */
  dept2: string | null;
  /** 三级部门 */
  dept3: string | null;
  /** 四级部门 */
  dept4: string | null;
  /** 五级部门 */
  dept5: string | null;
  /** 六级部门 */
  dept6: string | null;
  /** 所属条线 */
  lineName: string | null;
  /** 个人岗位 */
  personalPost: string | null;
  /** 标准岗位 */
  standardPost: string | null;
  /** 个人职级 */
  personalRank: string | null;
  /** 岗位号码 */
  postNumber: string | null;
  /** 岗位职责内容 */
  jobContent: string | null;
  /** 钉钉Unionid */
  unionId: string | null;
  /** 相关访谈数   */
  taskNumber: string | null;
  /** 已完成访谈数 */
  completeTaskNumber: string | null;
}

export type EmployeeListRequest = ListRequest;
export type EmployeeListResponse = PageResponse<EmployeeInfo>;

export interface PositionReportInfo {
  /** 标岗名称 */
  standardPost: string;
  /** 岗位ID */
  postNumber: string;
  /** 一级部门描述 */
  dept1: string;
  /** 相关访谈总数 */
  taskNumber: string;
  /** 已完成访谈数 */
  completeTaskNumber: number;
  /** 二~六级部门描述（可选） */
  dept2?: string;
  dept3?: string;
  dept4?: string;
  dept5?: string;
  dept6?: string;
}

export type PositionReportListRequest = ListRequest & {
  postNumber?: string;
  dept?: string;
};

export type PositionReportListResponse = PageResponse<PositionReportInfo>;

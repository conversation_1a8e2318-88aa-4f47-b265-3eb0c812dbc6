/**任务管理 */
import { del, get, post, put } from "./apiUtility";
import { defaultQuery } from "./common";
import { DataResponse, PageData, PageResponse } from "./type";
import {
  Contact,
  ContactListRequest,
  RerunReportRequest,
  RerunReportResponse,
  Task,
  TaskFullResponse,
  TaskListRequest,
  TaskStatusCount
} from "./types/task";
const service = {
  getTaskList: (data?: TaskListRequest) => {
    // 仅 employeeId 存在时才传递 contactStatus
    const { contactStatus, employeeId, ...rest } = data || {};
    const params: TaskListRequest = { ...defaultQuery, ...rest };
    if (employeeId) {
      params.employeeId = employeeId;
      if (contactStatus) params.contactStatus = contactStatus;
    }
    if (data?.postNumber) {
      params.postNumber = data.postNumber;
    }
    return get<PageResponse<TaskFullResponse>>({
      url: "/api/tasks",
      data: params,
    });
  },
  createTask: (data: Partial<Task>) => post({ url: "/api/tasks", data }),
  updateTask: (data: Partial<Task>, id: string) =>
    put({ url: `/api/tasks/${id}`, data }),
  getTaskDetail: (id: string) =>
    get<DataResponse<TaskFullResponse>>({ url: `/api/tasks/${id}` }),
  deleteTask: (id: string) => del({ url: `/api/tasks/${id}` }),
  startTask: (id: string) => post({ url: `/api/tasks/${id}/start` }),
  stopTask: (id: string) => post({ url: `/api/tasks/${id}/stop` }),
  notice: (id: string) => post({ url: `/api/tasks/${id}/notice` }),
  /**状态统计 */
  getTaskStatusCount: (data?: { taskId?: string; robotId?: string }) =>
    get<DataResponse<TaskStatusCount | Record<string, TaskStatusCount>>>({
      url: "/api/contacts/status/count",
      data,
    }),
  /**联系人列表 */
  getContactList: (data?: ContactListRequest) =>
    get<PageData<Contact>>({ url: "/api/contacts", data }),
  /**添加联系人 */
  addContact: (taskId: string, contact: { name: string; externalId: string }) =>
    post({ url: `/api/contacts/${taskId}/save`, data: contact }),
  /**删除联系人 */
  removeContact: (
    taskId: string,
    contact: { name: string; externalId: string }
  ) => del({ url: `/api/contacts/${taskId}/remove`, data: contact }),
  /**导入联系人Excel */
  importContacts: (taskId: string, file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    return fetch(`/api/contacts/${taskId}/import`, {
      method: "POST",
      body: formData,
    }).then((r) => r.json());
  },
  /** 重跑报告（员工/岗位） */
  rerunReport: (data: RerunReportRequest) =>
    post<RerunReportResponse>({ url: "/api/evalapp/repeat/run", data }),
};

export default service;

import { get } from "./apiUtility";
import { DataResponse } from "./type";
import { LlmModel, Model, TtsModel } from "./types/model";
const service = {
  getLlmModel: () => get<DataResponse<LlmModel[]>>({ url: "/api/model/llm" }),
  getTtsModel: () => get<DataResponse<TtsModel[]>>({ url: "/api/model/tts" }),
  getAsrModel: () => get<DataResponse<Model[]>>({ url: "/api/model/asr" }),
};

export default service;

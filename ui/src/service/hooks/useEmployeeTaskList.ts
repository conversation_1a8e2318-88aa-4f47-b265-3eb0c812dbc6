import { useRequest } from "ahooks";
import EmployeeTaskService from "../employeeTask";
import { EmployeeTaskListRequest } from "../types/employeeTask";

export const useEmployeeTaskList = (params?: EmployeeTaskListRequest) => {
  return useRequest(
    (paginationParams?: EmployeeTaskListRequest) =>
      EmployeeTaskService.getEmployeeTaskList({ ...params, ...paginationParams }),
    {
      manual: true,
    }
  );
};

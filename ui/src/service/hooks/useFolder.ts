import { useRequest } from "ahooks";
import {
  createFolder,
  fetchFolderList,
  getFolderById,
  moveToFolder,
  removeFromFolder,
} from "../folder";
import {
  CreateFolderRequest,
  CreateFolderResponse,
  FolderListResponse,
  MoveToFolderRequest,
  MoveToFolderResponse,
  RemoveFromFolderRequest,
  RemoveFromFolderResponse,
} from "../types/folder";

export function useFetchFolderList() {
  return useRequest<FolderListResponse, [Parameters<typeof fetchFolderList>[0]]>(
    fetchFolderList,
    { manual: true }
  );
}

export function useFolderDetail() {
  return useRequest(getFolderById, {
    manual: true,
  });
}

export function useCreateFolder() {
  return useRequest<CreateFolderResponse, [CreateFolderRequest]>(createFolder, {
    manual: true,
  });
}

export function useMoveToFolder() {
  return useRequest<MoveToFolderResponse, [MoveToFolderRequest]>(moveToFolder, {
    manual: true,
  });
}

export function useRemoveFromFolder() {
  return useRequest<RemoveFromFolderResponse, [RemoveFromFolderRequest]>(removeFromFolder, {
    manual: true,
  });
}

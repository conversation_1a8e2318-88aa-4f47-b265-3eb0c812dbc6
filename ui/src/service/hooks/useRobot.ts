import { useRequest } from "ahooks";
import RobotService from "../robot";

export const useRobotList = () => {
  return useRequest(RobotService.getRobotList, { manual: true });
};
export const useCreateRobot = () => {
  return useRequest(RobotService.createRobot, { manual: true });
};

export const useUpdateRobot = () => {
  return useRequest(RobotService.updateRobot, { manual: true });
};
export const useDeleteRobot = () => {
  return useRequest(RobotService.deleteRobot, { manual: true });
};

export const useGetRobotDetail = () => {
  return useRequest(RobotService.getRobotDetail, {
    manual: true,
  });
};

import { useRequest } from "ahooks";
import TaskService from "../task";
import { TaskStatusCount } from "../types/task";
export const useTaskList = () => {
  return useRequest(TaskService.getTaskList, {
    manual: true,
  });
};

export const useCreateTask = () => {
  return useRequest(TaskService.createTask, {
    manual: true,
  });
};

export const useUpdateTask = () => {
  return useRequest(TaskService.updateTask, {
    manual: true,
  });
};

export const useDeleteTask = () => {
  return useRequest(TaskService.deleteTask, {
    manual: true,
  });
};

export const useGetTaskDetail = () => {
  return useRequest(TaskService.getTaskDetail, {
    manual: true,
  });
};
export const useStartTask = () => {
  return useRequest(TaskService.startTask, {
    manual: true,
  });
};
export const useStopTask = () => {
  return useRequest(TaskService.stopTask, {
    manual: true,
  });
};
export const useNotice = () => {
  return useRequest(TaskService.notice, {
    manual: true,
  });
};
export const useRerunReport = () => {
  return useRequest(TaskService.rerunReport, { manual: true });
};
export const initialTaskStatusCount: TaskStatusCount = {
  busy: "0",
  failed: "0",
  initial: "0",
  notexist: "0",
  processing: "0",
  success: "0",
  total: "0",
};
export const useTaskStatusCount = (robotId?: string) => {
  const res = useRequest(() => TaskService.getTaskStatusCount({ robotId }));
  return {
    ...res,
    data: res?.data?.success
      ? (res.data.data as TaskStatusCount)
      : initialTaskStatusCount,
  };
};

export const useTaskStatusCountByTakIds = () => {
  const initialData: Record<string, TaskStatusCount> = {};
  const res = useRequest(TaskService.getTaskStatusCount, {
    manual: true,
  });
  return {
    ...res,
    data: res?.data?.success
      ? (res.data.data as Record<string, TaskStatusCount>)
      : initialData,
  };
};

export const useContactList = () => {
  return useRequest(TaskService.getContactList, {
    manual: true,
  });
};

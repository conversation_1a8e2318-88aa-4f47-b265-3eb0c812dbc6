import { useRequest } from "ahooks";
import LlmsService from "../llms";

export const useLlmList = () => {
  return useRequest(LlmsService.getLlmList, { manual: true });
};

export const useCreateLlm = () => {
  return useRequest(LlmsService.createLlm, { manual: true });
};

export const useUpdateLlm = () => {
  return useRequest(LlmsService.updateLlm, { manual: true });
};

export const useDeleteLlm = () => {
  return useRequest(LlmsService.deleteLlm, { manual: true });
};
export const useGetLlmDetail = () => {
  return useRequest(LlmsService.getLlmDetail, {
    manual: true,
  });
};
export const useGetLlmProviders = () => {
  const res = useRequest(LlmsService.getLlmProviders, {
    // manual: true,
  });
  return {
    ...res,
    data: res?.data?.success
      ? (res.data.data as { desc: string; code: string }[])
      : [],
  };
};

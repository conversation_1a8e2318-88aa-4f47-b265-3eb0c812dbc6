import { useRequest } from "ahooks";
import EmployeeService from "../employee";

export const useEmployeeList = () => {
  return useRequest(EmployeeService.getEmployeeList, {
    manual: true,
  });
};
export const usePositionReportList = () => {
  return useRequest(EmployeeService.getPositionReportList, {
    manual: true,
  });
};

export const useEmployeeDetail = () => {
  return useRequest(EmployeeService.getEmployeeDetail, {
    manual: true,
  });
};

import { useRequest } from "ahooks";
import AssistantService from "../assistant";

export const useAssistantList = () => {
  return useRequest(AssistantService.getAssistantList, { manual: true });
};
export const useCreateAssistant = () => {
  return useRequest(AssistantService.createAssistant, { manual: true });
};

export const useUpdateAssistant = () => {
  return useRequest(AssistantService.updateAssistant, { manual: true });
};
export const useDeleteAssistant = () => {
  return useRequest(AssistantService.deleteAssistant, { manual: true });
};

export const useGetAssistantDetail = () => {
  return useRequest(AssistantService.getAssistantDetail, {
    manual: true,
  });
};

import { get, post } from "./apiUtility";
import { DataResponse } from "./type";
import {
  CreateFolderRequest,
  CreateFolderResponse,
  FolderInfo,
  FolderListResponse,
  MoveToFolderRequest,
  MoveToFolderResponse,
  RemoveFromFolderRequest,
  RemoveFromFolderResponse,
} from "./types/folder";

// 获取分组列表
export function fetchFolderList(params?: {
  keyword?: string;
  order?: string;
  page?: number;
  pageSize?: number;
  sort?: string;
  type?: string;
}) {
  return get<FolderListResponse>({ url: "/api/folder", data: params });
}

// 新建分组
export function createFolder(data: CreateFolderRequest) {
  return post<CreateFolderResponse>({ url: "/api/folder", data });
}

// 删除分组
export function getFolderById(id: string) {
  return get<DataResponse<FolderInfo>>({ url: `/api/folder/${id}` });
}

// 移动内容到分组
export function moveToFolder(data: MoveToFolderRequest) {
  return post<MoveToFolderResponse>({ url: "/api/folder/addToFolder", data });
}

// 移出分组
export function removeFromFolder(data: RemoveFromFolderRequest) {
  return post<RemoveFromFolderResponse>({
    url: "/api/folder/removeFromFolder",
    data,
  });
}

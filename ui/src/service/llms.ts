/**模型管理 */
import { del, get, post, put } from "./apiUtility";
import { defaultQuery } from "./common";
import { DataResponse, ListRequest, PageData } from "./type";
import { Llm } from "./types/model";

const service = {
  getLlmList: (data?: ListRequest) =>
    get<PageData<Llm>>({
      url: "/api/llms",
      data: data ? { ...defaultQuery, ...data } : defaultQuery,
    }),
  createLlm: (data: Partial<Llm>) => post({ url: "/api/llms", data }),
  updateLlm: (id: string, data: Partial<Llm>) =>
    put({ url: `/api/llms/${id}`, data }),
  getLlmDetail: (id: string) =>
    get<DataResponse<Llm>>({ url: `/api/llms/${id}` }),
  deleteLlm: (id: string) => del({ url: `/api/llms/${id}` }),
  getLlmProviders: () =>
    get<DataResponse<{ desc: string; code: string }[]>>({
      url: `/api/llms/providers`,
    }),
};

export default service;

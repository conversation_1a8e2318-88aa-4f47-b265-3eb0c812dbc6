import { get } from "./apiUtility";
import { DataResponse } from "./type";
import {
  EmployeeInfo,
  EmployeeListRequest,
  EmployeeListResponse,
  PositionReportListRequest,
  PositionReportListResponse,
} from "./types/employee";

const EmployeeService = {
  getEmployeeList: (data?: EmployeeListRequest) =>
    get<EmployeeListResponse>({
      url: "/api/employee",
      data,
    }),
  getPositionReportList: (params: PositionReportListRequest) =>
    get<PositionReportListResponse>({
      path: "/api/employee/postNumber",
      data: params,
    }),
  // 新增：获取单个员工详情
  getEmployeeDetail: (id: string) =>
    get<DataResponse<EmployeeInfo>>({
      url: `/api/employee/${id}`,
    }),
};

export default EmployeeService;

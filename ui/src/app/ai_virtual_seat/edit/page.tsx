"use client";
import BindingAnalysisAssistant from "@/app/task_board/edit/BindingAnalysisAssistant";
import AsrSelector from "@/common/component/AsrSelector";
import BlackHeadCard from "@/common/component/lib/BlackHeadCard";
import BlackTab from "@/common/component/lib/BlackTab";
import BtnModal from "@/common/component/lib/BtnModal";
import ContentWithBackIcon from "@/common/component/lib/ContentWithBackIcon";
import CustomAlert from "@/common/component/lib/CustomAlert";
import SmallTag from "@/common/component/lib/SmallTag";
import listStyles from "@/common/component/list.module.css";
import LlmConfigForm from "@/common/component/LlmConfigForm";
import { getStatus } from "@/common/component/RobotInfoCard";
import { useTtsList } from "@/common/component/TtsSelector";
import { getUrl } from "@/common/component/util";
import { PageUrlMap } from "@/common/constant";
import DoubaoModel from "@/common/svg/DoubaoModel";
import {
  useCreateRobot,
  useDeleteRobot,
  useGetRobotDetail,
  useUpdateRobot,
} from "@/service/hooks/useRobot";
import { LlmModel, TtsModel } from "@/service/types/model";
import { Robot, ValidPlantform } from "@/service/types/robots";
import {
  CaretRightOutlined,
  UploadOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  App,
  Avatar,
  Button,
  Col,
  ConfigProvider,
  Form,
  Input,
  Row,
  Select,
  Slider,
  Space,
  Spin,
  Switch,
  Tooltip,
  Typography,
  Upload,
} from "antd";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import styles from "./page.module.css";
import Statistic from "./Statistic";
const { TextArea } = Input;
const { Title, Text } = Typography;

export default function EditPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EditContent />
    </Suspense>
  );
}

enum TabKey {
  CONFIG = "config",
  STATISTIC = "statistic",
}

function EditContent() {
  const { message } = App.useApp();
  const router = useRouter();
  const searchParams = useSearchParams();
  const urlParamsId = searchParams.get("id");
  const platform = (searchParams.get("platform") ??
    ValidPlantform.Volcano) as ValidPlantform;
  const { runAsync: onCreate, loading: createLoading } = useCreateRobot();
  const {
    data: detailResponse,
    runAsync: getDetail,
    loading: detailLoading,
  } = useGetRobotDetail();
  const { runAsync: onUpdate, loading: updateLoading } = useUpdateRobot();
  const { runAsync: onDelete, loading: deleteLoading } = useDeleteRobot();

  const {
    modelList: ttsModelList,
    providerList,
    loading: ttsLoading,
  } = useTtsList();
  const [firstLoad, setFirstLoad] = useState(true);
  const [name, setName] = useState("");
  const [desc, setDesc] = useState("");
  const [mode, setMode] = useState<"view" | "edit">("edit");
  const [isActive, setIsActive] = useState(false);
  const [avatar, setAvatar] = useState(null);
  const [tab, setTab] = useState(TabKey.CONFIG);
  const [ttsParams, setTtsParams] = useState<
    TtsModel["providerParams"] | undefined
  >();
  const [llmParams] = useState<LlmModel["providerParams"] | undefined>();
  const detail: Partial<Robot> = detailResponse?.success
    ? detailResponse.data
    : {};
  const currentId = detail?.id;
  const [form] = Form.useForm<Partial<Robot>>();
  const ttsProvider = Form.useWatch(["ttsConfig", "provider"], form);
  const speedRatio = Form.useWatch(["ttsConfig", "speedRatio"], form);
  const volumeRatio = Form.useWatch(["ttsConfig", "volumeRatio"], form);
  const temperature = Form.useWatch(["llmConfig", "temperature"], form);
  const ttsModels = ttsModelList[ttsProvider] ?? [];

  useEffect(() => {
    if (!ttsLoading && urlParamsId && detail.id) {
      const currentTtsModel = ttsModels.find(
        (item) => item.voiceType === detail.ttsConfig.voiceType
      );
      setTtsParams(currentTtsModel);
      form.setFieldsValue(detail);
    }
  }, [ttsLoading, detail.id]);

  useEffect(() => {
    if (urlParamsId) {
      getDetail(urlParamsId).then((res) => {
        if (res.success) {
          const { name, desc, disabled, avatar } = res.data;
          setName(name);
          setDesc(desc);
          setIsActive(!disabled);
          setAvatar(avatar);
          setMode("view");
          setFirstLoad(false);
          form.setFieldsValue({
            ...res.data,
          });
        }
      });
    }
  }, [urlParamsId]);
  const isView = mode === "view";
  const onFinish = async (values: Partial<Robot>) => {
    const data = {
      ...values,
      name,
      desc,
      avatar,
      disabled: isActive,
      platform,
    };
    const res = currentId
      ? await onUpdate(data, currentId)
      : await onCreate(data);
    if (res.success) {
      setMode("view");
      message.success("保存成功");
      router.push(getUrl({ url: PageUrlMap.RobotList }));
    } else {
      message.error(res.error.message);
    }
  };
  const statusInfo = getStatus(detail as Robot);
  const readonly = false;
  // const readonly = statusInfo.status === "service";
  return (
    <ContentWithBackIcon>
      <Spin
        spinning={
          urlParamsId && firstLoad && (!detailResponse || detailLoading)
        }
      >
        <App className={styles.container}>
          <Row
            style={{
              marginBottom: "60px",
            }}
            gutter={12}
          >
            <Col>
              {isView ? (
                <Avatar size={40} src={avatar} icon={<UserOutlined />} />
              ) : (
                <Upload
                  showUploadList={false}
                  maxCount={1}
                  action={"/api/image/upload"}
                  accept="image/*"
                  beforeUpload={(file) => {
                    const isLt2M = file.size / 1024 / 1024 < 2;
                    if (!isLt2M) {
                      message.error("图片大小不超过2MB");
                    }
                    return isLt2M;
                  }}
                  onChange={async (info) => {
                    if (info.file.status === "done") {
                      if (info.file.response.success) {
                        setAvatar(info.file.response.data.url);
                      } else {
                        message.error(`${info.file.name} 文件上传失败`);
                      }
                    } else if (info.file.status === "error") {
                      message.error(`${info.file.name} 文件上传失败`);
                    }
                  }}
                >
                  <Tooltip title={"点击更换头像"}>
                    <Avatar size={40} icon={<UploadOutlined />} src={avatar} />
                  </Tooltip>
                </Upload>
              )}
            </Col>
            {isView ? (
              <Col span={23}>
                <Row justify="space-between">
                  <Col>
                    <Space>
                      <Title level={4}>{name}</Title>
                      <Tooltip title={statusInfo.tooltip}>
                        <Switch
                          checked={isActive}
                          disabled={statusInfo.status === "service"}
                          onChange={(checked) => {
                            setIsActive(checked);
                            onUpdate(
                              {
                                disabled: !checked,
                              },
                              currentId
                            ).catch((err) => {
                              message.error(err.message);
                              setIsActive(!checked);
                            });
                          }}
                        />
                      </Tooltip>
                    </Space>
                  </Col>
                  <Col>
                    <Space size={"small"}>
                      <ConfigProvider
                        theme={{
                          components: {
                            Button: {
                              colorLink: "#171F2D",
                              colorLinkHover: "#303847",
                            },
                          },
                        }}
                      >
                        <Button
                          type="link"
                          onClick={() => {
                            router.push(
                              getUrl({
                                url: PageUrlMap.TaskEdit,
                                params: {
                                  robotId: currentId,
                                },
                              })
                            );
                          }}
                        >
                          创建新任务
                        </Button>
                      </ConfigProvider>
                      <Button
                        onClick={() => setMode("edit")}
                        loading={updateLoading}
                      >
                        编辑
                      </Button>
                    </Space>
                  </Col>
                </Row>
                <Text type="secondary">{desc}</Text>
              </Col>
            ) : (
              <Col span={23}>
                <Row justify="space-between">
                  <Col>
                    <ConfigProvider
                      theme={{
                        components: {
                          Input: {
                            colorTextPlaceholder: "#000",
                          },
                        },
                      }}
                    >
                      <Input
                        placeholder="请输入访谈助手名称"
                        variant="borderless"
                        style={{
                          paddingLeft: 0,
                          fontSize: "20px",
                          fontWeight: "500",
                        }}
                        value={name}
                        onChange={(e) => {
                          setName(e.target.value);
                        }}
                      />
                    </ConfigProvider>
                  </Col>
                  <Button
                    type="primary"
                    loading={createLoading}
                    onClick={async () => {
                      const data = {
                        name,
                        desc,
                        avatar,
                      };
                      const res = currentId
                        ? await onUpdate(data, currentId)
                        : await onCreate(data);
                      if (res.success) {
                        setMode("view");
                        message.success("保存成功");
                      } else {
                        message.error(res.message);
                      }
                    }}
                  >
                    完成
                  </Button>
                </Row>
                <TextArea
                  style={{
                    marginTop: "20px",
                  }}
                  placeholder="请输入该访谈助手名称简介"
                  autoSize={{ minRows: 2, maxRows: 6 }}
                  value={desc}
                  onChange={(e) => {
                    setDesc(e.target.value);
                  }}
                />
              </Col>
            )}
          </Row>
          {urlParamsId && (
            <BlackTab
              items={[
                {
                  key: TabKey.CONFIG,
                  label: "访谈助手配置",
                },
                {
                  key: TabKey.STATISTIC,
                  label: (
                    <Space>
                      任务状态
                      <SmallTag color={statusInfo.color}>
                        {statusInfo.label}
                      </SmallTag>
                    </Space>
                  ),
                },
              ]}
              onChange={(key) => {
                setTab(key as TabKey);
              }}
            />
          )}
          {tab === TabKey.CONFIG && (
            <>
              {readonly && (
                <CustomAlert message="该访谈助手正在执行任务中，暂不支持编辑配置，请于任务结束再进行编辑" />
              )}
              <Form
                layout="vertical"
                form={form}
                initialValues={{
                  ...detail,
                }}
                onFinish={onFinish}
                // disabled={readonly}
              >
                <Row gutter={[24, 24]} align="stretch">
                  <Col span={12}>
                    <LlmConfigForm
                      form={form}
                      llmParams={llmParams}
                      temperature={temperature}
                      visibleFields={
                        platform === ValidPlantform.Aliyun
                          ? [
                              "systemMessage",
                              "temperature",
                              "welcomeMessage",
                              "historyLength",
                            ]
                          : undefined
                      }
                      // readonly={readonly}
                    />
                  </Col>
                  {platform === ValidPlantform.Volcano && (
                    <>
                      <Col span={12}>
                        <BlackHeadCard
                          className={styles.section}
                          title="TTS设置"
                        >
                          <Form.Item
                            name={["ttsConfig", "provider"]}
                            label="声音供应商"
                          >
                            <Select
                              loading={ttsLoading}
                              placeholder="请选择"
                              onChange={(value) => {
                                const currentTtsModel =
                                  ttsModelList[value]?.[0];
                                setTtsParams(currentTtsModel);
                                form.setFieldsValue({
                                  ...form.getFieldsValue(),
                                  ttsConfig: {
                                    ...form.getFieldsValue().ttsConfig,
                                    provider: value,
                                    voiceType: currentTtsModel?.voiceType,
                                    speedRatio:
                                      currentTtsModel?.speedRatioDefault,
                                    volumeRatio:
                                      currentTtsModel?.volumeRatioDefault,
                                  },
                                });
                              }}
                              options={providerList.map((item) => ({
                                label: (
                                  <Space>
                                    {item.provider.startsWith("volcano") && (
                                      <div className={listStyles.doubao}>
                                        <DoubaoModel />
                                      </div>
                                    )}
                                    {item.provider.startsWith("minimax") && (
                                      <div className={listStyles.doubao}>
                                        <Image
                                          src={getUrl({
                                            url: "/minimax.jpeg",
                                            isPage: false,
                                          })}
                                          alt="minimax"
                                          width={20}
                                          height={20}
                                        />
                                      </div>
                                    )}
                                    {item.providerName}
                                  </Space>
                                ),
                                value: item.provider,
                              }))}
                            />
                          </Form.Item>
                          <Form.Item
                            name={["ttsConfig", "voiceType"]}
                            label="类型"
                          >
                            <Select
                              placeholder="请选择类型"
                              options={ttsModels.map((item) => ({
                                label: item.voiceTypeName,
                                value: item.voiceType,
                              }))}
                              onChange={(value) => {
                                const currentTtsModel = ttsModels.find(
                                  (item) => item.voiceType === value
                                );
                                setTtsParams(currentTtsModel);
                                form.setFieldsValue({
                                  ...form.getFieldsValue(),
                                  ttsConfig: {
                                    ...form.getFieldsValue().ttsConfig,
                                    voiceType: value,
                                    speedRatio:
                                      currentTtsModel?.speedRatioDefault,
                                    volumeRatio:
                                      currentTtsModel?.volumeRatioDefault,
                                  },
                                });
                              }}
                            />
                          </Form.Item>
                          <Row justify="space-between">
                            <Col span={8}>
                              <Form.Item
                                name={["ttsConfig", "speedRatio"]}
                                label="速度"
                              >
                                <Row gutter={12} align="middle">
                                  <Col span={22}>
                                    <Slider
                                      value={speedRatio}
                                      min={ttsParams?.speedRatioMin}
                                      max={ttsParams?.speedRatioMax}
                                      step={0.1}
                                      onChange={(value) => {
                                        form.setFieldValue(
                                          ["ttsConfig", "speedRatio"],
                                          value
                                        );
                                      }}
                                    />
                                  </Col>
                                  <Col span={2}>{ttsParams?.speedRatioMax}</Col>
                                </Row>
                              </Form.Item>
                            </Col>
                            <Col span={8}>
                              <Form.Item
                                name={["ttsConfig", "volumeRatio"]}
                                label="音调"
                              >
                                <Row gutter={12} align="middle">
                                  <Col span={22}>
                                    <Slider
                                      value={volumeRatio}
                                      min={ttsParams?.volumeRatioMin}
                                      max={ttsParams?.volumeRatioMax}
                                      step={0.1}
                                      onChange={(value) => {
                                        form.setFieldValue(
                                          ["ttsConfig", "volumeRatio"],
                                          value
                                        );
                                      }}
                                    />
                                  </Col>
                                  <Col span={2}>
                                    {ttsParams?.volumeRatioMax}
                                  </Col>
                                </Row>
                              </Form.Item>
                            </Col>
                          </Row>
                          <Row style={{ marginTop: "20px" }}>
                            <Col span={4}>
                              <Button
                                type="primary"
                                icon={<CaretRightOutlined />}
                              >
                                播放声音
                              </Button>
                            </Col>
                          </Row>
                        </BlackHeadCard>
                      </Col>
                      <Col span={12}>
                        <BlackHeadCard
                          className={styles.section}
                          title="ASR设定"
                        >
                          <Form.Item
                            name={["asrConfig", "provider"]}
                            label="ASR供应商"
                          >
                            <AsrSelector />
                          </Form.Item>
                        </BlackHeadCard>
                      </Col>
                    </>
                  )}
                  {platform === ValidPlantform.Aliyun && (
                    <Col span={12}>
                      <BlackHeadCard
                        className={styles.section}
                        title="平台设置"
                      >
                        <Form.Item name="externalAgentId" label="智能体ID">
                          <Input placeholder="请输入智能体ID" />
                        </Form.Item>
                      </BlackHeadCard>
                    </Col>
                  )}
                  <Col span={12}>
                    <BlackHeadCard className={styles.section} title="任务指派">
                      <Form.Item name="evalAppIds" label="绑定分析助手">
                        <BindingAnalysisAssistant multiple />
                      </Form.Item>
                    </BlackHeadCard>
                  </Col>
                </Row>
              </Form>
              {!readonly && (
                <Row
                  justify={urlParamsId ? "space-between" : "start"}
                  align="middle"
                  style={{ marginTop: "20px" }}
                >
                  <Space size={16}>
                    <Button
                      type="primary"
                      onClick={form.submit}
                      loading={updateLoading || createLoading}
                    >
                      保存
                    </Button>
                    <Button
                      onClick={() => {
                        router.push(getUrl({ url: PageUrlMap.RobotList }));
                      }}
                    >
                      取消
                    </Button>
                  </Space>
                  {urlParamsId && (
                    <BtnModal
                      btnText="删除访谈助手"
                      title="确定删除该访谈助手？"
                      onOk={async () => {
                        const res = await onDelete(currentId);
                        if (res.success) {
                          message.success("删除成功");
                          router.push(getUrl({ url: PageUrlMap.RobotList }));
                          return true;
                        } else {
                          message.error(res.error.message);
                          return false;
                        }
                      }}
                      btnProps={{
                        loading: deleteLoading,
                      }}
                      description="访谈助手一旦删除, 无法恢复"
                    />
                  )}
                </Row>
              )}
            </>
          )}
          {tab === TabKey.STATISTIC && <Statistic robotId={urlParamsId} />}
        </App>
      </Spin>
    </ContentWithBackIcon>
  );
}

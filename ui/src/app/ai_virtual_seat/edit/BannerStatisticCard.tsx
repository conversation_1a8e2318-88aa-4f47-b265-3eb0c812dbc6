"use client";

import BlackStatisticCard, {
  StaticNumber,
} from "@/common/component/lib/BlackStatisticCard";
import { TaskStatusCount } from "@/service/types/task";
import { Col, Row, Spin } from "antd";

type StatisticProps = {
  cardProps?: {
    bgColor?: string;
  };
  badgeProps?: {
    textColor?: string;
  };
  numberProps?: {
    textColor?: string;
  };
};
const SelectedBgColor =
  "linear-gradient(107deg, #2175F2 51.75%, #DAF0FE 240.63%)";

export enum Status {
  PROCESSING = "processing",
  FAILED = "failed",
  INITIAL = "initial",
  SUCCESS = "success",
}
export const StatusMap = {
  [Status.PROCESSING]: "processing",
  [Status.FAILED]: "failed,notexist,busy",
  [Status.INITIAL]: "initial",
  [Status.SUCCESS]: "success",
};
export default function BannerStatisticCard({
  loading,
  data,
  processingProps,
  selectConfig,
}: {
  loading: boolean;
  data: TaskStatusCount;
  /**实时外呼中 */
  processingProps?: StatisticProps;
  // /**实时未接通(空号/盲线/故障) */
  // errorProps?: StatisticProps;
  // /**待外呼 */
  // successProps?: StatisticProps;
  // /**待外呼 */
  // initialProps?: StatisticProps;
  selectConfig?: {
    enable?: boolean;
    onSelect?: (data: Status) => void;
    selectedKey?: Status;
  };
}) {
  const { processing, notexist, busy, failed, initial, success } = data ?? {
    busy: "0",
    failed: "0",
    initial: "0",
    notexist: "0",
    processing: "0",
    success: "0",
    total: "0",
  };
  const {
    enable,
    onSelect,
    selectedKey = Status.PROCESSING,
  } = selectConfig ?? {};
  return (
    <Spin spinning={loading}>
      <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
        <Col sm={12} xxl={6} span={6}>
          <BlackStatisticCard
            badge={{
              color: "#23E4A6",
              text: "实时外呼中",
              style: {
                color: processingProps?.badgeProps?.textColor || "#fff",
              },
            }}
            cardBgColor={
              enable && selectedKey === Status.PROCESSING
                ? SelectedBgColor
                : processingProps?.cardProps?.bgColor
            }
            onClick={() => {
              onSelect?.(Status.PROCESSING);
            }}
          >
            <StaticNumber
              value={processing}
              style={{
                color: processingProps?.numberProps?.textColor || "#fff",
              }}
            />
          </BlackStatisticCard>
        </Col>
        <Col sm={12} xxl={6} span={6}>
          <BlackStatisticCard
            badge={{ status: "error", text: "实时未接通(空号/盲线/故障)" }}
            cardBgColor={
              enable && selectedKey === Status.FAILED
                ? SelectedBgColor
                : undefined
            }
            onClick={() => {
              onSelect?.(Status.FAILED);
            }}
          >
            <StaticNumber value={`${+notexist + +busy + +failed}`} />
          </BlackStatisticCard>
        </Col>
        <Col sm={12} xxl={6} span={6}>
          <BlackStatisticCard
            badge={{ status: "warning", text: "待外呼" }}
            cardBgColor={
              enable && selectedKey === Status.INITIAL
                ? SelectedBgColor
                : undefined
            }
            onClick={() => {
              onSelect?.(Status.INITIAL);
            }}
          >
            <StaticNumber value={initial} />
          </BlackStatisticCard>
        </Col>
        <Col sm={12} xxl={6} span={6}>
          <BlackStatisticCard
            badge={{ text: "已完成", color: "#A8A8A8" }}
            cardBgColor={
              enable && selectedKey === Status.SUCCESS
                ? SelectedBgColor
                : undefined
            }
            onClick={() => {
              onSelect?.(Status.SUCCESS);
            }}
          >
            <StaticNumber value={success} />
          </BlackStatisticCard>
        </Col>
      </Row>
    </Spin>
  );
}

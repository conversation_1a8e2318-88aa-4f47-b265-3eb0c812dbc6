//TODO: 样式变形需要优化
"use client";
import BlackHeadCard from "@/common/component/lib/BlackHeadCard";
import listStyles from "@/common/component/list.module.css";
import { useTtsList } from "@/common/component/TtsSelector";
import DoubaoModel from "@/common/svg/DoubaoModel";
import { TtsModel } from "@/service/types/model";
import type { TtsConfig } from "@/service/types/robots";
import { CaretRightOutlined } from "@ant-design/icons";
import { Button, Col, Form, Row, Select, Slider, Space } from "antd";
import { useEffect, useState } from "react";
import styles from "./page.module.css";
export default function TtsConfig({ data }: { data: TtsConfig }) {
  const {
    modelList: ttsModelList,
    providerList,
    loading: ttsLoading,
  } = useTtsList();
  const [ttsParams, setTtsParams] = useState<
    TtsModel["providerParams"] | undefined
  >();
  const [form] = Form.useForm<Partial<TtsConfig>>();
  const ttsProvider = Form.useWatch(["ttsConfig", "provider"], form);
  const ttsModels = ttsModelList[ttsProvider] ?? [];
  useEffect(() => {
    form.setFieldsValue(data);
  }, [JSON.stringify(data)]);
  useEffect(() => {
    if (!ttsLoading && data) {
      const currentTtsModel = ttsModels.find(
        (item) => item.voiceType === data.voiceType
      );
      setTtsParams(currentTtsModel);
    }
  }, [ttsLoading]);
  return (
    <BlackHeadCard className={styles.section} title="TTS设置">
      <Form form={form}>
        <Form.Item name={["ttsConfig", "provider"]} label="声音供应商">
          <Select
            loading={ttsLoading}
            placeholder="请选择"
            options={providerList.map((item) => ({
              label: (
                <Space>
                  {item.provider.startsWith("volcano") && (
                    <div className={listStyles.doubao}>
                      <DoubaoModel />
                    </div>
                  )}
                  {item.providerName}
                </Space>
              ),
              value: item.provider,
            }))}
          />
        </Form.Item>
        <Form.Item name={"voiceType"} label="类型">
          <Select
            placeholder="请选择类型"
            options={ttsModels.map((item) => ({
              label: item.voiceTypeName,
              value: item.voiceType,
            }))}
            onChange={(value) => {
              const currentTtsModel = ttsModels.find(
                (item) => item.voiceType === value
              );
              setTtsParams(currentTtsModel);
              form.setFieldsValue({
                ...form.getFieldsValue(),
                voiceType: value,
                speedRatio: currentTtsModel?.speedRatioDefault,
                volumeRatio: currentTtsModel?.volumeRatioDefault,
              });
            }}
          />
        </Form.Item>
        <Row justify="space-between">
          <Col span={8}>
            <Form.Item name={"speedRatio"} label="速度">
              <Row gutter={12} align="middle">
                <Col span={22}>
                  <Slider
                    min={ttsParams?.speedRatioMin}
                    max={ttsParams?.speedRatioMax}
                    step={0.1}
                    onChange={(value) => {
                      form.setFieldValue("speedRatio", value);
                    }}
                  />
                </Col>
                <Col span={2}>{ttsParams?.speedRatioMax}</Col>
              </Row>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name={"volumeRatio"} label="音调">
              <Row gutter={12} align="middle">
                <Col span={22}>
                  <Slider
                    min={ttsParams?.volumeRatioMin}
                    max={ttsParams?.volumeRatioMax}
                    step={0.1}
                    onChange={(value) => {
                      form.setFieldValue("volumeRatio", value);
                    }}
                  />
                </Col>
                <Col span={2}>{ttsParams?.volumeRatioMax}</Col>
              </Row>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Row style={{ marginTop: "20px" }}>
        <Col span={4}>
          <Button type="primary" icon={<CaretRightOutlined />}>
            播放声音
          </Button>
        </Col>
      </Row>
    </BlackHeadCard>
  );
}

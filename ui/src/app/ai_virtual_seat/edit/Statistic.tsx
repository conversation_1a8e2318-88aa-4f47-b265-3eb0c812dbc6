"use client";

import TaskList from "@/app/task/TaskList";
import BlackHeadCard from "@/common/component/lib/BlackHeadCard";
import { useTaskStatusCount } from "@/service/hooks/useTask";
import BannerStatisticCard from "./BannerStatisticCard";
export default function Statistic({ robotId }: { robotId?: string }) {
  const { loading: countLoading, data: count } = useTaskStatusCount(robotId);

  return (
    <>
      <BannerStatisticCard
        loading={countLoading}
        data={count}
        processingProps={{
          badgeProps: {
            textColor: "#23E4A6",
          },
          numberProps: {
            textColor: "#23E4A6",
          },
        }}
      />
      <BlackHeadCard title="任务执行拦">
        <TaskList robotId={robotId} />
      </BlackHeadCard>
    </>
  );
}

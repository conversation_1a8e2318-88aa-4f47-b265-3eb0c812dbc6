"use client";
import OptionsDropdown, {
  IOptions,
} from "@/common/component/lib/OptionsDropdown";
import PageHeader from "@/common/component/lib/PageHeader";
import ScrollList from "@/common/component/lib/ScrollList";
import RobotInfoCard from "@/common/component/RobotInfoCard";
import { getUrl } from "@/common/component/util";
import { NavigationKey, page_schema, PageUrlMap } from "@/common/constant";
import { useRobotList } from "@/service/hooks/useRobot";
import { ListRequest } from "@/service/type";
import { RobotFullResponse, ValidPlantform } from "@/service/types/robots";
import { TaskStatus } from "@/service/types/task";
import { PlusOutlined } from "@ant-design/icons";
import { Button, Col, Dropdown, Layout } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";

const { Content } = Layout;

const Platforms: { key: ValidPlantform; label: string }[] = [
  {
    key: ValidPlantform.Volcano,
    label: "火山引擎",
  },
  {
    key: ValidPlantform.Aliyun,
    label: "阿里云",
  },
];

const statusOptions: IOptions[] = [
  {
    label: "启用中",
    value: "true",
  },
  {
    label: "已禁用",
    value: "false",
  },
];

export default function AIVirtualSeatPage() {
  const router = useRouter();
  const { loading: listLoading, runAsync: getList } = useRobotList();
  const [keyword, setKeyword] = useState("");
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [status, setStatus] = useState("");

  return (
    <Content>
      <PageHeader
        title={page_schema[NavigationKey.AIVirtualSeat].header.title}
        description={
          page_schema[NavigationKey.AIVirtualSeat].header.description
        }
        customBtn={
          <Dropdown
            menu={{
              items: Platforms.map((item) => {
                return {
                  key: item.key,
                  label: item.label,
                  onClick: () => {
                    router.push(
                      getUrl({
                        url: PageUrlMap.RobotEdit,
                        params: { platform: item.key },
                      })
                    );
                  },
                };
              }),
            }}
            trigger={["click"]}
          >
            <Button
              type="primary"
              icon={<PlusOutlined />}
              loading={listLoading}
            >
              创建新员工
            </Button>
          </Dropdown>
        }
        onSearch={(newKeyword) => {
          setKeyword(newKeyword);
        }}
        extraFilter={[
          <OptionsDropdown
            value={status}
            onChange={setStatus}
            options={statusOptions}
            placeholder="全部启用状态"
            key="status"
          />,
        ]}
      />
      <ScrollList<
        RobotFullResponse,
        ListRequest & { status?: TaskStatus; robotId?: string }
      >
        gutter={[16, 16]}
        renderItem={(item) => (
          <Col span={6} key={item.id}>
            <RobotInfoCard
              robot={item}
              refreshList={() => {
                setRefreshFlag(refreshFlag + 1);
              }}
            />
          </Col>
        )}
        getList={getList}
        params={{
          keyword,
          disabled: status === "false",
        }}
        refreshDeps={[keyword, refreshFlag, status]}
      />
    </Content>
  );
}

"use client";
import AssistantC<PERSON> from "@/common/component/AssistantCard";
import OptionsDropdown, { IOptions } from "@/common/component/lib/OptionsDropdown";
import PageHeader from "@/common/component/lib/PageHeader";
import ScrollList from "@/common/component/lib/ScrollList";
import { getUrl } from "@/common/component/util";
import { NavigationKey, page_schema, PageUrlMap } from "@/common/constant";
import { useAssistantList } from "@/service/hooks/useAssistant";
import { Assistant, AssistantListRequest } from "@/service/types/assistant";
import { Col, Layout } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";

const { Content } = Layout;

const statusOptions: IOptions[] = [
  { label: "在线", value: "online" },
  { label: "离线", value: "offline" },
  { label: "空闲", value: "free" },
];

export default function AnalysisAssistantPage() {
  const router = useRouter();
  const { loading: listLoading, runAsync: getList } = useAssistantList();
  const [keyword, setKeyword] = useState("");
  const [status, setStatus] = useState("");

  return (
    <Content>
      <PageHeader
        title={page_schema[NavigationKey.AnalysisAssistant].header.title}
        description={
          page_schema[NavigationKey.AnalysisAssistant].header.description
        }
        btn={{
          text: "创建新分析助手",
          onClick: () => {
            router.push(getUrl({ url: PageUrlMap.AnalysisAssistantEdit }));
          },
          loading: listLoading,
        }}
        onSearch={(newKeyword) => {
          setKeyword(newKeyword);
        }}
        extraFilter={[
          <OptionsDropdown
            value={status}
            onChange={setStatus}
            options={statusOptions}
            placeholder="全部状态"
            key="status"
          />,
        ]}
      />
      <ScrollList<Assistant, AssistantListRequest>
        gutter={[16, 16]}
        renderItem={(item) => (
          <Col span={6} key={item.id}>
            <AssistantCard assistant={item} />
          </Col>
        )}
        getList={getList}
        params={{
          keyword,
          status: status || undefined,
        }}
        refreshDeps={[keyword, status]}
      />
    </Content>
  );
}

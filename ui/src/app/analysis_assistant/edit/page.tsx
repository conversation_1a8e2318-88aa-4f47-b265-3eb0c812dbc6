"use client";
import BtnModal from "@/common/component/lib/BtnModal";
import ContentWithBackIcon from "@/common/component/lib/ContentWithBackIcon";
import LlmConfigForm from "@/common/component/LlmConfigForm";
import { getUrl } from "@/common/component/util";
import { PageUrlMap } from "@/common/constant";
import {
  useCreateAssistant,
  useDeleteAssistant,
  useGetAssistantDetail,
  useUpdateAssistant,
} from "@/service/hooks/useAssistant";
import { Assistant, AssistantDetailResponse } from "@/service/types/assistant";
import { LlmModel } from "@/service/types/model";
import {
  UploadOutlined,
  UserOutlined
} from "@ant-design/icons";
import {
  App,
  Avatar,
  Button,
  Col,
  ConfigProvider,
  Form,
  Input,
  Layout,
  Row,
  Space,
  Spin,
  Switch,
  Tooltip,
  Typography,
  Upload,
} from "antd";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import styles from "../../ai_virtual_seat/edit/page.module.css";
const { Content } = Layout;
const { TextArea } = Input;
const { Title, Text } = Typography;

export default function EditPage() {
  return (
    <Spin spinning={false}>
      <EditContent />
    </Spin>
  );
}

function EditContent() {
  const { message } = App.useApp();
  const router = useRouter();
  const searchParams = useSearchParams();
  const urlParamsId = searchParams.get("id");
  const { runAsync: onCreate, loading: createLoading } = useCreateAssistant();
  const {
    data: detailResponse,
    runAsync: getDetail,
    loading: detailLoading,
  } = useGetAssistantDetail();
  const { runAsync: onUpdate, loading: updateLoading } = useUpdateAssistant();
  const { runAsync: onDelete, loading: deleteLoading } = useDeleteAssistant();
  const [firstLoad, setFirstLoad] = useState(true);
  const [name, setName] = useState("");
  const [desc, setDesc] = useState("");
  const [isActive, setIsActive] = useState(false);
  const [avatar, setAvatar] = useState<string | null>(null);
  const [mode, setMode] = useState<"view" | "edit">("edit");
  const [llmParams] = useState<LlmModel["providerParams"] | undefined>();

  const [form] = Form.useForm<
    Partial<
      Assistant & {
        llmConfig?: {
          temperature: number;
          systemMessage: string;
        };
      }
    >
  >();
  const temperature = Form.useWatch(["llmConfig", "temperature"], form);

  useEffect(() => {
    if (urlParamsId) {
      getDetail(urlParamsId).then((res: AssistantDetailResponse) => {
        if (res.success) {
          const { name, desc, avatar, disabled } = res.data;
          setName(name);
          setDesc(desc);
          setIsActive(!disabled);
          setAvatar(avatar);
          setMode("view");
          setFirstLoad(false);
          form.setFieldsValue({
            ...res.data,
            llmConfig: {
              temperature: res.data.temperature,
              systemMessage: res.data.systemMessage,
            },
          });
        }
      });
    }
  }, [urlParamsId]);
  const isView = mode === "view";
  const onFinish = async (
    values: Partial<
      Assistant & {
        llmConfig?: {
          temperature: number;
          systemMessage: string;
        };
      }
    >
  ) => {
    const data: Partial<
      Assistant & {
        llmConfig?: {
          temperature: number;
          systemMessage: string;
        };
      }
    > = {
      ...values,
      name,
      desc,
      avatar,
      disabled: !isActive,
      status: isActive ? "free" : "offline",
      llmConfig: undefined,
      ...(values.llmConfig ? values.llmConfig : {}),
    };
    let res;
    if (urlParamsId) {
      res = await onUpdate(data, urlParamsId);
    } else {
      res = await onCreate(data);
    }
    if (res.success) {
      setMode("view");
      message.success("保存成功");
      router.push(getUrl({ url: PageUrlMap.AnalysisAssistantList }));
    } else {
      message.error("保存失败");
    }
  };
  return (
    <ContentWithBackIcon>
      <Spin
        spinning={
          urlParamsId && firstLoad && (!detailResponse || detailLoading)
        }
      >
        <App className={styles.container}>
          <Row style={{ marginBottom: "60px" }} gutter={12}>
            <Col>
              {isView ? (
                <Avatar size={40} src={avatar} icon={<UserOutlined />} />
              ) : (
                <Upload
                  showUploadList={false}
                  maxCount={1}
                  action={"/api/image/upload"}
                  accept="image/*"
                  beforeUpload={(file) => {
                    const isLt2M = file.size / 1024 / 1024 < 2;
                    if (!isLt2M) {
                      message.error("图片大小不超过2MB");
                    }
                    return isLt2M;
                  }}
                  onChange={async (info) => {
                    if (info.file.status === "done") {
                      if (info.file.response.success) {
                        setAvatar(info.file.response.data.url);
                      } else {
                        message.error(`${info.file.name} 文件上传失败`);
                      }
                    } else if (info.file.status === "error") {
                      message.error(`${info.file.name} 文件上传失败`);
                    }
                  }}
                >
                  <Tooltip title={"点击更换头像"}>
                    <Avatar
                      size={40}
                      icon={<UploadOutlined />}
                      src={avatar || undefined}
                    />
                  </Tooltip>
                </Upload>
              )}
            </Col>
            {isView ? (
              <Col span={23}>
                <Row justify="space-between">
                  <Col>
                    <Space>
                      <Title level={4}>{name}</Title>
                      <Switch
                        checked={isActive}
                        onChange={(checked) => setIsActive(checked)}
                      />
                    </Space>
                  </Col>
                  <Col>
                    <Button
                      onClick={() => setMode("edit")}
                      loading={updateLoading}
                    >
                      编辑
                    </Button>
                  </Col>
                </Row>
                <Text type="secondary">{desc}</Text>
              </Col>
            ) : (
              <Col span={23}>
                <Row justify="space-between">
                  <Col>
                    <ConfigProvider
                      theme={{
                        components: {
                          Input: { colorTextPlaceholder: "#000" },
                        },
                      }}
                    >
                      <Input
                        placeholder="请输入分析助手名称"
                        variant="borderless"
                        style={{
                          paddingLeft: 0,
                          fontSize: "20px",
                          fontWeight: "500",
                        }}
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                      />
                    </ConfigProvider>
                  </Col>
                  <Button
                    type="primary"
                    loading={createLoading || updateLoading}
                    onClick={form.submit}
                  >
                    完成
                  </Button>
                </Row>
                <TextArea
                  style={{ marginTop: "20px" }}
                  placeholder="请输入该分析助手名称简介"
                  autoSize={{ minRows: 2, maxRows: 6 }}
                  value={desc}
                  onChange={(e) => setDesc(e.target.value)}
                />
              </Col>
            )}
          </Row>
          <Form
            layout="vertical"
            form={form}
            initialValues={{ name, desc, avatar }}
            onFinish={onFinish}
          >
            <Row gutter={[24, 24]} align="stretch">
              <Col span={24}>
                <LlmConfigForm
                  form={form}
                  llmParams={llmParams}
                  temperature={temperature}
                  // readonly={isView}
                  visibleFields={["llmId", "systemMessage", "temperature"]}
                />
              </Col>
            </Row>
            <Row
              justify="space-between"
              align="middle"
              style={{ marginTop: "20px" }}
            >
              <Space size={16}>
                <Button
                  type="primary"
                  onClick={form.submit}
                  loading={updateLoading || createLoading}
                >
                  保存
                </Button>
                <Button onClick={() => router.back()}>取消</Button>
              </Space>
              {urlParamsId && (
                <BtnModal
                  btnText="删除分析助手"
                  title="确定删除该分析助手？"
                  btnProps={{
                    loading: deleteLoading,
                  }}
                  onOk={async () => {
                    const res = await onDelete(urlParamsId);
                    if (res.success) {
                      message.success("删除成功");
                      router.back();
                      return true;
                    } else {
                      message.error("删除失败");
                      return false;
                    }
                  }}
                />
              )}
            </Row>
          </Form>
        </App>
      </Spin>
    </ContentWithBackIcon>
  );
}

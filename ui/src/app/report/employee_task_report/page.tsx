"use client";

import ContentWithBackIcon from "@/common/component/lib/ContentWithBackIcon";
import OptionsDropdown, {
  IOptions,
} from "@/common/component/lib/OptionsDropdown";
import PageHeader from "@/common/component/lib/PageHeader";
import { useEmployeeDetail } from "@/service/hooks/useEmployeeList";
import { useFetchFolderList } from "@/service/hooks/useFolder";
import { useRerunReport } from "@/service/hooks/useTask";
import TaskService from "@/service/task";
import { EmployeeInfo } from "@/service/types/employee";
import {
  ContactStatus,
  ContactStatusMap,
  RerunReportRequest,
  Task,
  TaskFullResponse,
  TaskStatus,
  TaskStatusMap,
} from "@/service/types/task";
import { Button, Divider, message, Table, Tooltip, Typography } from "antd";
import moment from "moment";
import { useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { sortIcon } from "../page";

const statusMap: Record<string, { text: string; color: string }> = {
  initial: { text: "未访谈", color: "#fbc02d" },
  processing: { text: "进行中", color: "#23e4a6" },
  completed: { text: "已结束", color: "#4096ff" },
};

const statusOptions = Object.entries(TaskStatusMap).map(([value, label]) => ({
  label,
  value,
}));
const contactStatusOptions = Object.entries(ContactStatusMap).map(
  ([value, label]) => ({ label, value })
);

export default function EmployeeTaskReport() {
  const searchParams = useSearchParams();
  const type = searchParams.get("type") || "user";
  const isEmployee = type === "user";
  const employeeId = searchParams.get("employeeId");
  const postNumber = searchParams.get("postNumber");
  // 获取员工/岗位信息
  const { data: employeeInfoRes, runAsync: getEmployeeDetail } =
    useEmployeeDetail();
  const employeeInfo = employeeInfoRes?.success
    ? employeeInfoRes.data
    : ({} as EmployeeInfo);
  const positionInfo = {
    standardPost: searchParams.get("standardPost"),
    postNumber,
    dept1: searchParams.get("dept1") ?? "-",
    dept2: searchParams.get("dept2") ?? "-",
    dept3: searchParams.get("dept3") ?? "-",
  };
  // 筛选与搜索
  const [keyword, setKeyword] = useState("");
  const [status, setStatus] = useState<TaskStatus | "">("");
  const [contactStatus, setContactStatus] = useState<ContactStatus | "">("");
  // 任务分组相关
  const { runAsync: fetchFolderList } = useFetchFolderList();
  const [folderOptions, setFolderOptions] = useState<IOptions[]>([]);
  const [folderId, setFolderId] = useState<string | undefined>(undefined);
  useEffect(() => {
    fetchFolderList(undefined).then((res) => {
      if (res.success) {
        setFolderOptions(
          res.data.records.map((f) => ({ label: f.name || "-", value: f.id }))
        );
      }
    });
  }, [fetchFolderList]);
  const [data, setData] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const { runAsync: rerunReport, loading: rerunLoading } = useRerunReport();

  // 获取岗位信息
  useEffect(() => {
    if (type === "position" && postNumber) {
      // 这里只查单个岗位
      setLoading(true);
      // 复用岗位报表接口
      TaskService.getTaskList({
        postNumber,
        keyword,
        contactStatus: contactStatus || undefined,
        folderId: folderId,
      }).then((res) => {
        setLoading(false);
        if (res.success) {
          setData(res.data.records);
        } else {
          setData([]);
        }
      });
    }
  }, [type, postNumber, keyword, contactStatus, folderId]);

  // 获取员工任务数据
  useEffect(() => {
    if (isEmployee && employeeId) {
      setLoading(true);
      getEmployeeDetail(employeeId);
      TaskService.getTaskList({
        employeeId,
        keyword,
        status: status || undefined,
        folderId: folderId,
      }).then((res) => {
        setLoading(false);
        if (res.success) {
          setData(res.data.records);
        } else {
          setData([]);
        }
      });
    }
  }, [type, employeeId, keyword, status, folderId]);

  const columns = useMemo(
    () => [
      {
        title: "开始时间",
        dataIndex: "beginTime",
        key: "beginTime",
        render: (v: string) =>
          v ? moment(v).format("YYYY-MM-DD HH:mm:ss") : "-",
        sorter: true,
        sortIcon,
      },
      {
        title: "结束时间",
        dataIndex: "endTime",
        key: "endTime",
        render: (v: string) =>
          v ? moment(v).format("YYYY-MM-DD HH:mm:ss") : "-",
        sorter: true,
        sortIcon,
      },
      {
        title: "任务名称",
        dataIndex: "name",
        key: "name",
        render: (v: string) => v || "-",
      },
      {
        title: "任务组",
        dataIndex: "groupName",
        key: "groupName",
        render: (v: string) => v || "-",
      },
      {
        title: "任务状态",
        dataIndex: "status",
        key: "status",
        render: (v: string) => {
          const map = statusMap[v as keyof typeof statusMap];
          return map ? (
            <span style={{ color: map.color, fontWeight: 600 }}>
              {map.text}
            </span>
          ) : (
            "-"
          );
        },
        sorter: true,
        sortIcon,
      },
      {
        title: "操作",
        dataIndex: "opt",
        key: "opt",
        render: (_: unknown, record: TaskFullResponse) => {
          if (record.taskStatus === "completed") {
            return <Button type="link">查看报告</Button>;
          }
          return (
            <Button
              type="link"
              loading={rerunLoading}
              disabled={record.taskStatus === "processing"}
              onClick={async () => {
                // 生成报告
                const params: RerunReportRequest = isEmployee
                  ? {
                      type: "user",
                      userId: employeeId,
                      taskId: String(record.id),
                    }
                  : {
                      type: "task",
                      postNumber: postNumber,
                      taskId: String(record.id),
                    };
                const res = await rerunReport(params);
                if (res.success) {
                  message.success("生成成功");
                } else {
                  message.error("生成失败");
                }
              }}
            >
              生成报告
            </Button>
          );
        },
      },
    ],
    [isEmployee, employeeId, postNumber, rerunReport, rerunLoading]
  );

  const employeeInfoMap = [
    {
      title: "员工工号",
      key: "jobNumber",
    },
    {
      title: "一级部门名称/二级部门名称/三级部门名称",
      key: "dept",
      render: (record: EmployeeInfo) =>
        `${record?.dept1 || "-"}/${record?.dept2 || "-"}/${
          record?.dept3 || "-"
        }`,
    },
    {
      title: "标准岗位",
      key: "standardPost",
    },
    {
      title: "岗位号码",
      key: "postNumber",
    },
    {
      title: "个人岗位名称",
      key: "personalPost",
    },
    {
      title: "个人职级描述",
      key: "personalRank",
    },
  ];

  return (
    <ContentWithBackIcon>
      <PageHeader
        title={
          isEmployee
            ? `${employeeInfo?.name || "-"}访谈任务列表`
            : `${positionInfo?.standardPost}访谈任务列表`
        }
        description={
          isEmployee ? (
            <>
              {employeeInfoMap.map((item, idx) => (
                <span key={item.key}>
                  <Tooltip title={item.title}>
                    {item.render
                      ? item.render(employeeInfo)
                      : employeeInfo[item.key] ?? "-"}
                  </Tooltip>
                  {idx !== employeeInfoMap.length - 1 && (
                    <Divider type="vertical" />
                  )}
                </span>
              ))}
            </>
          ) : (
            <>
              <Tooltip title={"一级部门名称/二级部门名称/三级部门名称"}>
                {positionInfo.dept1}/{positionInfo.dept2}/{positionInfo.dept3}
              </Tooltip>
            </>
          )
        }
        onSearch={setKeyword}
        extraFilter={[
          <OptionsDropdown
            value={status}
            onChange={(v) => setStatus(v as TaskStatus | "")}
            options={statusOptions}
            placeholder="全部访谈任务状态"
            key="status"
          />,
          <OptionsDropdown
            value={contactStatus}
            onChange={(v) => setContactStatus(v as ContactStatus | "")}
            options={contactStatusOptions}
            placeholder="全部执行状态"
            key="contactStatus"
          />,
          <OptionsDropdown
            value={folderId}
            onChange={setFolderId}
            options={folderOptions}
            placeholder="全部任务分组"
            key="folderId"
          />,
        ]}
      />
      <Typography.Title
        level={5}
        style={{
          margin: "16px 0",
        }}
      >
        {`${
          isEmployee ? employeeInfo?.name : positionInfo?.standardPost
        }的全部访谈任务 (${data.length})`}
      </Typography.Title>
      <Table
        rowKey="id"
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{ pageSize: 10 }}
        scroll={{ x: 1000 }}
        size="small"
      />
    </ContentWithBackIcon>
  );
}

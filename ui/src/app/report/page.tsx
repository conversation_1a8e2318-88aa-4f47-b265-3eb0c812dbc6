"use client";

import BlackTab from "@/common/component/lib/BlackTab";
import OptionsDropdown from "@/common/component/lib/OptionsDropdown";
import PageHeader from "@/common/component/lib/PageHeader";
import { getUrl } from "@/common/component/util";
import { NavigationKey, page_schema, PageUrlMap } from "@/common/constant";
import {
  useEmployeeList,
  usePositionReportList,
} from "@/service/hooks/useEmployeeList";
import { EmployeeInfo, PositionReportInfo } from "@/service/types/employee";
import { RightOutlined } from "@ant-design/icons";
import {
  Layout,
  message,
  Table,
  TablePaginationConfig,
  TableProps,
  Typography,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
const { Content } = Layout;

export const sortIcon = () => (
  <img
    src={getUrl({ url: "/sort.svg", isPage: false })}
    alt="sort"
    style={{ width: 14, height: 14 }}
  />
);

export const TableTheme = {
  headerBg: "#171F2D",
  headerColor: "#fff",
  headerSortHoverBg: "#171F2D",
  headerSortActiveBg: "#171F2D",
};

const employeeColumns: ColumnsType<EmployeeInfo> = [
  {
    title: "员工工号",
    dataIndex: "jobNumber",
    render: (_: unknown, record: EmployeeInfo) => record.id ?? "-",
  },
  {
    title: "姓名",
    dataIndex: "name",
    render: (_: unknown, record: EmployeeInfo) => record.name ?? "-",
  },
  {
    title: "一级部门",
    dataIndex: "dept1",
    render: (_: unknown, record: EmployeeInfo) => record.dept1 ?? "-",
  },
  {
    title: "标准岗位",
    dataIndex: "standardPost",
    render: (_: unknown, record: EmployeeInfo) => record.standardPost ?? "-",
  },
  {
    title: "岗位号码",
    dataIndex: "postNumber",
    render: (_: unknown, record: EmployeeInfo) => record.postNumber ?? "-",
  },
  {
    title: "个人岗位",
    dataIndex: "personalPost",
    render: (_: unknown, record: EmployeeInfo) => record.personalPost ?? "-",
  },
  {
    title: "相关访谈数",
    dataIndex: "taskNumber",
    sorter: true,
    sortIcon,
    render: (_: unknown, record: EmployeeInfo) => {
      return record.taskNumber ?? "-";
    },
  },
  {
    title: "已完成访谈数",
    dataIndex: "completeTaskNumber",
    sorter: true,
    sortIcon,
    render: (_: unknown, record: EmployeeInfo) => {
      return record.completeTaskNumber ?? "-";
    },
  },
  {
    title: " ",
    dataIndex: "opt",
    render: () => {
      return <RightOutlined />;
    },
  },
];

const positionColumns: ColumnsType<PositionReportInfo> = [
  {
    title: "一级部门",
    dataIndex: "dept1",
    render: (_: unknown, record: PositionReportInfo) => record.dept1 ?? "-",
  },
  {
    title: "标岗",
    dataIndex: "standardPost",
    render: (_: unknown, record: PositionReportInfo) =>
      record.standardPost ?? "-",
  },
  {
    title: "岗位ID",
    dataIndex: "postNumber",
    render: (_: unknown, record: PositionReportInfo) =>
      record.postNumber ?? "-",
  },
  {
    title: "相关访谈总量",
    dataIndex: "taskNumber",
    sorter: true,
    sortIcon,
    render: (_: unknown, record: PositionReportInfo) =>
      record.taskNumber ?? "-",
  },
  {
    title: "已完成访谈数",
    dataIndex: "completeTaskNumber",
    sorter: true,
    sortIcon,
    render: (_: unknown, record: PositionReportInfo) =>
      record.completeTaskNumber ?? "-",
  },
  {
    title: " ",
    dataIndex: "opt",
    render: () => {
      return <RightOutlined />;
    },
  },
];

type Sorter = {
  field?: string;
  order?: "ascend" | "descend";
};

type TabKey = "employee" | "position";

export default function ReportPage() {
  const [activeTab, setActiveTab] = useState<TabKey>("employee");
  const [sorter, setSorter] = useState<Sorter>({});
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState(0);
  const { runAsync: fetchEmployeeList, loading: employeeLoading } =
    useEmployeeList();
  const { runAsync: fetchPositionReportList, loading: positionLoading } =
    usePositionReportList();
  const [dept, setDept] = useState<string>("");
  const [postNumber, setPostNumber] = useState<string>("");
  const [keyword, setKeyword] = useState("");
  const router = useRouter();
  const [employeeList, setEmployeeList] = useState<EmployeeInfo[]>([]);
  const [positionList, setPositionList] = useState<PositionReportInfo[]>([]);
  const isEmployee = activeTab === "employee";
  useEffect(() => {
    const params: Record<string, unknown> = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword,
      dept,
      postNumber,
    };
    if (sorter.field && sorter.order) {
      params.sort = sorter.field;
      params.order = sorter.order === "ascend" ? "asc" : "desc";
    }
    if (isEmployee) {
      fetchEmployeeList(params).then((res) => {
        if (res.success) {
          setEmployeeList(res.data.records);
          setTotal(res.data.total);
        } else if ("error" in res) {
          message.error(res.error.message);
        }
      });
    } else if (activeTab === "position") {
      // 岗位分析报告
      fetchPositionReportList(params).then((res) => {
        if (res.success) {
          setPositionList(res.data.records);
          setTotal(res.data.total);
        } else if ("error" in res) {
          message.error(res.error.message);
        }
      });
    }
  }, [
    pagination,
    sorter,
    activeTab,
    keyword,
    dept,
    postNumber,
    fetchEmployeeList,
    fetchPositionReportList,
  ]);

  // 修改 handleTableChange 的类型
  const handleTableChange: TableProps<
    EmployeeInfo | PositionReportInfo
  >["onChange"] = (pagination, _filters, sorter) => {
    if (!Array.isArray(sorter)) {
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
      setSorter({
        field: sorter.field as string,
        order: sorter.order as "ascend" | "descend",
      });
    }
  };

  const handleSearch = (kw: string) => {
    setKeyword(kw);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  return (
    <Content>
      <PageHeader
        title={page_schema[NavigationKey.Report].header.title}
        description={page_schema[NavigationKey.Report].header.description}
        onSearch={handleSearch}
        extraFilter={[
          <OptionsDropdown
            value={dept}
            onChange={setDept}
            // options={positionList.map((e) => ({
            //   label: e.standardPost,
            //   value: e.postNumber,
            // }))}
            options={[]}
            placeholder="全部部门"
            key="department"
          />,
          <OptionsDropdown
            value={postNumber}
            onChange={setPostNumber}
            options={[]}
            // options={positionList.map((e) => ({
            //   label: e.standardPost,
            //   value: e.postNumber,
            // }))}
            placeholder="全部标岗"
            key="standardPosition"
          />,
        ]}
        middleContent={
          <BlackTab
            items={[
              { key: "employee", label: "员工分析报告" },
              { key: "position", label: "岗位分析报告" },
            ]}
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key as TabKey);
              setPagination({ current: 1, pageSize: 10 });
              setSorter({});
            }}
          />
        }
      />
      <Typography.Title
        level={5}
        style={{
          marginTop: "24px",
        }}
      >
        {isEmployee ? `全部员工（${total}）` : `全部岗位（${total}）`}
      </Typography.Title>
      <Table
        rowKey={isEmployee ? "id" : "postNumber"}
        size="small"
        columns={
          (isEmployee ? employeeColumns : positionColumns) as ColumnsType<
            EmployeeInfo | PositionReportInfo
          >
        }
        dataSource={isEmployee ? employeeList : positionList}
        loading={isEmployee ? employeeLoading : positionLoading}
        pagination={{
          ...pagination,
          total,
          showQuickJumper: true,
          showSizeChanger: false,
          showTotal: (t) => `共 ${t} 条`,
        }}
        onRow={(record) => ({
          onClick: () => {
            router.push(
              getUrl({
                url: PageUrlMap.EmployeeTaskReport,
                params: isEmployee
                  ? {
                      type: "user",
                      employeeId: (record as EmployeeInfo).id,
                    }
                  : {
                      type: "task",
                      standardPost: record.standardPost,
                      postNumber: record.postNumber,
                      dept1: record.dept1,
                      dept2: record.dept2,
                      dept3: record.dept3,
                    },
              })
            );
          },
          style: { cursor: isEmployee ? "pointer" : undefined },
        })}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
        style={{ marginTop: 24 }}
      />
    </Content>
  );
}

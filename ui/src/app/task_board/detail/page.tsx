"use client";
import BlackTab from "@/common/component/lib/BlackTab";
import BtnModal from "@/common/component/lib/BtnModal";
import ContentWithBackIcon from "@/common/component/lib/ContentWithBackIcon";
import { useFolderDetail } from "@/service/hooks/useFolder";
import { useGetRobotDetail } from "@/service/hooks/useRobot";
import { useGetTaskDetail, useStopTask } from "@/service/hooks/useTask";
import { TaskFullResponse } from "@/service/types/task";
import { UserOutlined } from "@ant-design/icons";
import {
  Avatar,
  Button,
  Divider,
  message,
  Row,
  Space,
  Spin,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import AssignmentTab from "./AssignmentTab";
import HistoryTab from "./HistoryTab";
const { Title } = Typography;

const tabItems = [
  { key: "assignment", label: "任务人员安排" },
  { key: "history", label: "访谈执行历史记录" },
];

export default function TaskDetailPage() {
  const searchParams = useSearchParams();
  const taskId = searchParams.get("id");
  const [activeTab, setActiveTab] = useState("assignment");

  // 任务详情
  const {
    runAsync: fetchTaskDetail,
    data: taskDetailData,
    loading: taskDetailLoading,
  } = useGetTaskDetail();
  const { runAsync: stopTask, loading: stopTaskLoading } = useStopTask();
  const {
    runAsync: getFolderDetail,
    loading: folderLoading,
    data: folderDetail,
  } = useFolderDetail();
  const {
    runAsync: fetchRobotDetail,
    data: robotDetail,
    loading: robotLoading,
  } = useGetRobotDetail();
  // 任务详情数据
  const detail: Partial<TaskFullResponse> = taskDetailData?.success
    ? taskDetailData.data
    : {};
  // 状态标签
  const getStatus = (status) => {
    switch (status) {
      case "initial":
        return { color: "#FBBC05", label: "未开始" };
      case "processing":
        return { color: "#23E4A6", label: "进行中" };
      case "completed":
        return { color: "#BEBEBE", label: "已结束" };
      default:
        return { color: "#FBBC05", label: "未开始" };
    }
  };
  const statusInfo = getStatus(detail.status);

  useEffect(() => {
    if (taskId) {
      fetchTaskDetail(taskId).then((res) => {
        if (res.success) {
          if (res.data.folderId) {
            getFolderDetail(res.data.folderId);
          }
          if (res.data.robotId) {
            fetchRobotDetail(res.data.robotId);
          }
          if (res.data.evalAppIds) {
          }
        }
      });
    }
  }, [fetchRobotDetail, fetchTaskDetail, getFolderDetail, taskId]);

  return (
    <ContentWithBackIcon>
      <div
        style={{
          marginBottom: 32,
          display: "flex",
          alignItems: "flex-start",
          justifyContent: "space-between",
        }}
      >
        <div style={{ display: "flex", alignItems: "flex-start" }}>
          <div>
            <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
              <Title level={3} style={{ margin: 0, fontWeight: 600 }}>
                {detail.name}
              </Title>
              <Tag color={statusInfo.color}>{statusInfo.label}</Tag>
            </div>
            <div
              style={{ color: "#666", fontSize: 16, margin: "12px 0 8px 0" }}
            >
              {detail.desc}
            </div>
            <Spin spinning={folderLoading || robotLoading}>
              <Row align={"middle"}>
                {detail.folderId && (
                  <>
                    <Tooltip title="任务组名称">
                      {folderDetail?.success && folderDetail.data.name}
                    </Tooltip>
                    <Divider type="vertical" />
                  </>
                )}
                {detail.robotId && (
                  <>
                    <Tooltip title="访谈助手">
                      <Space>
                        <Avatar
                          size={40}
                          src={robotDetail?.success && robotDetail.data.avatar}
                          icon={<UserOutlined />}
                        />
                        {robotDetail?.success && robotDetail.data.name}
                      </Space>
                    </Tooltip>
                    <Divider type="vertical" />
                  </>
                )}
                {detail.evalAppIds && (
                  <>
                    <Tooltip title="分析助手">
                      <Space>
                        <Avatar
                          size={40}
                          // src={robotDetail?.success && robotDetail.data.avatar}
                          icon={<UserOutlined />}
                        />
                        {/* {robotDetail?.success && robotDetail.data.name} */}
                        xxxx
                      </Space>
                    </Tooltip>
                    <Divider type="vertical" />
                  </>
                )}
              </Row>
            </Spin>
          </div>
        </div>
        <Space>
          <Button>复制任务</Button>
          {detail?.status === "processing" && (
            <BtnModal
              btnText="关闭任务"
              title="确定关闭该访谈任务?"
              btnProps={{
                loading: stopTaskLoading,
                type: "link",
              }}
              description="访谈任务一旦关闭, 无法重新启动"
              modalProps={{
                okButtonProps: {
                  loading: stopTaskLoading,
                },
              }}
              onOk={async () => {
                const res = await stopTask(taskId);
                if (res.success) {
                  message.success("已关闭");
                  return true;
                } else {
                  message.error(res.error.message);
                  return false;
                }
              }}
            />
          )}
        </Space>
      </div>
      <BlackTab
        items={tabItems}
        activeKey={activeTab}
        onChange={setActiveTab}
      />
      <Spin spinning={taskDetailLoading}>
        {activeTab === "assignment" && <AssignmentTab taskId={taskId} />}
        {activeTab === "history" && <HistoryTab />}
      </Spin>
    </ContentWithBackIcon>
  );
}

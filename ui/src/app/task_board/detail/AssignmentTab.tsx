import { sortIcon } from "@/app/report/page";
import BlackStatisticCard, {
  StaticNumber,
} from "@/common/component/lib/BlackStatisticCard";
import BtnModal from "@/common/component/lib/BtnModal";
import CommonModal from "@/common/component/lib/CommonModal";
import Search from "@/common/component/lib/Search";
import {
  useContactList,
  useNotice,
  useTaskStatusCountByTakIds,
} from "@/service/hooks/useTask";
import { Contact } from "@/service/types/task";
import {
  Col,
  Row,
  Spin,
  Table,
  TablePaginationConfig,
  Typography,
  message,
} from "antd";
import { ColumnsType } from "antd/es/table";
import { useEffect, useMemo, useState } from "react";
const { Title } = Typography;

interface AssignmentTabProps {
  taskId: string;
}
export default function AssignmentTab({ taskId }: AssignmentTabProps) {
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [keyword, setKeyword] = useState("");
  const [successModal, setSuccessModal] = useState<boolean>(false);
  const [searchFlag, setSearchFlag] = useState(-1);
  const { runAsync: onNotice, loading: noticeLoading } = useNotice();
  const { data: statusCountRes, runAsync: fetchStatusCount } =
    useTaskStatusCountByTakIds();
  const statusCount = statusCountRes[taskId];
  const {
    runAsync: fetchContactList,
    loading: contactListLoading,
    data: contactListData,
  } = useContactList();
  const contactList = contactListData?.success
    ? contactListData.data.records
    : [];
  useEffect(() => {
    if (taskId) {
      fetchContactList({
        taskId,
        page: pagination.current,
        pageSize: pagination.pageSize,
        keyword,
      });
      fetchStatusCount({
        taskId,
      });
    }
  }, [
    taskId,
    pagination.pageSize,
    fetchContactList,
    pagination,
    fetchStatusCount,
    searchFlag,
  ]);
  const handleTableChange = (pag: TablePaginationConfig) => {
    setPagination((prev) => ({ ...prev, ...pag }));
  };
  const totalCount = statusCount?.total ?? "0";
  const startedCount =
    Number(statusCount?.processing) + Number(statusCount?.success) || "0";
  const completionRate =
    statusCount && statusCount.total && Number(statusCount.total) > 0
      ? `${(
          (Number(statusCount.success) / Number(statusCount.total)) *
          100
        ).toFixed(1)}%`
      : "0.0%";
  const columns: ColumnsType<Contact> = useMemo(
    () => [
      { title: "员工工号", dataIndex: "jobNumber" },
      { title: "姓名", dataIndex: "name" },
      { title: "一级部门", dataIndex: "dept1" },
      {
        title: "标准岗位",
        dataIndex: "standardPost",
      },
      { title: "岗位号码", dataIndex: "postNumber" },
      {
        title: "个人岗位",
        dataIndex: "personalPost",
      },
      {
        title: "访谈历史次数",
        dataIndex: "interviewCount",
        sorter: true,
        sortIcon,
      },
    ],
    []
  );
  return (
    <>
      <Row gutter={[24, 16]} style={{ marginBottom: "24px" }}>
        <Col span={6}>
          <BlackStatisticCard>
            <span>任务覆盖总人数</span>
            <StaticNumber value={totalCount} />
          </BlackStatisticCard>
        </Col>
        <Col span={6}>
          <BlackStatisticCard>
            <span>开启访谈人数</span>
            <StaticNumber value={String(startedCount)} />
          </BlackStatisticCard>
        </Col>
        <Col span={6}>
          <BlackStatisticCard>
            <span>任务完成率</span>
            <StaticNumber value={completionRate} />
          </BlackStatisticCard>
        </Col>
        <Col
          span={6}
          style={{
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <BtnModal
            btnText="发送钉钉任务提醒"
            title="发送钉钉任务提醒"
            description="确定要通过钉钉向所有未完成访谈任务的员工发送任务提醒吗?"
            btnProps={{
              type: "primary",
              loading: noticeLoading,
            }}
            onOk={async () => {
              const res = await onNotice(taskId);
              if (res.success) {
                setSuccessModal(true);
                return true;
              } else {
                message.error(res.error.message);
                return false;
              }
            }}
          />
        </Col>
        {successModal && (
          <CommonModal
            title="提醒发送成功"
            description="已通过钉钉向所有未完成访谈任务的员工发送了任务提醒"
            onCancel={() => setSuccessModal(false)}
            onOk={() => setSuccessModal(false)}
            okText="好的"
            cancelText={null}
          />
        )}
      </Row>
      <Search
        onChange={(e) => setKeyword(e.target.value)}
        onPressEnter={() => setSearchFlag((pre) => pre + 1)}
      />
      <Spin spinning={contactListLoading}>
        <div style={{ marginTop: "24px" }}>
          <Title level={5}>
            全部人员安排 (
            {contactListData?.success ? contactListData.data.total : 0})
          </Title>
          <Table<Contact>
            size="small"
            rowKey="id"
            columns={columns}
            loading={contactListLoading}
            dataSource={contactList}
            pagination={{
              ...pagination,
              total: contactListData?.success ? contactListData.data.total : 0,
              showTotal: (t) => `共 ${t} 条`,
              showQuickJumper: true,
              showSizeChanger: false,
            }}
            onChange={handleTableChange}
            bordered
            style={{ marginTop: 10 }}
            scroll={{ x: 1200 }}
          />
        </div>
      </Spin>
    </>
  );
}

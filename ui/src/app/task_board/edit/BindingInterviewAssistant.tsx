import { useRobotList } from "@/service/hooks/useRobot";
import { Robot } from "@/service/types/robots";
import { <PERSON><PERSON>, Col, Drawer, Row, Select, Space } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import RobotInfoCard from "../../../common/component/RobotInfoCard";

export interface BindingInterviewAssistantProps {
  /** 当前选中的访谈助手ID（单个） */
  value?: string;
  /** 选中变化回调 */
  onChange?: (val: string) => void;
  /** 按钮文案 */
  buttonText?: string;
  /** Drawer标题 */
  drawerTitle?: string;
  /** 是否只读 */
  readonly?: boolean;
}

const BindingInterviewAssistant: React.FC<BindingInterviewAssistantProps> = ({
  value,
  onChange,
  drawerTitle = "绑定访谈助手",
  readonly = false,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<string | undefined>(value);
  const { data, runAsync: fetchList } = useRobotList();

  // 初始化选中
  useEffect(() => {
    setSelectedId(value);
  }, [value]);

  // 获取访谈助手列表
  useEffect(() => {
    fetchList();
  }, []);

  const robots: Robot[] = useMemo(() => {
    if (data?.success) return data.data.records;
    return [];
  }, [data]);

  // 选择逻辑
  const handleSelect = (id: string) => {
    setSelectedId(id);
  };

  // 确认绑定
  const handleConfirm = () => {
    if (onChange && selectedId) {
      onChange(selectedId);
    }
    setOpen(false);
  };

  // 当前已选访谈助手
  const selectedRobot = robots.find((r) => r.id === selectedId);

  return (
    <>
      <Space>
        <Select
          style={{ width: 240 }}
          placeholder="请选择访谈助手"
          value={selectedRobot ? selectedRobot.id : undefined}
          open={false}
          onClick={() => setOpen(true)}
          options={robots.map((r) => ({ label: r.name, value: r.id }))}
          disabled={readonly}
        />
      </Space>
      <Drawer
        open={open}
        onClose={() => setOpen(false)}
        title={drawerTitle}
        width={700}
        footer={
          readonly ? null : (
            <Row justify="end">
              <Space>
                <Button
                  type="primary"
                  onClick={handleConfirm}
                  disabled={!selectedId}
                >
                  确定
                </Button>
                <Button onClick={() => setOpen(false)}>取消</Button>
              </Space>
            </Row>
          )
        }
      >
        <Row gutter={[16, 16]}>
          {robots.map((robot) => (
            <Col span={8} key={robot.id}>
              <RobotInfoCard
                robot={robot}
                selectable={!readonly}
                selected={selectedId === robot.id}
                onSelect={() => handleSelect(robot.id)}
                readonly
                cardProps={{
                  style: {
                    borderColor:
                      selectedId === robot.id ? "#1677ff" : undefined,
                    boxShadow:
                      selectedId === robot.id
                        ? "0 0 0 2px #1677ff22"
                        : undefined,
                  },
                }}
              />
            </Col>
          ))}
        </Row>
      </Drawer>
    </>
  );
};

export default BindingInterviewAssistant;

import { useAssistantList } from "@/service/hooks/useAssistant";
import { Assistant } from "@/service/types/assistant";
import { <PERSON><PERSON>, Col, Drawer, Row, Select, Space } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import AssistantCard from "../../../common/component/AssistantCard";

export interface BindingAnalysisAssistantProps {
  /**
   * 当前选中的分析助手ID字符串（逗号分隔）
   */
  value?: string;
  /**
   * 选中变化回调
   */
  onChange?: (val: string) => void;
  /**
   * 是否多选
   */
  multiple?: boolean;
  /**
   * 按钮文案
   */
  buttonText?: string;
  /**
   * Drawer标题
   */
  drawerTitle?: string;
  /**
   * 是否只读
   */
  readonly?: boolean;
}

const BindingAnalysisAssistant: React.FC<BindingAnalysisAssistantProps> = ({
  value,
  onChange,
  multiple = false,
  drawerTitle = "绑定分析助手",
  readonly = false,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const { data, runAsync: fetchList } = useAssistantList();

  // 初始化选中
  useEffect(() => {
    if (typeof value === "string") {
      setSelectedIds(value ? value.split(",") : []);
    }
  }, [value]);

  // 获取助手列表
  useEffect(() => {
    fetchList();
  }, []);

  const assistants: Assistant[] = useMemo(() => {
    if (data?.success) return data.data.records;
    return [];
  }, [data]);

  // 选择逻辑
  const handleSelect = (id: string) => {
    if (multiple) {
      setSelectedIds((prev) =>
        prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
      );
    } else {
      setSelectedIds([id]);
    }
  };

  // 确认绑定
  const handleConfirm = () => {
    if (onChange) {
      onChange(selectedIds.join(","));
    }
    setOpen(false);
  };

  // 当前已选助手
  const selectedAssistants = assistants.filter((a) =>
    selectedIds.includes(a.id)
  );

  return (
    <>
      <Space>
        <Select
          style={{ width: 240 }}
          placeholder="请选择分析助手"
          value={selectedAssistants.map(a => a.id)}
          open={false}
          onClick={() => setOpen(true)}
          mode={multiple ? "multiple" : undefined}
          options={assistants.map(a => ({ label: a.name, value: a.id }))}
          disabled={readonly}
        />
      </Space>
      <Drawer
        open={open}
        onClose={() => setOpen(false)}
        title={drawerTitle}
        width={700}
        footer={
          readonly ? null : (
            <Row justify="end">
              <Space>
                <Button type="primary" onClick={handleConfirm} disabled={!selectedIds.length}>
                  确定
                </Button>
                <Button onClick={() => setOpen(false)}>取消</Button>
              </Space>
            </Row>
          )
        }
      >
        <Row gutter={[16, 16]}>
          {assistants.map((assistant) => (
            <Col span={8} key={assistant.id}>
              <AssistantCard
                assistant={assistant}
                selectable={!readonly}
                selected={selectedIds.includes(assistant.id)}
                onSelect={() => handleSelect(assistant.id)}
                readonly
                cardProps={{
                  style: {
                    borderColor: selectedIds.includes(assistant.id)
                      ? "#1677ff"
                      : undefined,
                    boxShadow: selectedIds.includes(assistant.id)
                      ? "0 0 0 2px #1677ff22"
                      : undefined,
                  },
                }}
              />
            </Col>
          ))}
        </Row>
      </Drawer>
    </>
  );
};

export default BindingAnalysisAssistant;

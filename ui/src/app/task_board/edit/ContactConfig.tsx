import BlackBtn from "@/common/component/lib/BlackBtn";
import ScrollList from "@/common/component/lib/ScrollList";
import { getUrl } from "@/common/component/util";
import { useContactList } from "@/service/hooks/useTask";
import TaskService from "@/service/task";
import {
  DeleteOutlined,
  DownloadOutlined,
  PlusOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { useRequest } from "ahooks";
import type { UploadProps } from "antd";
import {
  Button,
  Divider,
  Input,
  message,
  Popconfirm,
  Space,
  Upload,
} from "antd";
import _ from "lodash";
import React, { useState } from "react";

interface Contact {
  externalId: string;
  name: string;
}

interface ContactConfigProps {
  taskId: string;
}

const ContactConfig: React.FC<ContactConfigProps> = ({ taskId }) => {
  const [name, setName] = useState("");
  const [externalId, setExternalId] = useState("");
  const [refreshFlag, setRefreshFlag] = useState(0);
  const { runAsync: getContactList } = useContactList();

  const { run: addContact, loading: adding } = useRequest(
    (contact: { name: string; externalId: string }) =>
      TaskService.addContact(taskId, contact),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.success) {
          message.success("添加成功");
          setName("");
          setExternalId("");
          setRefreshFlag((prev) => prev + 1);
        } else {
          message.error(res.error?.message || "添加失败");
        }
      },
      onError: () => message.error("添加失败"),
    }
  );

  const { run: removeContact, loading: removing } = useRequest(
    (contact: { name: string; externalId: string }) =>
      TaskService.removeContact(
        taskId,
        _.pick(contact, ["externalId", "name"])
      ),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.success) {
          message.success("删除成功");
          setRefreshFlag((prev) => prev + 1);
        } else {
          message.error(res.error?.message || "删除失败");
        }
      },
      onError: () => message.error("删除失败"),
    }
  );

  const { run: importContacts, loading: uploading } = useRequest(
    (file: File) => TaskService.importContacts(taskId, file),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.success) {
          message.success("导入成功");
          setRefreshFlag((prev) => prev + 1);
        } else {
          message.error(res.message || "导入失败");
        }
      },
      onError: () => message.error("导入失败"),
    }
  );

  const handleAdd = async () => {
    if (!name.trim() || !externalId.trim()) {
      message.warning("请填写完整信息");
      return;
    }
    addContact({ name, externalId });
  };

  const handleDelete = async (contact: Contact) => {
    removeContact(contact);
  };

  const uploadProps: UploadProps = {
    name: "file",
    accept: ".xlsx,.xls",
    customRequest: async (options) => {
      importContacts(options.file as File);
      if (options.onSuccess) options.onSuccess({}, options.file);
    },
    showUploadList: false,
  };

  return (
    <>
      <Space direction="vertical" style={{ width: "100%" }} size="large">
        <Space>
          <Input
            placeholder="工号"
            value={externalId}
            onChange={(e) => setExternalId(e.target.value)}
            style={{ width: 120 }}
          />
          <Input
            placeholder="姓名"
            value={name}
            onChange={(e) => setName(e.target.value)}
            style={{ width: 120 }}
          />
          <BlackBtn
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            loading={adding}
          >
            添加
          </BlackBtn>
        </Space>
        <Space>
          <Upload {...uploadProps}>
            <Button icon={<UploadOutlined />} loading={uploading}>
              Excel导入
            </Button>
          </Upload>
          <BlackBtn
            icon={<DownloadOutlined />}
            href={getUrl({
              url: "/contact_template.xlsx",
              isPage: false,
            })}
            style={{
              border: "none",
            }}
          >
            下载模版
          </BlackBtn>
        </Space>
      </Space>
      <ScrollList
        getList={getContactList}
        renderItem={(item) => (
          <div
            key={item.externalId}
            style={{ width: "100%", padding: "0 10px" }}
          >
            <Space
              style={{
                width: "100%",
                display: "flex",
                justifyContent: "space-between",
              }}
              size={"large"}
            >
              <Space size={"large"}>
                <span style={{ color: "#888" }}>{item.externalId}</span>
                <span style={{ fontWeight: 500 }}>{item.name}</span>
              </Space>
              <Popconfirm
                title="确定删除该联系人？"
                onConfirm={() => handleDelete(item)}
                okText="删除"
                cancelText="取消"
                key="delete-confirm"
                okButtonProps={{
                  loading: removing,
                }}
              >
                <DeleteOutlined />
              </Popconfirm>
            </Space>
            <Divider style={{ margin: "10px 0" }} />
          </div>
        )}
        params={{
          taskId,
        }}
        refreshDeps={[refreshFlag, taskId]}
        gutter={0}
        maxHeight={300}
      />
    </>
  );
};

export default ContactConfig;

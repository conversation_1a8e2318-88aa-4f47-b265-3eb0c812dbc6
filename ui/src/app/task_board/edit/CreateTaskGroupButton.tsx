import { createFolder } from "@/service/folder";
import { PlusOutlined } from "@ant-design/icons";
import type { InputRef } from "antd";
import { Input, Typography, message } from "antd";
import { useRef } from "react";
import BtnModal from "../../../common/component/lib/BtnModal";

interface CreateTaskGroupButtonProps {
  onCreated?: (folder: { id: string; name: string }) => void;
  btnType?: "link" | "default" | "primary";
  btnText?: string;
}

const CreateTaskGroupButton = ({
  onCreated,
  btnType = "link",
  btnText = "创建新任务组",
}: CreateTaskGroupButtonProps) => {
  const inputRef = useRef<InputRef>(null);
  return (
    <BtnModal
      btnText={btnText}
      title={"创建新任务组"}
      btnProps={{
        type: btnType,
        icon: <PlusOutlined />,
      }}
      onOk={async () => {
        const name = inputRef.current?.input?.value?.trim();
        if (!name) {
          message.error("请输入新任务组名称");
          return false;
        }
        const res = await createFolder({ name });
        if (res.success) {
          message.success("创建成功");
          onCreated?.({ id: res.data.id, name: res.data.name });
          return true;
        } else if ("error" in res) {
          message.error(res?.error?.message || "创建失败");
          return false;
        }
      }}
    >
      <Typography.Text>确定添加新的任务组别</Typography.Text>
      <Input
        style={{ margin: "18px 0" }}
        ref={inputRef}
        placeholder="请输入新任务组名称"
        maxLength={20}
      />
    </BtnModal>
  );
};

export default CreateTaskGroupButton;

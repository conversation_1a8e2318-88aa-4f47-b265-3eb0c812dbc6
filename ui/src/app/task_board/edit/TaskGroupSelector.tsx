import Search from "@/common/component/lib/Search";
import { fetchFolderList } from "@/service/folder";
import type { FolderInfo } from "@/service/types/folder";
import { <PERSON>ton, Drawer, Input, Radio, Row, Space, Spin } from "antd";
import { useEffect, useMemo, useState } from "react";
import CreateTaskGroupButton from "./CreateTaskGroupButton";

const TaskGroupSelector = ({
  value,
  onChange,
}: {
  value?: string;
  onChange?: (val: string) => void;
}) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [folderList, setFolderList] = useState<FolderInfo[]>([]);
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState<string | undefined>(value);

  const loadList = async (keyword = "") => {
    setLoading(true);
    try {
      const res = await fetchFolderList({ keyword, page: 1, pageSize: 100 });
      if (res.success) {
        setFolderList(res.data.records || []);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) loadList();
  }, [open]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    loadList(e.target.value);
  };

  const handleSelect = (val: string) => {
    setSelected(val);
    onChange?.(val);
    setOpen(false);
  };

  // 组装选项，包含"不加入任务组"
  const options = useMemo(
    () => [
      { label: "不加入任务组", value: "" },
      ...folderList.map((f) => ({ label: f.name || "-", value: f.id })),
    ],
    [folderList]
  );

  return (
    <>
      <Input
        style={{ width: 240, cursor: "pointer" }}
        placeholder="请选择任务分组"
        value={options.find((o) => o.value === selected)?.label || ""}
        readOnly
        onClick={() => setOpen(true)}
      />
      <Drawer
        open={open}
        onClose={() => setOpen(false)}
        title={
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <span>任务组选择</span>
            <CreateTaskGroupButton
              btnType="default"
              btnText="创建新任务组"
              onCreated={() => loadList(search)}
            />
          </div>
        }
        width={520}
        footer={
          <Row justify={"end"}>
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  if (selected !== undefined) handleSelect(selected);
                }}
              >
                保存
              </Button>
              <Button onClick={() => setOpen(false)}>取消</Button>
            </Space>
          </Row>
        }
      >
        <Search
          value={search}
          onChange={handleSearch}
          style={{
            width: "100%",
          }}
        />
        <Spin spinning={loading}>
          <Radio.Group
            style={{ width: "100%" }}
            value={selected}
            onChange={(e) => setSelected(e.target.value)}
          >
            <div style={{ maxHeight: 400, overflowY: "auto" }}>
              {options.map((opt) => (
                <div
                  key={opt.value}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    height: 48,
                    borderBottom: "1px solid #f0f0f0",
                    padding: "0 8px",
                  }}
                >
                  <Radio value={opt.value}>{opt.label}</Radio>
                </div>
              ))}
            </div>
          </Radio.Group>
        </Spin>
      </Drawer>
    </>
  );
};
export default TaskGroupSelector;

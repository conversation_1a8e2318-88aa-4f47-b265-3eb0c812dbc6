"use client";
import { sortIcon } from "@/app/report/page";
import CreateTaskGroupButton from "@/app/task_board/edit/CreateTaskGroupButton";
import CommonModal from "@/common/component/lib/CommonModal";
import OptionsDropdown, {
  IOptions,
} from "@/common/component/lib/OptionsDropdown";
import PageHeader from "@/common/component/lib/PageHeader";
import { getStatusColor } from "@/common/component/TaskInfoCard";
import { getUrl } from "@/common/component/util";
import { NavigationKey, page_schema, PageUrlMap } from "@/common/constant";
import ExportBtnIcon from "@/common/svg/ExportBtnIcon";
import { useFetchFolderList, useMoveToFolder } from "@/service/hooks/useFolder";
import { useTaskList } from "@/service/hooks/useTask";
import {
  TaskFullResponse,
  TaskStatus,
  TaskStatusMap,
} from "@/service/types/task";
import {
  But<PERSON>,
  Divider,
  Dropdown,
  Layout,
  message,
  Space,
  Table,
  TablePaginationConfig,
  TableProps,
  Tag,
  Typography,
} from "antd";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

const { Content } = Layout;
const statusOptions: IOptions[] = Object.entries(TaskStatusMap).map(
  ([value, label]) => ({ label, value })
);
function getColumns(
  batchMode: "none" | "group" | "export",
  router: ReturnType<typeof useRouter>
) {
  const base = [
    {
      title: "任务ID",
      dataIndex: "id",
      sorter: true,
      sortIcon,
    },
    {
      title: "任务名称",
      dataIndex: "name",
    },
    {
      title: "任务状态",
      dataIndex: "status",
      sorter: true,
      sortIcon,
      render: (status: TaskStatus) => {
        return (
          <Tag color={getStatusColor(status)}>{TaskStatusMap[status]}</Tag>
        );
      },
    },
    {
      title: "覆盖员工数量",
      dataIndex: "employeeCoverageCount",
      sorter: true,
      sortIcon,
      render: (_: unknown, record: TaskFullResponse) => {
        const r = record as unknown as Record<string, unknown>;
        return r.employeeCoverageCount ?? r.coverageCount ?? r.coverage ?? "-";
      },
    },
    {
      title: "任务完成数量",
      dataIndex: "taskCompletionCount",
      sorter: true,
      sortIcon,
      render: (_: unknown, record: TaskFullResponse) => {
        const r = record as unknown as Record<string, unknown>;
        return r.taskCompletionCount ?? r.completionCount ?? r.completed ?? "-";
      },
    },
    {
      title: "任务完成率",
      dataIndex: "taskCompletionRate",
      sorter: true,
      sortIcon,
      render: (_: unknown, record: TaskFullResponse) => {
        const r = record as unknown as Record<string, unknown>;
        const value = r.taskCompletionRate ?? r.completionRate ?? r.rate;
        return value ? `${value}%` : "-";
      },
    },
    {
      title: "平均耗时(分)",
      dataIndex: "avgDuration",
      sorter: true,
      sortIcon,
      render: (_: unknown, record: TaskFullResponse) => {
        const r = record as unknown as Record<string, unknown>;
        return r.avgDuration ?? r.averageDuration ?? r.avgTime ?? "-";
      },
    },
  ];
  if (batchMode === "none") {
    base.push({
      title: "操作",
      dataIndex: "action",
      sorter: false,
      sortIcon: undefined,
      render: (_: unknown, record: TaskFullResponse) => (
        <Button
          type="link"
          onClick={() => {
            router.push(
              getUrl({
                url: PageUrlMap.TaskBoardDetail,
                params: { id: record.id },
              })
            );
          }}
        >
          编辑与查看
        </Button>
      ),
    });
  }
  return base;
}

type Sorter = {
  field?: string;
  order?: "ascend" | "descend";
};

export default function TaskBoardPage() {
  const router = useRouter();
  const [sorter, setSorter] = useState<Sorter>({});
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [data, setData] = useState<TaskFullResponse[]>([]);
  const [total, setTotal] = useState(0);
  const { runAsync: fetchTaskList, loading } = useTaskList();
  const [keyword, setKeyword] = useState("");
  // 批量操作相关
  const [batchMode, setBatchMode] = useState<"none" | "group" | "export">(
    "none"
  );
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [status, setStatus] = useState<TaskStatus | "">("");
  const [folderId, setFolderId] = useState<string | undefined>(undefined);
  const [folderOptions, setFolderOptions] = useState<IOptions[]>([]);
  // 导出成功弹窗
  const [exportSuccessOpen, setExportSuccessOpen] = useState(false);
  const { runAsync: moveToFolderAsync } = useMoveToFolder();
  const { runAsync: fetchFolderListAsync } = useFetchFolderList();

  useEffect(() => {
    const params: Record<string, unknown> = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword,
      status: status || undefined,
      folderId,
    };
    if (sorter.field && sorter.order) {
      params.sort = sorter.field;
      params.order = sorter.order === "ascend" ? "asc" : "desc";
    }
    fetchTaskList(params).then((res) => {
      if (res.success) {
        setData(res.data.records);
        setTotal(res.data.total);
      }
    });
  }, [pagination, sorter, keyword, status, folderId, fetchTaskList]);

  // 获取分组列表并转为 options
  const loadFolderOptions = useCallback(async () => {
    const res = await fetchFolderListAsync(undefined);
    if (res.success) {
      setFolderOptions(
        res.data.records.map((f) => ({ label: f.name || "-", value: f.id }))
      );
    }
  }, [fetchFolderListAsync]);

  useEffect(() => {
    loadFolderOptions();
  }, [loadFolderOptions]);

  const handleTableChange: TableProps<TaskFullResponse>["onChange"] = (
    pagination,
    _filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
      setSorter({
        field: sorter.field as string,
        order: sorter.order as "ascend" | "descend",
      });
    }
  };

  const handleSearch = (kw: string) => {
    setKeyword(kw);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 标题区渲染
  const renderHeader = () => (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginTop: 16,
      }}
    >
      <Typography.Title level={5}>全部访谈任务（{total}）</Typography.Title>
      {batchMode === "none" ? (
        <Space>
          <Button onClick={() => setBatchMode("group")} type="primary">
            批量分组
          </Button>
          <Button
            onClick={() => setBatchMode("export")}
            type="primary"
            icon={<ExportBtnIcon />}
          >
            批量导出
          </Button>
        </Space>
      ) : batchMode === "group" ? (
        <Space>
          <Button
            onClick={() => {
              setBatchMode("none");
              setSelectedRowKeys([]);
            }}
          >
            取消
          </Button>
          <Dropdown
            menu={{
              items: [
                ...folderOptions,
                {
                  value: "divider",
                  label: <Divider style={{ margin: 0 }} />,
                  disabled: true,
                },
                {
                  value: "create",
                  label: (
                    <CreateTaskGroupButton
                      btnType="link"
                      btnText="创建任务组"
                      onCreated={(folder) => {
                        setFolderId(folder.id);
                        loadFolderOptions();
                      }}
                    />
                  ),
                },
              ].map((f) => ({
                key: f.value,
                label: f.label,
                disabled: f.disabled,
              })),
              onClick: async ({ key }) => {
                if (key === "create") {
                  return;
                }
                await moveToFolderAsync({
                  contentIds: selectedRowKeys.map((id) => Number(id)),
                  folderId: Number(key),
                });
                setBatchMode("none");
                setSelectedRowKeys([]);
                setFolderId(key);
                const res = await fetchTaskList({
                  page: pagination.current,
                  pageSize: pagination.pageSize,
                  keyword,
                  status: status || undefined,
                  folderId: key,
                });
                if (res.success) {
                  setData(res.data.records);
                  setTotal(res.data.total);
                  message.success("操作成功");
                } else if ("error" in res) {
                  message.error(res.error.message);
                }
              },
            }}
            trigger={["click"]}
            disabled={!selectedRowKeys.length}
          >
            <Button type="primary" disabled={!selectedRowKeys.length}>
              选择移动到分组
            </Button>
          </Dropdown>
        </Space>
      ) : (
        <Space>
          <Button
            onClick={() => {
              setBatchMode("none");
              setSelectedRowKeys([]);
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            onClick={() => setExportSuccessOpen(true)}
          >
            批量导出选中内容
          </Button>
        </Space>
      )}
    </div>
  );

  return (
    <Content>
      <PageHeader
        title={page_schema[NavigationKey.TaskBoard].header.title}
        description={page_schema[NavigationKey.TaskBoard].header.description}
        onSearch={handleSearch}
        btn={{
          text: "创建新访谈任务",
          onClick: () => {
            router.push(getUrl({ url: PageUrlMap.TaskBoardEdit }));
          },
        }}
        extraFilter={[
          <OptionsDropdown
            key="status"
            value={status}
            onChange={(e) => setStatus(e as TaskStatus | "")}
            options={statusOptions}
            placeholder="全部状态"
          />,
          <OptionsDropdown
            key="folder"
            placeholder="全部任务分组"
            value={folderId}
            onChange={(v) => {
              setFolderId(v);
              setPagination((prev) => ({ ...prev, current: 1 }));
              loadFolderOptions(); // 切换时刷新分组列表
            }}
            options={folderOptions}
          />,
        ]}
      />
      {renderHeader()}
      <Table<TaskFullResponse>
        rowKey="id"
        size="small"
        columns={getColumns(batchMode, router)}
        dataSource={data}
        loading={loading}
        pagination={{
          ...pagination,
          total,
          showTotal: (t) => `共 ${t} 条`,
          showQuickJumper: true,
          showSizeChanger: false,
        }}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
        style={{ marginTop: 24 }}
        rowSelection={
          batchMode !== "none"
            ? {
                selectedRowKeys,
                onChange: setSelectedRowKeys,
              }
            : undefined
        }
      />
      <CommonModal
        open={exportSuccessOpen}
        title="导出成功"
        onOk={() => {
          setExportSuccessOpen(false);
          setBatchMode("none");
        }}
        onCancel={() => setExportSuccessOpen(false)}
        okText="好的"
        cancelText={null}
        description={"已将您选择的访谈任务信息, 导出Excel格式, 并完成下载"}
      />
    </Content>
  );
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  font-family: "PingFang SC";
}

.ant-layout-content {
  background: #f8f8fa;
  overflow: auto;
  padding: 30px 50px;
}

.select_divider_option {
  margin: 0;
  min-height: auto !important;
}

.ant-table-pagination {
  .ant-pagination-item {
    border-color: #171f2d !important;
    border-radius: 8px !important;
    border-width: 0.5px !important;
  }

  .ant-pagination-item-active {
    border-color: #171f2d !important;
    background-color: #171f2d !important;
    border-radius: 8px !important;

    a {
      color: #fff !important;
    }
  }
}


@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

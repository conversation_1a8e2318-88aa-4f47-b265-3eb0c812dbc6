"use client";

import { getUrl } from "@/common/component/util";
import { PageUrlMap } from "@/common/constant";
import { Button, Card, Form, Input, message, Row } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function LoginPage() {
  const [form] = Form.useForm();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  // 登录请求
  const handleLogin = async (values: {
    username: string;
    password: string;
  }) => {
    try {
      setLoading(true);
      const res = await fetch("/api/user/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      const data = await res.json();
      if (data.success) {
        message.success("登录成功");
        router.push(getUrl({ url: PageUrlMap.RobotList }));
      } else {
        setLoading(false);
        message.error(data.error?.message || "登录失败");
      }
    } catch {
      setLoading(false);
      message.error("登录失败");
    } finally {
      // setLoading(false);
    }
  };

  return (
    <Row
      align="middle"
      style={{
        width: "100vw",
        height: "100vh",
        background: `url(${getUrl({
          url: "/LoginBg.png",
          isPage: false,
        })}) center center / cover no-repeat`,
      }}
    >
      <Card
        style={{
          marginLeft: "300px",
          width: "500px",
        }}
        styles={{
          body: {
            padding: "24px",
          },
          header: {
            padding: "0",
            paddingLeft: "14px",
          },
        }}
        title={
          <img
            src={getUrl({ url: "/logo.png", isPage: false })}
            style={{ height: "80px" }}
          />
        }
      >
        <Form form={form} layout="vertical" onFinish={handleLogin}>
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: "请输入用户名" }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: "请输入密码" }]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              block
              onClick={form.submit}
              loading={loading}
            >
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </Row>
  );
}

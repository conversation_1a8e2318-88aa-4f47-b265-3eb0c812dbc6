"use client";

import Script from "next/script";
import { useEffect } from "react";

// const redirect_uri = encodeURIComponent(
//   "http://rtc-mgt-test.mengniu.cn"
// );
const redirect_uri = encodeURIComponent("https://rtc.rollingdigital.cn");
export default function LoginPage() {
  useEffect(() => {
    if (window.DTFrameLogin) {
      window.DTFrameLogin(
        {
          id: "self_defined_element",
          width: 300,
          height: 300,
        },
        {
          redirect_uri,
          client_id: "dingyy1ih2l7185thbx7",
          scope: "openid",
          response_type: "code",
          prompt: "consent",
        },
        (loginResult) => {
          const { redirectUrl, authCode } = loginResult;
          // 这里可以直接进行重定向
          window.location.href = redirectUrl;
          // 也可以在不跳转页面的情况下，使用code进行授权
          console.log(authCode);
        },
        (errorMsg) => {
          // 这里一般需要展示登录失败的具体原因,可以使用toast等轻提示
          console.error(`errorMsg of errorCbk: ${errorMsg}`);
        }
      );
    }
  }, []);

  return (
    <>
      <Script src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js" />
      <div
        style={{
          width: "100vw",
          height: "100vh",
          background: `url('/LoginBg.png') center center / cover no-repeat`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
        }}
      >
        <div
          id="self_defined_element"
          style={{
            width: "300px",
            height: "300px",
          }}
        />
      </div>
    </>
  );
}

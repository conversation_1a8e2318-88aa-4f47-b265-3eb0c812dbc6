"use client";

import BlackTab from "@/common/component/lib/BlackTab";
import ScrollList from "@/common/component/lib/ScrollList";
import TaskInfoCard from "@/common/component/TaskInfoCard";
import { useRobotList } from "@/service/hooks/useRobot";
import {
  useTaskList,
  useTaskStatusCountByTakIds,
} from "@/service/hooks/useTask";
import { RobotFullResponse } from "@/service/types/robots";
import {
  TaskFullResponse,
  TaskStatus,
  TaskStatusCount,
} from "@/service/types/task";
import { Col } from "antd";
import { useState } from "react";

// 定义扩展的任务类型
type ExtendedTaskType = TaskFullResponse & {
  taskStatusCount: TaskStatusCount;
  robot: RobotFullResponse;
};

export default function TaskList({
  keyword,
  status,
  onStatusChange,
  extra,
  robotId,
}: {
  keyword?: string;
  status?: TaskStatus;
  onStatusChange?: (status: TaskStatus) => void;
  extra?: React.ReactNode;
  robotId?: string;
}) {
  const [tab, setTab] = useState<TaskStatus>(status);
  const { runAsync: getList } = useTaskList();
  const { loading: taskStatusCountLoading, runAsync: getTaskStatusCount } =
    useTaskStatusCountByTakIds();

  const { runAsync: getRobotList, loading: robotListLoading } =
    useRobotList();

  const fetchData = async ({ keyword, status, robotId, page, pageSize }) => {
    const res = await getList({
      keyword,
      status,
      robotId,
      page,
      pageSize,
    });

    if (res.success) {
      const newRecords: ExtendedTaskType[] = (res.data?.records || []).map(
        (record) => ({
          ...record,
          taskStatusCount: {} as TaskStatusCount,
          robot: null as unknown as RobotFullResponse,
        })
      );

      try {
        const taskIds: Record<string, string> = {};
        const robotIds: Record<string, string> = {};
        newRecords.forEach((item) => {
          if (!taskIds[item.id]) {
            taskIds[item.id] = item.id;
          }
          if (!robotIds[item.robotId]) {
            robotIds[item.robotId] = item.robotId;
          }
        });

        const countRes = await getTaskStatusCount({
          taskId: Object.keys(taskIds).join(","),
        });

        const robotRes = await getRobotList({
          ids: Object.keys(robotIds).join(","),
        });

        newRecords.forEach((item) => {
          item.taskStatusCount = countRes.success ? countRes.data[item.id] : {};
          item.robot = robotRes.success
            ? robotRes.data.records.find(
                (robot) => robot.id === item.robotId
              )
            : (null as unknown as RobotFullResponse);
        });
        return {
          success: true,
          data: {
            records: newRecords,
            total: res.data.total,
          },
        };
      } catch (error) {
        console.error("Error fetching related data:", error);
        return {
          success: false,
          error: {
            message: "获取相关数据失败",
          },
        };
      }
    }
  };

  return (
    <>
      <BlackTab
        items={[
          {
            key: "processing",
            label: "进行中",
          },
          {
            key: "initial",
            label: "待启动",
          },
          {
            key: "completed",
            label: "已结束",
          },
        ]}
        onChange={(key) => {
          setTab(key as TaskStatus);
          onStatusChange?.(key as TaskStatus);
        }}
      />
      {extra}
      <ScrollList<TaskFullResponse>
        getList={fetchData}
        params={{
          keyword,
          status: tab,
          robotId,
        }}
        refreshDeps={[tab, keyword, robotId]}
        renderItem={(item: ExtendedTaskType) => (
          <Col key={item.id} span={8}>
            <TaskInfoCard
              task={item}
              onStart={() => {
                setTab("processing");
              }}
              statusCount={item.taskStatusCount}
              loading={{
                countLoading: taskStatusCountLoading,
                robotLoading: robotListLoading,
              }}
            />
          </Col>
        )}
      />
    </>
  );
}

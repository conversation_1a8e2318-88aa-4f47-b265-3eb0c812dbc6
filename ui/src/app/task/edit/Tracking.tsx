import BannerStatisticCard, {
  Status,
  StatusMap,
} from "@/app/ai_virtual_seat/edit/BannerStatisticCard";
import BtnModal from "@/common/component/lib/BtnModal";
import ScrollList from "@/common/component/lib/ScrollList";
import { useGetRobotDetail } from "@/service/hooks/useRobot";
import {
  useContactList,
  useStartTask,
  useTaskStatusCountByTakIds,
} from "@/service/hooks/useTask";
import { Contact, Task } from "@/service/types/task";
import { App, Avatar, Col, Divider, Row, Space, Spin } from "antd";
import moment from "moment";
import { useEffect, useState } from "react";
import ContactCard from "./ContactCard";
import { CallInWithLabel, CallOutWithLabel } from "./page";
export default function Tracking({
  task,
  statusLabel,
}: {
  task: Task;
  statusLabel: string;
}) {
  const {
    data: robotDetailRes,
    run: getRobotDetail,
    loading: robotLoading,
  } = useGetRobotDetail();
  const { runAsync: startTask, loading: startTaskLoading } = useStartTask();
  const { runAsync: getContactList } = useContactList();
  const [selectedStatus, setSelectedStatus] = useState<Status>(
    Status.PROCESSING
  );
  const { message } = App.useApp();
  const { robotId, type, status, beginTime } = task;
  const robotDetail = robotDetailRes?.success
    ? robotDetailRes.data
    : null;
  const {
    data: taskStatusCount,
    loading: taskStatusCountLoading,
    runAsync: getTaskStatusCount,
  } = useTaskStatusCountByTakIds();
  useEffect(() => {
    if (robotId) {
      getRobotDetail(robotId);
    }
  }, [robotId, getRobotDetail]);

  useEffect(() => {
    if (task.id) {
      getTaskStatusCount({
        taskId: task.id,
        robotId,
      });
    }
  }, [task.id, robotId, getTaskStatusCount]);

  const showOptBtn = status === "initial" || status === "completed";

  return (
    <div>
      <App>
        <Row align={"middle"} justify={showOptBtn ? "space-between" : "start"}>
          <Col span={12}>
            {robotLoading ? (
              <Spin />
            ) : (
              <Space>
                <Avatar src={robotDetail?.avatar} />
                {robotDetail?.name}
              </Space>
            )}
            <Divider type="vertical" />
            {type === "inbound" ? <CallInWithLabel /> : <CallOutWithLabel />}
            <Divider type="vertical" />
            {status === "initial"
              ? statusLabel
              : `${moment(beginTime).format("YYYY-MM-DD HH:mm:ss")} 启动`}
          </Col>
          {showOptBtn && (
            <Col>
              <Space>
                <BtnModal
                  btnText="删除任务"
                  title="确定删除该任务？"
                  description="任务一旦删除，无法恢复"
                  onOk={async () => {
                    console.log("删除任务");
                    return true;
                  }}
                />
                {status === "initial" && (
                  <BtnModal
                    btnText="启动任务"
                    title="确定启动该任务"
                    btnProps={{
                      loading: startTaskLoading,
                      type: "primary",
                    }}
                    description="任务一旦启动，无法终止"
                    onOk={async () => {
                      const res = await startTask(task.id);
                      if (res.success) {
                        message.success("启动成功");
                        return true;
                      } else {
                        message.error(res.error.message);
                        return false;
                      }
                    }}
                  />
                )}
              </Space>
            </Col>
          )}
        </Row>
        <Row
          style={{
            marginTop: "24px",
          }}
        >
          <Col span={24}>
            <BannerStatisticCard
              loading={taskStatusCountLoading}
              data={taskStatusCount[task.id]}
              processingProps={{
                cardProps:
                  status === "initial"
                    ? {
                        bgColor:
                          "linear-gradient(111deg, #41D189 52.04%, rgba(50, 255, 50, 0.10) 179.69%)",
                      }
                    : {},
              }}
              selectConfig={{
                enable: true,
                onSelect: (status) => {
                  setSelectedStatus(status);
                },
                selectedKey: selectedStatus,
              }}
            />
          </Col>
        </Row>
        <ScrollList<Contact>
          refreshDeps={[selectedStatus]}
          getList={getContactList}
          params={{
            status: StatusMap[selectedStatus],
            robotId,
            taskId: task.id,
          }}
          gutter={[16, 16]}
          renderItem={(item) => (
            <Col span={8} key={item.id}>
              <ContactCard
                selectedStatus={selectedStatus}
                contact={item}
                task={task}
                robot={robotDetail}
              />
            </Col>
          )}
        />
      </App>
    </div>
  );
}

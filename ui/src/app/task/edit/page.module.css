.container {
  background: #f8f8fa;
}

.section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.callerNumberContent {
  max-height: 228px;
  overflow-y: scroll;
}

.callIn {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #DAF0FE;
}

.callIn svg,
.callOut svg {
  width: 10px;
  height: 10px;
}

.callOut {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #202836;
}

.bgImg :global(.ant-image) {
  height: 100%;
}

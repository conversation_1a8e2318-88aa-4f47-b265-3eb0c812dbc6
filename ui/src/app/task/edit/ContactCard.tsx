import { Status } from "@/app/ai_virtual_seat/edit/BannerStatisticCard";
import ScrollList from "@/common/component/lib/ScrollList";
import SmallTag from "@/common/component/lib/SmallTag";
import CallCompleted from "@/common/svg/CallCompleted";
import CallFailed from "@/common/svg/CallFailed";
import CallInitial from "@/common/svg/CallInitial";
import CallProcessing from "@/common/svg/CallProcessing";
import { useConversationList } from "@/service/hooks/useConversation";
import { Conversation } from "@/service/types/conversation";
import { Robot } from "@/service/types/robots";
import { Contact, Task } from "@/service/types/task";
import {
  DownloadOutlined,
  LoadingOutlined,
  RobotOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  App,
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Drawer,
  Empty,
  Row,
  Space,
  Tooltip,
  Typography,
} from "antd";
import moment from "moment";
import { ReactNode, useEffect, useRef, useState } from "react";
import * as XLSX from "xlsx";

const { Title, Text } = Typography;
export default function ContactCard({
  selectedStatus,
  contact,
  task,
  robot,
}: {
  selectedStatus: Status;
  contact: Contact;
  task: Task;
  robot: Robot;
}) {
  const { name, status, phone, durationText , externalId} = contact;
  const { runAsync: getConversationList } = useConversationList();
  const [open, setOpen] = useState(false);
  const [realTimeMessages, setRealTimeMessages] = useState<Conversation[]>([]);
  const [wsConnected, setWsConnected] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);
  const realTimeChatRef = useRef<HTMLDivElement>(null);
  const { message } = App.useApp();

  useEffect(() => {
    if (open && externalId && status === "processing") {
      const { protocol, host } = window.location;
      const wsUrl = `${
        protocol === "https:" ? "wss:" : "ws:"
      }//${host}/api/ws/${externalId}`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log("WebSocket连接已建立");
        setWsConnected(true);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const newMessage = {
            id: data.id,
            content: data.content ?? "",
            userId: data.userId,
            userType: data.userType ?? "robot",
            createTime: data.createTime,
          };

          setRealTimeMessages((prev) => [...prev, newMessage as Conversation]);
        } catch (error) {
          console.error("解析WebSocket消息失败:", error);
        }
      };

      ws.onerror = (error) => {
        console.error("WebSocket错误:", error);
        setWsConnected(false);
      };

      ws.onclose = () => {
        console.log("WebSocket连接已关闭");
        setWsConnected(false);
      };

      wsRef.current = ws;

      return () => {
        if (
          ws.readyState === WebSocket.OPEN ||
          ws.readyState === WebSocket.CONNECTING
        ) {
          ws.close();
        }
      };
    }
  }, [open, externalId]);

  useEffect(() => {
    if (!open) {
      setRealTimeMessages([]);
      if (
        wsRef.current &&
        (wsRef.current.readyState === WebSocket.OPEN ||
          wsRef.current.readyState === WebSocket.CONNECTING)
      ) {
        wsRef.current.close();
        setWsConnected(false);
      }
    }
  }, [open]);

  const scrollToBottom = () => {
    if (realTimeChatRef.current) {
      realTimeChatRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  useEffect(() => {
    if (open && realTimeMessages.length > 0) {
      scrollToBottom();
    }
  }, [realTimeMessages]);

  const getStatusInfo = () => {
    let icon: ReactNode = null;
    let color = "";
    let desc = "";

    switch (selectedStatus) {
      case "success":
        {
          color = "#BEBEBE";
          desc = "通话已完成";
          icon = <CallCompleted />;
        }
        break;
      case "failed":
        {
          icon = <CallFailed />;
          color = "#FF004D";
          if (status === "notexist") {
            desc = "空号";
          } else if (status === "busy") {
            desc = "占线";
          } else {
            desc = "失败";
          }
        }
        break;
      case "processing":
        {
          icon = <CallProcessing />;
          color = "#23E4A6";
          desc = "正在通话中";
        }
        break;
      case "initial":
        {
          icon = <CallInitial />;
          color = "#FBBC05";
          desc = "通话待进行";
        }
        break;

      default:
        break;
    }
    return {
      icon,
      color,
      desc,
    };
  };

  const { icon, color, desc } = getStatusInfo();

  // 渲染聊天消息
  const renderChatMessage = (message: Conversation) => {
    const isRobot = message.userType === "robot";
    const formattedTime = moment(message.createTime).format(
      "YYYY-MM-DD HH:mm:ss"
    );

    return (
      <Row
        key={message.id}
        justify={isRobot ? "start" : "end"}
        style={{ marginBottom: 16, width: "100%" }}
      >
        <Space align="start" style={{ maxWidth: "80%" }}>
          {isRobot && (
            <Avatar
              icon={<RobotOutlined />}
              style={{ backgroundColor: "#1677ff" }}
            />
          )}
          <div>
            <div
              style={{
                background: isRobot ? "#f0f2f5" : "#1677ff",
                color: isRobot ? "#000" : "#fff",
                padding: "8px 12px",
                borderRadius: "8px",
                borderTopLeftRadius: isRobot ? 0 : "8px",
                borderTopRightRadius: isRobot ? "8px" : 0,
                wordBreak: "break-word",
              }}
            >
              {message.content}
            </div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {formattedTime}
            </Text>
          </div>
          {!isRobot && (
            <Avatar
              icon={<UserOutlined />}
              style={{ backgroundColor: "#87d068" }}
            />
          )}
        </Space>
      </Row>
    );
  };
  const hasLog =
    selectedStatus === Status.PROCESSING || selectedStatus === Status.SUCCESS;

  const downloadAllConversations = async () => {
    try {
      setDownloading(true);
      const pageSize = 100;
      let currentPage = 1;
      let total = 0;
      let allConversations: Conversation[] = [];

      while (true) {
        const data = await getConversationList({
          contactId: contact.id,
          taskId: task.id,
          page: currentPage,
          pageSize: pageSize,
        });
        total = data.data.total;

        if (!data.data.records || data.data.records.length === 0) {
          break;
        }

        allConversations = [...allConversations, ...data.data.records];

        if (currentPage * pageSize >= total) {
          break;
        }

        currentPage++;
      }

      const excelData = allConversations.map((conv) => ({
        时间: moment(conv.createTime).format("YYYY-MM-DD HH:mm:ss"),
        发送者: conv.userType === "robot" ? "AI助手" : "用户",
        内容: conv.content,
        访谈助手名称: robot.name,
        访谈助手id: robot.id,
        任务名称: task.name,
        任务id: task.id,
        联系人名称: name,
        号码: phone,
      }));

      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(excelData);
      XLSX.utils.book_append_sheet(wb, ws, "聊天记录");
      XLSX.writeFile(wb, `${task.name}_${name}_${phone}.xlsx`);
      message.success("下载成功");
    } catch (error) {
      console.error("下载失败:", error);
      message.error("下载失败，请重试");
    } finally {
      setDownloading(false);
    }
  };

  return (
    <App>
      <Card>
        <Row justify={"space-between"}>
          <Col>
            <Space size="small">
              {icon}
              <Tooltip title={"客户名称/设备名称"}>
                <Title level={5}>{name}</Title>
              </Tooltip>
              <Title level={5}>/</Title>
              <Tooltip title={"号码"}>
                <Title level={5} ellipsis>
                  {phone}
                </Title>
              </Tooltip>
            </Space>
          </Col>
          <Col>
            <SmallTag color={color}>{desc}</SmallTag>
          </Col>
        </Row>
        <Text
          type="secondary"
          style={{
            marginLeft: "32px",
          }}
        >
          {selectedStatus === Status.INITIAL
            ? "暂无数据"
            : moment(contact.createTime).format("MM/DD HH:mm")}
        </Text>
        {durationText && (
          <>
            <Divider type="vertical" />
            <Text type="secondary">{durationText}</Text>
          </>
        )}
        {hasLog && (
          <Row justify={"end"}>
            <Button onClick={() => setOpen(true)}>日志</Button>
          </Row>
        )}
      </Card>
      <Drawer
        open={open}
        onClose={() => setOpen(false)}
        title="日志详情"
        width={400}
      >
        <Space
          size={"small"}
          style={{
            display: "flex",
            alignItems: "baseline",
          }}
        >
          <Typography.Title level={5}>历史聊天记录</Typography.Title>
          {downloading ? (
            <LoadingOutlined />
          ) : (
            <Tooltip title="下载历史聊天记录">
              <DownloadOutlined onClick={downloadAllConversations} />
            </Tooltip>
          )}
        </Space>
        {open && (
          <>
            <ScrollList<Conversation>
              renderItem={renderChatMessage}
              getList={getConversationList}
              params={{
                userId: externalId,
                taskId: task.id,
              }}
            />
            {status === "processing" && (
              <>
                <Divider />
                <div>
                  <Typography.Title level={5}>
                    实时聊天记录
                    {wsConnected && (
                      <SmallTag color="#23E4A6" style={{ marginLeft: 8 }}>
                        已连接
                      </SmallTag>
                    )}
                    {!wsConnected && (
                      <SmallTag color="#FF004D" style={{ marginLeft: 8 }}>
                        未连接
                      </SmallTag>
                    )}
                  </Typography.Title>

                  {realTimeMessages.length > 0 ? (
                    <div>
                      {realTimeMessages.map(renderChatMessage)}
                      <div ref={realTimeChatRef} />
                    </div>
                  ) : (
                    <Empty description="暂无实时对话记录" />
                  )}
                </div>
              </>
            )}
          </>
        )}
      </Drawer>
    </App>
  );
}

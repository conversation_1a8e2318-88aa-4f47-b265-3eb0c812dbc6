"use client";
import BlackHeadCard from "@/common/component/lib/BlackHeadCard";
import BlackTab from "@/common/component/lib/BlackTab";
import BtnModal from "@/common/component/lib/BtnModal";
import CustomAlert from "@/common/component/lib/CustomAlert";
import SmallTag from "@/common/component/lib/SmallTag";
import { getUrl } from "@/common/component/util";
import { PageUrlMap } from "@/common/constant";
import CallIn from "@/common/svg/CallIn";
import CallOut from "@/common/svg/CallOut";
import {
  useCreateTask,
  useDeleteTask,
  useGetTaskDetail,
  useNotice,
  useUpdateTask,
} from "@/service/hooks/useTask";
import { Task, TaskStatus } from "@/service/types/task";
import {
  ArrowLeftOutlined,
  DownloadOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  App,
  Button,
  Checkbox,
  Col,
  ConfigProvider,
  DatePicker,
  Form,
  Image,
  Input,
  InputNumber,
  Layout,
  Modal,
  Row,
  Space,
  Spin,
  Tooltip,
  Typography,
  Upload,
} from "antd";
import { useWatch } from "antd/es/form/Form";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import _ from "lodash";

import BindingAnalysisAssistant from "@/app/task_board/edit/BindingAnalysisAssistant";
import BindingInterviewAssistant from "@/app/task_board/edit/BindingInterviewAssistant";
import TaskGroupSelector from "@/app/task_board/edit/TaskGroupSelector";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import ContactConfig from "./ContactConfig";
import styles from "./page.module.css";
import Tracking from "./Tracking";

const { Content } = Layout;
const { TextArea } = Input;
const { Title, Text } = Typography;

enum TabKey {
  CONFIG = "config",
  STATISTIC = "statistic",
}

export default function TaskEdit() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TaskEditContent />
    </Suspense>
  );
}

const getStatus = (status: TaskStatus) => {
  switch (status) {
    case "initial":
      return {
        color: "#FBBC05",
        label: "待启动",
      };
    case "processing":
      return {
        color: "#23E4A6",
        label: "进行中",
      };
    case "completed":
      return {
        color: "#BEBEBE",
        label: "已结束",
      };
  }
};

function TaskEditContent() {
  const { message } = App.useApp();
  const searchParams = useSearchParams();
  const urlParamsId = searchParams.get("id");
  const urlrobotId = searchParams.get("robotId");
  const [mode, setMode] = useState<"view" | "edit">("edit");
  const [name, setName] = useState("");
  const [desc, setDesc] = useState("");
  const [holdTalkTimeout, setHoldTalkTimeout] = useState(60);
  const [showAudioWave, setShowAudioWave] = useState(false);
  const [tab, setTab] = useState(TabKey.CONFIG);
  const [firstLoad, setFirstLoad] = useState(true);
  // const [bgUrlLoading, setBgUrlLoading] = useState(false);
  const router = useRouter();
  const isView = mode === "view";
  const { runAsync: onCreate, loading: createLoading } = useCreateTask();
  const { runAsync: onUpdate, loading: updateLoading } = useUpdateTask();
  const { runAsync: onDelete, loading: deleteLoading } = useDeleteTask();
  const { runAsync: onNotice } = useNotice();
  const {
    runAsync: getDetail,
    data: detailData,
    loading: detailLoading,
  } = useGetTaskDetail();
  const detail: Partial<Task> = detailData?.success ? detailData.data : {};
  const currentId = detail?.id;
  const [form] = Form.useForm<Partial<Task>>();
  const type = "inbound";
  const callerNumber = useWatch("callerNumber", form) ?? "";
  const callerNumberArr = (callerNumber?.split(",") ?? []).filter(Boolean);
  const bgUrl = useWatch("bgUrl", form);
  const statusInfo = getStatus(detail?.status ?? "initial");

  const onFinish = async (values: Partial<Task>) => {
    const data = {
      name,
      desc,
      inboundConfig: JSON.stringify({ holdTalkTimeout, showAudioWave }),
      ...values,
      // TODO: 需要去除"on", 不知道哪来的
      callerNumber: callerNumberArr.filter((c) => c !== "on").join(","),
      contacts: (form.getFieldValue("contacts") ?? []).map((c) => ({
        externalId: c.externalId,
        name: c.name,
      })),
      endTime: values.endTime
        ? dayjs(values.endTime).utc().format()
        : undefined,
    };

    const res = currentId
      ? await onUpdate(data, currentId)
      : await onCreate(data);
    if (res.success) {
      setMode("view");
      message.success("保存成功");
      router.push(getUrl({ url: PageUrlMap.TaskList }));
    } else {
      message.error(res.error.message);
    }
  };

  const sendNotice = () => {
    Modal.confirm({
      title: "发送钉钉任务提醒",
      content: "确定要通过钉钉向所有未完成访谈任务的员工发送任务提醒吗？",
      onOk: async () => {
        const res = await onNotice(currentId);
        console.log(res);
      },
    });
  };

  useEffect(() => {
    dayjs.extend(utc);
    if (urlParamsId) {
      getDetail(urlParamsId).then((res) => {
        if (res.success) {
          const { name, desc, inboundConfig } = res.data;
          setName(name);
          setDesc(desc);
          setMode("view");
          setFirstLoad(false);
          form.setFieldsValue({
            ...res.data,
            endTime: res.data.endTime ? dayjs(res.data.endTime) : undefined,
          });
          const inboundConfigObj = JSON.parse(inboundConfig || "{}");
          setHoldTalkTimeout(inboundConfigObj.holdTalkTimeout || 60);
          setShowAudioWave(inboundConfigObj.showAudioWave || false);
        }
      });
    } else if (urlrobotId) {
      form.setFieldValue("robotId", urlrobotId);
    }
  }, [urlParamsId, urlrobotId]);

  return (
    <Content>
      <Spin
        spinning={urlParamsId && firstLoad && (!detailData || detailLoading)}
      >
        <App>
          <Row gutter={12}>
            <Col
              style={{
                paddingTop: "8px",
              }}
            >
              <ArrowLeftOutlined
                onClick={() => {
                  router.back();
                }}
              />
            </Col>
            <Col span={23}>
              <Row
                style={{
                  marginBottom: "60px",
                }}
                gutter={12}
              >
                {isView ? (
                  <Col span={23}>
                    <Row justify="space-between">
                      <Col>
                        <Title level={4}>{name}</Title>
                      </Col>
                      <Col>
                        <Space>
                          <Button
                            onClick={() => setMode("edit")}
                            loading={updateLoading}
                          >
                            编辑
                          </Button>
                          <Button type="primary" onClick={sendNotice}>
                            发送钉钉通知
                          </Button>
                          {tab === TabKey.STATISTIC && (
                            <Button
                              icon={<DownloadOutlined />}
                              type="link"
                              href={`/api/tasks/${urlParamsId}/conversation/export`}
                            >
                              批量导出
                            </Button>
                          )}
                        </Space>
                      </Col>
                    </Row>
                    <Text type="secondary">{desc}</Text>
                  </Col>
                ) : (
                  <Col span={23}>
                    <Row justify="space-between">
                      <Col>
                        <ConfigProvider
                          theme={{
                            components: {
                              Input: {
                                colorTextPlaceholder: "#000",
                              },
                            },
                          }}
                        >
                          <Input
                            placeholder="请输入访谈任务名称"
                            variant="borderless"
                            style={{
                              paddingLeft: 0,
                              fontSize: "20px",
                              fontWeight: "500",
                            }}
                            value={name}
                            onChange={(e) => {
                              setName(e.target.value);
                            }}
                          />
                        </ConfigProvider>
                      </Col>
                      <Button
                        type="primary"
                        loading={createLoading}
                        onClick={async () => {
                          if (
                            currentId &&
                            _.isEqual(name, detail?.name) &&
                            _.isEqual(desc, detail?.desc)
                          ) {
                            setMode("view");
                            return;
                          }
                          const data = {
                            name,
                            desc,
                          };
                          const res = currentId
                            ? await onUpdate(data, currentId)
                            : await onCreate(data);
                          if (res.success) {
                            setMode("view");
                            message.success("保存成功");
                            getDetail(res?.data.id);
                          } else {
                            message.error(res.message);
                          }
                        }}
                      >
                        完成
                      </Button>
                    </Row>
                    <TextArea
                      style={{
                        marginTop: "20px",
                      }}
                      placeholder="请输入访谈任务简介"
                      autoSize={{ minRows: 2, maxRows: 6 }}
                      value={desc}
                      onChange={(e) => {
                        setDesc(e.target.value);
                      }}
                    />
                  </Col>
                )}
              </Row>
              {urlParamsId && (
                <BlackTab
                  items={[
                    {
                      key: TabKey.CONFIG,
                      label: "任务配置",
                    },
                    {
                      key: TabKey.STATISTIC,
                      label: (
                        <Space>
                          任务追踪
                          <SmallTag color={statusInfo.color}>
                            {statusInfo.label}
                          </SmallTag>
                        </Space>
                      ),
                    },
                  ]}
                  onChange={(key) => {
                    setTab(key as TabKey);
                  }}
                />
              )}
              {tab === TabKey.CONFIG && (
                <>
                  {detail.status === "processing" && (
                    <CustomAlert message="该任务正在进行中，暂不支持编辑配置" />
                  )}
                  {detail.status === "completed" && (
                    <CustomAlert message="该任务已结束，暂不支持编辑配置" />
                  )}
                  <Form
                    layout="vertical"
                    form={form}
                    onFinish={onFinish}
                    initialValues={{
                      type: "inbound",
                      ...detail,
                    }}
                    // disabled={readonly}
                  >
                    <Row gutter={[24, 24]} align="stretch">
                      <Col span={24}>
                        <BlackHeadCard title="任务分组设置">
                          <Form.Item label="任务组别选择" name="folderId">
                            <TaskGroupSelector />
                          </Form.Item>
                        </BlackHeadCard>
                      </Col>
                      <Col span={24}>
                        <BlackHeadCard title="任务设定">
                          {type === "inbound" && (
                            <>
                              <Form.Item label="">
                                <Checkbox
                                  checked={showAudioWave}
                                  onChange={(e) =>
                                    setShowAudioWave(e.target.checked)
                                  }
                                >
                                  是否显示声音波纹
                                </Checkbox>
                              </Form.Item>
                              <Form.Item label="超时未对话，则结束通话">
                                <InputNumber
                                  placeholder="请输入"
                                  addonAfter="秒"
                                  min={10}
                                  value={holdTalkTimeout}
                                  onChange={(value) =>
                                    setHoldTalkTimeout(value)
                                  }
                                />
                              </Form.Item>
                              <Form.Item
                                label={
                                  <span>
                                    背景图片
                                    <span
                                      style={{
                                        fontSize: "13px",
                                        color: "#999",
                                      }}
                                    >
                                      （ 建议尺寸大小 750px × 1334px ）
                                    </span>
                                  </span>
                                }
                                name="bgUrl"
                              >
                                <Upload
                                  showUploadList={false}
                                  maxCount={1}
                                  action={"/api/image/upload"}
                                  accept="image/*"
                                  className={styles.bgImg}
                                  beforeUpload={(file) => {
                                    const isLt2M = file.size / 1024 / 1024 < 5;
                                    if (!isLt2M) {
                                      message.error("图片大小不超过5MB");
                                    }
                                    return isLt2M;
                                  }}
                                  onChange={async (info) => {
                                    if (info.file.status === "uploading") {
                                      // setBgUrlLoading(true);
                                      return;
                                    }
                                    if (info.file.status === "done") {
                                      if (info.file.response.success) {
                                        form.setFieldValue(
                                          "bgUrl",
                                          info.file.response.data.url
                                        );
                                      } else {
                                        message.error(
                                          `${info.file.name} 图片上传失败`
                                        );
                                      }
                                    } else if (info.file.status === "error") {
                                      message.error(
                                        `${info.file.name} 图片上传失败`
                                      );
                                    }
                                  }}
                                  listType="picture-card"
                                >
                                  {bgUrl ? (
                                    <Tooltip title={"点击更换背景图片"}>
                                      <div style={{ height: "100%" }}>
                                        <Image
                                          src={bgUrl}
                                          alt="背景图片"
                                          preview={false}
                                          style={{
                                            height: "100%",
                                          }}
                                        />
                                      </div>
                                    </Tooltip>
                                  ) : (
                                    <Tooltip title={"点击更换背景图片"}>
                                      <PlusOutlined />
                                    </Tooltip>
                                  )}
                                </Upload>
                              </Form.Item>
                              <Form.Item label="预计完成时间" name="endTime">
                                <DatePicker
                                  showTime
                                  format="YYYY-MM-DD HH:mm:ss"
                                />
                              </Form.Item>
                            </>
                          )}
                        </BlackHeadCard>
                      </Col>
                      {currentId && (
                        <Col span={12}>
                          <BlackHeadCard
                            title="员工配置"
                            className={styles.section}
                          >
                            <ContactConfig taskId={urlParamsId} />
                          </BlackHeadCard>
                        </Col>
                      )}
                      <Col span={currentId ? 12 : 24}>
                        <BlackHeadCard
                          title="任务指派"
                          className={styles.section}
                        >
                          <Form.Item name="robotId" label="绑定访谈助手">
                            <BindingInterviewAssistant
                              value={form.getFieldValue("robotId")}
                              onChange={(val) =>
                                form.setFieldValue("robotId", val)
                              }
                            />
                          </Form.Item>
                          <Form.Item name="evalAppIds" label="绑定分析助手">
                            <BindingAnalysisAssistant
                              value={form.getFieldValue("evalAppIds")}
                              onChange={(val) =>
                                form.setFieldValue("evalAppIds", val)
                              }
                              multiple
                            />
                          </Form.Item>
                        </BlackHeadCard>
                      </Col>
                    </Row>
                  </Form>
                  <Row
                    justify={
                      detail?.status === "initial" ? "space-between" : "start"
                    }
                    style={{ marginTop: "20px" }}
                  >
                    <Space size={16}>
                      <Button
                        type="primary"
                        onClick={form.submit}
                        loading={updateLoading || createLoading}
                      >
                        保存
                      </Button>
                      <Button
                        onClick={() => {
                          router.push(getUrl({ url: PageUrlMap.TaskList }));
                        }}
                      >
                        取消
                      </Button>
                    </Space>
                    {detail?.status === "initial" && (
                      <BtnModal
                        btnText="删除任务"
                        title="确定删除该任务？"
                        onOk={async () => {
                          const res = await onDelete(currentId);
                          if (res.success) {
                            message.success("删除成功");
                            router.push(getUrl({ url: PageUrlMap.TaskList }));
                            return true;
                          } else {
                            message.error(res.error.message);
                            return false;
                          }
                        }}
                        btnProps={{
                          loading: deleteLoading,
                        }}
                        description="任务一旦删除，无法恢复"
                      />
                    )}
                  </Row>
                </>
              )}
              {tab === TabKey.STATISTIC && (
                <Tracking
                  task={detail as Task}
                  statusLabel={statusInfo.label}
                />
              )}
            </Col>
          </Row>
        </App>
      </Spin>
    </Content>
  );
}

export function CallInWithLabel() {
  return (
    <Space>
      <div className={styles.callIn}>
        <CallIn />
      </div>
      <span>网络呼入</span>
    </Space>
  );
}

export function CallOutWithLabel() {
  return (
    <Space>
      <div className={styles.callOut}>
        <CallOut />
      </div>
      <span>网络呼出</span>
    </Space>
  );
}

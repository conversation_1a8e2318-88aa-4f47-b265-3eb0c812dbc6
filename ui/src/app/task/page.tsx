"use client";

import BlackStatisticCard, {
  StaticNumber,
} from "@/common/component/lib/BlackStatisticCard";
import PageHeader from "@/common/component/lib/PageHeader";
import { getPercent, getUrl } from "@/common/component/util";
import { NavigationKey, page_schema, PageUrlMap } from "@/common/constant";
import { useTaskStatusCount } from "@/service/hooks/useTask";
import { TaskStatus, TaskStatusCount } from "@/service/types/task";
import { QuestionCircleOutlined } from "@ant-design/icons";
import {
  Col,
  ConfigProvider,
  Divider,
  Layout,
  Progress,
  Row,
  Space,
  Spin,
  Tooltip,
} from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";
import TaskList from "./TaskList";
const { Content } = Layout;
export default function TaskPage() {
  const router = useRouter();
  const [tab, setTab] = useState<TaskStatus>("processing");
  const [keyword, setKeyword] = useState("");

  const { data: countData, loading: taskStatusCountLoading } =
    useTaskStatusCount();

  //有效任务数：总呼叫量-空号-盲线-呼入呼叫失败
  const validTaskCount =
    +countData.total -
    +countData.notexist -
    +countData.busy -
    +countData.processing -
    +countData.failed;
  return (
    <Content>
      <PageHeader
        title={page_schema[NavigationKey.Task].header.title}
        description={page_schema[NavigationKey.Task].header.description}
        btn={{
          text: "创建新任务",
          onClick: () => {
            router.push(getUrl({ url: PageUrlMap.TaskEdit }));
          },
        }}
        onSearch={setKeyword}
      />
      <TaskList
        keyword={keyword}
        status={tab}
        onStatusChange={(status) => {
          setTab(status);
        }}
        extra={
          tab === "processing" && (
            <Spin spinning={taskStatusCountLoading}>
              <Row
                style={{
                  marginBottom: "24px",
                }}
                gutter={[24, 16]}
                wrap
              >
                <ConfigProvider
                  theme={{
                    components: {
                      Divider: {
                        colorSplit: "#979797",
                      },
                      Badge: {
                        colorText: "#fff",
                      },
                    },
                  }}
                >
                  <Col sm={12} xxl={5} span={5}>
                    <BlackStatisticCard
                      badge={{
                        status: "success",
                        text: "实时总呼叫量(外呼+呼入)",
                      }}
                    >
                      <StaticNumber value={countData.total} />
                    </BlackStatisticCard>
                  </Col>
                  <Col sm={12} xxl={7} span={7}>
                    <BlackStatisticCard>
                      <Row align="middle" justify="space-between">
                        <Col>
                          <Space>
                            <span>任务成功率</span>
                            <Tooltip title="已完成数/（总呼叫量-空号-盲线-呼入呼叫失败）">
                              <QuestionCircleOutlined />
                            </Tooltip>
                          </Space>
                          <StaticNumber
                            value={
                              validTaskCount
                                ? getPercent(+countData.success, validTaskCount)
                                : "-"
                            }
                            showPercent
                          />
                        </Col>
                        <Divider type="vertical" />
                        <Col>
                          <Space>
                            <span>接通率</span>
                            <Tooltip title="已完成/总呼叫量">
                              <QuestionCircleOutlined />
                            </Tooltip>
                          </Space>
                          <StaticNumber
                            value={
                              +countData.total
                                ? getPercent(
                                    +countData.success,
                                    +countData.total
                                  )
                                : "-"
                            }
                            showPercent
                          />
                        </Col>
                      </Row>
                    </BlackStatisticCard>
                  </Col>
                  <Col sm={24} xxl={12} span={12}>
                    <BlackStatisticCard
                      badge={{ status: "success", text: "外呼状态统计" }}
                    >
                      <ProgressList
                        statusCount={countData}
                        validTaskCount={validTaskCount}
                      />
                    </BlackStatisticCard>
                  </Col>
                </ConfigProvider>
              </Row>
            </Spin>
          )
        }
      />
    </Content>
  );
}

const ProgressList = ({
  statusCount,
  validTaskCount,
}: {
  statusCount: TaskStatusCount;
  validTaskCount: number;
}) => {
  const line2 = [
    {
      label: "空号",
      percent: validTaskCount
        ? +getPercent(+statusCount.notexist, validTaskCount)
        : "0",
    },
    {
      label: "进行中",
      percent: validTaskCount
        ? +getPercent(+statusCount.processing, validTaskCount)
        : "0",
    },
    {
      label: "盲线",
      percent: validTaskCount
        ? +getPercent(+statusCount.busy, validTaskCount)
        : "0",
    },
    {
      label: "已完成",
      percent: validTaskCount
        ? +getPercent(+statusCount.success, validTaskCount)
        : "0",
    },
  ];
  return (
    <div
      style={{
        marginTop: "16px",
        marginBottom: "16px",
      }}
    >
      <Row gutter={24} justify="space-between">
        {line2.map((item) => (
          <Col span={12} key={item.label}>
            <ProgressItem key={item.label} {...item} />
          </Col>
        ))}
      </Row>
    </div>
  );
};
const ProgressItem = ({
  label,
  percent,
}: {
  label: string;
  percent: number;
}) => {
  return (
    <Space key={label}>
      <span
        style={{
          color: "#fff",
          width: "46px",
          display: "inline-block",
        }}
      >
        {label}
      </span>
      <span>
        <Progress
          percent={percent}
          strokeColor="#4B96FF"
          trailColor="rgba(255, 255, 255, 0.1)"
          format={(percent) => `${percent}%`}
          showInfo={false}
          style={{
            width: "150px",
          }}
        />
      </span>
      <span>
        <span style={{ color: "#fff" }}>{percent}%</span>
      </span>
    </Space>
  );
};

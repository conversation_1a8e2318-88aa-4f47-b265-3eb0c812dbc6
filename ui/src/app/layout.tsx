import Navigation from "@/common/Navigation";
import RootStyleRegistry from "@/common/RootStyleRegistry";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Rolling AI",
  description: "Rolling AI",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        <AntdRegistry>
          <RootStyleRegistry>
            <ConfigProvider
              theme={{
                token: {
                  // colorPrimary: "#171F2D",
                },
              }}
              locale={zhCN}
            >
              <Navigation>{children}</Navigation>
            </ConfigProvider>
          </RootStyleRegistry>
        </AntdRegistry>
      </body>
    </html>
  );
}

"use client";
import BtnModal from "@/common/component/lib/BtnModal";
import PageHeader from "@/common/component/lib/PageHeader";
import ScrollList from "@/common/component/lib/ScrollList";
import { NavigationKey, page_schema } from "@/common/constant";
import {
  useCreateLlm,
  useDeleteLlm,
  useGetLlmProviders,
  useLlmList,
  useUpdateLlm,
} from "@/service/hooks/useLlms";
import { Llm } from "@/service/types/model";
import {
  DeploymentUnitOutlined,
  PlusOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import {
  App,
  Avatar,
  Card,
  Col,
  Form,
  Input,
  Layout,
  Row,
  Select,
  Space,
  Tooltip,
  Typography,
  Upload,
} from "antd";
import { isArray } from "lodash";
import { useState } from "react";
const { Content } = Layout;
const { Text, Title } = Typography;
const FormSchema = {
  ArkV3: [
    {
      name: "接入点ID",
      field: "endPointId",
    },
  ],
  CustomLLM: [
    {
      name: "第三方模型名称",
      field: ["providerParams", "modelName"],
    },
    {
      name: "第三方模型URL",
      field: ["providerParams", "apiUrl"],
      inVisible: true,
    },
    {
      name: "第三方模型APIkey",
      field: ["providerParams", "apiKey"],
      inVisible: true,
    },
  ],
};
export default function LlmPage() {
  const { message } = App.useApp();
  const [keyword, setKeyword] = useState("");
  const { runAsync: getList } = useLlmList();
  const { runAsync: createLlm, loading: createLlmLoading } = useCreateLlm();
  const { runAsync: deleteLlm, loading: deleteLlmLoading } = useDeleteLlm();
  const { runAsync: updateLlm, loading: updateLlmLoading } = useUpdateLlm();
  const [forceRefresh, setForceRefresh] = useState(0);
  const [form] = Form.useForm();
  const icon = Form.useWatch("icon", form);
  const provider = Form.useWatch("provider", form);
  const { data: providers, loading: providersLoading } = useGetLlmProviders();
  const createBtn = (type: "create" | "edit", item?: Llm) => {
    const isNew = type === "create";
    return (
      <BtnModal
        btnText={isNew ? "创建新模型" : "编辑模型"}
        title={isNew ? "创建新模型" : "编辑模型"}
        onOk={async () => {
          const data = form.getFieldsValue();
          const res = isNew
            ? await createLlm(data)
            : await updateLlm(item.id, data);
          if (res.success) {
            message.success("创建成功");
            setForceRefresh((pre) => pre + 1);
            return true;
          } else {
            message.error(res.error.message);
            return false;
          }
        }}
        btnExtraEvent={() => {
          if (isNew) {
            form.resetFields();
          } else {
            form.setFieldsValue(item);
          }
        }}
        btnProps={{
          loading: createLlmLoading || updateLlmLoading,
          type: isNew ? "primary" : "link",
          icon: isNew ? <PlusOutlined /> : null,
        }}
        modalProps={{
          width: 700,
        }}
      >
        <Form
          form={form}
          wrapperCol={{
            span: 18,
          }}
          labelCol={{
            span: 6,
          }}
        >
          <Form.Item label="模型提供商" name="provider">
            <Select
              options={
                providers.map((item) => ({
                  label: item.desc,
                  value: item.code,
                })) ?? []
              }
              loading={providersLoading}
            />
          </Form.Item>
          <Form.Item label="模型名称" name="name">
            <Input />
          </Form.Item>
          {provider &&
            FormSchema[provider].map((item) => (
              <Form.Item label={item.name} name={item.field} key={item.field}>
                <Input />
              </Form.Item>
            ))}
          <Form.Item label="模型图标" name="icon">
            <Upload
              showUploadList={false}
              maxCount={1}
              action={"/api/image/upload"}
              accept="image/*"
              beforeUpload={(file) => {
                const isLt2M = file.size / 1024 / 1024 < 2;
                if (!isLt2M) {
                  message.error("图片大小不超过2MB");
                }
                return isLt2M;
              }}
              onChange={async (info) => {
                if (info.file.status === "done") {
                  if (info.file.response.success) {
                    form.setFieldsValue({
                      icon: info.file.response.data.url,
                    });
                  } else {
                    message.error(`${info.file.name} 图片上传失败`);
                  }
                } else if (info.file.status === "error") {
                  message.error(`${info.file.name} 图片上传失败`);
                }
              }}
            >
              <Tooltip title={"点击上传图片"}>
                <Avatar size={40} icon={<UploadOutlined />} src={icon} />
              </Tooltip>
            </Upload>
          </Form.Item>
          <Form.Item label="备注" name="remark">
            <Input />
          </Form.Item>
        </Form>
      </BtnModal>
    );
  };
  return (
    <Content>
      <PageHeader
        title={page_schema[NavigationKey.ModelManage].header.title}
        description={page_schema[NavigationKey.ModelManage].header.description}
        customBtn={createBtn("create")}
        onSearch={setKeyword}
      />
      <ScrollList<Llm>
        getList={getList}
        gutter={[16, 16]}
        renderItem={(item) => {
          return (
            <Col span={8} key={item.id}>
              <Card
                actions={[
                  createBtn("edit", item),
                  <BtnModal
                    key="delete"
                    btnText="删除"
                    title="删除模型"
                    description="模型一旦删除，无法恢复"
                    btnProps={{
                      loading: deleteLlmLoading,
                      type: "link",
                    }}
                    onOk={async () => {
                      const res = await deleteLlm(item.id);
                      if (res.success) {
                        message.success("删除成功");
                        setForceRefresh((pre) => pre + 1);
                        return true;
                      } else {
                        message.error(res.error.message);
                        return false;
                      }
                    }}
                  />,
                ]}
              >
                <Space
                  direction="vertical"
                  size={"small"}
                  style={{
                    display: "flex",
                  }}
                >
                  <Row
                    align="middle"
                    style={{
                      marginBottom: "8px",
                    }}
                  >
                    <Space>
                      <Avatar
                        size={30}
                        icon={<DeploymentUnitOutlined />}
                        src={item.icon}
                      />
                      <Title
                        level={5}
                        style={{
                          marginBottom: "0",
                        }}
                      >
                        {item.name}
                      </Title>
                    </Space>
                  </Row>
                  <RowContent
                    label="模型提供商"
                    value={
                      providers.find((p) => p.code === item.provider)?.desc
                    }
                  />
                  {item.provider &&
                    FormSchema[item.provider].map((config) => {
                      if (config.inVisible) {
                        return null;
                      }
                      const value = isArray(config.field)
                        ? config.field.reduce((acc, cur) => {
                            return acc[cur];
                          }, item)
                        : item[config.field];
                      return (
                        <RowContent
                          key={config.field}
                          label={config.name}
                          value={value}
                        />
                      );
                    })}
                  <RowContent label="备注" value={item.remark} />
                </Space>
              </Card>
            </Col>
          );
        }}
        params={{
          keyword,
        }}
        refreshDeps={[keyword, forceRefresh]}
      />
    </Content>
  );
}

const RowContent = ({ label, value }: { label: string; value: string }) => {
  return (
    <Row gutter={8} wrap={false}>
      <Text ellipsis={{ tooltip: true }}>
        {label}: {value ?? "-"}
      </Text>
    </Row>
  );
};

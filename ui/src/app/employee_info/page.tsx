"use client";
import PageHeader from "@/common/component/lib/PageHeader";
import { NavigationKey, page_schema } from "@/common/constant";
import { useEmployeeList } from "@/service/hooks/useEmployeeList";
import type { EmployeeInfo } from "@/service/types/employee";
import { Layout, Table, message } from "antd";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { useEffect, useState } from "react";
const { Content } = Layout;

const columns: ColumnsType<EmployeeInfo> = [
  { title: "姓名", dataIndex: "name", key: "name", width: 100 },
  { title: "员工工号", dataIndex: "jobNumber", key: "jobNumber", width: 120 },
  { title: "一级部门", dataIndex: "dept1", key: "dept1", width: 120 },
  { title: "二级部门", dataIndex: "dept2", key: "dept2", width: 120 },
  { title: "三级部门", dataIndex: "dept3", key: "dept3", width: 120 },
  { title: "四级部门", dataIndex: "dept4", key: "dept4", width: 120 },
  { title: "五级部门", dataIndex: "dept5", key: "dept5", width: 120 },
  { title: "六级部门", dataIndex: "dept6", key: "dept6", width: 120 },
  { title: "所属条线", dataIndex: "lineName", key: "lineName", width: 120 },
  {
    title: "个人岗位",
    dataIndex: "personalPost",
    key: "personalPost",
    width: 120,
  },
  {
    title: "标准岗位",
    dataIndex: "standardPost",
    key: "standardPost",
    width: 120,
  },
  {
    title: "个人职级",
    dataIndex: "personalRank",
    key: "personalRank",
    width: 120,
  },
  { title: "岗位号码", dataIndex: "postNumber", key: "postNumber", width: 120 },
  {
    title: "岗位职责内容",
    dataIndex: "jobContent",
    key: "jobContent",
    width: 200,
  },
  { title: "钉钉Unionid", dataIndex: "unionId", key: "unionId", width: 200 },
];

export default function EmployeeInfoPage() {
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [keyword, setKeyword] = useState("");
  const { data, loading, error, run } = useEmployeeList();

  useEffect(() => {
    run({ page: pagination.current, pageSize: pagination.pageSize, keyword });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, pagination.pageSize, keyword]);

  useEffect(() => {
    if (error) message.error("员工信息加载失败");
  }, [error]);

  const handleTableChange = (pag: TablePaginationConfig) => {
    setPagination((prev) => ({ ...prev, ...pag }));
  };

  const handleSearch = (kw: string) => {
    setKeyword(kw);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  return (
    <Content>
      <PageHeader
        title={page_schema[NavigationKey.EmployeeInfo].header.title}
        description={page_schema[NavigationKey.EmployeeInfo].header.description}
        onSearch={handleSearch}
      />
      <Table<EmployeeInfo>
        size="small"
        rowKey="id"
        columns={columns}
        dataSource={data?.data.records || []}
        loading={loading}
        pagination={{
          ...pagination,
          total: data?.data.total || 0,
          showTotal: (total) => `共 ${total} 条`,
          showQuickJumper: true,
          showSizeChanger: false,
        }}
        scroll={{ x: 1800 }}
        onChange={handleTableChange}
        bordered
        style={{ marginTop: 24 }}
      />
    </Content>
  );
}

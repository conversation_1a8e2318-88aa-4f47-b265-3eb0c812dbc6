import AnalysisAssistant from "./svg/AnalysisAssistant";
import EmployeeInfo from "./svg/EmployeeInfo";
import InterviewAssistant from "./svg/InterviewAssistant";
import Model from "./svg/Model";
import Report from "./svg/Report";
import TaskBoard from "./svg/TaskBoard";
import TaskIcon from "./svg/TaskSvg";
import UserPermission from "./svg/UserPermission";
export enum PageUrlMap {
  /**登录 */
  Login = "/login",
  /**访谈助手管理 */
  RobotList = "/ai_virtual_seat",
  /**访谈助手编辑｜预览 */
  RobotEdit = "/ai_virtual_seat/edit",
  /**分析助手管理 */
  AnalysisAssistantList = "/analysis_assistant",
  /**分析助手编辑｜预览 */
  AnalysisAssistantEdit = "/analysis_assistant/edit",
  /**分析助手复制 */
  AnalysisAssistantCopy = "/analysis_assistant/copy",
  /**访谈助手复制 */
  RobotCopy = "/ai_virtual_seat/copy",
  /**任务列表 */
  TaskList = "/task",
  /**任务编辑｜预览 */
  TaskEdit = "/task/edit",
  /**任务执行监控 */
  TaskBoard = "/task_board",
  /**任务执行监控详情 */
  TaskBoardDetail = "/task_board/detail",
  /**任务执行监控编辑 */
  TaskBoardEdit = "/task_board/edit",
  /**访谈分析报告 */
  Report = "/report",
  /** 员工分析报告 */
  EmployeeTaskReport = "/report/employee_task_report",
}

export enum NavigationKey {
  /**任务管理 */
  Task = "task",
  /**AI 虚拟坐席 */
  AIVirtualSeat = "ai_virtual_seat",
  /**登录 */
  Login = "login",
  /**用户权限管理 */
  UserPermission = "user_permission",
  /**员工信息管理 */
  EmployeeInfo = "employee_info",
  /**分析助手管理 */
  AnalysisAssistant = "analysis_assistant",
  /**任务执行监控 */
  TaskBoard = "task_board",
  /**访谈分析报告 */
  Report = "report",
  /**模型管理 */
  ModelManage = "model_manage",
}

export const page_schema = {
  [NavigationKey.Task]: {
    label: "任务管理[正在改造]",
    icon: TaskIcon,
    header: {
      title: "任务管理",
      description: "创建和维护所有AI虚拟角色任务",
    },
  },
  [NavigationKey.UserPermission]: {
    label: "用户权限管理",
    icon: UserPermission,
    header: {
      title: "用户权限管理",
      description: "管理所有用户权限",
    },
  },
  [NavigationKey.EmployeeInfo]: {
    label: "员工信息管理",
    icon: EmployeeInfo,
    header: {
      title: "员工信息管理",
      description: "管理所有员工信息",
    },
  },
  [NavigationKey.AIVirtualSeat]: {
    label: "访谈助手管理",
    icon: InterviewAssistant,
    header: {
      title: "访谈助手管理",
      description: "创建和维护所有访谈助手",
    },
  },
  [NavigationKey.AnalysisAssistant]: {
    label: "分析助手管理",
    icon: AnalysisAssistant,
    header: {
      title: "分析助手管理",
      description: "创建和维护所有分析助手",
    },
  },
  [NavigationKey.TaskBoard]: {
    label: "任务执行监控",
    icon: TaskBoard,
    header: {
      title: "任务执行监控",
      description: "实时多维度查看与监控访谈任务",
    },
  },
  [NavigationKey.Report]: {
    label: "访谈分析报告",
    icon: Report,
    header: {
      title: "访谈分析报告",
      description: "查看员工/岗位下所有任务统计数据",
    },
  },
  [NavigationKey.ModelManage]: {
    label: "模型管理",
    icon: Model,
    header: {
      title: "模型管理",
      description: "管理所有AI模型接入点",
    },
  },
};

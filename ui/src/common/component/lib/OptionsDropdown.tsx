import { DownOutlined } from "@ant-design/icons";
import { Divider, Dropdown, Space } from "antd";
import React from "react";

export interface IOptions {
  label: React.ReactNode;
  value: string;
  disabled?: boolean;
}

export interface OptionsDropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: IOptions[];
  placeholder?: string;
}

export const OptionsDropdown: React.FC<OptionsDropdownProps> = ({
  value,
  onChange,
  options,
  placeholder,
}) => {
  const menuItems = [
    { label: placeholder, value: "" },
    { label: <Divider style={{ margin: 0 }} />, value: "--", disabled: true },
    ...options,
  ].map((item) => ({
    key: item.value,
    label: item.label,
    disabled: item.disabled,
  }));
  return (
    <Dropdown
      trigger={["click"]}
      menu={{
        items: menuItems,
        selectable: true,
        selectedKeys: [value],
        onClick: ({ key }) => onChange(key),
      }}
    >
      <Space
        style={{
          color: value ? "#69b1ff" : "#000",
          cursor: "pointer",
        }}
      >
        {menuItems.find((item) => item.key === value)?.label ?? placeholder}
        <DownOutlined />
      </Space>
    </Dropdown>
  );
};

export default OptionsDropdown;

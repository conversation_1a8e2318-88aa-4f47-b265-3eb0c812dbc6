"use client";

import {
  Button,
  ButtonProps,
  ModalProps
} from "antd";
import { FC, useState } from "react";
import CommonModal from "./CommonModal";

const BtnModal: FC<{
  title?: string;
  btnProps?: ButtonProps;
  btnExtraEvent?: () => void;
  onOk?: () => Promise<boolean>;
  btnText?: string;
  description?: string;
  children?: React.ReactNode;
  modalProps?: ModalProps;
}> = ({
  title,
  btnProps,
  btnExtraEvent,
  onOk,
  btnText,
  description,
  children,
  modalProps,
}) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button
        loading={btnProps?.loading}
        onClick={() => {
          setOpen(true);
          btnExtraEvent?.();
        }}
        {...btnProps}
      >
        {btnText}
      </Button>
      {open && (
        <CommonModal
          title={title}
          open={open}
          onCancel={() => setOpen(false)}
          onOk={async () => {
            const res = await onOk?.();
            if (res) {
              setOpen(false);
            }
          }}
          description={description}
          {...modalProps}
        >
          {children}
        </CommonModal>
      )}
    </>
  );
};

export default BtnModal;

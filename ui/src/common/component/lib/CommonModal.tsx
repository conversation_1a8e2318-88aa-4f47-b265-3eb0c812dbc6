"use client";

import {
  Button,
  ConfigProvider,
  Modal,
  ModalProps,
  Row,
  Space,
  Typography,
} from "antd";
import { FC } from "react";
const { Text } = Typography;

interface CommonModalProps extends ModalProps {
  description?: string;
}

const CommonModal: FC<CommonModalProps> = ({
  open,
  title,
  onOk,
  onCancel,
  description,
  children,
  cancelText = "取消",
  okText = "确定",
  ...modalProps
}) => {
  return (
    <Modal
      title={title}
      open={open}
      onCancel={onCancel}
      onOk={onOk}
      footer={
        <Row justify={"end"}>
          <ConfigProvider
            theme={{
              components: {
                Button: {
                  colorPrimary: "#171F2D",
                  colorPrimaryHover: "#303847",
                },
              },
            }}
          >
            <Space>
              {cancelText && (
                <Button
                  onClick={onCancel}
                  style={{
                    border: "none",
                  }}
                >
                  {cancelText}
                </Button>
              )}

              {okText && (
                <Button type="primary" onClick={onOk}>
                  {okText}
                </Button>
              )}
            </Space>
          </ConfigProvider>
        </Row>
      }
      {...modalProps}
    >
      {description && <Text type="secondary">{description}</Text>}
      {children}
    </Modal>
  );
};

export default CommonModal;

import { Button, ButtonProps, ConfigProvider } from "antd";

export default function BlackBtn({
  children,
  ...props
}: ButtonProps & { children?: React.ReactNode }) {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            colorPrimary: "#171F2D",
            colorPrimaryHover: "#303847",
            defaultBg: "none",
            defaultHoverBg: "none",
            colorLink: "#171F2D",
            colorLinkHover: "#303847",
          },
        },
      }}
    >
      <Button {...props}>{children}</Button>
    </ConfigProvider>
  );
}

import { ConfigProvider, Tabs, TabsProps } from "antd";

export default function BlackTab({ children, ...props }: TabsProps) {
  return (
    <ConfigProvider
      theme={{
        components: {
          Tabs: {
            inkBarColor: "#171F2D",
            itemHoverColor: "#000",
            itemSelectedColor: "#000",
          },
        },
      }}
    >
      <Tabs {...props}>{children}</Tabs>
    </ConfigProvider>
  );
}

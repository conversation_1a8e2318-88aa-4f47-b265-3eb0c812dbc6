import { Card, CardProps, ConfigProvider } from "antd";

export default function BlackHeadCard({ children, ...props }: CardProps) {
  return (
    <ConfigProvider
      theme={{
        components: {
          Card: {
            headerBg: "#171F2D",
            colorBgContainer: "#f8f8fa",
            colorTextHeading: "#fff",
          },
        },
      }}
    >
      <Card {...props}>{children}</Card>
    </ConfigProvider>
  );
}

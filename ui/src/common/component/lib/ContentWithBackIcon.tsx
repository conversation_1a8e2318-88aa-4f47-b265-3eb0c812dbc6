"use client";
import ReturnIcon from "@/common/svg/ReturnIcon";
import { Col, Layout, Row } from "antd";
import { useRouter } from "next/navigation";
const { Content } = Layout;

const ContentWithBackIcon = ({ children }) => {
  const router = useRouter();
  return (
    <Content>
      <Row wrap={false}>
        <Col
          style={{
            cursor: "pointer",
            width: 60,
            minWidth: 60,
            maxWidth: 60,
            flex: "0 0 60px",
          }}
        >
          <span
            onClick={() => {
              router.back();
            }}
          >
            <ReturnIcon />
          </span>
        </Col>
        <Col style={{ flex: 1, minWidth: 0, whiteSpace: "nowrap" }}>
          {children}
        </Col>
      </Row>
    </Content>
  );
};
export default ContentWithBackIcon;

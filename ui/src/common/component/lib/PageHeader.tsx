"use client";
import { PlusOutlined } from "@ant-design/icons";
import {
  <PERSON>ton,
  ButtonProps,
  Col,
  Config<PERSON>rovider,
  InputProps,
  Row,
  Space,
  Typography,
} from "antd";
import { ReactNode, useState } from "react";
import Search from "./Search";
const { Title, Text } = Typography;

export default function PageHeader({
  title,
  description,
  btn,
  onSearch,
  restSearch,
  customBtn,
  extraFilter,
  middleContent,
}: {
  title: ReactNode;
  description: ReactNode;
  btn?: {
    text: string;
  } & ButtonProps;
  customBtn?: ReactNode;
  onSearch?: (keyword: string) => void;
  restSearch?: InputProps;
  extraFilter?: ReactNode[];
  middleContent?: ReactNode;
}) {
  const [keyword, setKeyword] = useState("");
  const { text, onClick, loading } = btn ?? {};
  return (
    <>
      <Row
        justify={"space-between"}
        style={{ marginBottom: "24px" }}
        align={"middle"}
      >
        <Col>
          <Title level={2}>{title}</Title>
          <Text type="secondary">{description}</Text>
        </Col>
        <ConfigProvider
          theme={{
            components: {
              Button: {
                colorPrimary: "#171F2D",
                colorPrimaryHover: "#303847",
              },
            },
          }}
        >
          {customBtn}
          {btn && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={onClick}
              loading={loading}
            >
              {text}
            </Button>
          )}
        </ConfigProvider>
      </Row>
      {middleContent}
      <Space size={"middle"}>
        {onSearch && (
          <Search
            onChange={(e) => {
              setKeyword(e.target.value);
            }}
            onPressEnter={() => {
              onSearch(keyword);
            }}
            {...restSearch}
          />
        )}
        {extraFilter}
      </Space>
    </>
  );
}

import { InfoCircleOutlined } from "@ant-design/icons";
import { Alert, Space } from "antd";
export default function CustomAlert({ message }: { message: string }) {
  return (
    <Alert
      style={{
        border: "2px dashed #FBBC05",
        backgroundColor: "rgba(251, 188, 5, 0.25)",
        textAlign: "center",
        paddingTop: "35px",
        paddingBottom: "35px",
        marginBottom: "24px",
      }}
      type="warning"
      message={
        <Space>
          <InfoCircleOutlined style={{ color: "#975923" }} />
          <span style={{ color: "#975923" }}>{message}</span>
        </Space>
      }
    />
  );
}

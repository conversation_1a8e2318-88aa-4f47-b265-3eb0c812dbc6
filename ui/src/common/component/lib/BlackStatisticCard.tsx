import { Badge, BadgeProps, Card, ConfigProvider, Statistic } from "antd";

import { CardProps } from "antd";

export default function BlackStatisticCard({
  children,
  badge,
  cardBgColor = "#171F2D",
  ...props
}: CardProps & {
  badge?: BadgeProps;
  cardBgColor?: string;
}) {
  return (
    <ConfigProvider
      theme={{
        components: {
          Card: {
            colorBgContainer: cardBgColor,
            colorText: "#fff",
            bodyPadding: "24px 24px 0 24px",
          },
          Badge: {
            colorText: "#fff",
          },
        },
      }}
    >
      <Card {...props}>
        {badge && <Badge {...badge} />}
        {children}
      </Card>
    </ConfigProvider>
  );
}

export const StaticNumber = ({
  value,
  showPercent = false,
  style,
}: {
  value: string;
  showPercent?: boolean;
  style?: React.CSSProperties;
}) => {
  return (
    <div>
      <Statistic
        value={value}
        groupSeparator=","
        valueStyle={{
          fontSize: "48px",
          fontWeight: 600,
          fontFamily: "DIN Condensed",
          color: "#fff",
          ...style,
        }}
        formatter={(value) => {
          return showPercent ? (
            <>
              <span>{value}</span>
              <span
                style={{
                  fontSize: "24px",
                  fontWeight: 400,
                  fontFamily: "PingFang SC",
                }}
              >
                %
              </span>
            </>
          ) : (
            value
          );
        }}
      />
    </div>
  );
};

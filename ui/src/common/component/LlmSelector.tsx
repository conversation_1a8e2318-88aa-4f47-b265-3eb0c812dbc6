import { useLlmList } from "@/service/hooks/useLlms";
import { Avatar, Select, SelectProps, Space } from "antd";
import { useEffect } from "react";
import DoubaoModel from "../svg/DoubaoModel";
import styles from "./list.module.css";
export default function LlmSelector(props: SelectProps) {
  // const { data, loading, runAsync: getList } = useLlmModel();
  const { data, loading, runAsync: getList } = useLlmList();
  const list = (data?.success && data.data.records) ?? [];
  useEffect(() => {
    getList();
  }, []);
  return (
    <Select
      options={list?.map((item) => ({
        ...item,
        label: (
          <Space>
            {item.icon ? (
              <Avatar src={item.icon} size={18} />
            ) : (
              <>
                {item.provider.startsWith("ArkV3") && (
                  <div className={styles.doubao}>
                    <DoubaoModel />
                  </div>
                )}
              </>
            )}
            {item.name}
          </Space>
        ),
        value: item.id,
      }))}
      loading={loading}
      placeholder="请选择"
      {...props}
    />
  );
}

import { LlmModel } from "@/service/types/model";
import { Col, Form, Input, InputNumber, Row, Slider } from "antd";
import { FormInstance } from "antd/es/form";
import { FC } from "react";
import BlackHeadCard from "./lib/BlackHeadCard";
import LlmSelector from "./LlmSelector";

const { TextArea } = Input;

interface LlmConfigFormProps {
  form: FormInstance;
  llmParams?: LlmModel["providerParams"];
  temperature?: number;
  readonly?: boolean;
  /**
   * 控制显示哪些字段，默认全部显示。
   * 可选项：'llmId' | 'systemMessage' | 'temperature' | 'welcomeMessage' | 'historyLength'
   */
  visibleFields?: Array<
    | "llmId"
    | "systemMessage"
    | "temperature"
    | "welcomeMessage"
    | "historyLength"
  >;
}

const defaultFields = [
  "llmId",
  "systemMessage",
  "temperature",
  "welcomeMessage",
  "historyLength",
];

const LlmConfigForm: FC<LlmConfigFormProps> = ({
  form,
  llmParams,
  // readonly,
  visibleFields,
  temperature,
}) => {
  const fields = visibleFields || defaultFields;
  return (
    <BlackHeadCard className="section" title="LLM设定">
      {fields.includes("llmId") && (
        <Form.Item name="llmId" label="基础模型">
          <LlmSelector
          // disabled={readonly}
          />
        </Form.Item>
      )}
      {fields.includes("systemMessage") && (
        <Form.Item name={["llmConfig", "systemMessage"]} label="提示词">
          <TextArea
            placeholder="请输入提示词"
            rows={4}
            // disabled={readonly}
          />
        </Form.Item>
      )}
      {fields.includes("temperature") && (
        <Row>
          <Col span={12}>
            <Form.Item
              name={["llmConfig", "temperature"]}
              label="温度"
              tooltip={
                "温度 (Temperature)：控制输出的随机性。高温→更多创意；低温→保守（例：0.2=精准回答，1.0=平衡，2.0=天马行空）"
              }
            >
              <Row gutter={12} align="middle">
                <Col span={22}>
                  <Slider
                    value={temperature}
                    min={llmParams?.temperatureMin ?? 0}
                    max={llmParams?.temperatureMax ?? 1}
                    step={0.1}
                    onChange={(value) => {
                      form.setFieldValue(["llmConfig", "temperature"], value);
                    }}
                    // disabled={readonly}
                  />
                </Col>
                <Col span={2}>{llmParams?.temperatureMax ?? "1.0"}</Col>
              </Row>
            </Form.Item>
          </Col>
        </Row>
      )}
      {fields.includes("welcomeMessage") && (
        <Form.Item name={["llmConfig", "welcomeMessage"]} label="欢迎语">
          <Input
            placeholder="请输入欢迎语"
            // disabled={readonly}
          />
        </Form.Item>
      )}
      {fields.includes("historyLength") && (
        <Form.Item name={["llmConfig", "historyLength"]} label="对话记忆轮数">
          <InputNumber
            placeholder="请输入对话记忆轮数"
            style={{ width: "200px" }}
            min={3}
            // disabled={readonly}
          />
        </Form.Item>
      )}
    </BlackHeadCard>
  );
};

export default LlmConfigForm;

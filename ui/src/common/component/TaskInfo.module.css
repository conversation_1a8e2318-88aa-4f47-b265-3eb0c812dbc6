.taskInfo {
  /* width: 430px; */
  /* width: 332px; */

  .callIn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #DAF0FE;
  }

  .callOut {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #202836;
  }

  .progressTitle {
    display: block;
    font-size: 14px;
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.6);
  }

  .progressInfo {
    margin-bottom: 16px;

  }

  .progress {
    margin-bottom: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
    height: 8px;
    flex-shrink: 0;
  }

  .progressItem {
    display: inline-block;
    height: 8px;
    position: relative;
    top: -8px;
    border-radius: 0 2px 2px 0;
  }
}

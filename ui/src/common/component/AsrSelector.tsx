import { Select, SelectProps, Space } from "antd";
import _ from "lodash";
import { useEffect } from "react";
import { useAsrModel } from "../../service/hooks/useModel";
import DoubaoModel from "../svg/DoubaoModel";
import styles from "./list.module.css";
export default function AsrSelector(props: SelectProps) {
  const { data, loading, runAsync: getList } = useAsrModel();
  const list = (data?.success && data.data) ?? [];
  useEffect(() => {
    getList();
  }, []);
  return (
    <Select
      options={_.uniqBy(list, "provider")?.map((item) => ({
        label: (
          <Space>
            {item.provider.startsWith("volcano") && (
              <div className={styles.doubao}>
                <DoubaoModel />
              </div>
            )}
            {item.providerName}
          </Space>
        ),
        value: item.provider,
      }))}
      loading={loading}
      placeholder="请选择"
      {...props}
    />
  );
}

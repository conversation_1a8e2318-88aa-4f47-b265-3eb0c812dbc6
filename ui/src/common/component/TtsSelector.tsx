import { Model, TtsModel } from "@/service/types/model";
import { Select, SelectProps, Space } from "antd";
import _ from "lodash";
import { useEffect } from "react";
import { useTtsModel } from "../../service/hooks/useModel";
import DoubaoModel from "../svg/DoubaoModel";
import styles from "./list.module.css";
export default function TtsSelector(props: SelectProps) {
  const { data, loading, runAsync: getList } = useTtsModel();
  const list = (data?.success && data.data) ?? [];
  useEffect(() => {
    getList();
  }, []);

  return (
    <Select
      options={_.uniqBy(list, "provider")?.map((item) => ({
        label: (
          <Space>
            {item.provider.startsWith("volcano") && (
              <div className={styles.doubao}>
                <DoubaoModel />
              </div>
            )}
            {item.providerName}
          </Space>
        ),
        value: item.provider,
      }))}
      loading={loading}
      placeholder="请选择"
      {...props}
    />
  );
}

export const useTtsList = () => {
  const { data, loading, runAsync: getList } = useTtsModel();
  const providerList = (data?.success && data.data) ?? [];
  useEffect(() => {
    getList();
  }, []);
  const modelList = groupByProvider(providerList);
  return {
    providerList: _.uniqBy(providerList, "provider"),
    modelList,
    loading,
  };
};

export const groupByProvider = (
  data: Model[]
): Record<string, TtsModel["providerParams"][]> => {
  const result = {};

  for (const item of data) {
    const provider = item.provider;
    const providerParams = item.providerParams;

    if (!result[provider]) {
      result[provider] = [];
    }

    result[provider].push(providerParams);
  }

  return result;
};

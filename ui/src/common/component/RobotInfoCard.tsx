"use client";

import { getUrl } from "@/common/component/util";
import { PageUrlMap } from "@/common/constant";
import { useUpdateRobot } from "@/service/hooks/useRobot";
import { UserOutlined } from "@ant-design/icons";
import {
  Avatar,
  Button,
  Card,
  CardProps,
  Checkbox,
  Col,
  message,
  Row,
  Space,
  Switch,
  Tooltip,
} from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Robot, ValidPlantform } from "../../service/types/robots";
import BadgeTitle from "./lib/BadgeTitle";
import styles from "./RobotInfoCard.module.css";

export const getStatus = (robot: Robot) => {
  if (robot.processingTaskCount > 0) {
    return {
      status: "service",
      tooltip: "服务中不支持修改",
      color: "#23E4A6",
      label: "服务",
    };
  } else {
    if (robot.disabled) {
      return {
        status: "offline",
        tooltip: "点击发布",
        color: "#BEBEBE",
        label: "离线",
      };
    } else {
      return {
        status: "idle",
        tooltip: "点击取消发布",
        color: "#FBBC05",
        label: "空闲",
      };
    }
  }
};
const RobotInfoCard = ({
  robot,
  cardProps,
  readonly = false,
  selectable,
  selected,
  onSelect,
}: {
  robot: Robot;
  cardProps?: CardProps;
  readonly?: boolean;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: (checked: boolean) => void;
  refreshList?: () => void;
}) => {
  const router = useRouter();
  const [detail, setDetail] = useState<Robot>(robot);
  const { avatar, name, desc, disabled, id, todayCompletedTaskCount } = detail;
  const { runAsync: onUpdate, loading: updateLoading } = useUpdateRobot();
  const { status, label, color, tooltip } = getStatus(robot);
  return (
    <Card
      variant="borderless"
      className={`${styles.RobotInfoCard} ${selected ? styles.selected : ""}`}
      {...cardProps}
    >
      <Row
        justify={"space-between"}
        align={"middle"}
        style={{
          marginBottom: 32,
        }}
      >
        <Col>
          <Avatar size={64} src={avatar} alt={name} icon={<UserOutlined />} />
        </Col>
        {!readonly && (
          <Col>
            <Tooltip title={tooltip}>
              <Switch
                loading={updateLoading}
                checked={!disabled}
                disabled={status === "service"}
                onChange={async (checked) => {
                  const res = await onUpdate(
                    {
                      disabled: !checked,
                    },
                    id
                  );
                  if (res.success) {
                    setDetail({
                      ...detail,
                      disabled: !checked,
                    });
                  } else {
                    message.error("操作失败");
                  }
                }}
              />
            </Tooltip>
          </Col>
        )}
        {selectable && (
          <Col>
            <Checkbox
              checked={selected}
              onChange={(e) => onSelect(e.target.checked)}
              className={styles.checkbox}
            />
          </Col>
        )}
      </Row>
      <BadgeTitle
        title={name}
        badge={{
          color,
          text: label,
        }}
        desc={desc}
      />
      <div className={styles.processed_container}>
        <Row justify={"space-between"}>
          <Col className={styles.processed}>
            今日处理量: {todayCompletedTaskCount}通
          </Col>
          {!readonly && (
            <Col>
              <Space className={styles.button_group}>
                <Button
                  type="link"
                  onClick={() => {
                    router.push(
                      getUrl({
                        url: PageUrlMap.RobotCopy,
                        params: {
                          id: robot.id,
                          platform: robot.platform || ValidPlantform.Volcano,
                        },
                      })
                    );
                  }}
                >
                  复制
                </Button>
                <Button
                  type="link"
                  onClick={() => {
                    router.push(
                      getUrl({
                        url: PageUrlMap.RobotEdit,
                        params: {
                          id: robot.id,
                          platform: robot.platform || ValidPlantform.Volcano,
                        },
                      })
                    );
                  }}
                >
                  详情
                </Button>
              </Space>
            </Col>
          )}
        </Row>
      </div>
    </Card>
  );
};

export default RobotInfoCard;

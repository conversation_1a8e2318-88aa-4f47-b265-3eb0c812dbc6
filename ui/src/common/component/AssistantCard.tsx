"use client";
import { PageUrlMap } from "@/common/constant";
import { useUpdateAssistant } from "@/service/hooks/useAssistant";
import { Assistant } from "@/service/types/assistant";
import { UserOutlined } from "@ant-design/icons";
import {
  Avatar,
  Button,
  Card,
  CardProps,
  Checkbox,
  Col,
  message,
  Row,
  Space,
  Switch,
  Tooltip,
} from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";
import BadgeTitle from "./lib/BadgeTitle";
import styles from "./RobotInfoCard.module.css";
import { getUrl } from "./util";

export interface AssistantCardProps {
  assistant: Assistant;
  cardProps?: CardProps;
  onDetail?: () => void;
  readonly?: boolean;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: (checked: boolean) => void;
}

const getAssistantStatus = (status?: string) => {
  switch (status) {
    case "online":
      return { color: "#23E4A6", text: "在线", tooltip: "工作中不支持修改" };
    case "offline":
      return { color: "#BEBEBE", text: "离线" };
    case "free":
      return { color: "#FBBC05", text: "空闲", };
    default:
      return { color: "#BEBEBE", text: "未知" };
  }
};

const AssistantCard = ({
  assistant,
  cardProps,
  onDetail,
  readonly = false,
  selectable,
  selected,
  onSelect,
}: AssistantCardProps) => {
  const { avatar, name, desc, todayCompletedcount = 0, id } = assistant;
  const [detail, setDetail] = useState(assistant);
  const { runAsync: onUpdate, loading: updateLoading } = useUpdateAssistant();
  const disabled = detail.disabled;
  const status = detail.status;
  const router = useRouter();

  return (
    <Card variant="borderless" className={styles.RobotInfoCard} {...cardProps}>
      <Row justify="space-between" align="middle" style={{ marginBottom: 32 }}>
        <Col>
          <Avatar size={64} src={avatar} alt={name} icon={<UserOutlined />} />
        </Col>
        {!readonly && (
          <Col>
            <Tooltip title={getAssistantStatus(assistant.status).tooltip}>
              <Switch
                loading={updateLoading}
                checked={!disabled}
                disabled={status === "online"}
                onChange={async (checked) => {
                  const res = await onUpdate(
                    {
                      disabled: !checked,
                    },
                    id
                  );
                  if (res.success) {
                    setDetail({
                      ...detail,
                      disabled: !checked,
                      status: checked ? "free" : "offline",
                    });
                  } else {
                    message.error("操作失败");
                  }
                }}
              />
            </Tooltip>
          </Col>
        )}
        {selectable && (
          <Col>
            <Checkbox
              checked={selected}
              onChange={(e) => onSelect(e.target.checked)}
              className={styles.checkbox}
            />
          </Col>
        )}
      </Row>
      <BadgeTitle
        title={name}
        badge={getAssistantStatus(assistant.status)}
        desc={desc || ""}
      />
      <div className={styles.processed_container}>
        <Row justify="space-between">
          <Col className={styles.processed}>
            今日分析量: {todayCompletedcount}
          </Col>
          {!readonly && (
            <Col>
              <Space size={"small"} className={styles.button_group}>
                <Button
                  onClick={() => {
                    router.push(
                      getUrl({
                        url: PageUrlMap.AnalysisAssistantCopy,
                        params: { id },
                      })
                    );
                  }}
                  type="link"
                >
                  复制
                </Button>
                <Button
                  type="link"
                  onClick={() => {
                    if (onDetail) {
                      onDetail();
                    } else {
                      router.push(
                        getUrl({
                          url: PageUrlMap.AnalysisAssistantEdit,
                          params: { id },
                        })
                      );
                    }
                  }}
                >
                  详情
                </Button>
              </Space>
            </Col>
          )}
        </Row>
      </div>
    </Card>
  );
};

export default AssistantCard;

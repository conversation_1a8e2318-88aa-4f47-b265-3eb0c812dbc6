const isDevelopment = process.env.NODE_ENV !== "production";
export const getUrl = (data: {
  url: string;
  params?: Record<string, string>;
  isPage?: boolean;
}) => {
  const { url, params, isPage = true } = data;
  let targetUrl = url;
  if (!isDevelopment) {
    targetUrl = `/ui${targetUrl}`;
    if (isPage) {
      targetUrl = `${targetUrl}.html`;
    }
  }
  if (params) {
    targetUrl = `${targetUrl}?${new URLSearchParams(params).toString()}`;
  }
  return targetUrl;
};

export const getPercent = (a: number, b: number) => {
  return b ? ((a / b) * 100).toFixed(2) : "-";
};

"use client";

import { PageUrlMap } from "@/common/constant";
import { useStartTask, useStopTask } from "@/service/hooks/useTask";
import { RobotFullResponse } from "@/service/types/robots";
import {
  TaskFullResponse,
  TaskStatus,
  TaskStatusCount,
  TaskStatusMap,
} from "@/service/types/task";
import { UserOutlined } from "@ant-design/icons";
import {
  App,
  Avatar,
  Button,
  Card,
  Col,
  ConfigProvider,
  Row,
  Space,
  Spin,
  Tooltip,
  Typography
} from "antd";
import { useRouter } from "next/navigation";
import CallIn from "../svg/CallIn";
import CallOut from "../svg/CallOut";
import BadgeTitle from "./lib/BadgeTitle";
import BtnModal from "./lib/BtnModal";
import styles from "./TaskInfo.module.css";
import { getPercent, getUrl } from "./util";
const { Text } = Typography;

export const getStatusColor = (status: TaskStatus) => {
  switch (status) {
    case "initial":
      return "#FBBC05";
    case "processing":
      return "#23E4A6";
    case "completed":
      return "#BEBEBE";
    default:
      return "#BEBEBE";
  }
};

export default function TaskInfoCard({
  task,
  onStart,
  statusCount,
  loading,
}: {
  task: TaskFullResponse & { robot: RobotFullResponse };
  onStart?: () => void;
  statusCount: TaskStatusCount;
  loading?: {
    countLoading?: boolean;
    robotLoading?: boolean;
  };
}) {
  const { runAsync: startTask, loading: startTaskLoading } = useStartTask();
  const { runAsync: stopTask, loading: stopTaskLoading } = useStopTask();
  const { name, desc, status, type } = task;
  const router = useRouter();
  const { message } = App.useApp();
  const { total, processing, success, initial, failed, busy, notexist } =
    statusCount ?? {};

  return (
    <ConfigProvider
      theme={{
        components: {
          Card: {
            colorBgContainer: "#fff",
          },
        },
      }}
    >
      <Card variant="borderless" className={styles.taskInfo}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            {type === "outbound" ? (
              <div className={styles.callOut}>
                <CallOut />
              </div>
            ) : (
              <div className={styles.callIn}>
                <CallIn />
              </div>
            )}
          </Col>
          <Col span={24}>
            <BadgeTitle
              title={name}
              desc={desc}
              badge={{
                color: getStatusColor(status),
                text: TaskStatusMap[status],
              }}
            />
          </Col>
          <Col span={24}>
            <Spin spinning={loading?.countLoading}>
              {(status === "processing" || status === "completed") && (
                <>
                  <Text className={styles.progressTitle}>
                    {type === "outbound" ? "外呼进度统计" : "呼入进度统计"}
                  </Text>
                  {(type === "outbound" || type === "out") && (
                    <div className={styles.progress} style={{ width: "100%" }}>
                      <Tooltip title={`拨打中：${processing}`}>
                        <div
                          className={styles.progressItem}
                          style={{
                            width: `${getPercent(+processing, +total)}%`,
                            backgroundColor: "#41D189",
                            borderRadius: "2px",
                          }}
                        />
                      </Tooltip>
                      <Tooltip title={`未接通：${+failed + +busy + +notexist}`}>
                        <div
                          className={styles.progressItem}
                          style={{
                            width: `${getPercent(
                              +failed + +busy + +notexist,
                              +total
                            )}%`,
                            backgroundColor: "#FF004D",
                            right: "2px",
                          }}
                        />
                      </Tooltip>
                      <Tooltip title={`待拨打：${initial}`}>
                        <div
                          className={styles.progressItem}
                          style={{
                            width: `${getPercent(+initial, +total)}%`,
                            backgroundColor: "#FFCE52",
                            right: "4px",
                          }}
                        />
                      </Tooltip>
                      <Tooltip title={`已完成：${success}`}>
                        <div
                          className={styles.progressItem}
                          style={{
                            width: `${getPercent(+success, +total)}%`,
                          }}
                        />
                      </Tooltip>
                    </div>
                  )}
                  {(type === "inbound" || type === "in") && (
                    <div className={styles.progress} style={{ width: "100%" }}>
                      <Tooltip title={`拨打中：${processing}`}>
                        <div
                          className={styles.progressItem}
                          style={{
                            width: `${getPercent(+processing, +total)}%`,
                            backgroundColor: "#FBBC05",
                            borderRadius: "2px",
                          }}
                        />
                      </Tooltip>
                      <Tooltip title={`故障：${+failed + +busy + +notexist}`}>
                        <div
                          className={styles.progressItem}
                          style={{
                            width: `${getPercent(
                              +failed + +busy + +notexist,
                              +total
                            )}%`,
                            backgroundColor: "#FF004D",
                            borderRadius: "2px",
                          }}
                        />
                      </Tooltip>
                      <Tooltip title={`已呼入：${success}`}>
                        <div
                          className={styles.progressItem}
                          style={{
                            width: `${getPercent(+success, +total)}%`,
                            backgroundColor: "#41D189",
                          }}
                        />
                      </Tooltip>
                    </div>
                  )}
                </>
              )}
            </Spin>
          </Col>
          <Col span={24}>
            <Spin spinning={loading?.robotLoading}>
              <div className={styles.progressInfo}>
                <div>执行AI虚拟角色</div>
                <Avatar
                  size={24}
                  src={task?.robot?.avatar}
                  icon={<UserOutlined />}
                />
              </div>
            </Spin>
          </Col>
        </Row>
        <Row justify="end">
          <Space>
            {status === "initial" && (
              <BtnModal
                btnText="启动任务"
                title="确定启动该任务"
                btnProps={{
                  loading: startTaskLoading,
                  type: "primary",
                }}
                // description="任务一旦启动，无法终止"
                onOk={async () => {
                  const res = await startTask(task.id);
                  if (res.success) {
                    message.success("启动成功");
                    onStart();
                    return true;
                  } else {
                    message.error(res.error.message);
                    return false;
                  }
                }}
              />
            )}
            {status === "processing" && (
              <BtnModal
                btnText="停止任务"
                title="确定停止该任务"
                btnProps={{
                  loading: stopTaskLoading,
                  type: "link",
                }}
                // description="任务一旦启动，无法终止"
                onOk={async () => {
                  const res = await stopTask(task.id);
                  if (res.success) {
                    message.success("已停止");
                    return true;
                  } else {
                    message.error(res.error.message);
                    return false;
                  }
                }}
              />
            )}
            <Button
              onClick={() => {
                router.push(
                  getUrl({
                    url: PageUrlMap.TaskEdit,
                    params: {
                      id: task.id,
                    },
                  })
                );
              }}
            >
              详情
            </Button>
          </Space>
        </Row>
      </Card>
    </ConfigProvider>
  );
}

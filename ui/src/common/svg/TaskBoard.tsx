import styles from "./svg.module.css";

const TaskBoard = ({ theme }: { theme: "light" | "dark" }) => {
  if (theme === "light") {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.66992 2.5H18.3399"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10 15.5701V17.5001"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.5399 5.03003H2.46992C2.02809 5.03003 1.66992 5.3882 1.66992 5.83003V14.77C1.66992 15.2119 2.02809 15.57 2.46992 15.57H17.5399C17.9818 15.57 18.3399 15.2119 18.3399 14.77V5.83003C18.3399 5.3882 17.9818 5.03003 17.5399 5.03003Z"
          fill="none"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4.59961 12.2901L8.61961 9.35007L11.0096 11.8801L15.3996 8.32007"
          stroke="#171F2D"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  } else {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.66992 2.5H18.3399"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10 15.5701V17.5001"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.5399 5.03003H2.46992C2.02809 5.03003 1.66992 5.3882 1.66992 5.83003V14.77C1.66992 15.2119 2.02809 15.57 2.46992 15.57H17.5399C17.9818 15.57 18.3399 15.2119 18.3399 14.77V5.83003C18.3399 5.3882 17.9818 5.03003 17.5399 5.03003Z"
          fill="white"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4.59961 12.2901L8.61961 9.35007L11.0096 11.8801L15.3996 8.32007"
          stroke="#171F2D"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
};

export default TaskBoard;

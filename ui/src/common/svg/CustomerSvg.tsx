import styles from "./svg.module.css";

const CustomerIcon = ({ theme }: { theme: "light" | "dark" }) => {
  const color = theme === "light" ? "#000" : "#fff";
  return (
    <svg
      className={styles.svg}
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        d="M17.5003 3.33325H2.50033C2.04009 3.33325 1.66699 3.70635 1.66699 4.16659V15.8333C1.66699 16.2935 2.04009 16.6666 2.50033 16.6666H17.5003C17.9606 16.6666 18.3337 16.2935 18.3337 15.8333V4.16659C18.3337 3.70635 17.9606 3.33325 17.5003 3.33325Z"
        stroke={color}
        strokeOpacity="0.8"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.333 10.4167V13.3334"
        stroke={color}
        strokeOpacity="0.8"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 6.66675V13.3334"
        stroke={color}
        strokeOpacity="0.8"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.66699 8.33325V13.3333"
        stroke={color}
        strokeOpacity="0.8"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CustomerIcon;

import styles from "./svg.module.css";

const AnalysisAssistant = ({ theme }: { theme: "light" | "dark" }) => {
  if (theme === "light") {
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={styles.svg}
      >
        <path
          d="M10 5.31006V7.53006"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.5399 7.53003H2.46992C2.02809 7.53003 1.66992 7.8882 1.66992 8.33003V16.7C1.66992 17.1419 2.02809 17.5 2.46992 17.5H17.5399C17.9818 17.5 18.3399 17.1419 18.3399 16.7V8.33003C18.3399 7.8882 17.9818 7.53003 17.5399 7.53003Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.0009 5.08C10.7134 5.08 11.2909 4.50245 11.2909 3.79C11.2909 3.07755 10.7134 2.5 10.0009 2.5C9.28849 2.5 8.71094 3.07755 8.71094 3.79C8.71094 4.50245 9.28849 5.08 10.0009 5.08Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10 11.7V17.5H15.8C15.8 14.29 13.2 11.7 10 11.7Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M9.99922 11.7C6.78922 11.7 4.19922 14.3 4.19922 17.5H9.99922V11.7Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  } else {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10 5.31006V7.53006"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.5399 7.53003H2.46992C2.02809 7.53003 1.66992 7.8882 1.66992 8.33003V16.7C1.66992 17.1419 2.02809 17.5 2.46992 17.5H17.5399C17.9818 17.5 18.3399 17.1419 18.3399 16.7V8.33003C18.3399 7.8882 17.9818 7.53003 17.5399 7.53003Z"
          fill="white"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.0009 5.08C10.7134 5.08 11.2909 4.50245 11.2909 3.79C11.2909 3.07755 10.7134 2.5 10.0009 2.5C9.28849 2.5 8.71094 3.07755 8.71094 3.79C8.71094 4.50245 9.28849 5.08 10.0009 5.08Z"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10 11.7V17.5H15.8C15.8 14.29 13.2 11.7 10 11.7Z"
          fill="#171F2D"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M9.99922 11.7C6.78922 11.7 4.19922 14.3 4.19922 17.5H9.99922V11.7Z"
          fill="#171F2D"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
};

export default AnalysisAssistant;

import styles from "./svg.module.css";

const Model = ({ theme }: { theme: "light" | "dark" }) => {
  if (theme === "light") {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.36992 2.5H2.91992C2.22992 2.5 1.66992 3.06 1.66992 3.75V9.03C1.66992 9.72 2.22992 10.28 2.91992 10.28H7.36992C8.05992 10.28 8.61992 9.72 8.61992 9.03V3.75C8.61992 3.06 8.05992 2.5 7.36992 2.5Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.36992 12.98H2.91992C2.22992 12.98 1.66992 13.54 1.66992 14.23V16.25C1.66992 16.94 2.22992 17.5 2.91992 17.5H7.36992C8.05992 17.5 8.61992 16.94 8.61992 16.25V14.23C8.61992 13.54 8.05992 12.98 7.36992 12.98Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.6309 17.5002H17.0809C17.7709 17.5002 18.3309 16.9402 18.3309 16.2502V10.9702C18.3309 10.2802 17.7709 9.72021 17.0809 9.72021H12.6309C11.9409 9.72021 11.3809 10.2802 11.3809 10.9702V16.2502C11.3809 16.9402 11.9409 17.5002 12.6309 17.5002Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.6309 7.02H17.0809C17.7709 7.02 18.3309 6.46 18.3309 5.77V3.75C18.3309 3.06 17.7709 2.5 17.0809 2.5H12.6309C11.9409 2.5 11.3809 3.06 11.3809 3.75V5.77C11.3809 6.46 11.9409 7.02 12.6309 7.02Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  } else {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.37012 12.9805C8.06003 12.9806 8.62012 13.5405 8.62012 14.2305V16.25C8.62011 16.9399 8.06002 17.4999 7.37012 17.5H2.91992C2.22993 17.5 1.66993 16.94 1.66992 16.25V14.2305C1.66992 13.5405 2.22992 12.9805 2.91992 12.9805H7.37012ZM17.0811 9.7207C17.771 9.72081 18.3311 10.2808 18.3311 10.9707V16.25C18.3311 16.9399 17.771 17.4999 17.0811 17.5H12.6309C11.9409 17.5 11.3809 16.94 11.3809 16.25V10.9707C11.3809 10.2807 11.9409 9.7207 12.6309 9.7207H17.0811ZM7.37012 2.5C8.06003 2.50011 8.62012 3.06007 8.62012 3.75V9.03027C8.61997 9.72008 8.05994 10.2802 7.37012 10.2803H2.91992C2.23001 10.2803 1.67007 9.72015 1.66992 9.03027V3.75C1.66992 3.06 2.22992 2.5 2.91992 2.5H7.37012ZM17.0811 2.5C17.771 2.50011 18.3311 3.06007 18.3311 3.75V5.76953C18.3311 6.45947 17.771 7.01943 17.0811 7.01953H12.6309C11.9409 7.01953 11.3809 6.45953 11.3809 5.76953V3.75C11.3809 3.06 11.9409 2.5 12.6309 2.5H17.0811Z"
          fill="white"
        />
        <path
          d="M7.37012 12.9805L7.37022 12.2805H7.37012V12.9805ZM8.62012 16.25L9.32012 16.25V16.25H8.62012ZM7.37012 17.5V18.2H7.37022L7.37012 17.5ZM2.91992 17.5L2.91992 18.2H2.91992V17.5ZM1.66992 16.25H0.969922V16.25L1.66992 16.25ZM2.91992 12.9805V12.2805H2.91992L2.91992 12.9805ZM17.0811 9.7207L17.0812 9.0207H17.0811V9.7207ZM17.0811 17.5V18.2H17.0812L17.0811 17.5ZM7.37012 2.5L7.37022 1.8H7.37012V2.5ZM8.62012 9.03027L9.32012 9.03042V9.03027H8.62012ZM7.37012 10.2803V10.9803H7.37022L7.37012 10.2803ZM2.91992 10.2803L2.91992 10.9803H2.91992V10.2803ZM1.66992 9.03027H0.969922V9.03042L1.66992 9.03027ZM2.91992 2.5V1.8H2.91992L2.91992 2.5ZM17.0811 2.5L17.0812 1.8H17.0811V2.5ZM17.0811 7.01953V7.71953H17.0812L17.0811 7.01953ZM7.37012 12.9805L7.37001 13.6805C7.67351 13.6805 7.92012 13.9272 7.92012 14.2305H8.62012H9.32012C9.32012 13.1538 8.44654 12.2806 7.37022 12.2805L7.37012 12.9805ZM8.62012 14.2305H7.92012V16.25H8.62012H9.32012V14.2305H8.62012ZM8.62012 16.25L7.92012 16.25C7.92011 16.5532 7.67349 16.8 7.37001 16.8L7.37012 17.5L7.37022 18.2C8.44655 18.1998 9.3201 17.3266 9.32012 16.25L8.62012 16.25ZM7.37012 17.5V16.8H2.91992V17.5V18.2H7.37012V17.5ZM2.91992 17.5L2.91992 16.8C2.61654 16.8 2.36993 16.5534 2.36992 16.25L1.66992 16.25L0.969922 16.25C0.969938 17.3266 1.84332 18.2 2.91992 18.2L2.91992 17.5ZM1.66992 16.25H2.36992V14.2305H1.66992H0.969922V16.25H1.66992ZM1.66992 14.2305H2.36992C2.36992 13.9271 2.61652 13.6805 2.91992 13.6805L2.91992 12.9805L2.91992 12.2805C1.84332 12.2805 0.969922 13.1539 0.969922 14.2305H1.66992ZM2.91992 12.9805V13.6805H7.37012V12.9805V12.2805H2.91992V12.9805ZM17.0811 9.7207L17.0809 10.4207C17.3844 10.4207 17.6311 10.6675 17.6311 10.9707H18.3311H19.0311C19.0311 9.89406 18.1575 9.02087 17.0812 9.0207L17.0811 9.7207ZM18.3311 10.9707H17.6311V16.25H18.3311H19.0311V10.9707H18.3311ZM18.3311 16.25H17.6311C17.6311 16.5532 17.3844 16.8 17.0809 16.8L17.0811 17.5L17.0812 18.2C18.1575 18.1998 19.0311 17.3266 19.0311 16.25H18.3311ZM17.0811 17.5V16.8H12.6309V17.5V18.2H17.0811V17.5ZM12.6309 17.5V16.8C12.3275 16.8 12.0809 16.5534 12.0809 16.25H11.3809H10.6809C10.6809 17.3266 11.5543 18.2 12.6309 18.2V17.5ZM11.3809 16.25H12.0809V10.9707H11.3809H10.6809V16.25H11.3809ZM11.3809 10.9707H12.0809C12.0809 10.6673 12.3275 10.4207 12.6309 10.4207V9.7207V9.0207C11.5543 9.0207 10.6809 9.8941 10.6809 10.9707H11.3809ZM12.6309 9.7207V10.4207H17.0811V9.7207V9.0207H12.6309V9.7207ZM7.37012 2.5L7.37001 3.2C7.67351 3.20005 7.92012 3.44677 7.92012 3.75H8.62012H9.32012C9.32012 2.67336 8.44654 1.80016 7.37022 1.8L7.37012 2.5ZM8.62012 3.75H7.92012V9.03027H8.62012H9.32012V3.75H8.62012ZM8.62012 9.03027L7.92012 9.03012C7.92005 9.33349 7.67327 9.58023 7.37001 9.58027L7.37012 10.2803L7.37022 10.9803C8.4466 10.9801 9.31989 10.1067 9.32012 9.03042L8.62012 9.03027ZM7.37012 10.2803V9.58027H2.91992V10.2803V10.9803H7.37012V10.2803ZM2.91992 10.2803L2.91992 9.58027C2.61676 9.58027 2.36999 9.33366 2.36992 9.03012L1.66992 9.03027L0.969922 9.03042C0.970153 10.1066 1.84326 10.9803 2.91992 10.9803L2.91992 10.2803ZM1.66992 9.03027H2.36992V3.75H1.66992H0.969922V9.03027H1.66992ZM1.66992 3.75H2.36992C2.36992 3.4466 2.61652 3.2 2.91992 3.2L2.91992 2.5L2.91992 1.8C1.84332 1.8 0.969922 2.6734 0.969922 3.75H1.66992ZM2.91992 2.5V3.2H7.37012V2.5V1.8H2.91992V2.5ZM17.0811 2.5L17.0809 3.2C17.3844 3.20005 17.6311 3.44677 17.6311 3.75H18.3311H19.0311C19.0311 2.67336 18.1575 1.80016 17.0812 1.8L17.0811 2.5ZM18.3311 3.75H17.6311V5.76953H18.3311H19.0311V3.75H18.3311ZM18.3311 5.76953H17.6311C17.6311 6.07276 17.3844 6.31948 17.0809 6.31953L17.0811 7.01953L17.0812 7.71953C18.1575 7.71937 19.0311 6.84617 19.0311 5.76953H18.3311ZM17.0811 7.01953V6.31953H12.6309V7.01953V7.71953H17.0811V7.01953ZM12.6309 7.01953V6.31953C12.3275 6.31953 12.0809 6.07293 12.0809 5.76953H11.3809H10.6809C10.6809 6.84613 11.5543 7.71953 12.6309 7.71953V7.01953ZM11.3809 5.76953H12.0809V3.75H11.3809H10.6809V5.76953H11.3809ZM11.3809 3.75H12.0809C12.0809 3.4466 12.3275 3.2 12.6309 3.2V2.5V1.8C11.5543 1.8 10.6809 2.6734 10.6809 3.75H11.3809ZM12.6309 2.5V3.2H17.0811V2.5V1.8H12.6309V2.5Z"
          fill="white"
        />
      </svg>
    );
  }
};

export default Model;

import styles from "./svg.module.css";

const EmployeeInfo = ({ theme }: { theme: "light" | "dark" }) => {
  if (theme === "light") {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.66992 10.03H5.07992"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.66992 6.57007H5.07992"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.66992 13.48H5.07992"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.5301 2.5H4.13008C3.68825 2.5 3.33008 2.85817 3.33008 3.3V16.7C3.33008 17.1418 3.68825 17.5 4.13008 17.5H17.5301C17.9719 17.5 18.3301 17.1418 18.3301 16.7V3.3C18.3301 2.85817 17.9719 2.5 17.5301 2.5Z"
          fill="none"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14.7708 12.1001H8.10078C7.65895 12.1001 7.30078 12.4583 7.30078 12.9001V14.1801C7.30078 14.6219 7.65895 14.9801 8.10078 14.9801H14.7708C15.2126 14.9801 15.5708 14.6219 15.5708 14.1801V12.9001C15.5708 12.4583 15.2126 12.1001 14.7708 12.1001Z"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.4394 10.2C12.7262 10.2 13.7694 9.15686 13.7694 7.87004C13.7694 6.58322 12.7262 5.54004 11.4394 5.54004C10.1526 5.54004 9.10938 6.58322 9.10938 7.87004C9.10938 9.15686 10.1526 10.2 11.4394 10.2Z"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  } else {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.66992 10.03H5.07992"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.66992 6.57007H5.07992"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.66992 13.48H5.07992"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.5301 2.5H4.13008C3.68825 2.5 3.33008 2.85817 3.33008 3.3V16.7C3.33008 17.1418 3.68825 17.5 4.13008 17.5H17.5301C17.9719 17.5 18.3301 17.1418 18.3301 16.7V3.3C18.3301 2.85817 17.9719 2.5 17.5301 2.5Z"
          fill="white"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14.7708 12.1001H8.10078C7.65895 12.1001 7.30078 12.4583 7.30078 12.9001V14.1801C7.30078 14.6219 7.65895 14.9801 8.10078 14.9801H14.7708C15.2126 14.9801 15.5708 14.6219 15.5708 14.1801V12.9001C15.5708 12.4583 15.2126 12.1001 14.7708 12.1001Z"
          stroke="#171F2D"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.4394 10.2C12.7262 10.2 13.7694 9.15686 13.7694 7.87004C13.7694 6.58322 12.7262 5.54004 11.4394 5.54004C10.1526 5.54004 9.10938 6.58322 9.10938 7.87004C9.10938 9.15686 10.1526 10.2 11.4394 10.2Z"
          stroke="#171F2D"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
};

export default EmployeeInfo;

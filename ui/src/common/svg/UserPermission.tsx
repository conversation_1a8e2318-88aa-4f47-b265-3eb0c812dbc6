import styles from "./svg.module.css";

// 用户权限管理
const UserPermission = ({ theme }: { theme: "light" | "dark" }) => {
  if (theme === "light") {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.91667 8.33333C9.5275 8.33333 10.8333 7.0275 10.8333 5.41667C10.8333 3.80584 9.5275 2.5 7.91667 2.5C6.30583 2.5 5 3.80584 5 5.41667C5 7.0275 6.30583 8.33333 7.91667 8.33333Z"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.75 3.33325C13.75 3.33325 14.6875 5.20825 13.75 7.49992"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.666 1.66675C16.666 1.66675 18.541 5.04175 16.666 9.16675"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.66602 17.0001V17.5001H14.166V17.0001C14.166 15.1332 14.166 14.1998 13.8027 13.4868C13.4831 12.8596 12.9732 12.3496 12.346 12.03C11.6329 11.6667 10.6995 11.6667 8.83268 11.6667H6.99935C5.13252 11.6667 4.1991 11.6667 3.48605 12.03C2.85884 12.3496 2.3489 12.8596 2.02933 13.4868C1.66602 14.1998 1.66602 15.1332 1.66602 17.0001Z"
          stroke="#323232"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  } else {
    return (
      <svg
        className={styles.svg}
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.91667 8.33333C9.5275 8.33333 10.8333 7.0275 10.8333 5.41667C10.8333 3.80584 9.5275 2.5 7.91667 2.5C6.30583 2.5 5 3.80584 5 5.41667C5 7.0275 6.30583 8.33333 7.91667 8.33333Z"
          fill="white"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.75 3.33325C13.75 3.33325 14.6875 5.20825 13.75 7.49992"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.666 1.66675C16.666 1.66675 18.541 5.04175 16.666 9.16675"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.66602 17.0001V17.5001H14.166V17.0001C14.166 15.1332 14.166 14.1998 13.8027 13.4868C13.4831 12.8596 12.9732 12.3496 12.346 12.03C11.6329 11.6667 10.6995 11.6667 8.83268 11.6667H6.99935C5.13252 11.6667 4.1991 11.6667 3.48605 12.03C2.85884 12.3496 2.3489 12.8596 2.02933 13.4868C1.66602 14.1998 1.66602 15.1332 1.66602 17.0001Z"
          fill="white"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
};

export default UserPermission;

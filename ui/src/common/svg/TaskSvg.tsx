import styles from "./svg.module.css";

const TaskSvg = ({ theme }: { theme: "light" | "dark" }) => {
  if (theme === "light") {
    return (
      <svg
        className={styles.svg}
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path
          d="M17.0837 2.5H2.91699C2.22664 2.5 1.66699 3.05964 1.66699 3.75V16.25C1.66699 16.9404 2.22664 17.5 2.91699 17.5H17.0837C17.774 17.5 18.3337 16.9404 18.3337 16.25V3.75C18.3337 3.05964 17.774 2.5 17.0837 2.5Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3.66699 5.83334H16.3337"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.33301 10H14.9997"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.33301 13.3333H14.9997"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5 10H5.83333"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5 13.3333H5.83333"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  } else {
    return (
      <svg
        className={styles.svg}
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path
          d="M17.0834 2.5H2.91675C2.22639 2.5 1.66675 3.05964 1.66675 3.75V16.25C1.66675 16.9404 2.22639 17.5 2.91675 17.5H17.0834C17.7738 17.5 18.3334 16.9404 18.3334 16.25V3.75C18.3334 3.05964 17.7738 2.5 17.0834 2.5Z"
          fill="white"
          stroke="white"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3.66675 5.83334H16.3334"
          stroke="#1A2230"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.33325 10H14.9999"
          stroke="#1A2230"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.33325 13.3333H14.9999"
          stroke="#1A2230"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5 10H5.83333"
          stroke="#1A2230"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5 13.3333H5.83333"
          stroke="#1A2230"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
};

export default TaskSvg;

import styles from "./svg.module.css";

const Report = ({ theme }: { theme: "light" | "dark" }) => {
  if (theme === "light") {
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={styles.svg}
      >
        <path
          d="M9.37 4.21997C5.58 4.21997 2.5 7.29997 2.5 11.09C2.5 14.88 5.58 17.96 9.37 17.96C13.16 17.96 16.24 14.88 16.24 11.09C16.24 11.09 16.24 11.09 16.24 11.08H9.37V4.21997Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.5508 2.04004V8.90004H18.4208C18.4208 5.11004 15.3408 2.04004 11.5508 2.04004Z"
          stroke="black"
          strokeOpacity="0.8"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  } else {
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={styles.svg}
      >
        <path
          d="M9.37 4.21997C5.58 4.21997 2.5 7.29997 2.5 11.09C2.5 14.88 5.58 17.96 9.37 17.96C13.16 17.96 16.24 14.88 16.24 11.09C16.24 11.09 16.24 11.09 16.24 11.08H9.37V4.21997Z"
          fill="white"
        />
        <path
          d="M11.5508 2.04004V8.90004H18.4208C18.4208 5.11004 15.3408 2.04004 11.5508 2.04004Z"
          fill="white"
        />
      </svg>
    );
  }
};

export default Report;

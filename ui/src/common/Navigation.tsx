"use client";
import { getUrl } from "@/common/component/util";
import { PageUrlMap } from "@/common/constant";
import { get as apiGet, post as apiPost } from "@/service/apiUtility";
import {
  Avatar,
  Button,
  ConfigProvider,
  Layout,
  Menu,
  message,
  Popover,
  Spin,
} from "antd";
import { usePathname, useRouter } from "next/navigation";
import { FC, PropsWithChildren, useEffect, useState } from "react";
import { NavigationKey, page_schema } from "./constant";
import styles from "./Navigation.module.css";

const { Sider, Header } = Layout;

// 用户信息类型
interface User {
  id: string;
  createTime: string;
  updateTime: string;
  creatorId?: string | null;
  creatorName?: string | null;
  updaterId?: string | null;
  updaterName?: string | null;
  name: string;
  avatar: string;
  phone: string;
  email: string;
}

const Navigation: FC<PropsWithChildren> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  // 用户信息
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  let toLoginFlag = false;
  const redirectToLogin = () => {
    if (!toLoginFlag) {
      toLoginFlag = true;
      router.push(getUrl({ url: PageUrlMap.Login }));
    }
  };
  const isValidPage = () => {
    return [
      NavigationKey.AIVirtualSeat,
      NavigationKey.Task,
      NavigationKey.UserPermission,
      NavigationKey.EmployeeInfo,
      NavigationKey.AnalysisAssistant,
      NavigationKey.TaskBoard,
      NavigationKey.Report,
      NavigationKey.ModelManage,
    ].includes(selectedKeys as NavigationKey);
  };

  useEffect(() => {
    if (isValidPage()) {
      apiGet({ path: "/api/user/current" })
        .then(
          (res: {
            success: boolean;
            data?: User;
            error?: { code: number; message: string };
          }) => {
            if (res.success) {
              setUser(res.data!);
            } else {
              if (res.error?.code === 401) {
                message.error("登录已过期，请重新登录");
                redirectToLogin();
              } else {
                message.error(res.error?.message || "获取用户信息失败");
              }
            }
          }
        )
        .catch((err: unknown) => {
          const errorObj = err as {
            responseJSON?: { error?: { code?: number; message?: string } };
          };
          if (errorObj?.responseJSON?.error?.code === 401) {
            redirectToLogin();
          } else {
            message.error(
              errorObj?.responseJSON?.error?.message || "获取用户信息失败"
            );
          }
        })
        .finally(() => setLoading(false));
    }
  }, [pathname]);

  // 退出登录
  const handleLogout = async () => {
    try {
      await apiPost({ path: "/api/user/logout" });
    } finally {
      router.push(getUrl({ url: PageUrlMap.Login }));
    }
  };

  //TODO: 需要优化
  const selectedKeys = pathname
    .replace("/ui", "")
    .replace(".html", "")
    .replace(/\?.*$/, "")
    .split("/")[1];
  if (pathname.includes("login")) {
    return children;
  }
  if (!isValidPage()) {
    redirectToLogin();
  }
  return (
    <Layout style={{ height: "100vh" }}>
      <Sider width={320} style={{ background: "#fff" }}>
        <div
          style={{
            height: "10%",
            position: "relative",
            top: "-10px",
            left: "24px",
          }}
        >
          <img
            src={getUrl({ url: "/logo.png", isPage: false })}
            height="70"
            style={{
              position: "relative",
              top: "120px",
              left: "20px",
            }}
          />
        </div>
        <div className={styles.menuContainer}>
          <ConfigProvider
            theme={{
              components: {
                Menu: {
                  itemSelectedBg: "#171F2D",
                  itemSelectedColor: "#fff",
                },
              },
            }}
          >
            <Menu
              className={styles.navigationMenu}
              style={{
                flex: 1,
                padding: "60% 24px 0 24px",
              }}
              mode="vertical"
              selectedKeys={[selectedKeys]}
              items={Object.keys(page_schema).map((key) => {
                const { label, icon: Icon } = page_schema[key];
                return {
                  label,
                  key,
                  icon: (
                    <Icon theme={selectedKeys === key ? "dark" : "light"} />
                  ),
                };
              })}
              onClick={(e) => {
                router.push(getUrl({ url: `/${e.key}` }));
              }}
            />
          </ConfigProvider>
        </div>
      </Sider>
      <Layout style={{ overflow: "auto" }}>
        <Header
          style={{
            background: "#f8f8fa",
            display: "flex",
            justifyContent: "flex-end",
            paddingLeft: "16px",
          }}
        >
          {loading ? (
            <Spin size="small" />
          ) : user ? (
            <Popover
              placement="bottomRight"
              content={
                <Button type="text" danger block onClick={handleLogout}>
                  退出登录
                </Button>
              }
              trigger="click"
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <Avatar
                  src={user.avatar}
                  size={36}
                  style={{ marginRight: 8 }}
                />
                <span style={{ fontWeight: 500 }}>{user.name}</span>
              </div>
            </Popover>
          ) : null}
        </Header>
        <ConfigProvider
          theme={{
            components: {
              Table: {
                headerBg: "#171F2D",
                headerColor: "#fff",
                headerSortHoverBg: "#171F2D",
                headerSortActiveBg: "#171F2D",
              },
            },
          }}
        >
          {user ? children : <Spin />}
        </ConfigProvider>
      </Layout>
    </Layout>
  );
};

export default Navigation;

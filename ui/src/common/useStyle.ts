import { ConfigProvider } from "antd";
import { createStyles } from "antd-style";
import React from "react";

export const useStyle = () => {
  const { getPrefixCls } = React.useContext(ConfigProvider.ConfigContext);
  const layoutPrefixCls = getPrefixCls("layout");
  return createStyles(({ css }) => ({
    layout: css`
      background: #fff;
      .${layoutPrefixCls}-content {
        background: #fff;
        overflow: auto;
        padding: 24px;
      }
    `,
  }))();
};
